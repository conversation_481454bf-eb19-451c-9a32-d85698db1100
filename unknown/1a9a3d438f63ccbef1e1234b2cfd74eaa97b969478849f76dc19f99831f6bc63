global:
  productName: yflow
  appVersion: {{chartversion}}
  trackAdminSessions: true
  urlApiCognitiveServices: https://ysmart.ysocial.net/api/v2/
  cognitivityApiVersion: 2
  dniServiceUrl: https://yflow-dni-validator.ysocial.net
  urlCentralize: https://fbsocial.ysocial.net/
  amountRecordImport: 998
  storageYFlowPath: /home/<USER>/StorageYflow/ 
  hostInsideIIS: false
  virtualDirPath: ./
  multiCompany: false
  serviceBusUse: false
  enabledLanguajes: es,en,pt
  urlClientAppCognitiveServices: https://ysmart.ysocial.net/api/home/<USER>
  flowType: Bot
  downloadReportTypes: background
  NODE_ENV: prod
  yFlowUtilsUrl: https://common.ysocial.net
  yBiometricUrl: https://biometric.ysocial.net
  yoizMeUrl: yoiz.me
  useAzureBlobStorage: true
  standAlone: false
  PORT: 3000
  registry: yoizenacr.azurecr.io
  resourceGroup: k8s-rg
  storageAccount: yoizenaks


executor:
  appName: executor
  enableIngress: true
  requestsCPU: "50m"
  requestsMEM: "200Mi"
  replicas: "1"
  mountPath: "/home/<USER>/StorageYflow/"
  healthcheck: "/api/health_check"
  requiredConfigMapEnv:
    - client
    - gmt
    - PORT
    - enabledLanguajes
    - defaultLanguaje
    - ySocialUrl
    - yflowUrl
    - urlApiCognitiveServices
    - dniServiceUrl
    - dbintervalsdialect
    - dbintegrationsauditdialect
    - dbdialect
    - useAzureBlobStorage
    - isContingencyBot
    - dbcontextdialect
    - amountRecordImport
  optionalConfigMapEnv:
    - NODE_ENV
    - key
    - iv
    - generateFilesForDefaultAnswerBlock
    - standAlone
    - yFlowUtilsUrl
    - yBiometricUrl
    - cognitivityApiVersion
    - yflowUrlAccountLinkingPostback
    - yflowUrlLocationPicker
    - yoizMeToken
    - yoizMeUrl
    - dbintervalsMaxPoolSize
    - dbcontextMaxPoolSize
  requiredSecretEnv:
    - ySocialAccessToken
    - ySocialAccessTokenSecret
    - dniServiceApiKey
    - dbintervalsConnectionString
    - yFlowConnectionString
    - storageConnectionString
    - redisConnectionString
  optionalSecretEnv:
    - dbcontextConnectionString
    - dbintegrationsauditConnectionString

intervals:
  appName: intervals
  requestsCPU: "50m"
  requestsMEM: "200Mi"
  replicas: "1"
  mountPath: "/home/<USER>/StorageYflow/"
  requiredConfigMapEnv:
    - client
    - gmt
    - defaultLanguaje
    - ySocialUrl
    - yflowUrl
    - dniServiceUrl
    - dbcontextdialect    
    - dbintervalsdialect
    - dbintegrationsauditdialect
    - dbdialect
    - useAzureBlobStorage
    - urlCentralize
    - storageYFlowPath
    - isContingencyBot
  optionalConfigMapEnv:
    - NODE_ENV
    - key
    - iv
    - standAlone
    - generateFilesForDefaultAnswerBlock
    - amountRecordImport
    - dbintervalsMaxPoolSize
    - dbcontextMaxPoolSize
  requiredSecretEnv:
    - dniServiceApiKey
    - redisConnectionString
    - dbintervalsConnectionString
    - yFlowConnectionString
    - storageConnectionString
  optionalSecretEnv:
    - dbcontextConnectionString
    - dbintegrationsauditConnectionString


web:
  appName: web
  enableIngress: true
  requestsCPU: "50m"
  requestsMEM: "200Mi"
  replicas: "1"
  mountPath: "/home/<USER>/StorageYflow/"
  healthcheck: "/api/health_check"
  requiredConfigMapEnv:
    - multipleCores
    - enabledLanguajes
    - defaultLanguaje
    - client
    - storageYFlowPath
    - dbdialect
    - executorUrl
    - flowType
    - downloadReportTypes
    - useAzureBlobStorage
    - isContingencyBot
    - ySmartEnabled
    - yflowUrl
    - hostInsideIIS
    - useSocketIo
    - updateCasePieceEnabledInChat
    - trackAdminSessions
    - gmt
    - ySocialUrl
  optionalConfigMapEnv:
    - key
    - iv
    - NODE_ENV
    - standAlone
    - multiCompany
    - serviceBusUse
    - googleClientID
    - urlApiCognitiveServices
    - urlClientAppCognitiveServices
    - cognitivityApiVersion
    - dbMaxPoolSize
  requiredSecretEnv:
    - ySocialAccessToken
    - ySocialAccessTokenSecret
    - yFlowConnectionString 
    - redisConnectionString
    - googleApiKey
  optionalSecretEnv:
    - tokenCognitiveService

worker: 
  appName: worker
  requestsCPU: "50m"
  requestsMEM: "100Mi"
  replicas: "1"
  mountPath: "/home/<USER>/StorageYflow/"
  requiredConfigMapEnv:
    - maxConcurrentCalls
    - storageYFlowPath
    - client
    - dbcontextdialect
  requiredSecretEnv:
    - redisConnectionString
    - endpointSB
