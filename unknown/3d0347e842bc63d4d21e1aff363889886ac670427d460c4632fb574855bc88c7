import { isEnvironmentVariableValid, parseToBoolean } from "./Helpers";
import { logger } from "./Logger";

export class ConfigFileStorage {
    storageConnectionString: string;
    useAzureBlobStorage: boolean;
}

export const configFileStorage: ConfigFileStorage = {
    storageConnectionString: process.env.storageConnectionString,
    useAzureBlobStorage: parseToBoolean(process.env.useAzureBlobStorage, null)
}

const validateAndFormatConfig = () => {
    if (configFileStorage.useAzureBlobStorage === null) {
        logger.error(`faltan inicializar variables de entorno: useAzureBlobStorage`);
        process.exit(9);
    }

    if (configFileStorage.useAzureBlobStorage && !isEnvironmentVariableValid(configFileStorage.storageConnectionString)) {
        logger.error(`faltan inicializar variables de entorno: storageConnectionString`);
        process.exit(9);
    }
}

validateAndFormatConfig();