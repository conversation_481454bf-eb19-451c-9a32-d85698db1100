import { VariableInputValidator } from "./variable-input-validator";
import { isStringValid } from "../urlutils.module";

export class StringUtils {

  static isEmpty(str) : boolean {
      return (!str || 0 === str.length);
  }

// commented out by mlopez
/*  static validateStringWithScripts(str: string): boolean {
    if(!isStringValid(str)) {
      return false;
    }
    let validator = new VariableInputValidator(null);
    let searchScript = validator.getScriptPositionsInText(str);
    return !searchScript.isScriptTagOpen;
  }*/

}