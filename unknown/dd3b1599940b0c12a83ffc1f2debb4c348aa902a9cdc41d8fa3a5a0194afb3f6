import { DbConfigBase } from "./ConfigDbBase";
import { isEnvironmentVariableValid, parseToInt, parseToBoolean } from "./Helpers";
import { logger } from "./Logger";

export class DbIntervals extends DbConfigBase {
}

export const dbIntervals = new DbIntervals();
dbIntervals.host = process.env.dbintervalshost;
dbIntervals.port = parseToInt(process.env.dbintervalsport);
dbIntervals.name = process.env.dbintervalsname;
dbIntervals.username = process.env.dbintervalsusername;
dbIntervals.password = process.env.dbintervalspassword;
dbIntervals.dialect = process.env.dbintervalsdialect;
dbIntervals.dbtimeout = parseToInt(process.env.dbintervalsdbtimeout, 30000);
dbIntervals.dbcanceltimeout = parseToInt(process.env.dbintervalsdbcanceltimeout, 5000);
dbIntervals.ssl = parseToBoolean(process.env.dbintervalsssl, false);
dbIntervals.enable = true;
dbIntervals.connectionString = process.env.dbintervalsConnectionString;
dbIntervals.poolMaxSize = parseToInt(process.env.dbintervalsMaxPoolSize, 5);

const validateAndFormatConfig = () => {
    if (!isEnvironmentVariableValid(dbIntervals.dialect)) {
        logger.error(`Faltan datos de conexión al Sql de Intervals - Si no va usar esta base de datos, pase la palabra 'disabled'`);
        process.exit(9);
    }

    dbIntervals.dialect = dbIntervals.dialect.toLowerCase();

    if (dbIntervals.dialect !== 'disabled') {
        dbIntervals.parseConnectionString()

        if (!(isEnvironmentVariableValid(dbIntervals.host) &&
            isEnvironmentVariableValid(dbIntervals.port) &&
            isEnvironmentVariableValid(dbIntervals.name) &&
            isEnvironmentVariableValid(dbIntervals.username) &&
            isEnvironmentVariableValid(dbIntervals.password) &&
            isEnvironmentVariableValid(dbIntervals.dialect))) {
            logger.error(`Faltan datos de conexión al Sql de Intervals`);
            process.exit(9);
        }
    }
    else {
        dbIntervals.enable = false;
    }
}

validateAndFormatConfig();