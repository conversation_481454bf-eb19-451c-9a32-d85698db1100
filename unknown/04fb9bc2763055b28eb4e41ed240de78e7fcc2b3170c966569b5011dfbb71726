import { IIntegrationAuditPort } from './ports/IIntegrationAuditPort';
import { SequelizeIntegrationAuditAdapter } from './adapters/sequelize/SequelizeIntegrationAuditAdapter';
import { dbIntegrationsAudit } from '../../Yoizen.yFlow.Helpers/src/ConfigDbIntegrationsAudit';
import { IntegrationAuditMock } from './adapters/mock/IntegrationAuditMock';
import { logger } from '../../Yoizen.yFlow.Helpers/src/Logger';

export async function InitIntegrationAuditServices() {
    // Inicializo la base de datos
    logger.info('yFlow.Infrastructure Inicializando base de datos de la auditoría de integraciones');

    // Inicializo repositorios
    let systemIntegrationAuditPort: IIntegrationAuditPort;
    switch (dbIntegrationsAudit.dialect) {
        case 'mysql':
            systemIntegrationAuditPort = new SequelizeIntegrationAuditAdapter();
            break;
        case 'mssql':
            systemIntegrationAuditPort = new SequelizeIntegrationAuditAdapter();
            break;
        default:
            if (!dbIntegrationsAudit.enable) {
                systemIntegrationAuditPort = new IntegrationAuditMock();
            } else {
                throw new Error('El dialecto de la base de datos IntegrationAudit no es válido');
            }

    }
    await systemIntegrationAuditPort.connect();

    return {
        systemIntegrationAuditPort
    }
}