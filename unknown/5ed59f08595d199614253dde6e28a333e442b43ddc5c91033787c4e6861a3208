import { IConfigurationPort } from "../../ports/IConfigurationPort";
import ConfigurationRepository from '../../../../Yoizen.yFlow.Web/models/configurations';
import { TableHints } from "sequelize";
import { logger } from "../../../../Yoizen.yFlow.Helpers/src/Logger";

export class SequelizeConfigurationAdapter implements IConfigurationPort {

    async Get(): Promise<any> {
        try {
            return await ConfigurationRepository.findOne({
                attributes: ['content'],
                where: {
                    name: 'AutoReportData'
                },
                tableHint: TableHints.NOLOCK
            });
        } catch (error) {
            logger.error({ error: error }, `Error en el cliente de SequelizeConfigurationAdapter`);
        }

    }
}