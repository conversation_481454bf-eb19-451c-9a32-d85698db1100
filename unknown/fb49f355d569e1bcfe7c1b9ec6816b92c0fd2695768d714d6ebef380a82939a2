
import { createClient, RedisClientType } from 'redis';
import { ICachePort } from '../ports/ICachePort';
import myCache from '../../../Yoizen.yFlow.Web/helpers/mycache';
import { config } from '../../../Yoizen.yFlow.Helpers/src/Config';
import { dbRedis } from '../../../Yoizen.yFlow.Helpers/src/ConfigRedis';
import { logger } from '../../../Yoizen.yFlow.Helpers/src/Logger';

export class FlowRedisCacheRepository implements ICachePort {
    public client: RedisClientType;

    constructor() {

        this.client = createClient({
            socket: {
                host: dbRedis.host,
                port: dbRedis.port,
                passphrase: dbRedis.password,
                tls: true,
                keepAlive: 30000,  // Enviar keep-alive cada 30 segundos
                reconnectStrategy: (retries) => {
                    if (retries > 10) { // Máximo 10 reintentos
                        return new Error("Max retries reached");
                    }
                    const delay = Math.min(retries * 1000, 10000);
                    logger.info(`🟡 Intentando reconexión #${retries} en ${delay}ms`);
                    return delay;
                },
                connectTimeout: 5000,
            },
            password: dbRedis.password,
            name: config.client
        });
        /*} else {
            this.client = createClient({
                socket: {
                    host: dbRedis.host,
                    port: dbRedis.port,
                    passphrase: dbRedis.password,
                    tls: true,
                    //key: config.redisKey,
                },
                password: dbRedis.password,
                name: config.client
            });
        }*/

        this.onConnect();
    }
    async init(): Promise<void> {
        try {
            await this.client.connect();
            await this.client.subscribe(config.client, function (message, channel) {
                try {
                    logger.info(`Se recibió mensaje por el canal ${channel} con mensaje ${message}`);

                    let info = JSON.parse(message);
                    if (typeof (info.action) === 'string') {  
                        switch (info.action) {
                            case 'publish':
                                if (myCache.has(info.cacheKey)) {
                                    myCache.del(info.cacheKey);
                                }
                                break;
                            case 'saving':
                                if (myCache.has(info.cacheKey)) {
                                    myCache.del(info.cacheKey);
                                }
                                break;
                            case 'smtpconfig':
                                let smtpCacheKey = `${config.client}-smtp`;
                                if (myCache.has(smtpCacheKey)) {
                                    myCache.del(smtpCacheKey);
                                }
                                break;
                            case 'changeLogLevel':
                                // Aplicar el cambio de nivel de log
                                if (info.level && ['trace', 'debug', 'info', 'warn', 'error', 'fatal'].includes(info.level)) {
                                    const { changeLogLevel } = require('../../../Yoizen.yFlow.Helpers/src/Logger');
                                    changeLogLevel(info.level);
                                    logger.debug(`Nivel de log cambiado a través de Redis: ${info.level}`);
                                }
                                break;
                            default:
                                logger.info(`No hay programado para multicores procesar el evento ${info.action}`);
                                break;
                        }
                    }
                } catch (error) {
                    logger.error(`Error en el cliente de redis Flow: ${error.toString()}`);
                }
            });
        } catch (error) {
            logger.error(`Error en el cliente de redis Flow: ${error.toString()}`);
        }
    }

    type(): string {
        return 'Flow';
    }

    onConnect(): void {
        setInterval(() => {
            if (this.client.isReady) { // Verificar si la conexión está activa
                this.client.ping().catch(err => {
                    logger.error("Error al enviar PING:", err);
                });
            }
        }, 60000); // 60 segundos

        this.client.on("error", function (error) {
            if (error.code === "ETIMEDOUT" || error.code === "ECONNRESET") {
                logger.info("Error de conexión transitorio:", error.message);
            } else if (typeof (error) === 'object') {
                logger.fatal(`CRITICAL - Error en el cliente de redis Flow: ${JSON.stringify(error)}`);
            }
            else {
                logger.fatal(`CRITICAL - Error en el cliente de redis Flow: ${error.toString()}`);
            }
        });

        this.client.on('ready', function () {
            logger.info(`Cliente redis Flow ready`);
        });

        this.client.on('connect', function () {
            logger.info(`Cliente redis Flow connected`);
        });

        this.client.on('reconnecting', function () {
            logger.info(`Cliente redis Flow reconnecting`);
        });

        this.client.on('end', async function (error) {
            if (error.message.includes("Max retries reached")) { // Ejemplo
                logger.fatal("CRITICAL - Reconexión fallida. Reiniciando cliente...");
                await this.client.disconnect(); // Limpia conexiones
                await this.client.connect(); // Reinicia
            }

            logger.fatal(`CRITICAL - Cliente redis Flow ended`);
        });
    }
}