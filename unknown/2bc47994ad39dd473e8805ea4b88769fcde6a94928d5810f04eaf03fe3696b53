import { Component, EventEmitter, Input, OnInit } from '@angular/core';
import { EditorService } from 'src/app/services/editor.service';
import { TranslateService } from '@ngx-translate/core';
import { ModalService } from "../../../../services/Tools/ModalService";
import { GoogleConfiguration } from "../../../../models/GoogleConfiguration";

@Component({
  selector: 'app-google-configuration',
  templateUrl: './google-configuration.component.html',
  styleUrls: ['./google-configuration.component.scss']
})
export class GoogleConfigurationComponent implements OnInit {
  @Input() readOnly: boolean = false;
  googleConfiguration: GoogleConfiguration = null;

  constructor(public editorService: EditorService, private translateService: TranslateService, private modalService: ModalService) { }

  ngOnInit() {
    if (this.googleConfiguration === null) {
      this.googleConfiguration = this.editorService.getGoogleConfiguration();
    }
  }
}
