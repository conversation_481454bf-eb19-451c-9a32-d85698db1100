{"name": "yoizen.yflow.domain", "version": "1.0.0", "description": "", "main": "index.js", "type": "module", "scripts": {"start": "node --env-file=.env --loader ts-node/esm src/index.ts", "dev": "node --env-file=.env --watch --loader ts-node/esm src/index.ts", "build": "tsc", "buildWeb": "tsc --build tsconfig.web.json", "lint": "eslint . --ext .ts", "lint:fix": "eslint . --fix --ext .ts"}, "keywords": [], "author": "", "license": "ISC", "dependencies": {"moment": "^2.30.1", "sequelize": "^6.37.4"}, "devDependencies": {"ts-node": "^10.9.2", "typescript": "^4.9.5", "@types/moment": "^2.11.29", "@types/sequelize": "^4.28.20"}}