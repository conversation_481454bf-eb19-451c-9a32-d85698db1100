import { HistoryDailyBase, HistoryDailyInfoTypes } from "./HistoryDailyBase";
import { Moment } from "moment";
import { DataTypes } from 'sequelize';
import sequelize from '../../../../../Yoizen.yFlow.Web/helpers/sequelize';

export class HistoryDailyByStatisticEvent extends HistoryDailyBase {
    declare channel: string;
    declare flowId: number;
    declare version: number;
    declare total: number;
    statisticEvent: { id: string; name: string; };
    block: { id: string; name: string; };
    declare statisticEventId: string;
    declare blockId: string;

    init(datetime: Moment, data) {
        this.initBase(datetime);
        this.channel = data.channel;
        this.flowId = data.flowId;
        this.version = data.version;
        this.blockId = data.blockId;
        this.statisticEventId = data.statisticEventId;
        this.total = data.total;
        this.statisticEvent = {
            id: data.statisticEventId,
            name: undefined
        };
        this.block = {
            id: data.blockId,
            name: undefined
        };
    }

    type() {
        return HistoryDailyInfoTypes.StatisticEvent;
    }
}

HistoryDailyByStatisticEvent.init({
    /*id: {
        type: Sequelize.INTEGER,
        primaryKey: true
    },*/
    date: DataTypes.DATE,
    datetime: {
        type: DataTypes.DATE,
        field: 'interval_datetime'
    },
    interval: DataTypes.INTEGER,
    total: DataTypes.INTEGER,
    version: DataTypes.INTEGER,
    flowId: {
        type: DataTypes.INTEGER,
        field: 'flow_id'
    },
    statisticEventId: {
        type: DataTypes.INTEGER,
        field: 'statistic_event_id',
    },
    blockId: {
        type: DataTypes.STRING,
        field: 'block_id',
    },
    channel: DataTypes.STRING
}, {
    sequelize: sequelize,
    modelName: 'history_daily_by_statistic_event',
    tableName: 'history_daily_by_statistic_event',
    timestamps: false
});