<div class="configurable-item">
  <div class="title">
    {{ 'CONFIGURATION_ICEBREAKERS_TITLE' | translate }}
  </div>
  <div class="description">
    {{ 'CONFIGURATION_ICEBREAKERS_DESCRIPTION' | translate }}
  </div>
  <div class="menu-listcontainer">
    <ng-container>
      <div dragula="MESSAGE_BUTTON_ELEMENT" [(dragulaModel)]="model.buttons">
        <app-button-element *ngFor="let button of model?.buttons let i = index"
                            [Model]="button"
                            [Index]="i"
                            (onDelete)="deleteButtonElement($event)"
                            [AllowedButtonTypes]="getAllowedButtonTypes()"
                            [expandedBtn]="expandButton"
                            [showStats]="false"
                            [IsIceBreaker]="true"
                            [readOnly]="readOnly"
                            (onShowDetail)="onShowButtonDetail($event)">
        </app-button-element>
      </div>
    </ng-container>
    <div class="addButton" (click)="addNewButton(); $event.stopPropagation();" *ngIf="canAddButton() && !readOnly">
      <span class="fa fa-plus"></span> {{ 'CARD_MESSAGE_ADD_BUTTON' | translate }}
    </div>
    <div *ngIf="!canAddButton() && !readOnly">
      <div class="alert alert-info">
        <span class="fa fa-lg fa-info-circle icon"></span>
        {{'MAX_BUTTON_REACHED' | translate}}
      </div>
    </div>
  </div>
</div>
