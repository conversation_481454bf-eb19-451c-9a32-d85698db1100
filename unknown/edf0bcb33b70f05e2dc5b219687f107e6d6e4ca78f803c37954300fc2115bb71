import { DataTypes, Model } from 'sequelize';
import sequelize from '../../../../Yoizen.yFlow.Web/helpers/sequelize';
import moment from 'moment';

export class DetailedEvent extends Model {
    declare id: number;
    declare fecha: Date;
    declare accion_realizada: string;
    declare entidad: string;
    declare usuario: string;
    declare usuario_editado: string;
    declare id_entidad: string;
    declare nombre_entidad: string;
    declare propiedad: string;
    declare valor_actual: string;
    declare valor_anterior: string;

    static generateExcel(start: moment.Moment, end: moment.Moment, offset: number, defaultLanguage: string = 'es'): Promise<string> {
        return this.generateReport(start, end, offset, defaultLanguage, true);
    }

    static generateText(folder: string, start: moment.Moment, end: moment.Moment, offset: number, operator: string, defaultLanguage: string = 'es'): Promise<string> {
        return this.generateReport(start, end, offset, defaultLanguage, false, operator);
    }

    private static async generateReport(
        start: moment.Moment, 
        end: moment.Moment, 
        offset: number, 
        defaultLanguage: string = 'es',
        isExcel: boolean = true,
        operator: string = ','
    ): Promise<string> {
        const fs = require('fs');
        const archiver = require('archiver');
        const Excel = require('exceljs');
        const path = require('path');
        const { reportsFolder } = require('../../../../Yoizen.yFlow.Web/helpers/folders');

        const startDateStr = start.format('YYYY-MM-DD HH:mm:ss');
        const endDateStr = end.format('YYYY-MM-DD HH:mm:ss');
        
        const events = await DetailedEvent.findAll({
            where: {
                fecha: {
                    [sequelize.Op.between]: [startDateStr, endDateStr]
                }
            },
            order: [['fecha', 'DESC']]
        });

        const fileName = `detailed_events_${start.format('YYYYMMDD')}.${isExcel ? 'xlsx' : 'csv'}`;
        const filePath = path.join(reportsFolder, fileName);

        if (isExcel) {
            const workbook = new Excel.Workbook();
            const worksheet = workbook.addWorksheet('Detallado de Eventos');
            
            worksheet.columns = [
                { header: 'Id', key: 'id', width: 10 },
                { header: 'Fecha', key: 'fecha', width: 20 },
                { header: 'Acción Realizada', key: 'accion_realizada', width: 15 },
                { header: 'Entidad', key: 'entidad', width: 15 },
                { header: 'Usuario', key: 'usuario', width: 15 },
                { header: 'Usuario Editado', key: 'usuario_editado', width: 15 },
                { header: 'Id de la Entidad', key: 'id_entidad', width: 15 },
                { header: 'Nombre de la Entidad', key: 'nombre_entidad', width: 20 },
                { header: 'Propiedad', key: 'propiedad', width: 15 },
                { header: 'Valor Actual', key: 'valor_actual', width: 20 },
                { header: 'Valor Anterior', key: 'valor_anterior', width: 20 }
            ];
            
            events.forEach(event => {
                worksheet.addRow({
                    id: event.id,
                    fecha: moment(event.fecha).format('DD/MM/YYYY HH:mm:ss'),
                    accion_realizada: event.accion_realizada,
                    entidad: event.entidad,
                    usuario: event.usuario,
                    usuario_editado: event.usuario_editado,
                    id_entidad: event.id_entidad,
                    nombre_entidad: event.nombre_entidad,
                    propiedad: event.propiedad,
                    valor_actual: event.valor_actual,
                    valor_anterior: event.valor_anterior
                });
            });
            
            await workbook.xlsx.writeFile(filePath);
        } else {
            const csvRows = [`Id${operator}Fecha${operator}Acción Realizada${operator}Entidad${operator}Usuario${operator}Usuario Editado${operator}Id de la Entidad${operator}Nombre de la Entidad${operator}Propiedad${operator}Valor Actual${operator}Valor Anterior`];
            
            events.forEach(event => {
                const row = [
                    event.id,
                    moment(event.fecha).format('DD/MM/YYYY HH:mm:ss'),
                    event.accion_realizada,
                    event.entidad,
                    event.usuario,
                    event.usuario_editado,
                    event.id_entidad,
                    event.nombre_entidad,
                    event.propiedad,
                    event.valor_actual,
                    event.valor_anterior
                ].join(operator);
                
                csvRows.push(row);
            });
            
            fs.writeFileSync(filePath, csvRows.join('\n'));
        }
        
        const zipFilePath = path.join(reportsFolder, `detailed_events_${start.format('YYYYMMDD')}.zip`);
        const output = fs.createWriteStream(zipFilePath);
        const archive = archiver('zip', { zlib: { level: 9 } });
        
        return new Promise((resolve, reject) => {
            output.on('close', () => {
                if (fs.existsSync(filePath)) {
                    fs.unlinkSync(filePath);
                }
                resolve(zipFilePath);
            });
            
            archive.on('error', (err) => {
                reject(err);
            });
            
            archive.pipe(output);
            archive.file(filePath, { name: fileName });
            archive.finalize();
        });
    }
}

DetailedEvent.init({
    id: {
        type: DataTypes.INTEGER,
        autoIncrement: true,
        primaryKey: true
    },
    fecha: {
        type: DataTypes.DATE,
        allowNull: false
    },
    accion_realizada: {
        type: DataTypes.STRING(50),
        allowNull: false
    },
    entidad: {
        type: DataTypes.STRING(100),
        allowNull: false
    },
    usuario: {
        type: DataTypes.STRING(100),
        allowNull: false
    },
    usuario_editado: {
        type: DataTypes.STRING(100),
        allowNull: true
    },
    id_entidad: {
        type: DataTypes.STRING(50),
        allowNull: false
    },
    nombre_entidad: {
        type: DataTypes.STRING(100),
        allowNull: false
    },
    propiedad: {
        type: DataTypes.STRING(100),
        allowNull: false
    },
    valor_actual: {
        type: DataTypes.TEXT,
        allowNull: true
    },
    valor_anterior: {
        type: DataTypes.TEXT,
        allowNull: true
    }
}, {
    sequelize,
    modelName: 'detailed_events',
    tableName: 'detailed_events',
    timestamps: false
});