import { Component, OnInit, EventEmitter, Input, Output } from '@angular/core';
import { LoginType, UserPublicInfo } from "../../../models/UserPublicInfo";
import { ModalService } from "../../../services/Tools/ModalService";
import { ServerService } from "../../../services/server.service";
import { TranslateService } from "@ngx-translate/core";
import { ToasterService } from "angular2-toaster";
import { StatusResponse } from "../../../models/StatusResponse";
import { finalize } from "rxjs/operators";
import { getTokenPayload, ySmartEnabled } from "../../../Utils/window";
import { DomSanitizer, SafeHtml } from '@angular/platform-browser';

@Component({
  selector: 'app-user-popup',
  templateUrl: './delete-user-popup.component.html',
  styleUrls: ['./delete-user-popup.component.scss']
})
export class DeleteUserPopupComponent implements OnInit {
  editing: boolean = false;
  superAdmin: boolean = false;
  confirmationPassword: string = null;
  passwordStrengthValue: number = 0;
  canEditFields: boolean = false;
  canValidatePasswords: boolean = false;
  ySmartEnabled: boolean = false;
  buttonPressed: boolean = false;
  loginTypes = LoginType;

  @Input() user: UserPublicInfo = null;
  @Input() validator: (name: string) => boolean = null;
  @Output() loading: EventEmitter<boolean> = new EventEmitter<boolean>();
  @Output() acceptAction: EventEmitter<UserPublicInfo> = new EventEmitter<UserPublicInfo>();

  constructor(
    private modalService: ModalService,
    private serverService: ServerService,
    private toasterService: ToasterService,
    private translateService: TranslateService,
    private sanitizer: DomSanitizer
  ) { }

  getTitleTranslation(): SafeHtml {
    const translatedText = this.translateService.instant('USER_DELETE_TITLE', { name: this.user.name });
    return this.sanitizer.bypassSecurityTrustHtml(translatedText);
  }

  onAccept() {
    this.buttonPressed = true;
    this.toasterService.clear();
    this.loading.emit(true);
    this.deleteUser()
  }

  private deleteUser() {
    this.loading.emit(true);
    this.serverService.deleteUser(this.user)
      .pipe(finalize(() => {
        this.loading.emit(false);
      }))
      .subscribe(
        (status: StatusResponse) => {
          this.user.is_deleted = true
          this.acceptAction.emit(this.user);
          this.modalService.destroy();
        },
        error => {
          this.toasterService.pop('error', this.translateService.instant('USER_DIALOG_ERROR'));
          this.buttonPressed = false;
        }
      );
  }

  ngOnInit() {
    let tokenPayload = getTokenPayload();
    if (tokenPayload.uid == 1 || (tokenPayload.admin && (this.user === null || !this.user.is_admin))) {
      this.canEditFields = true;
    }
    if (tokenPayload.canValidatePasswords) {
      this.canValidatePasswords = true;
    }
    if (this.user === null) {
      this.user = new UserPublicInfo();
      this.user.login_type = LoginType.LOCAL;
      this.editing = false;
    } else {
      this.editing = true;
      this.user = this.user.clone();
      this.superAdmin = (this.user.id === 1);
    }
    if (!this.user.enabled) {
      this.canValidatePasswords = false;
    }

    if (this.user.login_type === null || this.user.login_type === undefined) {
      this.user.login_type = LoginType.LOCAL;
    }

    this.ySmartEnabled = ySmartEnabled().toLocaleLowerCase() === "true";
  }

  onCancel() {
    this.modalService.destroy();
  }
}
