import { HistoryDailyBase, HistoryDailyInfoTypes } from "./HistoryDailyBase";
import { Moment } from "moment";
import { DataTypes } from 'sequelize';
import sequelize from '../../../../../Yoizen.yFlow.Web/helpers/sequelize';
import { DailyByFlow } from "../daily/DailyByFlow";
import { isNullOrUndefined } from "../../../../../Yoizen.yFlow.Helpers/src/Helpers";

export class HistoryDailyByFlow extends HistoryDailyBase {
    declare channel: string;
    declare flowId: number;
    flow: { id: number; name: string; };
    declare newCase: number;
    declare transferredToYSocial: number;
    declare closedByYFlow: number;
    declare newMessage: number;
    declare hsmCase: number;
    declare caseAbandoned: number;
    declare monthlyUsers: number;

    init(datetime: Moment, daily) {
        this.initBase(datetime);
        this.newCase = isNullOrUndefined(daily.newCase) ? 0 : parseInt(daily.newCase);
        this.transferredToYSocial = isNullOrUndefined(daily.transferredToYSocial) ? 0 : parseInt(daily.transferredToYSocial);
        this.closedByYFlow = isNullOrUndefined(daily.closedByYFlow) ? 0 : parseInt(daily.closedByYFlow);
        this.newMessage = isNullOrUndefined(daily.newMessage) ? 0 : parseInt(daily.newMessage);
        this.hsmCase = isNullOrUndefined(daily.hsmCase) ? 0 : parseInt(daily.hsmCase);
        this.caseAbandoned = isNullOrUndefined(daily.caseAbandoned) ? 0 : parseInt(daily.caseAbandoned);
        this.monthlyUsers = isNullOrUndefined(daily.monthlyUsers) ? 0 : parseInt(daily.monthlyUsers);

        this.channel = daily.channel;
        this.flowId = parseInt(daily.flowId);
        this.flow = {
            id: this.flowId,
            name: ""
        }
    }

    type() {
        return HistoryDailyInfoTypes.ByFlow;
    }
}

HistoryDailyByFlow.init({
    /*id: {
        type: Sequelize.INTEGER,
        primaryKey: true
    },*/
    date: DataTypes.DATE,
    datetime: {
        type: DataTypes.DATE,
        field: 'interval_datetime'
    },
    interval: DataTypes.INTEGER,
    newCase: {
        type: DataTypes.INTEGER,
        field: 'new_cases'
    },
    transferredToYSocial: {
        type: DataTypes.INTEGER,
        field: 'transferred'
    },
    closedByYFlow: {
        type: DataTypes.INTEGER,
        field: 'closed_by_yflow'
    },
    newMessage: {
        type: DataTypes.INTEGER,
        field: 'new_messages'
    },
    hsmCase: {
        type: DataTypes.INTEGER,
        field: 'hsm_case'
    },
    caseAbandoned: {
        type: DataTypes.INTEGER,
        field: 'case_abandoned'
    },
    monthlyUsers: {
        type: DataTypes.INTEGER,
        field: 'monthly_users'
    },
    flowId: {
        type: DataTypes.INTEGER,
        field: 'flow_id'
    },
    channel: DataTypes.STRING
}, {
    sequelize: sequelize,
    modelName: 'history_daily_by_flow',
    tableName: 'history_daily_by_flow',
    timestamps: false
})