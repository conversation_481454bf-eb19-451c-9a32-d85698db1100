import { logger } from '../../Yoizen.yFlow.Helpers/src/Logger';
import { InitIntervalServices, InitCache } from './InitServices';

const initializeRepositories = async () => {
    logger.info('yFlow.Infrastructure Inicializando Repositorios');
    const { dailyIntervalPort } = await InitIntervalServices();
    return {
        dailyIntervalPort
    };
};

const initializeCache = async () => {
    logger.info('yFlow.Infrastructure Inicializando cache');
    let cache = await InitCache();
    return {
        cache
    };
};

initializeRepositories();

initializeCache().catch(error => {
    logger.error('Error al inicializar la aplicación:', error);
});
