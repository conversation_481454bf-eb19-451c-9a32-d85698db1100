@import './variables';
@import './mixins';

html {
  height: 100%;
  width: 100%;
  overflow-y: hidden;
  overflow-x: hidden;
}

body {
  font-family: $fontFamily;
  font-size: $fontSize;
  color: $defaultColor;
  -webkit-font-smoothing: antialiased;
  text-rendering: geometricPrecision;
  font-feature-settings: "ss01" on;
  height: 100%;
  width: 100%;
  overflow-y: hidden;
  overflow-x: hidden;
  margin: 0;

  app-root {
    height: 100%;
    width: 100%;
    overflow-y: hidden;
    overflow-x: hidden;

    .content-container {
      height: 100%;
      width: 100%;
      overflow-y: hidden;
      overflow-x: hidden;
    }
  }
}

#toast-container {
  z-index: 111111111111111111 !important;
}
