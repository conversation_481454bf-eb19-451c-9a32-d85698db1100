import {Component, OnInit, Input, EventEmitter} from '@angular/core';
import { EditorService } from 'src/app/services/editor.service';
import {DeleteInput} from "../../../../models/UI/DeleteInput";
import {DeleteGroupQuestionComponent} from "../../popups/delete-group-question/delete-group-question.component";
import {ModalService} from "../../../../services/Tools/ModalService";
import {
  ApplePayMerchantConfigComponent
} from "../../popups/apple-pay-merchant-config/apple-pay-merchant-config.component";
import { ApplePayMerchantConfig } from '../../../../models/ApplePayMerchantConfig';
import {TypedJSON} from "typedjson";

@Component({
  selector: 'app-applepay-merchants-configuration',
  templateUrl: './applepay-merchants-configuration.component.html',
  styleUrls: ['./applepay-merchants-configuration.component.scss']
})
export class ApplepayMerchantsConfigurationComponent implements OnInit {
  @Input() readOnly: boolean = false;

  settings: ApplePayMerchantConfig[] = null;


  constructor(public editorService: EditorService, private modalService: ModalService) { }

  ngOnInit() {
    this.settings = this.editorService.getApplePayMerchantsConfig();
  }

  delete(merchantConfig: ApplePayMerchantConfig, index: number) {
    let emitAction = new EventEmitter();
    emitAction.subscribe(() => {
      this.settings.splice(index, 1);
    });

    let deleteInfo: DeleteInput = new DeleteInput();
    deleteInfo.ElementName = `${merchantConfig.merchantId} - ${merchantConfig.merchantName}}`;

    this.modalService.init(
      DeleteGroupQuestionComponent,
      {
        DeleteDetail: deleteInfo,
        Title: 'ARE_YOU_SURE_DELETE_APPLEPAYMERCHANT_QUESTION',
        HideAffected: true,
        HideConfirmation: true,
        deleteText: 'ACCEPT',
      }, {DeleteAction: emitAction}
    );
  }

  edit(merchantConfig: ApplePayMerchantConfig, index: number) {
    var emitAction = new EventEmitter();
    emitAction.subscribe((editedMerchantConfig: ApplePayMerchantConfig) => {
      this.settings[index] = editedMerchantConfig;
    });

    let copy: ApplePayMerchantConfig = TypedJSON.parse(TypedJSON.stringify(merchantConfig, ApplePayMerchantConfig), ApplePayMerchantConfig);

    this.modalService.init(
      ApplePayMerchantConfigComponent,
      {
        merchantConfig: copy
      }, {saveAction: emitAction}
    );
  }

  add() {
    var emitAction = new EventEmitter();
    emitAction.subscribe((merchantConfig: ApplePayMerchantConfig) => {
      this.settings.push(merchantConfig);
    });

    this.modalService.init(
      ApplePayMerchantConfigComponent,
      {
        merchantConfig: null
      }, {saveAction: emitAction}
    );
  }
}
