@import '_variables';
@import '_mixins';

.addCard {
  margin: 0 20px;

  & > .title {
    font-size: $fontSizeConfigurationTitles;
    font-weight: bold;
    color: #999;
    margin-bottom: 5px;
  }

  .option {
    display: flex;
    flex-direction: row;
    align-items: center;
    margin-bottom: 5px;
    width: 100%;

    .title {
      font-family: $fontFamilyTitles;
      font-weight: bold;
      margin-right: 10px;
      justify-self: center;
      align-self: center;
      text-align: center;
      flex-grow: 0;
      flex-shrink: 0;
    }

    ui-switch {
      height: 22px;
    }
  }

  .items {
    display: flex;
    flex-direction: row;
    flex-wrap: wrap;
    padding: 0;
  }

  .btn {
    background-color: DodgerBlue;
    border: none;
    color: white;
    font-size: 16px;
    cursor: pointer;
    max-width: 48px;
    max-height: 48px;
    margin-top: 14px;
  }

  .card {
    padding-bottom: 0px;
    margin: 10px;
    border-radius: 0px;
  }

  .cognitivity-filter {
    width: 100%;
    padding: 10px;
    margin-left: 12px;
  }

  .mat-form-field {
    font-size: 12px;
    width: 100%;
  }
}
