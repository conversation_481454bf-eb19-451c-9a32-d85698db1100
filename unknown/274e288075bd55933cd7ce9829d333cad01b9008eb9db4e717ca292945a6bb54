import { DailyBase, DailyInfoTypes } from "./DailyBase";
import { Moment } from "moment";

export class DailyByIntegrations extends DailyBase {
    channel: string;
    flowId: number;
    version: number;
    integration: { id: string; name: string; };
    integrationId: string;
    total: number;
    totalResponseTime: number;
    errorResponseTime: number;
    error: number;

    constructor(datetime: Moment, data?: { flowId: number, channel: string, integrationId: string, version: number }) {
        super(datetime);
        if (data) {
            this.channel = data.channel;
            this.flowId = data.flowId;
            this.integrationId = data.integrationId;
            this.version = data.version;
            this.integration = {
                id: data.integrationId,
                name: undefined
            }
        }
        this.totalResponseTime = 0;
        this.errorResponseTime = 0;
        this.total = 0;
        this.error = 0;
    }

    type() {
        return DailyInfoTypes.Integrations;
    }

    getColumns(): Array<string> {
        return ["date", "interval", "interval_datetime", "flow_id", "integration_id", "channel", "total", "error", "version", "total_response_time", "error_response_time"];
    }

    getValues(): Array<any> {
        return [this.date.utc().format('YYYY-MM-DD hh:mm:ss'), this.interval, this.datetime.utc().format('YYYY-MM-DD hh:mm:ss'), this.flowId, this.integrationId, this.channel, this.total, this.error, this.version, this.totalResponseTime, this.errorResponseTime];
    }

    getType() {
        return 'daily_integrations';
    }
}