import {Component, OnInit, EventEmitter, Input, ViewChild, ElementRef, Output} from '@angular/core';
import { Router } from '@angular/router';
import { FlowDefinition } from 'src/app/models/FlowDefinition';
import { EditorService } from 'src/app/services/editor.service';
import { ServerService } from 'src/app/services/server.service';
import { ModalService } from 'src/app/services/Tools/ModalService';

@Component({
  selector: 'app-dashboard-module-modal',
  templateUrl: './dashboard-module-modal.component.html',
  styleUrls: ['./dashboard-module-modal.component.scss']
})
export class DashboardModuleModalComponent implements OnInit {
  @Input() moduleFlows: FlowDefinition[] = [];
  @Output() CreateAction = new EventEmitter<any>();

  constructor(
    private modalService: ModalService,
    private editorService : EditorService,
    private routerService : Router,
    private serverService : ServerService) {
  }

  ngOnInit() {

  }

  onCancel() {
    this.modalService.destroy();
  }
}
