
resources:
  repositories:
    - repository: pipelines-templates
      type: git
      name: infra-cd-aplicaciones/pipeline-templates
      ref: refs/heads/main
    
variables:
  - group: global-variables

stages:
  - stage: acrbuild
    pool:
      name: devops-agent
    displayName: 'Build and push al acr'
    jobs:
      - job: buildandpush
        steps:
          - checkout: self
          - template: docker-acr-build-and-push.yml@pipelines-templates
            parameters:
              innerPath: '.'
              dockerfile: 'WebExecutor.Dockerfile'
              serviceConnectionName: 'azure'
              acrName: 'yoizenacr'
              appName: 'yflow-executor'
              appVersion: $(Build.SourceBranchName)
              buildArgs: "--build-arg versionName=$(Build.SourceBranchName)"
          - template: docker-acr-build-and-push.yml@pipelines-templates
            parameters:
              innerPath: '.'
              dockerfile: 'IntervalServices.Dockerfile'
              serviceConnectionName: 'azure'
              acrName: 'yoizenacr'
              appName: 'yflow-intervals'
              appVersion: $(Build.SourceBranchName)
              buildArgs: "--build-arg versionName=$(Build.SourceBranchName)"
          - template: docker-acr-build-and-push.yml@pipelines-templates
            parameters:
              innerPath: 'Yoizen.yFlow.Worker/'
              dockerfile: 'Yoizen.yFlow.Worker/Dockerfile'
              serviceConnectionName: 'azure'
              acrName: 'yoizenacr'
              appName: 'yflow-worker'
              appVersion: $(Build.SourceBranchName)
              buildArgs: "--build-arg versionName=$(Build.SourceBranchName)"
          - template: docker-acr-build-and-push.yml@pipelines-templates
            parameters:
              innerPath: 'Yoizen.yFlow.Web/'
              dockerfile: 'Yoizen.yFlow.Web/Dockerfile'
              serviceConnectionName: 'azure'
              acrName: 'yoizenacr'
              appName: 'yflow-web'
              appVersion: $(Build.SourceBranchName)
              buildArgs: "--build-arg versionName=$(Build.SourceBranchName)"
          - template: helm-acr-build-and-push.yml@pipelines-templates
            parameters:
              innerPath: './DevOps/Helm'
              serviceConnectionName: 'azure'
              acrName: 'yoizenacr'
              appName: 'yflow'
              appVersion: $(Build.SourceBranchName)
      
      - job: Notify_GoogleChat
        displayName: 'Notificar Resultado del Pipeline a Google Chat'
        dependsOn:
          - buildandpush
        condition: always()
        steps:
        - script: |
            curl -X POST -H 'Content-Type: application/json' \
            -d '{"text":"✅ *Pipeline yFlow-Docker-CI completado exitosamente*. `Versión: $(Build.SourceBranchName)` 🔗 Ver build: https://yoizen.visualstudio.com/yFlow/_build/results?buildId=$(Build.BuildId)"}' \
            "$(googleChatWebhook)" 
          displayName: 'Notificar Éxito a Google Chat'
          condition: succeeded()

        - script: |
            curl -X POST -H 'Content-Type: application/json' \
            -d '{"text":"❌ *Pipeline yFlow-Docker-CI falló*. Revisar errores. `Versión: $(Build.SourceBranchName)` 🔗 Ver build: https://yoizen.visualstudio.com/yFlow/_build/results?buildId=$(Build.BuildId)"}' \
            "$(googleChatWebhook)" 
          displayName: 'Notificar Falla a Google Chat'
          condition: failed()
trigger:
  branches:
    include:
      - refs/tags/version/*