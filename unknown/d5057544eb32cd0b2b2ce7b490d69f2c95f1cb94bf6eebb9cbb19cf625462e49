import { Injectable } from "@angular/core";
import { Location } from '@angular/common';
import { environment } from '../../environments/environment';
import { HttpClient, HttpHeaders } from '@angular/common/http';
import { Observable } from "rxjs/Observable";
import { StatusResponse } from "../models/StatusResponse";

@Injectable()
export class ServerService {
    private BaseUrl = Location.joinWithSlash(environment.serverUrl, "api/");
    private static SignaturePad = 'signature-pad';
    private static Account = 'account';

    private options: object;
    constructor(public http: HttpClient) {
        let headers = new HttpHeaders({
            'Accept': 'application/json',
            'Content-Type': 'application/json'
        });
        this.options = { headers: headers };
    }
    
    public saveSignaturePad(base64: string, token: string): Observable<StatusResponse> {
        const request = `${this.BaseUrl}${ServerService.SignaturePad}/Send`;
        return this.http.post<StatusResponse>(request, { Base64image: base64, Token: token }, this.options);
    }
    
    public accountUnlinking(token: string): Observable<StatusResponse> {
        const request = `${this.BaseUrl}${ServerService.Account}/unlinking?token=${token}`;
        return this.http.post<StatusResponse>(request, null, this.options);
    }

    public accountTest(url: string, token: string): Observable<StatusResponse> {
        const request = `${url}?token=${token}`;
        return this.http.post<StatusResponse>(request, {}, this.options);
    }
}
