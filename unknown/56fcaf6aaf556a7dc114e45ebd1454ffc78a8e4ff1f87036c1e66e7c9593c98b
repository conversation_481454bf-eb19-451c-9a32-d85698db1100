import { DailyBase, DailyInfoTypes } from "./DailyBase";
import { Moment } from "moment";

export class DailyByGroups extends DailyBase {
    channel: string;
    flowId: number;
    version: number;
    total: number;
    group: { id: string; name: string; };
    groupId: string;

    constructor(datetime: Moment, data?: { flowId: number, channel: string, groupId: string, version: number }) {
        super(datetime);
        if (data) {
            this.channel = data.channel;
            this.flowId = data.flowId;
            this.groupId = data.groupId;
            this.version = data.version;
            this.group = {
                id: data.groupId,
                name: undefined
            }
        }

        this.total = 0;
    }

    type() {
        return DailyInfoTypes.Groups;
    }

    getColumns(): Array<string> {
        return ["date", "interval", "interval_datetime", "flow_id", "group_id", "channel", "total", "version"];
    }

    getValues(): Array<any> {
        return [this.date.utc().format('YYYY-MM-DD hh:mm:ss'), this.interval, this.datetime.utc().format('YYYY-MM-DD hh:mm:ss'), this.flowId, this.groupId, this.channel, this.total, this.version];
    }

    getType() {
        return 'daily_group';
    }
}