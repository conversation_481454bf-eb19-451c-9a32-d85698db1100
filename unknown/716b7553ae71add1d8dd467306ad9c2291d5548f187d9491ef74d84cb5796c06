"use strict";
var __extends = (this && this.__extends) || (function () {
    var extendStatics = function (d, b) {
        extendStatics = Object.setPrototypeOf ||
            ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||
            function (d, b) { for (var p in b) if (b.hasOwnProperty(p)) d[p] = b[p]; };
        return extendStatics(d, b);
    };
    return function (d, b) {
        extendStatics(d, b);
        function __() { this.constructor = d; }
        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());
    };
})();
Object.defineProperty(exports, "__esModule", { value: true });
var StatusResponseBase = /** @class */ (function () {
    function StatusResponseBase() {
    }
    return StatusResponseBase;
}());
exports.StatusResponseBase = StatusResponseBase;
var StatusResponse = /** @class */ (function (_super) {
    __extends(StatusResponse, _super);
    function StatusResponse() {
        return _super !== null && _super.apply(this, arguments) || this;
    }
    return StatusResponse;
}(StatusResponseBase));
exports.StatusResponse = StatusResponse;
//# sourceMappingURL=StatusResponse.js.map