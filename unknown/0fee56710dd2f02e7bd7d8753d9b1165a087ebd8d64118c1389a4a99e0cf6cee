{"name": "yoizen.yflow.infrastructure", "version": "1.0.0", "description": "", "main": "index.js", "type": "module", "scripts": {"start": "node --env-file=.env --loader ts-node/esm src/index.ts", "dev": "node --env-file=.env --watch --loader ts-node/esm src/index.ts", "build": "tsc --build tsconfig.build.json", "buildWeb": "tsc --build tsconfig.web.json", "lint": "eslint . --ext .ts", "lint:fix": "eslint . --fix --ext .ts"}, "keywords": [], "author": "", "license": "ISC", "dependencies": {"@azure/storage-blob": "^12.18.0", "axios": "^1.7.2", "mssql": "^6.4.1", "mysql2": "^3.9.4", "redis": "^4.6.13", "sequelize": "^6.37.2", "uuid": "^10.0.0"}, "devDependencies": {"@types/cors": "^2.8.17", "@types/mssql": "^9.1.5", "@types/node": "^22.7.6", "@types/sequelize": "^4.28.20", "@types/uuid": "^10.0.0", "ts-node": "^10.9.2", "typescript": "^4.9.5"}}