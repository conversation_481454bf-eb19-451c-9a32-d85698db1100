/**
 * Script para configurar el entorno de de**rrollo personal
 */

const fs = require('fs');
const path = require('path');
const readline = require('readline');
const os = require('os');

const rl = readline.createInterface({
  input: process.stdin,
  output: process.stdout
});

// Detectar el nombre de usuario del sistema
const username = os.userInfo().username;
console.log(`\n==========================================`);
console.log(`  Configuración de entorno para yFlow`);
console.log(`==========================================\n`);
console.log(`Usuario detectado: ${username}\n`);

// Ruta al archivo de entorno de ejemplo
const exampleWebEnvPath = path.join(__dirname, '.env.web.example');
// Ruta donde se guardará el archivo de entorno personalizado
const personalWebEnvPath = path.join(__dirname, '..', 'Yoizen.yFlow.Web', `.env.${username}`);
// Crear el archivo .env.example si no existe
const defaulWebEnvContent = `# Configuración base
virtualDirPath=
standAlone=false
NODE_ENV=dev
storageYFlowPath=./storage
defaultLanguaje=es
enabledLanguajes=es,en,pt
downloadReportTypes=rt,background
flowType=Bot,Lite
client=YoizenTest
updateCasePieceEnabledInChat=true
gmt=-03:00
trackAdminSessions=true
hostInsideIIS=false
googleApiKey=AIzaSyASdb4ciNEaz9P6ga_azIrG5TCk8HhjFaw
executorUrl=http://localhost:3001

# Accesos cache
redisConnectionString=Yoizen-dev.redis.cache.windows.net:6380,password=BfSS5zOQc93REt0mIeavnzus8ButcHBCNAzCaPTZQ7Y=,ssl=True,abortConnect=False

# Accesos a otros sistemas
ySocialUrl=https://dev.ysocial.net/Social
ySocialAccessToken=57syoRUWCO8MoOg430SZ0W+HYqP8W95BQIbWMtxIvwQ=
ySocialAccessTokenSecret=nHJS3qbsN2sDzFovZmPLsA==
yflowUrl=http://localhost:3000/
useAzureBlobStorage=false

# Smart Services
ySmartEnabled=true
cognitivityApiVersion=2
tokenCognitiveService=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ1bmlxdWVfbmFtZSI6IkFkbWluaXN0cmFkb3IiLCJQcm9qZWN0Ijoie1wiSWRcIjowLFwiTmFtZVwiOlwiQWRtaW5pc3RyYWRvclwiLFwiVG9rZW5cIjpudWxsLFwiRW5hYmxlZFwiOnRydWUsXCJDb25maWRlbmNlXCI6MC4wLFwiRGlzY2FyZFwiOjAuMCxcIkxhbmdcIjpudWxsLFwiTGFzdFVwZGF0ZVwiOlwiMjAyMi0wMS0wM1QwOTo0NToyMC4wNzI0MjEzLTAzOjAwXCIsXCJQcm9qZWN0U2VydmljZUlkXCI6MCxcIlNlcnZpY2VcIjpudWxsLFwiQ3VzdG9tZXJJZFwiOjAsXCJDdXN0b21lclwiOntcIklkXCI6MCxcIk5hbWVcIjpcIkFkbWluaXN0cmFkb3JcIn0sXCJTdGF0dXNcIjowLFwiTGFzdFVwZGF0ZWRTdGF0dXNPa1wiOm51bGx9IiwibmJmIjoxNjQxMjEzOTIwLCJleHAiOjIxMTQ1OTk1MjAsImlhdCI6MTY0MTIxMzkyMCwiaXNzIjoiaHR0cDovL2xvY2F**G9zdDo0NDMzNSIsImF1ZCI6Imh0dHA6Ly9sb2NhbGhvc3Q6NDQzMzUifQ.ehJUB9wIhkWu0nXoDva73f2DA5ga3POq8S15WWRhmSo
urlApiCognitiveServices=http://dev.ysocial.net/ysmartcore/api/
urlClientAppCognitiveServices=https://dev.ysocial.net/ysmartcore/home/<USER>

# Otras configuraciones
yFlowUtilsUrl=https://common.ysocial.net
yBiometricUrl=https://localhost:7166
dniServiceUrl=https://yflow-dni-validator-qa.ysocial.net
dniServiceApiKey=57syoRUWCO8MoOg430SZ0W+HYqP8W95BQIbWMtxIvwQ=

# Bases de datos
dbdialect=mssql
yFlowConnectionString=Server=localhost;Database=yFlow;User Id=**;Password=**;

# Bot de contingencia
isContingencyBot=false
`;

// Verificar si el archivo de ejemplo existe
if (!fs.existsSync(exampleWebEnvPath)) {
  console.log('Archivo .env.web.example no encontrado, creando uno predeterminado...');
  try {
    fs.writeFileSync(exampleWebEnvPath, defaulWebEnvContent);
    console.log('Archivo .env.web.example creado correctamente.');
  } catch (error) {
    console.error('Error al crear el archivo .env.web.example:', error);
    rl.close();
    process.exit(9);
  }
}

const exampleWebExecutorEnvPath = path.join(__dirname, '.env.webexecutor.example');
// Ruta donde se guardará el archivo de entorno personalizado
const personalWebExecutorEnvPath = path.join(__dirname, '..', 'Yoizen.yFlow.WebExecutor', `.env.${username}`);
const defaulWebExecutorEnvContent = `# Configuración base
virtualDirPath=
standAlone=false
NODE_ENV=dev
storageYFlowPath=./storage
defaultLanguaje=es
enabledLanguajes=es,en,pt
downloadReportTypes=rt,background
flowType=Bot,Lite
client=Yoizen
updateCasePieceEnabledInChat=true
gmt=-03:00
PORT=5005

# Accesos cache
redisConnectionString=Yoizen-dev.redis.cache.windows.net:6380,password=BfSS5zOQc93REt0mIeavnzus8ButcHBCNAzCaPTZQ7Y=,ssl=True,abortConnect=False

# Accesos a otros sistemas
ySocialUrl=https://dev.ysocial.net/Social
ySocialAccessToken=57syoRUWCO8MoOg430SZ0W+HYqP8W95BQIbWMtxIvwQ=
ySocialAccessTokenSecret=nHJS3qbsN2sDzFovZmPLsA==
yflowUrl=http://localhost:3000/
useAzureBlobStorage=false

# Smart Services
ySmartEnabled=true
cognitivityApiVersion=2
tokenCognitiveService=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ1bmlxdWVfbmFtZSI6IkFkbWluaXN0cmFkb3IiLCJQcm9qZWN0Ijoie1wiSWRcIjowLFwiTmFtZVwiOlwiQWRtaW5pc3RyYWRvclwiLFwiVG9rZW5cIjpudWxsLFwiRW5hYmxlZFwiOnRydWUsXCJDb25maWRlbmNlXCI6MC4wLFwiRGlzY2FyZFwiOjAuMCxcIkxhbmdcIjpudWxsLFwiTGFzdFVwZGF0ZVwiOlwiMjAyMi0wMS0wM1QwOTo0NToyMC4wNzI0MjEzLTAzOjAwXCIsXCJQcm9qZWN0U2VydmljZUlkXCI6MCxcIlNlcnZpY2VcIjpudWxsLFwiQ3VzdG9tZXJJZFwiOjAsXCJDdXN0b21lclwiOntcIklkXCI6MCxcIk5hbWVcIjpcIkFkbWluaXN0cmFkb3JcIn0sXCJTdGF0dXNcIjowLFwiTGFzdFVwZGF0ZWRTdGF0dXNPa1wiOm51bGx9IiwibmJmIjoxNjQxMjEzOTIwLCJleHAiOjIxMTQ1OTk1MjAsImlhdCI6MTY0MTIxMzkyMCwiaXNzIjoiaHR0cDovL2xvY2F**G9zdDo0NDMzNSIsImF1ZCI6Imh0dHA6Ly9sb2NhbGhvc3Q6NDQzMzUifQ.ehJUB9wIhkWu0nXoDva73f2DA5ga3POq8S15WWRhmSo
urlApiCognitiveServices=http://dev.ysocial.net/ysmartcore/api/
urlClientAppCognitiveServices=https://dev.ysocial.net/ysmartcore/home/<USER>

# Otras configuraciones
yFlowUtilsUrl=https://common.ysocial.net
yBiometricUrl=https://localhost:7166
dniServiceUrl=https://yflow-dni-validator-qa.ysocial.net
dniServiceApiKey=57syoRUWCO8MoOg430SZ0W+HYqP8W95BQIbWMtxIvwQ=

# Bases de datos
dbdialect=mssql
yFlowConnectionString=Server=localhost;Database=yFlow;User Id=**;Password=**;

# DB Intervals
dbintervalsdialect=mysql
dbintervalsConnectionString=Server=localhost;Port=3306;Database=yFlowIntervals;Uid=root;Pwd=root;

# DB Context
dbcontextdialect=redis

# DB Integrations Audit
dbintegration**uditdialect=di**bled
dbintegrationAuditConnectionString=Server=localhost;Port=3306;Database=yFlowIntegrstionAudit;Uid=root;Pwd=root;

# Bot de contingencia
isContingencyBot=false
`;

if (!fs.existsSync(exampleWebExecutorEnvPath)) {
  console.log('Archivo .env.webexecutor.example no encontrado, creando uno predeterminado...');
  try {
    fs.writeFileSync(exampleWebExecutorEnvPath, defaulWebExecutorEnvContent);
    console.log('Archivo .env.webexecutor.example creado correctamente.');
  } catch (error) {
    console.error('Error al crear el archivo .env.webexecutor.example:', error);
    rl.close();
    process.exit(9);
  }
}

const exampleIntervalsEnvPath = path.join(__dirname, '.env.intervals.example');
// Ruta donde se guardará el archivo de entorno personalizado
const personalIntervalsEnvPath = path.join(__dirname, '..', 'Yoizen.yFlow.IntervalServices', `.env.${username}`);
const defaulIntervalsEnvContent = `# Configuración base
client=YoizenTest
NODE_ENV=prod
storageYFlowPath=./storage
defaultLanguaje=es
gmt=-03:00

# Accesos cache
redisConnectionString=Yoizen-dev.redis.cache.windows.net:6380,password=BfSS5zOQc93REt0mIeavnzus8ButcHBCNAzCaPTZQ7Y=,ssl=True,abortConnect=False

# Accesos a otros sistemas
urlCentralize=https://fbsocial.azurewebsites.net/api/centralizer
ySocialUrl=https://qa.ysocial.net/Oldversion/
ySocialAccessToken=57syoRUWCO8MoOg430SZ0W+HYqP8W95BQIbWMtxIvwQ=
ySocialAccessTokenSecret=nHJS3qbsN2sDzFovZmPLsA==
yflowUrl=https://qa.ysocial.net/Oldversion/yflow
useAzureBlobStorage=false

# Otras configuraciones
dniServiceUrl=https://yflow-dni-validator-qa.ysocial.net
dniServiceApiKey=57syoRUWCO8MoOg430SZ0W+HYqP8W95BQIbWMtxIvwQ=

# Bases de datos
dbdialect=mssql
yFlowConnectionString=Server=localhost;Database=yFlow;User Id=**;Password=**;

# DB Intervals
dbintervalsdialect=mysql
dbintervalsConnectionString=Server=localhost;Port=3306;Database=yFlowIntervals;Uid=root;Pwd=root;

# DB Context
dbcontextdialect=redis

# DB Integrations Audit
dbintegration**uditdialect=di**bled
dbintegrationAuditConnectionString=Server=localhost;Port=3306;Database=yFlowIntegrstionAudit;Uid=root;Pwd=root;

# Bot de contingencia
isContingencyBot=false
`;

if (!fs.existsSync(exampleIntervalsEnvPath)) {
  console.log('Archivo .env.intervals.example no encontrado, creando uno predeterminado...');
  try {
    fs.writeFileSync(exampleIntervalsEnvPath, defaulIntervalsEnvContent);
    console.log('Archivo .env.intervals.example creado correctamente.');
  } catch (error) {
    console.error('Error al crear el archivo .env.intervals.example:', error);
    rl.close();
    process.exit(9);
  }
}

// Verificar si ya existe un archivo de entorno personalizado
if (fs.existsSync(personalWebEnvPath) || fs.existsSync(personalWebExecutorEnvPath) || fs.existsSync(personalIntervalsEnvPath)) {
  rl.question('Ya existe un archivo de configuración personalizado. ¿Deseas sobrescribirlo? (s/n): ', (answer) => {
    if (answer.toLowerCase() === 's') {
      createConfigFile();
    } else {
      console.log('Operación cancelada.');
      rl.close();
    }
  });
} else {
  createConfigFile();
}

// Función para crear el archivo de configuración personalizado
function createConfigFile() {
  // No leer el archivo, u**r directamente el contenido predeterminado
  // Esto garantiza que todas las variables nece**rias estén presentes
  askDbConfig(defaulWebEnvContent, defaulWebExecutorEnvContent, defaulIntervalsEnvContent);
}

// Función para preguntar por la configuración de la base de datos
function askDbConfig(webEnvData, webExecutorEnvData, intervalsEnvData) {
  console.log('\n--- Configuración de la base de datos ---');

  rl.question('connectionString de la base de datos (default: Server=localhost;Database=yFlow;User Id=**;Password=**;): ', (yFlowConnectionString) => {
    yFlowConnectionString = yFlowConnectionString || 'Server=localhost;Database=yFlow;User Id=**;Password=**;';

    rl.question('dbintervalsConnectionString de la base de datos (default: Server=localhost;Port=3306;Database=yFlowIntervals;Uid=root;Pwd=root;): ', (dbintervalsConnectionString) => {
      dbintervalsConnectionString = dbintervalsConnectionString || 'Server=localhost;Port=3306;Database=yFlowIntervals;Uid=root;Pwd=root;';

      rl.question('dbintegrationAuditConnectionString de la base de datos (default: Server=localhost;Port=3306;Database=yFlowIntegrstionAudit;Uid=root;Pwd=root;): ', (dbintegrationAuditConnectionString) => {
        dbintegrationAuditConnectionString = dbintegrationAuditConnectionString || 'Server=localhost;Port=3306;Database=yFlowIntegrstionAudit;Uid=root;Pwd=root;';
        // Actualizar la configuración con los valores proporcionados
        let updatedwebEnvData = webEnvData
          .replace(/yFlowConnectionString=.+/, `yFlowConnectionString=${yFlowConnectionString}`)

        let updatedwebExecutorEnvData = webExecutorEnvData
          .replace(/yFlowConnectionString=.+/, `yFlowConnectionString=${yFlowConnectionString}`)
          .replace(/dbintervalsConnectionString=.+/, `dbintervalsConnectionString=${dbintervalsConnectionString}`)
          .replace(/dbintegrationAuditConnectionString=.+/, `dbintegrationAuditConnectionString=${dbintegrationAuditConnectionString}`)

        let updatedIntervalsEnvData = intervalsEnvData
          .replace(/yFlowConnectionString=.+/, `yFlowConnectionString=${yFlowConnectionString}`)
          .replace(/dbintervalsConnectionString=.+/, `dbintervalsConnectionString=${dbintervalsConnectionString}`)
          .replace(/dbintegrationAuditConnectionString=.+/, `dbintegrationAuditConnectionString=${dbintegrationAuditConnectionString}`)

        // Preguntar por otras configuraciones
        askOtherConfig(updatedwebEnvData, updatedwebExecutorEnvData, updatedIntervalsEnvData);
      });
    });
  });
}

// Función para preguntar por otras configuraciones
function askOtherConfig(webEnvData, webExecutorEnvData, intervalsEnvData) {
  console.log('\n--- Otras configuraciones ---');

  rl.question('URL del executor (default: http://localhost:3001): ', (executorUrl) => {
    executorUrl = executorUrl || 'http://localhost:3001';

    rl.question('Ruta de almacenamiento (default: ./storage): ', (storagePath) => {
      storagePath = storagePath || './storage';

      rl.question(`Nombre del cliente (default: ${username}): `, (client) => {
        client = client || username;

        // Actualizar la configuración con los valores proporcionados
        let updatedwebEnvData = webEnvData
          .replace(/executorUrl=.+/, `executorUrl=${executorUrl}`)
          .replace(/storageYFlowPath=.+/, `storageYFlowPath=${storagePath}`)
          .replace(/client=.+/, `client=${client}`);

        let updatedwebExecutorEnvData = webExecutorEnvData
          .replace(/executorUrl=.+/, `executorUrl=${executorUrl}`)
          .replace(/storageYFlowPath=.+/, `storageYFlowPath=${storagePath}`)
          .replace(/client=.+/, `client=${client}`);

        let updatedIntervalsEnvData = intervalsEnvData
          .replace(/storageYFlowPath=.+/, `storageYFlowPath=${storagePath}`)
          .replace(/client=.+/, `client=${client}`);

        // Guardar el archivo de configuración personalizado
        try {
          fs.writeFileSync(personalWebEnvPath, updatedwebEnvData, 'utf8');
          console.log(`\n✅ Configuración guardada en ${personalWebEnvPath}\n`);

          fs.writeFileSync(personalWebExecutorEnvPath, updatedwebExecutorEnvData, 'utf8');
          console.log(`\n✅ Configuración guardada en ${personalWebExecutorEnvPath}\n`);

          fs.writeFileSync(personalIntervalsEnvPath, updatedIntervalsEnvData, 'utf8');
          console.log(`\n✅ Configuración guardada en ${personalIntervalsEnvPath}\n`);

          // Verificar que el archivo se haya creado correctamente
          if (fs.existsSync(personalWebEnvPath) && fs.existsSync(personalWebExecutorEnvPath) && fs.existsSync(personalIntervalsEnvPath)) {

            console.log('Para utilizar esta configuración, ejecuta en cada proyecoto que quieras ejecutar:');
            console.log('Ejemplo: cd Yoizen.yFlow.WebExecutor ');
            console.log('  npm run dev');
            console.log('o');
            console.log('  npm run debug\n');
          } else {
            console.error('⚠︝ No se pudo verificar la creación del archivo');
          }
        } catch (error) {
          console.error('Error al guardar la configuración:', error);
        }

        rl.close();
      });
    });
  });
}
