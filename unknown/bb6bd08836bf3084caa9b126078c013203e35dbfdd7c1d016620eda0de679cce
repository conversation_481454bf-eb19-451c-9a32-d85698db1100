<div class="popup-overlay">
  <div class="popup-container">
    <div class="popup-header">
      <h3 class="title" [innerHTML]="getTitleTranslation()"></h3>
    </div>
    <div class="popup-content">
      <div class="section" *ngIf="editing && (canValidatePasswords) && user.login_type === loginTypes.LOCAL">
        <div class="field-group">
          <label>{{ 'NEW_USER_PASSWORD' | translate }}</label>
          <div class="password-strength">
            <input type="password" class="form-control" [(ngModel)]="user.password"
              (input)="calcPasswordStrength($event)" placeholder="{{'NEW_USER_PASSWORD' | translate }}">
            <meter [value]="passwordStrengthValue" min="0" max="4"></meter>
          </div>
        </div>

        <div class="field-group">
          <label>{{ 'USER_PASSWORD_CONFIRMATION' | translate }}</label>
          <input type="password" class="form-control" [(ngModel)]="confirmationPassword"
            placeholder="{{'USER_PASSWORD_CONFIRMATION' | translate }}">
        </div>
      </div>

      <div class="section" *ngIf="canEditFields">
        <div class="field-group" *ngIf="!editing">
          <label>{{ 'USER_NAME' | translate }}</label>
          <input type="text" class="form-control" [(ngModel)]="user.name" placeholder="{{'USER_NAME' | translate }}">
        </div>

        <div class="field-group">
          <label>{{ 'LOGIN_TYPE' | translate }}</label>
          <select class="form-control" [(ngModel)]="user.login_type">
            <option [value]="loginTypes.LOCAL">{{loginTypes.LOCAL}}</option>
            <option [value]="loginTypes.SAML">{{loginTypes.SAML}}</option>
          </select>
        </div>

        <div class="alert info" *ngIf="!editing && user.login_type === loginTypes.LOCAL">
          <i class="fas fa-info-circle"></i>
          {{ 'USER_PASSWORD_STRENGTH' | translate }}
        </div>

        <div *ngIf="!editing && user.login_type === loginTypes.LOCAL">
          <div class="field-group">
            <label>{{ 'USER_PASSWORD' | translate }}</label>
            <div class="password-strength">
              <input type="password" class="form-control" [(ngModel)]="user.password"
                (input)="calcPasswordStrength($event)" placeholder="{{'USER_PASSWORD' | translate }}">
              <meter [value]="passwordStrengthValue" min="0" max="4"></meter>
            </div>
          </div>

          <div class="field-group">
            <label>{{ 'USER_PASSWORD_CONFIRMATION' | translate }}</label>
            <input type="password" class="form-control" [(ngModel)]="confirmationPassword"
              placeholder="{{'USER_PASSWORD_CONFIRMATION' | translate }}">
          </div>
        </div>

        <div class="permissions-grid">
          <div class="permission-item" *ngIf="!superAdmin">
            <label>{{ 'USER_ADMIN' | translate }}</label>
            <ui-switch [(ngModel)]="user.is_admin" (change)="onChangeIsAdmin($event)" color="#45c195" size="small">
            </ui-switch>
          </div>

          <div class="permission-item" *ngIf="editing">
            <label>{{ 'USER_ENABLED' | translate }}</label>
            <ui-switch [(ngModel)]="user.enabled" color="#45c195" size="small"></ui-switch>
          </div>

          <div class="permission-item">
            <label>{{ 'USER_CAN_EDIT' | translate }}</label>
            <ui-switch [(ngModel)]="user.can_edit" color="#45c195" size="small"></ui-switch>
          </div>

          <div class="permission-item">
            <label>{{ 'USER_CAN_PUBLISH' | translate }}</label>
            <ui-switch [(ngModel)]="user.can_publish" color="#45c195" size="small"></ui-switch>
          </div>
          <div class="permission-item">
            <label>{{ 'USER_CAN_DELETE' | translate }}</label>
            <ui-switch [(ngModel)]="user.can_delete" color="#45c195" size="small"></ui-switch>
          </div>
          <div class="permission-item">
            <label>{{ 'USER_SEE_STATISTICS' | translate }}</label>
            <ui-switch [(ngModel)]="user.can_see_statistics" color="#45c195" size="small"></ui-switch>
          </div>

          <div class="permission-item">
            <label>{{ 'USER_CAN_VALIDATE_PASSWORDS' | translate }}</label>
            <ui-switch [(ngModel)]="user.can_validate_passwords" color="#45c195" size="small"></ui-switch>
          </div>

          <div class="permission-item" *ngIf="ySmartEnabled">
            <label>{{ 'USER_CAN_ACCESS_YSMART' | translate }}</label>
            <ui-switch [(ngModel)]="user.can_access_ysmart" color="#45c195" size="small"></ui-switch>
          </div>
        </div>
      </div>
    </div>
    <div class="popup-footer">
      <button class="btn btn-secondary" (click)="onCancel()">
        {{ 'CANCEL' | translate }}
      </button>
      <button class="btn btn-primary" (click)="onAccept()" [disabled]="buttonPressed">
        {{ 'ACCEPT' | translate }}
      </button>
    </div>
  </div>
</div>
