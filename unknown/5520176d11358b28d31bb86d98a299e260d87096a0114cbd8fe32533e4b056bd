-- INSERT para la tabla abandoned_cases_[fecha]_[hora]

INSERT INTO abandoned_cases_20240424_0800 
(`interval_datetime`, `flow_id`, `channel`, `block_id`, `total`, `version`)
VALUES 
('2024-04-24 08:00:00', 18, '16', '1', 1, 7),
('2024-04-24 08:00:00', 18, '16', '1', 1, 7),
('2024-04-24 08:00:00', 18, '16', '1', 1, 7),
('2024-04-24 08:00:00', 18, '16', '1', 1, 7),
('2024-04-24 08:00:00', 18, '16', '1', 1, 7),
('2024-04-24 08:00:00', 18, '16', '1', 1, 7),
('2024-04-24 08:00:00', 18, '16', '1', 1, 7),
('2024-04-24 08:00:00', 18, '16', '1', 1, 7),
('2024-04-24 08:00:00', 18, '16', '1', 1, 7),
('2024-04-24 08:00:00', 18, '16', '1', 1, 7);

INSERT INTO abandoned_cases_20240424_0830 
(`interval_datetime`, `flow_id`, `channel`, `block_id`, `total`, `version`)
VALUES 
('2024-04-24 08:30:00', 18, '16', '1', 1, 7),
('2024-04-24 08:30:00', 18, '16', '1', 1, 7),
('2024-04-24 08:30:00', 18, '16', '1', 1, 7),
('2024-04-24 08:30:00', 18, '16', '1', 1, 7),
('2024-04-24 08:30:00', 18, '16', '1', 1, 7),
('2024-04-24 08:30:00', 18, '16', '1', 1, 7),
('2024-04-24 08:30:00', 18, '16', '1', 1, 7),
('2024-04-24 08:30:00', 18, '16', '1', 1, 7),
('2024-04-24 08:30:00', 18, '16', '1', 1, 7),
('2024-04-24 08:30:00', 18, '16', '1', 1, 7);

INSERT INTO abandoned_cases_20240424_0900 
(`interval_datetime`, `flow_id`, `channel`, `block_id`, `total`, `version`)
VALUES 
('2024-04-24 09:00:00', 18, '16', '1', 1, 7),
('2024-04-24 09:00:00', 18, '16', '1', 1, 7),
('2024-04-24 09:00:00', 18, '16', '1', 1, 7),
('2024-04-24 09:00:00', 18, '16', '1', 1, 7),
('2024-04-24 09:00:00', 18, '16', '1', 1, 7),
('2024-04-24 09:00:00', 18, '16', '1', 1, 7),
('2024-04-24 09:00:00', 18, '16', '1', 1, 7),
('2024-04-24 09:00:00', 18, '16', '1', 1, 7),
('2024-04-24 09:00:00', 18, '16', '1', 1, 7),
('2024-04-24 09:00:00', 18, '16', '1', 1, 7);

INSERT INTO abandoned_cases_20240424_0930 
(`interval_datetime`, `flow_id`, `channel`, `block_id`, `total`, `version`)
VALUES 
('2024-04-24 09:30:00', 18, '16', '1', 1, 7),
('2024-04-24 09:30:00', 18, '16', '1', 1, 7),
('2024-04-24 09:30:00', 18, '16', '1', 1, 7),
('2024-04-24 09:30:00', 18, '16', '1', 1, 7),
('2024-04-24 09:30:00', 18, '16', '1', 1, 7),
('2024-04-24 09:30:00', 18, '16', '1', 1, 7),
('2024-04-24 09:30:00', 18, '16', '1', 1, 7),
('2024-04-24 09:30:00', 18, '16', '1', 1, 7),
('2024-04-24 09:30:00', 18, '16', '1', 1, 7),
('2024-04-24 09:30:00', 18, '16', '1', 1, 7);

INSERT INTO abandoned_cases_20240424_1000 
(`interval_datetime`, `flow_id`, `channel`, `block_id`, `total`, `version`)
VALUES 
('2024-04-24 10:00:00', 18, '16', '1', 1, 7),
('2024-04-24 10:00:00', 18, '16', '1', 1, 7),
('2024-04-24 10:00:00', 18, '16', '1', 1, 7),
('2024-04-24 10:00:00', 18, '16', '1', 1, 7),
('2024-04-24 10:00:00', 18, '16', '1', 1, 7),
('2024-04-24 10:00:00', 18, '16', '1', 1, 7),
('2024-04-24 10:00:00', 18, '16', '1', 1, 7),
('2024-04-24 10:00:00', 18, '16', '1', 1, 7),
('2024-04-24 10:00:00', 18, '16', '1', 1, 7),
('2024-04-24 10:00:00', 18, '16', '1', 1, 7);

INSERT INTO abandoned_cases_20240424_1030 
(`interval_datetime`, `flow_id`, `channel`, `block_id`, `total`, `version`)
VALUES 
('2024-04-24 10:30:00', 18, '16', '1', 1, 7),
('2024-04-24 10:30:00', 18, '16', '1', 1, 7),
('2024-04-24 10:30:00', 18, '16', '1', 1, 7),
('2024-04-24 10:30:00', 18, '16', '1', 1, 7),
('2024-04-24 10:30:00', 18, '16', '1', 1, 7),
('2024-04-24 10:30:00', 18, '16', '1', 1, 7),
('2024-04-24 10:30:00', 18, '16', '1', 1, 7),
('2024-04-24 10:30:00', 18, '16', '1', 1, 7),
('2024-04-24 10:30:00', 18, '16', '1', 1, 7),
('2024-04-24 10:30:00', 18, '16', '1', 1, 7);

INSERT INTO abandoned_cases_20240424_1100 
(`interval_datetime`, `flow_id`, `channel`, `block_id`, `total`, `version`)
VALUES 
('2024-04-24 11:00:00', 18, '16', '1', 1, 7),
('2024-04-24 11:00:00', 18, '16', '1', 1, 7),
('2024-04-24 11:00:00', 18, '16', '1', 1, 7),
('2024-04-24 11:00:00', 18, '16', '1', 1, 7),
('2024-04-24 11:00:00', 18, '16', '1', 1, 7),
('2024-04-24 11:00:00', 18, '16', '1', 1, 7),
('2024-04-24 11:00:00', 18, '16', '1', 1, 7),
('2024-04-24 11:00:00', 18, '16', '1', 1, 7),
('2024-04-24 11:00:00', 18, '16', '1', 1, 7),
('2024-04-24 11:00:00', 18, '16', '1', 1, 7);

INSERT INTO abandoned_cases_20240424_1130 
(`interval_datetime`, `flow_id`, `channel`, `block_id`, `total`, `version`)
VALUES 
('2024-04-24 11:30:00', 18, '16', '1', 1, 7),
('2024-04-24 11:30:00', 18, '16', '1', 1, 7),
('2024-04-24 11:30:00', 18, '16', '1', 1, 7),
('2024-04-24 11:30:00', 18, '16', '1', 1, 7),
('2024-04-24 11:30:00', 18, '16', '1', 1, 7),
('2024-04-24 11:30:00', 18, '16', '1', 1, 7),
('2024-04-24 11:30:00', 18, '16', '1', 1, 7),
('2024-04-24 11:30:00', 18, '16', '1', 1, 7),
('2024-04-24 11:30:00', 18, '16', '1', 1, 7),
('2024-04-24 11:30:00', 18, '16', '1', 1, 7);

INSERT INTO abandoned_cases_20240424_1200 
(`interval_datetime`, `flow_id`, `channel`, `block_id`, `total`, `version`)
VALUES 
('2024-04-24 12:00:00', 18, '16', '1', 1, 7),
('2024-04-24 12:00:00', 18, '16', '1', 1, 7),
('2024-04-24 12:00:00', 18, '16', '1', 1, 7),
('2024-04-24 12:00:00', 18, '16', '1', 1, 7),
('2024-04-24 12:00:00', 18, '16', '1', 1, 7),
('2024-04-24 12:00:00', 18, '16', '1', 1, 7),
('2024-04-24 12:00:00', 18, '16', '1', 1, 7),
('2024-04-24 12:00:00', 18, '16', '1', 1, 7),
('2024-04-24 12:00:00', 18, '16', '1', 1, 7),
('2024-04-24 12:00:00', 18, '16', '1', 1, 7);

INSERT INTO abandoned_cases_20240424_1230 
(`interval_datetime`, `flow_id`, `channel`, `block_id`, `total`, `version`)
VALUES 
('2024-04-24 12:30:00', 18, '16', '1', 1, 7),
('2024-04-24 12:30:00', 18, '16', '1', 1, 7),
('2024-04-24 12:30:00', 18, '16', '1', 1, 7),
('2024-04-24 12:30:00', 18, '16', '1', 1, 7),
('2024-04-24 12:30:00', 18, '16', '1', 1, 7),
('2024-04-24 12:30:00', 18, '16', '1', 1, 7),
('2024-04-24 12:30:00', 18, '16', '1', 1, 7),
('2024-04-24 12:30:00', 18, '16', '1', 1, 7),
('2024-04-24 12:30:00', 18, '16', '1', 1, 7),
('2024-04-24 12:30:00', 18, '16', '1', 1, 7);


{"ts-node":{"esm": false,"transpileOnly": true,"experimentalSpecifierResolution": "node"},"compilerOptions": {"esModuleInterop": true,"moduleResolution": "node","module": "ESNext","target": "ES2022","allowJs": true,"checkJs": false,"outDir": "./dist",},"include": ["src/*/"],"exclude": []}