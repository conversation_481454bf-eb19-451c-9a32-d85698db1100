import { IntervalMySqlRepository } from './adapters/mysql/IntervalMySqlRepository';
import { dbIntervals } from '../../Yoizen.yFlow.Helpers/src/ConfigDbInterval';
import { FlowRedisCacheRepository } from './adapters/RedisCacheRepository';
import { IIntervalPort } from './ports/IIntervalPort';
import { ISystemStatusPort } from './ports/ISystemStatusPort';
import { SequelizeSystemStatusAdapter } from './adapters/sequelize/SequelizeSystemStatusAdapter';
import { IHistoryDailyPort } from './ports/IHistoryDailyPort';
import { SequelizeHistoryDailyAdapter } from './adapters/sequelize/SequelizeHistoricalAdapter';
import { SequelizeDetailedAdapter } from './adapters/sequelize/SequelizeDetailedAdapter';
import { SequelizeConfigurationAdapter } from './adapters/sequelize/SequelizeConfigurationAdapter';
import { IConfigurationPort } from './ports/IConfigurationPort';
import { SequelizeFlowAdapter } from './adapters/sequelize/SequelizeFlowAdapter';
import { IFlowPort } from './ports/IFlowPort';
import { SequelizeReportAdapter } from './adapters/sequelize/SequelizeReportAdapter';
import { IReportPort } from './ports/IReportPort';
import { ILogPort } from './ports/ILogPort';
import { SequelizeLogsAdapter } from './adapters/sequelize/SequelizeLogsAdapter';
import { IntervalMSSQLRepository } from './adapters/mssql/IntervalMsSqlRepository';
import { IFilePort } from './ports/IFilePort';
import { AzureBlobStorageAdapter } from './adapters/azure/AzureBlobStorage';
import { configFileStorage } from '../../Yoizen.yFlow.Helpers/src/ConfigFileStorage';
import { IPersonalizedTablePort } from './ports/IPersonalizedTablePort';
import { SequelizePersonalizedTableAdapter } from './adapters/sequelize/SequelizePersonalizedTableAdapter';
import { dbContext } from '../../Yoizen.yFlow.Helpers/src/ConfigDbContext';
import { RedisFlowAdapter } from './adapters/redis/RedisFlowAdapter';
import { IntervalMock } from './adapters/mock/IntervalMock';
import { FlowMock } from './adapters/mock/FlowMock';
import { logger } from '../../Yoizen.yFlow.Helpers/src/Logger';

export async function InitIntervalServices() {
    // Inicializo la base de datos
    logger.info('yFlow.Infrastructure Inicializando base de datos de interval');

    // Inicializo repositorios
    let dailyIntervalPort: IIntervalPort;
    switch (dbIntervals.dialect) {
        case 'mysql':
            dailyIntervalPort = new IntervalMySqlRepository();
            break;
        case 'mssql':
            dailyIntervalPort = new IntervalMSSQLRepository();
            break;
        default:
            if (!dbIntervals.enable) {
                dailyIntervalPort = new IntervalMock();
            } else {
                throw new Error('El dialecto de la base de datos Intervals no es válido');
            }
    }
    await dailyIntervalPort.connect();
    return {
        dailyIntervalPort
    }
}

export async function InityFLowServices() {
    // Inicializo la base de datos
    logger.info('yFlow.Infrastructure Inicializando base de datos de yFlow');

    // Inicializo repositorios
    let systemStatusPort: ISystemStatusPort;
    systemStatusPort = new SequelizeSystemStatusAdapter();

    let historyDailyPort: IHistoryDailyPort;
    historyDailyPort = new SequelizeHistoryDailyAdapter();

    let configurationPort: IConfigurationPort;
    configurationPort = new SequelizeConfigurationAdapter();

    let reportPort: IReportPort;
    reportPort = new SequelizeReportAdapter();

    let flowPort: IFlowPort;
    switch (dbContext.dialect) {
        case 'mysql':
            flowPort = new SequelizeFlowAdapter();
            break;
        case 'mssql':
            flowPort = new SequelizeFlowAdapter();
            break;
        case 'redis':
            flowPort = new RedisFlowAdapter();
            break;
        default:
            if (!dbContext.enable) {
                flowPort = new FlowMock();
            } else {
                throw new Error('El dialecto de la base de datos caseContext no es válido');
            }
    }
    await flowPort.connect();

    let logPort: ILogPort;
    logPort = new SequelizeLogsAdapter();

    let detailIntervalPort: IIntervalPort;
    detailIntervalPort = new SequelizeDetailedAdapter();

    let personalizedTablePort: IPersonalizedTablePort;
    personalizedTablePort = new SequelizePersonalizedTableAdapter();

    let filePort: IFilePort;
    if (configFileStorage.useAzureBlobStorage) {
        filePort = new AzureBlobStorageAdapter();
        await filePort.init();
    }


    return {
        systemStatusPort,
        historyDailyPort,
        configurationPort,
        flowPort,
        reportPort,
        logPort,
        detailIntervalPort,
        filePort,
        personalizedTablePort
    }
}

export async function InitCache() {
    logger.info('yFlow.Infrastructure Inicializando cache');
    let cache = new FlowRedisCacheRepository();
    await cache.init();
    return { cache };
}