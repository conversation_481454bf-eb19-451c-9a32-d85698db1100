import { DailyBase, DailyInfoTypes } from "./DailyBase";
import { Moment } from "moment";

export class DailyByStatisticEvent extends DailyBase {
    channel: string;
    flowId: number;
    version: number;
    total: number;
    statisticEvent: { id: string; name: string; };
    block: { id: string; name: string; };
    statisticEventId: string;
    blockId: string;

    constructor(datetime: Moment, data?: { flowId: number, channel: string, statisticEventId: string, blockId: string, version: number }) {
        super(datetime);
        if (data) {
            this.channel = data.channel;
            this.flowId = data.flowId;
            this.version = data.version;
            this.blockId = data.blockId;
            this.statisticEventId = data.statisticEventId;
            this.statisticEvent = {
                id: data.statisticEventId,
                name: undefined
            };
            this.block = {
                id: data.blockId,
                name: undefined
            };
        }
        this.total = 0;
    }

    type() {
        return DailyInfoTypes.StatisticEvent;
    }

    getColumns(): Array<string> {
        return ["date", "interval", "interval_datetime", "flow_id", "statistic_event_id", "channel", "total", "version", "block_id"];
    }

    getValues(): Array<any> {
        return [this.date.utc().format('YYYY-MM-DD hh:mm:ss'), this.interval, this.datetime.utc().format('YYYY-MM-DD hh:mm:ss'), this.flowId, this.statisticEventId, this.channel, this.total, this.version, this.blockId];
    }

    getType() {
        return 'daily_statistic_event';
    }
}