FROM mcr.microsoft.com/dotnet/sdk:8.0 AS build
WORKDIR /src
COPY ["TokenManagerApi.csproj", "./"]
RUN dotnet restore "TokenManagerApi.csproj"
COPY . .
RUN dotnet build "TokenManagerApi.csproj" -c Release -o /app/build

FROM build AS publish
RUN dotnet publish "TokenManagerApi.csproj" -c Release -o /app/publish

FROM mcr.microsoft.com/dotnet/aspnet:8.0 AS final
WORKDIR /app
COPY --from=publish /app/publish .
ENV ASPNETCORE_URLS=http://+:80
ENV ASPNETCORE_HTTP_PORTS=80
EXPOSE 80
ENTRYPOINT ["dotnet", "TokenManagerApi.dll"]
