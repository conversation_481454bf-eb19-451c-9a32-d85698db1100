apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  name: yflow-dni-validator-qa-ing
  namespace: yflow-dni-validator-qa
  annotations:
    nginx.ingress.kubernetes.io/rewrite-target: /
    cert-manager.io/cluster-issuer: 'letsencrypt-private'
    nginx.ingress.kubernetes.io/ssl-passthrough: 'true'
    nginx.ingress.kubernetes.io/secure-backends: 'true'
    nginx.ingress.kubernetes.io/proxy-body-size: "32m"
    nginx.ingress.kubernetes.io/proxy-buffer-size: "64k"
    nginx.ingress.kubernetes.io/proxy-buffers-number: "16"
    nginx.ingress.kubernetes.io/proxy-buffers-size: "256k"
spec:
  ingressClassName: nginx-private
  tls:
  - hosts:
    - yflow-dni-validator-qa.ysocial.net
    secretName: cert-tls
  rules:
  - host: yflow-dni-validator-qa.ysocial.net
    http:
      paths:
      - pathType: Prefix
        path: "/"
        backend:
          service:
            name: yflow-dni-validator-qa-svc
            port:
              number: 3000
