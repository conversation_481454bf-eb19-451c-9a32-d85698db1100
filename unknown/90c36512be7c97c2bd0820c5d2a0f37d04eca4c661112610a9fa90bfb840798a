@import '_variables';
@import '_mixins';

:host {
  max-height: 100%;
  overflow-y: auto;
  background-color: $navbarBackgroundColor;
}

main.container {
  .tags {
    padding: 10px 8px 15px;
    display: flex;
    flex-direction: row;

    .tag {
      font-size: 14px;
      padding: .3em .4em .4em;
      margin: 0 .1em;
      background-color: rgb(166, 208, 248);
      position: relative;
      border-radius: 8px;
      color: white;
      display: flex;
      flex-direction: row;
      align-items: center;
      justify-content: flex-start;
      user-select: none;

      &.keyword {
        border: 1px solid #c2bdbd;
        background-color: #fff;
        color: #000;

        a {
          color: #000;
        }
      }

      &.facebookMessenger {
        background-color: $facebookMessengerColor;
      }

      &.instagram {
        background: radial-gradient(circle at 30% 107%, #fdf497 0%, #fdf497 5%, #fd5949 45%,#d6249f 60%,#285AEB 90%);
      }

      &.mercadolibre {
        background-color: $mercadoLibreColor;
      }

      &.twitter {
        background-color: $twitterColor;
      }

      &.whatsapp {
        background-color: $whatsappColor;
      }

      &.telegram {
        background-color: $telegramColor;
      }

      &.chat {
        background-color: $chatColor;
      }

      &.skype {
        background-color: $skypeColor;
      }

      &.generic {
        background-color: $genericColor;
      }

      &.applemessaging {
        background-color: $appleMessagingColor;
      }

      &.googlerbm {
        background-color: $googlerbmColor;
      }

      a {
        color: white;
        cursor: pointer;
        opacity: 0.4;
        margin: 0 0 0 .3em;
        line-height: 21px;

        &:hover {
          opacity: 1.0
        }

        span {
          font-style: initial;
          margin-left: 5px;
        }
      }
    }
  }

  & > .container {
    padding: 10px;
    h2 {
      font-size: 20px;
      margin-bottom: 20px;
      text-transform: uppercase;
      font-weight: normal;
      padding: 0 8px;
    }

    .tiles {
      display: flex;
      flex-wrap: wrap;
      margin: 0;
      margin-bottom: 20px;

      .content {
        display: inline-block;

        &.new {
          & > .new {
            @include flow;
          }
        }
      }
    }
  }
}

.overlay {
  position: fixed;
  left: 0;
  top: 0;
  bottom: 0;
  right: 0;
  background: #00000044;
  z-index: 100000;

  .spinner {
    display: inline-block;
    position: absolute;
    left: calc(50% - 32px);
    top: calc(50% - 32px);
  }
}
