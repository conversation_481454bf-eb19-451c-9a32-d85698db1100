<div class="configurable-item" >
  <div class="title">{{ 'APPLEPAY_MERCHANTS_CONFIGURATION_TITLE' | translate }}</div>
  <div class="description">
    <span [innerHTML]="'APPLEPAY_MERCHANTS_CONFIGURATION_DESCRIPTION' | translate"></span>
  </div>
  <div class="merchants">
    <div class="merchants-table" *ngIf="settings !== null && settings.length > 0">
      <div class="header">
        <div>{{ 'APPLEPAY_MERCHANTS_CONFIGURATION_HEADER_MERCHANTID' | translate }}</div>
        <div>{{ 'APPLEPAY_MERCHANTS_CONFIGURATION_HEADER_MERCHANTNAME' | translate }}</div>
        <div>{{ 'APPLEPAY_MERCHANTS_CONFIGURATION_HEADER_DOMAINNAME' | translate }}</div>
        <div>{{ 'APPLEPAY_MERCHANTS_CONFIGURATION_HEADER_PAYMENTGATEWAYURL' | translate }}</div>
        <div></div>
      </div>
      <div class="row" *ngFor="let applePayMerchantConfig of settings let i = index">
        <div class="name">{{ applePayMerchantConfig.merchantId }}</div>
        <div class="name">{{ applePayMerchantConfig.merchantName }}</div>
        <div class="name">{{ applePayMerchantConfig.domainName }}</div>
        <div class="name">{{ applePayMerchantConfig.paymentGatewayUrl }}</div>
        <div>
          <div class="icons">
            <div (click)="delete(applePayMerchantConfig, i)" *ngIf="!readOnly"
                 tooltipClass="tooltip-trash-left"
                 data-toggle="tooltip" ngbTooltip="{{ 'APPLEPAY_MERCHANTS_CONFIGURATION_REMOVE' | translate }}" placement="left">
              <span class="fa fa fa-trash-alt"></span>
            </div>
            <div (click)="edit(applePayMerchantConfig, i)" *ngIf="!readOnly"
                 tooltipClass="tooltip-trash-left"
                 data-toggle="tooltip" ngbTooltip="{{ 'APPLEPAY_MERCHANTS_CONFIGURATION_EDIT' | translate }}" placement="left">
              <span class="fa fa fa-edit"></span>
            </div>
          </div>
        </div>
      </div>
    </div>
    <div class="empty" *ngIf="settings === null || settings.length === 0" role="alert">
      <div class="alert alert-info">
        <span class="fa fa-exclamation-circle icon"></span>
        {{ 'APPLEPAY_MERCHANTS_CONFIGURATION_EMPTY' | translate }}
      </div>
    </div>
    <div class="add" (click)="add()" *ngIf="!readOnly">
      <span class="fa fa-plus"></span> {{ 'APPLEPAY_MERCHANTS_CONFIGURATION_ADD' | translate }}
    </div>
  </div>
</div>
