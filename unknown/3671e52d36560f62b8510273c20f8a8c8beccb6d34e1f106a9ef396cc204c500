import { Component, OnInit, Input, Output, EventEmitter } from '@angular/core';
import { PieceType } from '../../../../../models/PieceType'
import {
  trigger,
  state,
  style,
  animate,
  transition,
  keyframes,
  AnimationEvent
} from '@angular/animations';

@Component({
  selector: 'app-button-menu-pieces',
  templateUrl: './button-menu-pieces.component.html',
  styleUrls: ['./button-menu-pieces.component.scss'],
  animations: [
    trigger('Visible', [
      state('true', style({
        display: 'flex',
        opacity: 1
      })),
      state('false', style({
        display: 'none'
      })),
      transition('false => true', [
        style({
          opacity: 0,
        }),
        animate('0.75s 0.75s ease-out')
      ]),
      transition('true => false', [
        animate('0.75s 0.25s ease-in', style({
          opacity: 0,
        }))
      ])
    ])
  ]
})


export class ButtonMenuPiecesComponent implements OnInit {

  @Input() Piece : PieceType;

  @Output() onSelect : EventEmitter<PieceType> = new EventEmitter<PieceType>();
  constructor() {
  }

  ngOnInit() {
  }

  onClick() {
    this.onSelect.emit( this.Piece );
  }
}
