import { DailyBase, DailyInfoTypes } from "./DailyBase";
import { Moment } from "moment";

export class DailyByCommands extends DailyBase {
    channel: any;
    flowId: any;
    commandId: any;
    version: any;
    total: number;
    command: { id: any; name: any; };

    constructor(datetime: Moment, data?: { flowId: number, channel: string, commandId: string, version: number }) {
        super(datetime);
        if (data) {
            this.flowId = data.flowId;
            this.channel = data.channel;
            this.commandId = data.commandId;
            this.version = data.version;
            this.command = {
                id: data.commandId,
                name: undefined
            }
        }
        this.total = 0;
    }

    type() {
        return DailyInfoTypes.Commnads;
    }

    getColumns(): Array<string> {
        return ["date", "interval", "interval_datetime", "flow_id", "command_id", "channel", "total", "version"];
    }

    getValues(): Array<any> {
        return [this.date.utc().format('YYYY-MM-DD hh:mm:ss'), this.interval, this.datetime.utc().format('YYYY-MM-DD hh:mm:ss'), this.flowId, this.commandId, this.channel, this.total, this.version];
    }

    getType() {
        return 'daily_commands';
    }
}