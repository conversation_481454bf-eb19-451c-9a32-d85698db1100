const express = require('express');
const app = express();
const PORT = process.env.port || 3000;
const { DniService } = require('../src/helpers/eval-dni');
const { DniRoute } = require('../src/helpers/DniRoute');
const { RouterBase } = require('../src/helpers/RouteBase');

const initializeApp = async () => {
  app.use(express.json({ limit: "50mb" }));
  app.disable("x-powered-by");

  app.listen(PORT, () => {
    console.log(`Servidor escuchando en el puerto ${PORT}`);
  });

  console.log('Inicializando rutas');
  var dniService = new DniService();
  app.use('/api/dnis', new DniRoute(dniService).getRouter());
  app.use('/api', new RouterBase().getRouter());
};

initializeApp().catch(error => {
  console.error('Error al inicializar la aplicación:', error);
});
