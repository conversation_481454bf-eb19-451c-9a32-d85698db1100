import { IHistoryDailyPort } from "../../ports/IHistoryDailyPort";
import { HistoryDailyBase } from "../../../../Yoizen.yFlow.Domain/src/models/statistics/historical/HistoryDailyBase";
import sequelize from '../../../../Yoizen.yFlow.Web/helpers/sequelize';
import { logger } from "../../../../Yoizen.yFlow.Helpers/src/Logger";

export class SequelizeHistoryDailyAdapter implements IHistoryDailyPort {
    async saveInBulk(intervals: HistoryDailyBase[]): Promise<any> {
        try {
            const t = await sequelize.transaction();
            try {
                for (const interval of intervals) {
                    await interval.save({ transaction: t });
                }
                await t.commit();
                return intervals;
            } catch (error) {
                logger.error({ error: error }, `Error insertando los intervalos en la base de datos`);
                await t.rollback();
                throw error;
            }
        } catch (error) {
            logger.error({ error: error }, `Error insertando los intervalos en la base de datos:`);
            throw error;
        }
    }
}