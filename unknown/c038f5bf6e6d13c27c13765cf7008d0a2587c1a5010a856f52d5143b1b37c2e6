import { async, ComponentFixture, TestBed } from '@angular/core/testing';

import { DashboardFlowMenuComponent } from './dashboard-flow-menu.component';

describe('DashboardFlowMenuComponent', () => {
  let component: DashboardFlowMenuComponent;
  let fixture: ComponentFixture<DashboardFlowMenuComponent>;

  beforeEach(async(() => {
    TestBed.configureTestingModule({
      declarations: [ DashboardFlowMenuComponent ]
    })
    .compileComponents();
  }));

  beforeEach(() => {
    fixture = TestBed.createComponent(DashboardFlowMenuComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });
});
