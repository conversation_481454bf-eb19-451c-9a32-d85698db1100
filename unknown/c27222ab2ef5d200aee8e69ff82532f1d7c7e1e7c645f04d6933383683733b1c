import { EditorService } from 'src/app/services/editor.service';
import { ServerService } from 'src/app/services/server.service';
import { Intent } from './../../../../models/cognitivity/Intent';
import { Component, Input, OnInit, Renderer2, ViewChild, ElementRef, Output, EventEmitter } from '@angular/core';
import {normalizeString} from "../../../../urlutils.module";

@Component({
  selector: 'app-intent',
  templateUrl: './intent.component.html',
  styleUrls: ['./intent.component.scss']
})
export class IntentComponent implements OnInit {
  @Input() readOnly : boolean = false;
  @Input() intent : Intent = null;
  @Output() setIntent: EventEmitter<Intent> = new EventEmitter<Intent>();
  availableIntents : Intent[] = [];
  currentIntents : Intent[] = [];
  searchIntentString: String;
  lastSearchIntentString: String;

  @ViewChild('intentPicker', { static: false }) IntentPicker : ElementRef;

  constructor(private renderer: Renderer2, public editorService: EditorService, public serverService: ServerService) { }

  get Intents() : Intent[] {
    if (this.searchIntentString !== this.lastSearchIntentString || this.currentIntents === null) {
      this.currentIntents = this.updateIntents();
      this.lastSearchIntentString = this.searchIntentString;
    }
    return this.currentIntents;
  }

  get EmptyIntentSet() : boolean {
    return this.currentIntents.length === 0;
  }

  updateIntents() : Intent[] {
    let intents = [];

    const lowerIntentname = normalizeString(this.searchIntentString.toLowerCase());
    this.availableIntents.forEach( intent => {
      if (normalizeString(intent.name.toLowerCase()).includes(lowerIntentname)) {
        intents.push(intent);
      }
    });
    return intents;
  }

  ngOnInit() {
    this.availableIntents = this.editorService.getIntents();
    this.currentIntents = this.availableIntents;
  }

  hasIntent() {
    let intent = this.getIntentInfo();
    if (!intent) {
      return false;
    }
    return true;
  }

  getIntentInfo() {
    if (this.intent == null || !this.intent.isValid()) {
      return null;
    }
    return this.intent;
  }

  selectIntent(intentData) {
    this.intent = intentData;
    this.setIntent.emit(intentData);
    this.searchIntentString = intentData.name;
  }

  deleteIntent() {
    this.intent = null;
    this.setIntent.emit(null);
    this.searchIntentString = "";
  }

  onInputFocusIn() {
    this.renderer.removeClass(this.IntentPicker.nativeElement, 'hide');
  }

  onInputFocusOut() {
    setTimeout(() => {
      this.renderer.addClass(this.IntentPicker.nativeElement, 'hide');
    }, 500);
  }

  isValid() {
    if (this.intent != null) {
      return this.intent.isValid();
    }
    return false;
  }
}
