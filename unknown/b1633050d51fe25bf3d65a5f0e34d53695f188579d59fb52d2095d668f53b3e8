@import '_variables';
@import '_mixins';

.fa-handshake{
  color: white
}

.flow {
	@include flow;

  &.facebookMessenger {
    background-color: $facebookMessengerColor;
  }

  &.instagram {
    background: radial-gradient(circle at 30% 107%, #fdf497 0%, #fdf497 5%, #fd5949 45%,#d6249f 60%,#285AEB 90%);
  }

  &.mercadolibre {
    background-color: $mercadoLibreColor;
  }

  &.twitter {
    background-color: $twitterColor;
  }

  &.whatsapp {
    background-color: $whatsappColor;
  }

  &.telegram {
    background-color: $telegramColor;
  }

  &.chat {
    background-color: $chatColor;
  }

  &.skype {
    background-color: $skypeColor;
  }

  &.generic {
    background-color: $genericColor;
  }

  &.applemessaging {
    background-color: $appleMessagingColor;
  }

  &.googlerbm {
    background-color: $googlerbmColor;
  }

  &.development {
    &.facebookMessenger {
      background-color: darken($sidebarBackgroundColor, 5%);
      .icon, .module-icon {
        & > span {
          color: $facebookMessengerColor;
        }
      }
    }

    &.instagram {
      background-color: darken($sidebarBackgroundColor, 5%);
      .icon, .module-icon {
        & > span {
          color: $instagramColor;
        }
      }
    }

    &.mercadolibre {
      background-color: darken($sidebarBackgroundColor, 5%);
      .icon, .module-icon {
        & > span {
          color: $mercadoLibreColor;
        }
      }
    }

    &.twitter {
      background-color: darken($sidebarBackgroundColor, 5%);
      .icon, .module-icon {
        & > span {
          color: $twitterColor;
        }
      }
    }

    &.whatsapp {
      background-color: darken($sidebarBackgroundColor, 5%);
      .icon, .module-icon {
        & > span {
          color: $whatsappColor;
        }
      }
    }

    &.telegram {
      background-color: darken($sidebarBackgroundColor, 5%);
      .icon, .module-icon {
        & > span {
          color: $telegramColor;
        }
      }
    }

    &.chat {
      background-color: darken($sidebarBackgroundColor, 5%);
      .icon, .module-icon {
        & > span {
          color: $chatColor;
        }
      }
    }

    &.skype {
      background-color: darken($sidebarBackgroundColor, 5%);
      .icon, .module-icon {
        & > span {
          color: $skypeColor;
        }
      }
    }

    &.generic {
      background-color: darken($sidebarBackgroundColor, 5%);
      .icon, .module-icon {
        & > span {
          color: $genericColor;
        }
      }
    }

    &.applemessaging {
      background-color: darken($sidebarBackgroundColor, 5%);
      .icon, .module-icon {
        & > span {
          color: $appleMessagingColor;
        }
      }
    }

    &.googlerbm {
      background-color: darken($sidebarBackgroundColor, 5%);
      .icon, .module-icon {
        & > span {
          color: $googlerbmColor;
        }
      }
    }
  }
}
