using System.Net;
using System.Text;
using System.Text.Json;
using Microsoft.Extensions.Logging;
using Moq;
using Moq.Protected;
using TokenManagerApi.Models;
using TokenManagerApi.Services;

namespace TokenManagerApi.Tests;

public class TokenManagerServiceTests
{
    private readonly Mock<IHttpClientFactory> _httpClientFactoryMock;
    private readonly Mock<ILogger<TokenManagerService>> _loggerMock;
    private readonly TokenManagerService _service;
    private readonly TokenRequest _defaultTokenRequest;

    public TokenManagerServiceTests()
    {
        _httpClientFactoryMock = new Mock<IHttpClientFactory>();
        _loggerMock = new Mock<ILogger<TokenManagerService>>();
        _service = new TokenManagerService(_httpClientFactoryMock.Object, _loggerMock.Object);

        _defaultTokenRequest = new TokenRequest
        {
            Url = "https://api.test.com/token",
            Headers = new Dictionary<string, string> { { "Authorization", "Basic test123" } },
            Body = new { grant_type = "client_credentials" },
            Timeout = 30
        };
    }

    private Mock<HttpMessageHandler> ConfigureMockHandler(string token, int expiresIn = 3600)
    {
        var mockHandler = new Mock<HttpMessageHandler>();
        var responseContent = new
        {
            access_token = token,
            scope = "default",
            token_type = "Bearer",
            expires_in = expiresIn
        };

        mockHandler
            .Protected()
            .Setup<Task<HttpResponseMessage>>(
                "SendAsync",
                ItExpr.IsAny<HttpRequestMessage>(),
                ItExpr.IsAny<CancellationToken>()
            )
            .ReturnsAsync(new HttpResponseMessage
            {
                StatusCode = HttpStatusCode.OK,
                Content = new StringContent(
                    JsonSerializer.Serialize(responseContent),
                    Encoding.UTF8,
                    "application/json"
                )
            });

        return mockHandler;
    }

    private void ConfigurarHttpClient(Mock<HttpMessageHandler> mockHandler)
    {
        var client = new HttpClient(mockHandler.Object);
        _httpClientFactoryMock.Setup(x => x.CreateClient(It.IsAny<string>()))
            .Returns(client);
    }

    [Fact]
    public async Task GetTokenAsync_WhenNoTokenExists_GeneratesNewToken()
    {
        var expectedToken = "eyJ4NXQiOiJOV1UxTkRJNU1XTXhPR1ZqWTJZM05tUXlaalJsTXpGalpHUXpPR00wWWpZMVlUZzNZVEUyWWciLCJraWQiOiJZakF3WldZM05qTXlZMkZqWWpBek1ESXdObVEzTm1JNU1UazRNamsyTWpRd09UY3pabU01TlRJME5USTFabVF5TTJNeFltTXdPRGhsT0RZNU5qWmxNd19SUzI1NiIsImFsZyI6IlJTMjU2In0";
        var mockHandler = ConfigureMockHandler(expectedToken);
        ConfigurarHttpClient(mockHandler);

        var result = await _service.GetTokenAsync(_defaultTokenRequest);

        Assert.Equal(expectedToken, result.access_token);
    }

    [Fact]
    public async Task GetTokenAsync_WithValidExistingToken_ReturnsExistingToken()
    {
        var expectedToken = "validToken123";
        var mockHandler = ConfigureMockHandler(expectedToken);
        ConfigurarHttpClient(mockHandler);

        var firstResponse = await _service.GetTokenAsync(_defaultTokenRequest);
        var secondResponse = await _service.GetTokenAsync(_defaultTokenRequest);

        Assert.Equal(firstResponse.access_token, secondResponse.access_token);
        mockHandler.Protected().Verify(
            "SendAsync",
            Times.Once(),
            ItExpr.IsAny<HttpRequestMessage>(),
            ItExpr.IsAny<CancellationToken>()
        );
    }

    [Fact]
    public async Task GetTokenAsync_WithNearExpirationToken_GeneratesNewToken()
    {
        // Arrange
        var firstExpectedToken = "firstToken123";
        var secondExpectedToken = "secondToken456";
        var mockHandler = ConfigureMockHandler(firstExpectedToken, 10);
        ConfigurarHttpClient(mockHandler);

        // Act
        var firstToken = await _service.GetTokenAsync(_defaultTokenRequest);
        await Task.Delay(2000);

        mockHandler = ConfigureMockHandler(secondExpectedToken, 10);
        ConfigurarHttpClient(mockHandler);
        var secondToken = await _service.GetTokenAsync(_defaultTokenRequest);

        // Assert
        Assert.NotEqual(firstToken, secondToken);
        Assert.Equal(firstExpectedToken, firstToken.access_token);
        Assert.Equal(secondExpectedToken, secondToken.access_token);
    }

}
