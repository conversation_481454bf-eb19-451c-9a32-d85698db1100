import moment from "moment";
import { intervalDateTime } from "../../../../Yoizen.yFlow.Web/models/historical/interval";
import { SystemStatus, SystemStatusType } from "../../../../Yoizen.yFlow.Domain/src/models/statistics/SystemStatus";
import { ISystemStatusPort } from '../../../../Yoizen.yFlow.Infrastructure/src/ports/ISystemStatusPort';
import { IIntervalPort } from '../../../../Yoizen.yFlow.Infrastructure/src/ports/IIntervalPort';
import { HistoryDailyFactory } from "../../../../Yoizen.yFlow.Domain/src/models/statistics/historical/HistoryDailyFactory";
import { DailyFactory } from "../../../../Yoizen.yFlow.Domain/src/models/statistics/daily/DailyFactory";
import { IHistoryDailyPort } from "../../../../Yoizen.yFlow.Infrastructure/src/ports/IHistoryDailyPort";
import { logger } from "../../../../Yoizen.yFlow.Helpers/src/Logger";

type DailyProcessTypes = SystemStatusType.LastIntervalDaily |
  SystemStatusType.LastIntervalDailyByFlow |
  SystemStatusType.LastIntervalDailyByBlocks |
  SystemStatusType.LastIntervalDailyByGroups |
  SystemStatusType.LastIntervalDailyByCommands |
  SystemStatusType.LastIntervalDailyByIntegrations |
  SystemStatusType.LastIntervalDailyByDerivationKey |
  SystemStatusType.LastIntervalDailyByDefaultAnswers |
  SystemStatusType.LastIntervalDailyByStatisticEvent |
  SystemStatusType.LastIntervalByAbandonedCase |
  SystemStatusType.LastIntervalDailyByBlocksSequence |
  SystemStatusType.LastIntervalDailyByGroupsSequence;

type LastDailyInterval = {
  [key in DailyProcessTypes]: SystemStatus;
};

export class DailyIntervalService {
  lastDailyInterval: LastDailyInterval;

  constructor(
    private systemStatusPort: ISystemStatusPort,
    private intervalPort: IIntervalPort,
    private historicalDailyPort: IHistoryDailyPort
  ) {

    // Inicializo los intervalos
    this.lastDailyInterval = {
      [SystemStatusType.LastIntervalDaily]: null,
      [SystemStatusType.LastIntervalDailyByFlow]: null,
      [SystemStatusType.LastIntervalDailyByBlocks]: null,
      [SystemStatusType.LastIntervalDailyByGroups]: null,
      [SystemStatusType.LastIntervalDailyByCommands]: null,
      [SystemStatusType.LastIntervalDailyByIntegrations]: null,
      [SystemStatusType.LastIntervalDailyByDerivationKey]: null,
      [SystemStatusType.LastIntervalDailyByDefaultAnswers]: null,
      [SystemStatusType.LastIntervalDailyByStatisticEvent]: null,
      [SystemStatusType.LastIntervalByAbandonedCase]: null,
      [SystemStatusType.LastIntervalDailyByBlocksSequence]: null,
      [SystemStatusType.LastIntervalDailyByGroupsSequence]: null
    };
  }

  async Consolidate(lastInterval: SystemStatus, currentDateInterval: moment.Moment): Promise<SystemStatus> {
    // Verifico cual fue el ultimo intervalo en consolidarse, si falto uno o mas, voy consolidando hasta 
    while (lastInterval.Date.valueOf() <= currentDateInterval.valueOf()) {
      try {
        logger.info(`Voy a consolidar el intervalo ${lastInterval.Date.toLocaleString()} del tipo ${lastInterval.type}`);
        let daily = DailyFactory.CreateDailyBySystemStatus(lastInterval.type, lastInterval.Date);
        const results = await this.intervalPort.calculate(daily);

        let dailyToSave = [];

        results.forEach(row => {
          let historyAux = HistoryDailyFactory.CreateHistoryDailyBySystemStatus(lastInterval.type)
          historyAux.init(lastInterval.Date, row);
          dailyToSave.push(historyAux);
        })

        if (dailyToSave.length > 0) {
          await this.historicalDailyPort.saveInBulk(dailyToSave)
        }

        await this.systemStatusPort.Save(lastInterval);
        await this.intervalPort.dropTable(daily);

      } catch (error) {
        logger.fatal(`CRITICAL - Ocurrió un error al intentar consolidar el intervalo ${lastInterval.Date.toLocaleString()} del tipo ${lastInterval.type} Error: ${error}`);
      }

      lastInterval.Date = lastInterval.Date.add(30, 'minute');
    }

    return lastInterval;
  }

  async Process() {
    const now = moment();
    const currentDateInterval = intervalDateTime(now).add(-30, 'minute');

    for (let key in this.lastDailyInterval) {
      let value: SystemStatus = this.lastDailyInterval[key];
      logger.info(`voy a procesar el registro ${key} del intervalo ${value.Date.toLocaleString()}`);
      await this.Consolidate(value, currentDateInterval);
      logger.info(`terminé de procesar el registro ${key} del intervalo ${value.Date.toLocaleString()}`);
    }

    setTimeout(this.Process.bind(this), 60000 * 5);
  }

  async Init() {
    const now = moment();

    for (let key in this.lastDailyInterval) {
      await this.GetIntervalSystemStatus(now, key as SystemStatusType);
    }
  }

  private async GetIntervalSystemStatus(now: moment.Moment, systemStatusType: SystemStatusType) {
    if (!this.lastDailyInterval[systemStatusType]) {
      let status = await this.systemStatusPort.Get(systemStatusType);
      if (!status) {
        status = new SystemStatus();
        status.type = systemStatusType;
        let date = moment(now);
        date.set({ hour: 0, minute: 0, second: 0, millisecond: 0 });
        status.Date = date;
        logger.info(`No tengo registros en la bd, creo hoy el último intervalo ${this.lastDailyInterval[systemStatusType]} del tipo dailyByFlow`);
        this.lastDailyInterval[systemStatusType] = status;

        status = await this.systemStatusPort.Save(status);
      }

      logger.info(`Ultimo intervalo obtenido ${JSON.stringify(status)} del tipo ${systemStatusType}`);
      status.Date = status.Date.add(30, 'minute');
      this.lastDailyInterval[systemStatusType] = status;
    }
  }
}