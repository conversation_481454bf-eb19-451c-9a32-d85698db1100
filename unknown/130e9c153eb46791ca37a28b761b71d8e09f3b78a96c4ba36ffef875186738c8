import {Component, ViewChild, Inject, OnInit, AfterViewInit, HostListener, ElementRef} from '@angular/core';
import { Router, ActivatedRoute } from '@angular/router';
import { SignaturePad } from 'angular2-signaturepad';
import { ServerService } from '../../services/ServerService';
import { finalize } from "rxjs/operators";
import { StatusResponse } from '../../models/StatusResponse';
import * as CryptoJS from 'crypto-js'
import { Title } from '@angular/platform-browser';
import { DOCUMENT } from '@angular/common';
import {ToasterService} from 'angular5-toaster/dist';

@Component({
  selector: 'app-signature-pad.component',
  templateUrl: './signature-pad.component.html',
  styleUrls: ['./signature-pad.component.scss'],
})
export class SignaturePadComponent implements OnInit, AfterViewInit {
  @ViewChild(SignaturePad, { static: false }) signaturePad: SignaturePad;
  @ViewChild('signatureContainer', { static: false, read: ElementRef }) signatureContainerElement: ElementRef;


  Token = '';
  processing = false;
  pageToken = '';
  SignatureTitle = 'Firme aqui';
  SaveButton = 'Guardar';
  ClearButton = 'Limpiar';
  CloseButton = 'Cerrar';
  SignatureSubmited: string = 'La firma fue enviada';
  resizeTimeout: number;
  loading: boolean = false;
  submited: boolean = false;

  signaturePadOptions: Object = {
    'minWidth': 1
  };

  @HostListener('window:resize')
  onWindowResize() {
    if (this.resizeTimeout) {
      clearTimeout(this.resizeTimeout);
    }
    this.resizeTimeout = setTimeout((() => {
      if (typeof(this.signaturePad) !== 'undefined') {
        this.signaturePad.set('canvasWidth', this.signatureContainerElement.nativeElement.clientWidth);
        this.signaturePad.set('canvasHeight', this.signatureContainerElement.nativeElement.clientHeight - 5);
        this.signaturePad.clear();
      }
    }).bind(this), 500);
  }

  constructor(@Inject(DOCUMENT) private _document: HTMLDocument,
              private route: ActivatedRoute,
              private serverService: ServerService,
              private titleService: Title,
              private toasterService: ToasterService) {

  }

  ngOnInit() {
    this.route.queryParams
      .subscribe(params => {
        this.Token = params.token;
        this.pageToken = params.pageToken;
        const decryptedBytes = CryptoJS.AES.decrypt(this.pageToken, 'abcdefgabcdefg12');
        const plaintext = decryptedBytes.toString(CryptoJS.enc.Utf8);
        const jsonData = JSON.parse(plaintext);
        this.SignatureTitle = jsonData.SignatureTitle || this.SignatureTitle;
        this.SaveButton = jsonData.SaveButton || this.SaveButton;
        this.ClearButton = jsonData.ClearButton || this.ClearButton;
        this.CloseButton = jsonData.CloseButton || this.CloseButton;
        this.SignatureSubmited = jsonData.SignatureSubmited || this.SignatureSubmited;
        this.titleService.setTitle(jsonData.PageTitle);
        this._document.getElementById('appFavicon').setAttribute('href', jsonData.IcoUrl);
      });
  }

  ngAfterViewInit() {
    const pad: any = this.signaturePad.queryPad();
    if (typeof(pad.canvas) === 'undefined' && typeof(pad._canvas) === 'object') {
      pad.canvas = pad._canvas;
    }

    this.signaturePad.set('canvasWidth', this.signatureContainerElement.nativeElement.clientWidth);
    this.signaturePad.set('canvasHeight', this.signatureContainerElement.nativeElement.clientHeight - 5);
    this.signaturePad.clear();
  }

  public save() {
    this.processing = true;
    this.loading = true;
    const base64Data = this.signaturePad.toDataURL();
    this.serverService.saveSignaturePad(base64Data, this.Token)
      .pipe(finalize(() => {
        this.processing = false;
        this.loading = false;
        this.submited = true;
      }))
      .subscribe((res: StatusResponse) => {
        if (res.success === -1) {
          this.toasterService.pop('error', 'yFlow', 'Este link ya fue utilizado para enviar una firma anteriormente');
          window.close();
        }
        else if (res.success === 0) {
          this.toasterService.pop('error', 'yFlow', 'Ocurrió un error al procesar la solicitud');
        }
        else {
          window.close();
        }
      }, error => {
        this.toasterService.pop('error', 'yFlow', 'Error de conexion con el servidor');
        console.log(error);
      });
  }

  public clear() {
    this.signaturePad.clear();
  }

  close() {
    window.close();
  }
}
