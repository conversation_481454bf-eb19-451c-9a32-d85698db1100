import { Component, EventEmitter, Input, OnInit, ViewChild } from '@angular/core';
import { EditorService } from 'src/app/services/editor.service';
import { TranslateService } from '@ngx-translate/core';
import { ModalService } from "../../../../services/Tools/ModalService";
import { StatisticEventDefinition } from 'src/app/models/StatisticEventDefinition';
import { MatPaginator, MatTableDataSource, MatSort} from '@angular/material';
import { TypedJSON } from 'typedjson';
import { clone } from 'lodash';
import { StructuredStatisticEventDataComponent } from '../../popups/structured-statistic-event-data/structured-statistic-event-data.component';

@Component({
  selector: 'app-statistics-events-configuration',
  templateUrl: './statistics-events-configuration.component.html',
  styleUrls: ['./statistics-events-configuration.component.scss']
})
export class StatisticsEventsConfigurationComponent implements OnInit {
  @Input() readOnly: boolean = false;
  events: StatisticEventDefinition[] = null;

  displayedColumns: string[] = ['Name', 'Enabled', 'StructuredDataEnabled', 'StructuredData'];
  @ViewChild(MatSort, { static: false }) sort: MatSort;
  @ViewChild(MatPaginator, { static: false }) paginator: MatPaginator;
  //dataSource = null;
  dataSource = new MatTableDataSource(this.editorService.getAllStatisticEventList());

  constructor(public editorService: EditorService, private modalService: ModalService) { }

  ngOnInit() {
      this.dataSource.sort = this.sort;
      this.dataSource.paginator = this.paginator;
  }

  public isValid(): boolean{
    return this.editorService.statisticEventConfigurationIsValid();
  }

  openStructuredDataModal(statisticEvent: StatisticEventDefinition){
    let statisticEventEmitter = new EventEmitter<StatisticEventDefinition>();

    statisticEventEmitter.subscribe((event: StatisticEventDefinition) => {
      //let aux = TypedJSON.parse<StructuredStatisticEvent>(JSON.stringify(structuredData), StructuredStatisticEvent);
      //statisticEvent.StructuredData = structuredData;
    });
    this.modalService.init(StructuredStatisticEventDataComponent, { statisticEvent: statisticEvent, readOnly: this.readOnly }, {saveStatisticEvent: statisticEventEmitter});
  }
}
