@import "_variables";
@import "_mixins";
.button-content {
  position: relative;
  cursor: pointer;

  .addName {
    border-bottom: 1px solid $cardSeparatorBorderColor;
    padding: 10px;
    position: relative;

    .name {
      font-weight: bold;
      margin-right: 100px;
    }

    .name-invalid {
      font-style: italic;
      color: red;
    }

    .block {
      color: $linkActionColor;
    }

    .url, .login {
      text-decoration: underline;
    }

    .trash {
      @include trash;
      position: absolute;
      right: -17px;
      top: 50%;
      transform: translateY(-50%);

      &:hover {
        color: #555;
      }
    }

    &:hover {
      .trash {
        opacity: 1;
      }
    }

    .submenu-selection {
      font-size: 10px;
      position: absolute;
      right: 0;
      bottom: 0;
      height: 100%;
      display: flex;
      flex-direction: row;
      align-items: center;
      padding-right: 20px;

      span, div {
        display: inline-block;

        &:hover {
          color: lighten($linkActionColor, 10%);
        }
      }

      .invalid {
        display: none;
        font-size: 130%;
        margin-right: 3px;
      }

      .open {
        margin-left: 3px;
        justify-self: center;
        align-self: center;
        font-size: 12px;
      }

      &.invalid {
        color: red;

        span, div {
          &:hover {
            color: lighten(red, 10%);
          }
        }

        .invalid {
          display: inline-block;
        }
      }
    }
  }
}
