var jpeg = null, self = null, detectAndParseMrz = null;
const { RGBLuminanceSource, BinaryBitmap, HybridBinarizer, PDF417Reader } = require('@zxing/library');
try {
    jpeg = require("jpeg-js");

    self = require('../../node_modules/mrz-scanner/static-fs');

    var _readFile = self.readFile;
    self.readFile = function (url, encoding) {
        url = url.substring(url.search('mrz-detection') + 13, url.length).replace('\\', '/').replace('\\', '/');
        console.log('url', url);
        return _readFile(url, encoding);
    }
    detectAndParseMrz = require('./detect-and-parse-mrz.js')({ fs: self });
} catch (error) {
    console.error('Error al inicializar las librerias:', error);
}

const axios = require('axios');

class DniService {
    constructor() { }

    async validate(url) {
        var responseData = {
            mrzWithError: false,
            bardcodeWithError: false,
            result: {}
        };

        var img = await axios.get(url, { responseType: 'arraybuffer' });
        let result = {};
        try {
            await detectAndParseMrz(img.data, result, function progress(msg) {
                console.log(`[${url}] Procesando DNI: ${msg}`);
            })
                .then(function (result) {
                    if (result.error) {
                        console.log(result.error);
                        responseData.mrzWithError = true;
                    }
                    responseData.result = {
                        documentCode: result.parsed.fields.documentCode,
                        documentNumber: result.parsed.fields.documentNumber.toString().replace('o', 0).replace('O', 0),
                        sex: result.parsed.fields.sex,
                        birthday: result.parsed.fields.birthDate,
                        expirationDate: result.parsed.fields.expirationDate,
                        firstname: result.parsed.fields.firstName,
                        lastname: result.parsed.fields.lastName,
                    }
                })
                .catch(function (err) {
                    console.log(err);
                    responseData.mrzWithError = true;
                });
        } catch (err) {
            console.log(err);
            responseData.mrzWithError = true;
        }

        if (responseData.mrzWithError) {
            try {
                const buff = Buffer.from(img.data, 'base64');
                const imageData = jpeg.decode(buff);

                const reader = new PDF417Reader();

                const len = imageData.width * imageData.height;

                const luminanceSourceData = new Uint8ClampedArray(len);

                for (let i = 0; i < len; i++) {
                    luminanceSourceData[i] =
                        ((imageData.data[i * 4] + imageData.data[i * 4 + 1] * 2 + imageData.data[i * 4 + 2]) /
                            4) &
                        0xff;
                }

                const luminanceSource = new RGBLuminanceSource(luminanceSourceData, imageData.width, imageData.height);

                const binaryBitmap = new BinaryBitmap(new HybridBinarizer(luminanceSource));

                const data = reader.decode(binaryBitmap).getText();
                //verifico si es un dni nuevo o viejo
                if (data.startsWith('@')) {
                    result = data.split('@');

                    responseData.result = {
                        documentNumber: result[1].toString().replace('o', 0).replace('O', 0),
                        sex: result[8],
                        birthday: result[7],
                        emitDate: result[9],
                        firstname: result[5],
                        lastname: result[4],
                        exemplar: result[2],
                        tramitNumber: result[10],
                        beginEndCuil: null
                    };
                } else {
                    result = data.split('@');

                    responseData.result = {
                        documentNumber: result[4],
                        sex: result[3],
                        birthday: result[6],
                        emitDate: result[7],
                        firstname: result[2],
                        lastname: result[1],
                        exemplar: result[5],
                        tramitNumber: result[0],
                        beginEndCuil: result[8]
                    };
                }
            } catch (e) {
                console.error(e);
                responseData.bardcodeWithError = true;
            }
        }

        return responseData;
    }
}

module.exports.DniService = DniService;