import moment, { Moment } from "moment";
import { DataTypes, Model } from 'sequelize';
import sequelize from '../../../../Yoizen.yFlow.Web/helpers/sequelize';

export enum SystemStatusType {
    LastIntervalDaily = 'LAST_INTERVAL_DAILY',
    LastIntervalDailyByFlow = 'LAST_INTERVAL_DAILY_BY_FLOW',
    LastIntervalDailyByBlocks = 'LAST_INTERVAL_DAILY_BY_BLOCKS',
    LastIntervalDailyByGroups = 'LAST_INTERVAL_DAILY_BY_GROUPS',
    LastIntervalDailyByCommands = 'LAST_INTERVAL_DAILY_BY_COMMANDS',
    LastIntervalDailyByIntegrations = 'LAST_INTERVAL_DAILY_BY_INTEGRATIONS',
    LastIntervalDailyByDerivationKey = 'LAST_INTERVAL_DAILY_BY_DERIVATION_KEY',
    LastIntervalDailyByDefaultAnswers = 'LAST_INTERVAL_DAILY_BY_DEFAULT_ANSWERS',
    LastIntervalDailyByStatisticEvent = 'LAST_INTERVAL_DAILY_BY_STADISTIC_EVENT',
    LastIntervalDailyByDetailStatisticEvent = 'LAST_INTERVAL_DAILY_BY_DETAIL_STADISTIC_EVENT',
    LastIntervalByAbandonedCase = 'LAST_INTERVAL_ABANDONED',
    LastIntervalCentralize = 'LAST_INTERVAL_CENTRALIZE',
    LastIntervalFTP = 'LAST_INTERVAL_FTP',
    LastIntervalDailyByBlocksSequence = 'LAST_INTERVAL_DAILY_BY_BLOCKS_SEQUENCE',
    LastIntervalDailyByGroupsSequence = 'LAST_INTERVAL_DAILY_BY_GROUPS_SEQUENCE',
    LastIntervalByDetailedAbandonedCase = "LAST_INTERVAL_DETAILED_ABANDONED_CASE",
    LastIntervalLogs = "LAST_INTERVAL_LOGS",
    LastIntervalTableCreation = "LAST_INTERVAL_TABLE_CREATION",
}

export class SystemStatus extends Model {
    declare type: SystemStatusType;
    //lo hago privado porque el declare que necesito para sequelize me jode cuando quiero obtener el date con moment
    private declare date: Moment;

    public get Date(): Moment {
        return moment(this.date);
    }

    public set Date(value: Moment) {
        this.date = value;
    }
}

SystemStatus.init({
    type: DataTypes.STRING,
    date: DataTypes.DATE
}, {
    sequelize: sequelize,
    modelName: 'system_status',
    tableName: 'system_status',
    timestamps: false
});