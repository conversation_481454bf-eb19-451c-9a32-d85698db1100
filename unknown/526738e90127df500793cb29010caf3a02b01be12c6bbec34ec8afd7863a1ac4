<div class="configurable-item" [ngClass]="{ 'invalid': !isValid() }">
  <div class="title">
    {{ 'CONFIGURATION_STATISTICS_EVENTS_CONFIGURATION_TITLE' | translate }}
  </div>
  <div class="description">
    {{ 'CONFIGURATION_STATISTICS_EVENTS_CONFIGURATION_DESCRIPTION' | translate }}
  </div>
  <div class="data">
    <table mat-table [dataSource]="dataSource" matSort class="">

      <ng-container matColumnDef="Name">
        <th mat-header-cell *matHeaderCellDef mat-sort-header> {{'STATISTICS_EVENTS_CONFIGURATION_NAME' | translate}} </th>
        <td mat-cell *matCellDef="let element">
          <input class="input statistic-event-name" type="text" [(ngModel)]="element.Name" [disabled]="readOnly"
                 spellcheck="false" autocomplete="off"/>
        </td>
      </ng-container>

      <ng-container matColumnDef="Enabled">
        <th mat-header-cell *matHeaderCellDef mat-sort-header> {{'STATISTICS_EVENTS_CONFIGURATION_ENABLE' | translate}} </th>
        <td mat-cell *matCellDef="let element">
          <ui-switch [(ngModel)]="element.Enabled" [disabled]="readOnly"
                 color="#45c195" size="small" defaultBgColor="#e0e0e0" switchColor="#ffffff"></ui-switch>
        </td>
      </ng-container>

      <ng-container matColumnDef="StructuredDataEnabled">
        <th mat-header-cell *matHeaderCellDef mat-sort-header> {{'STATISTICS_EVENTS_CONFIGURATION_STRUCTURED_DATA' | translate}} </th>
        <td mat-cell *matCellDef="let element">
          <div class="center" *ngIf="element.StructuredDataEnabled">
            <span class="fa fa-check-circle icon-green"></span>
          </div>
        </td>
      </ng-container>

      <ng-container matColumnDef="StructuredData">
        <th mat-header-cell *matHeaderCellDef></th>
        <td mat-cell *matCellDef="let element">
          <button type="button" class="action-button" [disabled]="readOnly" (click)="openStructuredDataModal(element)">{{'STATISTICS_EVENTS_CONFIGURATION_STRUCTURED_DATA_VIEW' | translate}}</button>
        </td>
      </ng-container>

      <tr mat-header-row *matHeaderRowDef="displayedColumns; sticky: true"></tr>
      <tr mat-row *matRowDef="let row; columns: displayedColumns;"></tr>
    </table>
    <mat-paginator [pageSizeOptions]="[5, 10, 20, 50, 100]" showFirstLastButtons></mat-paginator>
  </div>
</div>