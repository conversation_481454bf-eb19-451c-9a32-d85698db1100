import { DailyBase, DailyInfoTypes } from "./DailyBase";
import { Moment } from "moment";

const BlocksSequenceType = {
    flow: 0,
    command: 1,
    system: 2
}

export class DailyByGroupsSequence extends DailyBase {
    channel: string;
    flowId: number;
    version: number;
    total: number;
    sourceGroupId: string;
    destGroupId: string;
    typeSequence: number;

    constructor(datetime: Moment, data?: { flowId: number, channel: string, sourceGroupId: string, destGroupId: string, type: number, version: number }) {
        super(datetime);

        if (data) {
            this.channel = data.channel;
            this.flowId = data.flowId;
            this.sourceGroupId = data.sourceGroupId;
            this.destGroupId = data.destGroupId;
            this.version = data.version;
            this.typeSequence = data.type;
        }

        this.total = 0;
    }

    type() {
        return DailyInfoTypes.GroupSequence;
    }

    getColumns(): Array<string> {
        return ["date", "interval", "interval_datetime", "flow_id", "source_group_id", "dest_group_id", "type", "channel", "total", "version"];
    }

    getValues(): Array<any> {
        return [this.date.utc().format('YYYY-MM-DD hh:mm:ss'), this.interval, this.datetime.utc().format('YYYY-MM-DD hh:mm:ss'), this.flowId, this.sourceGroupId, this.destGroupId, this.typeSequence, this.channel, this.total, this.version];
    }

    getType() {
        return 'daily_group_sequence';
    }
}