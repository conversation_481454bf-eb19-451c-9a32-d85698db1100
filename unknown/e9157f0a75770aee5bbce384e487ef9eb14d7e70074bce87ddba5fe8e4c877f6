import { isEnvironmentVariableValid, parseToBoolean, parseToInt } from "../../Yoizen.yFlow.Helpers/src/Helpers";
import CryptoJS from 'crypto-js';
import { logger } from "../../Yoizen.yFlow.Helpers/src/Logger";


class Config {
    urlCentralize: string;
    hourCentralize: number;
    minuteCentralize: number;
    gmt: string;
    offset: number;
    amountRecordImport: number;
    salt: CryptoJS.lib.WordArray;
}

export const config: Config = {
    urlCentralize: process.env.urlCentralize,
    hourCentralize: undefined,
    minuteCentralize: undefined,
    offset: undefined,
    gmt: process.env.gmt,
    amountRecordImport: parseToInt(process.env.amountRecordImport, 998),
    salt: CryptoJS.enc.Utf8.parse("$0c1@l")
};

const validateAndFormatConfig = () => {
    if (!isEnvironmentVariableValid(config.urlCentralize)) {
        logger.error(`faltan inicializar variables de entorno: urlCentralize`);
        process.exit(9);
    }

    if (isEnvironmentVariableValid(config.gmt)) {
        // Validar el formato del GMT (signo##:##, ejemplo: -03:00)
        const gmtRegex = /^[+-]\d{2}:\d{2}$/;
        if (!gmtRegex.test(config.gmt)) {
            logger.error(`El formato del GMT es incorrecto. Debe ser signo##:## (Ejemplo: -03:00)`);
            process.exit(9);
        }
    } else {
        logger.error(`Faltan inicializar variables de entorno: gmt`);
        process.exit(9);
    }

    // Convertir el formato GMT a offset en minutos
    try {
        const sign = config.gmt.charAt(0) === '-' ? -1 : 1;
        const parts = config.gmt.substring(1).split(':');
        config.hourCentralize = parseInt(parts[0], 10);
        config.minuteCentralize = parseInt(parts[1], 10);

        // Calcular el offset total en minutos
        config.offset = sign * (config.hourCentralize * 60 + config.minuteCentralize);
        logger.info(`GMT ${config.gmt} convertido a offset: ${config.offset}`);
        logger.info(`GMT ${config.gmt} convertido a hourCentralize: ${config.hourCentralize}`);
        logger.info(`GMT ${config.gmt} convertido a minuteCentralize: ${config.minuteCentralize}`);

    } catch (error) {
        logger.error(`Error al convertir GMT a offset: ${error.message}`);
        process.exit(9);
    }


}

validateAndFormatConfig();