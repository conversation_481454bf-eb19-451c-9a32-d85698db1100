const express = require('express');
const router = express.Router();
const errorHandler = require('../helpers/error-handler');
const pjson = require('../package.json');
const axios = require('axios').default;
const logger = require('../config/winston')
const appRoot = require('app-root-path');
const https = require('https');
const fs = require('fs');

// Usar variables de entorno
const clientCertificateCrl = process.env.CLIENT_CERTIFICATE_CRL;
const clientCertificateKey = process.env.CLIENT_CERTIFICATE_KEY;
const clientCertificatePassphrase = process.env.CLIENT_CERTIFICATE_PASSPHRASE;

const cert = fs.readFileSync(`${appRoot}/credential/client/${clientCertificateCrl}`);
logger.info(`Se leyó el certificado desde ${appRoot}/credential/client/${clientCertificateCrl}`);
const key = fs.readFileSync(`${appRoot}/credential/client/${clientCertificateKey}`);
logger.info(`Se leyó la clave desde ${appRoot}/credential/client/${clientCertificateKey}`);
const passphrase = clientCertificatePassphrase;

const httpsAgent = new https.Agent({
  rejectUnauthorized: false, // (NOTE: this will disable client verification)
  cert: cert,
  key: key,
  passphrase: passphrase
});

axios.interceptors.request.use( x => {
  // to avoid overwriting if another interceptor
  // already defined the same object (meta)
  x.meta = x.meta || {};
  x.meta.requestStartedAt = new Date().getTime();
  return x;
});

axios.interceptors.response.use(x => {
  logger.info(`Execution time for: ${x.config.url} - ${new Date().getTime() - x.config.meta.requestStartedAt} ms`);
  return x;
});

callApiGeeOsde = async (req, res) => {
  const method = req.body.method.toLowerCase();
  const url = req.body.url;
  let headers = {
    'Content-Type': 'application/json',
    'Accept': 'application/json'
  }
  if (req.headers.apiKey !== undefined) {
    headers.apiKey = req.headers.apiKey;
  }

  if (req.headers.apikey !== undefined) {
    headers.apikey = req.headers.apikey;
  }

  if (req.headers.appName !== undefined) {
    headers.appName = req.headers.appName;
  }

  if (req.headers.appname !== undefined) {
    headers.appname = req.headers.appname;
  }

  switch (method) {
    case 'get':
      logger.info(`Se realizará el GET a apigee de osde a la url ${url}`);
      try {
        let response = await axios.get(url, {httpsAgent: httpsAgent, headers: headers});
        logger.info(`La respuesta de apigee de osde fue: ${JSON.stringify(response.data)}`);
        return res.send(response.data);
      }
      catch (error) {
        logger.error(error);
        logger.error(`La respuesta de apigee de osde fue: ${JSON.stringify(error.response.data)}`);
        return res.status(error.response.status).send(error.response.data);
      }

    case 'post':
      const body = req.body.body;
      logger.info(`Se realizará el POST a apigee de osde a la url ${url} con datos ${JSON.stringify(body)}`);
      try {
        let response = await axios.post(url, body, {httpsAgent, headers});
        logger.info(`La respuesta de apigee de osde fue: ${JSON.stringify(response.data)}`);
        return res.send(response.data);
      }
      catch (error) {
        logger.error(error);
        logger.error(`La respuesta de apigee de osde fue: ${JSON.stringify(error.response.data)}`);
        return res.status(error.response.status).send(error.response.data);
      }
  }
};

/* GET users listing. */
router.post('/', errorHandler(callApiGeeOsde));

module.exports = router;
