<div>
  <app-nav-menu></app-nav-menu>
</div>
<div class='body-content'>
  <div class="container" *ngIf="!submited">
    <h1>{{SignatureTitle}}</h1>
    <div #signatureContainer class="signature-container">
      <!--<canvas #canvas class="signature-pad" width=600 height=250></canvas>-->
      <signature-pad [options]="signaturePadOptions"></signature-pad>
    </div>
    <div class="buttons-container">
      <button class="action-button-default" (click)="save()" [disabled]="processing">{{SaveButton}}</button>
      <button (click)="clear()">{{ClearButton}}</button>
    </div>
  </div>
  <div class="container" *ngIf="submited">
    <h1>{{SignatureTitle}}</h1>
    <h3>{{SignatureSubmited}}</h3>
    <div class="buttons-container">
      <button class="action-button-default" (click)="close()">{{CloseButton}}</button>
    </div>
  </div>
</div>
<div class="overlay" *ngIf="loading">
  <app-action-spinner class="spinner"></app-action-spinner>
</div>
