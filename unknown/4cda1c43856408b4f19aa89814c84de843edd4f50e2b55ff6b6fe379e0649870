<div class="title">
  <input type="text"
         placeholder="{{'COMMAND_NAME' | translate}}"
         [disabled]="readOnly"
         [(ngModel)]="selectedCommand.name"
         (change)="onCommandNameChanged($event)">
</div>
<div class="select-block">
  <label>{{ 'COMMAND_SELECT_BLOCK_TEXT' | translate }}</label>
  <app-block-picker class="input" [blockId]="selectedCommand.destinationBlockId"
                    (onSelectNewBlock)="onSelectBlock($event)"
                    (onDeleteBlock)="onDeleteBlock()"
                    [readOnly]="readOnly"
                    [isInvalid]="!selectedCommand.isTargetBlockValid(editorService)"></app-block-picker>
</div>
<div class="condition">
  <label>{{ 'COMMAND_CONDITION' | translate }}</label>
  <app-condition-group [currentDepth]="0"
                       [totalDepth]="calculateDepth(selectedCommand.rule)"
                       [readOnly]="readOnly"
                       [conditionGroup]="selectedCommand.rule"></app-condition-group>
</div>
