import { getTokenPayload, ySmartEnabled } from 'src/app/Utils/window';
import {Component, Input, OnInit} from '@angular/core';
import { VariableDefinition } from '../../../models/VariableDefinition'
import { EditorService } from '../../../services/editor.service'
import {ChannelTypes} from "../../../models/ChannelType";
import {environment} from "../../../../environments/environment";
import { FlowDefinition } from 'src/app/models/FlowDefinition';
import { FlowTypes } from 'src/app/models/FlowType';


@Component({
  selector: 'app-configuration-tab',
  templateUrl: './configuration-tab.component.html',
  styleUrls: ['./configuration-tab.component.scss']
})
export class ConfigurationTabComponent implements OnInit {
  @Input() readOnly: boolean = false;
  implicitVariables : VariableDefinition[];
  showPersistentMenuConfigSection : boolean = false;
  showIceBreakersConfigSection : boolean = false;
  showYSocialSettingsConfigSection : boolean = false;
  showGreetingsConfigSection : boolean = false;
  showStatisticsEventsConfigSection : boolean = true;
  showAccountLinkingConfigSection : boolean = false;
  showGoogleConfigurationConfigSection : boolean = true;
  showHsmJumpConfigSection : boolean = false;
  showApplePayMerchantsConfigSection : boolean = false;
  showImportExcel: boolean = false;
  isAdmin : boolean = false;
  isBot: boolean = false;
  currentFlow: FlowDefinition;
  ySmartEnabled : boolean = false;
  isContingencyBot: boolean = false;

  constructor(private editorService :EditorService) { }

  ngOnInit() {
  	this.implicitVariables = this.editorService.getImplicitVariablesList();
    this.currentFlow = this.editorService.getCurrentFlow();
    this.isAdmin = getTokenPayload().admin;
    this.isBot = this.currentFlow.type === FlowTypes.Bot;
    this.isContingencyBot = this.editorService.getIsContingencyBot();

    if (!this.readOnly){
      var currentModule = this.editorService.getCurrentModule();
      if (currentModule !== undefined){
        this.readOnly = !currentModule.isMaster();
      }
    }

    if(this.isBot && getTokenPayload().edit){
      this.showImportExcel = !this.readOnly;
    }

  	switch (this.currentFlow.channel) {
      case ChannelTypes.FacebookMessenger:
        this.showPersistentMenuConfigSection = true;
        this.showGreetingsConfigSection = true;
        this.showAccountLinkingConfigSection = true;
        this.showIceBreakersConfigSection = true;
        break;

      case ChannelTypes.Instagram:
        this.showIceBreakersConfigSection = true;
        break;

      case ChannelTypes.Twitter:
        this.showPersistentMenuConfigSection = true;
        this.showGreetingsConfigSection = true;
        break;

      case ChannelTypes.Chat:
        this.showPersistentMenuConfigSection = true;
        this.showGreetingsConfigSection = true;
        this.showIceBreakersConfigSection = true;
        break;

      case ChannelTypes.Skype:
        this.showPersistentMenuConfigSection = false;
        this.showGreetingsConfigSection = false;
        break;

      case ChannelTypes.WhatsApp:
        this.showHsmJumpConfigSection = true;
        break;

      case ChannelTypes.Generic:
        this.showGreetingsConfigSection = true;
        break;

      case ChannelTypes.AppleMessaging:
        this.showApplePayMerchantsConfigSection = true;
        break;
    }

    this.showYSocialSettingsConfigSection = !environment.standAlone;
    this.showGoogleConfigurationConfigSection = true;
    this.showStatisticsEventsConfigSection = true;
    this.ySmartEnabled = ySmartEnabled().toLocaleLowerCase() === "true";
  }

  getVariablesTypes() {
    const value = VariableDefinition.variableType;
    return value;
  }

  getVariableType(variable: VariableDefinition) : string {
    for (let i = 0; i < VariableDefinition.variableType.length; i++) {
      if (variable.Type === VariableDefinition.variableType[i].value) {
        return VariableDefinition.variableType[i].localized;
      }
    }

    return '';
  }
}
