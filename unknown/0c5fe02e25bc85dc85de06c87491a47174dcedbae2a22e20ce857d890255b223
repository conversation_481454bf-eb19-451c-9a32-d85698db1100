@import "_variables";
@import "_mixins";

.configurable-item {
  @include configurable-item;

  .days {
    margin-top: 10px;

    .card-info {
      font-style: italic;
      color: #aaaaaa;
    }

    .timetable-container {
      display: flex;
      flex-direction: column;
      align-items: flex-start;

      .timezone {
        margin-bottom: 5px;
      }

      .timetable {
        border-collapse: collapse;
        overflow: hidden;

        td, th {
          position: relative;

          &:hover::after {
            content: "";
            position: absolute;
            background-color: lighten($linkActionColor, 20%);
            left: 0;
            top: -5000px;
            height: 10000px;
            width: 100%;
            z-index: -1;
          }
        }

        .highlight {
          background-color: lighten($linkActionColor, 20%);
        }

        .header {
          border: 1px solid $sidebarBorderColor;

          &:first-child {
            width: 100px;
            border: 1px solid $sidebarBorderColor;
            border-left-style: none;
            border-top-style: none;
          }

          .day {
            width: 100px;
            text-align: center;
            border: 1px solid $sidebarBorderColor;

            a {
              cursor: pointer;
              -webkit-touch-callout: none;
              -webkit-user-select: none;
              -khtml-user-select: none;
              -moz-user-select: none;
              -ms-user-select: none;
              user-select: none;
            }
          }
        }

        .hours {
          .time {
            text-align: right;
            font-weight: bold;
            border-top: 1px solid $sidebarBorderColor;
            border-left: 1px solid $sidebarBorderColor;
            border-right: 1px solid $sidebarBorderColor;
          }

          .selectable-time {
            cursor: pointer;
            -webkit-touch-callout: none;
            -webkit-user-select: none;
            -khtml-user-select: none;
            -moz-user-select: none;
            -ms-user-select: none;
            user-select: none;
            border-top: 1px solid $sidebarBorderColor;
            border-left: 1px solid $sidebarBorderColor;
            border-right: 1px solid $sidebarBorderColor;
            height: 22px;

            &:hover {
              background-color: lighten($linkActionColor, 10%);
            }

            &.selected {
              background-color: $linkActionColor;

              &:hover {
                background-color: darken($linkActionColor, 5%);
              }
            }
          }

          &.interval {
            .time {
              font-size: 70%;
              font-weight: normal;
              border-top-style: none;
            }

            .selectable-time {
              border-top-style: dotted;
            }

            &:last-child {
              border-bottom: 1px solid $sidebarBorderColor;
            }
          }

          &:hover {
            background-color: lighten($linkActionColor, 20%);
          }
        }

        &.readonly {
          .selectable-time {
            cursor: default;
          }
        }
      }
    }

    .days-table {
      display: table;
      width: 100%;

      .header {
        display: table-header-group;
        font-family: $fontFamilyTitles;

        & > div {
          display: table-cell;
          vertical-align: middle;
          border-bottom: 1px solid $sidebarBorderColor;
        }
      }

      .day-row {
        display: table-row;
        width: 100%;
        margin-top: 5px;
        height: 40px;
        min-height: 40px;

        & > div {
          display: table-cell;
          vertical-align: middle;
          border-bottom: 1px solid $sidebarBorderColor;
        }

        .day-name {
          width: 100px;
        }

        .day-workType {
          width: 300px;
        }

        .day-hourRange {
          .time {
            padding: 5px 0;
            border-bottom: 1px solid $sidebarBorderColor;

            .time-title {
              margin-right: 5px;
            }
          }
        }
      }
    }
  }

  .non-working-days {
    margin-top: 15px;

    & > .info {
      font-family: $fontFamily;
      font-size: 15px;
      margin-bottom: 5px;
      color: #767676;
    }

    .calendar {
      display: flex;
      flex-direction: row;
      width: 100%;
      justify-content: flex-start;

      ::ng-deep .ngb-dp-day.hidden {
        display: inline-block !important;
      }

      .custom-day {
        text-align: center;
        padding: 0.185rem 0.25rem;
        display: inline-block;
        height: 2rem;
        width: 2rem;

        &.selected {
          background-color: $linkActionColor;
          color: white;
        }

        &.exception {
          background-color: $popup-delete-button-background;
          color: white;
        }

        &.disabled {
          font-style: italic;
          color: lightgrey;
        }

        &:not(.disabled) {
          &:hover {
            background-color: lighten($linkActionColor, 20%);
          }

          &.exception:hover {
            background-color: lighten($popup-delete-button-background, 20%);
          }
        }
      }
    }
  }

  .days, .non-working-days {
    & > .title {
      font-family: $fontFamilyTitles;
      font-weight: bold;
      font-size: 120%;
      margin-bottom: 5px;
    }
  }
}
