Siempre en español
Indicar si es un feature, fix o refactor y donde. Por ejemplo "feat(knowledge-base): agregar el prompt en las propiedads de la pieza base de conocimiento"
Detallar el cambio realizado, por qué se realizó y qué problema resuelve. Por ejemplo "Se agregó el prompt en las propiedades de la pieza base de conocimiento para que el usuario pueda ver el prompt al editar la pieza. Esto mejora la usabilidad del sistema y evita confusiones al editar la pieza."
Basarse unicamente en los archivos en staged
No incluir archivos que no estén en staged