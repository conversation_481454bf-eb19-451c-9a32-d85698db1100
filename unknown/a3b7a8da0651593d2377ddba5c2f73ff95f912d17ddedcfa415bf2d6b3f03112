{"folderSqlite": "C:\\Source\\Yoizen", "dbSqlite": "database.copy.db", "dbMssql": "data source=localhost,1433;User id=sa;Password=*********;Database=yFlow2", "tables": "{tablas:[{name:'flow_versions',with_identity:true,ignore:['ysocial_user_id','ysocial_user_name','published_by_ysocial_user_id','published_by_ysocial_user_name','ysocial_url','ysocial_access_token','ysocial_access_token_secret'],split_column_total:50,split_result:250},{name:'abandoned_case',with_identity:true,split_column_total:999,split_result:0},{name:'companies',with_identity:true,split_column_total:999,split_result:0},{name:'configurations',with_identity:false,split_column_total:999,split_result:0},{name:'detail_statistic_event',with_identity:true,split_column_total:999,split_result:0},{name:'flows',with_identity:true,ignore:['ysocial_url','ysocial_access_token','ysocial_access_token_secret'],split_column_total:999,split_result:0},{name:'history_daily',with_identity:true,expand_columns_from_json:[{source_column:'data',property:'newCase',dest_column:'new_cases'},{source_column:'data',property:'transferredToYSocial',dest_column:'transferred'},{source_column:'data',property:'closedByYFlow',dest_column:'closed_by_yflow'},{source_column:'data',property:'newMessage',dest_column:'new_messages'},{source_column:'data',property:'hsmCase',dest_column:'hsm_case'},{source_column:'data',property:'caseAbandoned',dest_column:'case_abandoned'},{source_column:'data',property:'monthlyUsers',dest_column:'monthly_users'}],split_column_total:999,split_result:0},{name:'history_daily_by_blocks',with_identity:true,split_column_total:999,split_result:0},{name:'history_daily_by_blocks_sequence',with_identity:true,split_column_total:999,split_result:0},{name:'history_daily_by_commands',with_identity:true,split_column_total:999,split_result:0},{name:'history_daily_by_default_answers',with_identity:true,split_column_total:999,split_result:0},{name:'history_daily_by_derivation_key',with_identity:true,split_column_total:999,split_result:0},{name:'history_daily_by_flow',with_identity:true,expand_columns_from_json:[{source_column:'data',property:'newCase',dest_column:'new_cases'},{source_column:'data',property:'transferredToYSocial',dest_column:'transferred'},{source_column:'data',property:'closedByYFlow',dest_column:'closed_by_yflow'},{source_column:'data',property:'newMessage',dest_column:'new_messages'},{source_column:'data',property:'hsmCase',dest_column:'hsm_case'},{source_column:'data',property:'caseAbandoned',dest_column:'case_abandoned'},{source_column:'data',property:'monthlyUsers',dest_column:'monthly_users'}],split_column_total:999,split_result:0},{name:'history_daily_by_group',with_identity:true,split_column_total:999,split_result:0},{name:'history_daily_by_groups_sequence',with_identity:true,split_column_total:999,split_result:0},{name:'history_daily_by_integrations',with_identity:true,split_column_total:999,split_result:0},{name:'history_daily_by_statistic_event',with_identity:true,split_column_total:999,split_result:0},{name:'system_status',with_identity:true,split_column_total:999,split_result:0},{name:'user_action_logs',with_identity:true,ignore:['ysocial_user_name','ysocial_user_id'],split_column_total:999,split_result:0},{name:'users',with_identity:true,split_column_total:999,split_result:0},{name:'users_permissions_flows',with_identity:true,split_column_total:999,split_result:0},{name:'reports',with_identity:true,rename:[{old:'start',new:'date_start'},{old:'end',new:'date_end'}],split_column_total:999,split_result:0}]}"}