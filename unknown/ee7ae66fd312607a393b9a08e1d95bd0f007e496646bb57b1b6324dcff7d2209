import { Component, Input, OnInit } from '@angular/core';
import { EditorService } from '../../../../services/editor.service';
import { TranslateService } from '@ngx-translate/core';
import { Greetings } from "../../../../models/Greeting";
import { VariableDefinition } from "../../../../models/VariableDefinition";
import { TypeDefinition } from "../../../../models/TypeDefinition";
import { AccountLinking } from "../../../../models/AccountLinking";
import { ChannelTypes } from "../../../../models/ChannelType";

@Component({
  selector: 'app-account-linking',
  templateUrl: './account-linking.component.html',
  styleUrls: ['./account-linking.component.scss']
})
export class AccountLinkingComponent implements OnInit {
  model: AccountLinking;
  implicitVariables: VariableDefinition[] = [];
  supportImplicitVariables: boolean = false;
  @Input() readOnly: boolean = false;

  constructor(private editorService: EditorService, private translateService: TranslateService) {
    this.model = null;
  }

  ngOnInit() {
    this.model = this.editorService.getAccountLinking();

    let flow = this.editorService.getCurrentFlow();
    switch (flow.channel) {
      case ChannelTypes.FacebookMessenger:
      case ChannelTypes.Instagram:
        this.supportImplicitVariables = true;
        this.implicitVariables = [];
        this.implicitVariables.push({ Id: 0, Name: 'flowId', Description: 'SYSTEMVARIABLE_FLOWID', DefaultValue: null, Type: TypeDefinition.Number, Constant: true, Private: false, SendYSmart: false, Persist: true });
        break;
    }
  }

  getVariableType(variable: VariableDefinition): string {
    for (let i = 0; i < VariableDefinition.variableType.length; i++) {
      if (variable.Type === VariableDefinition.variableType[i].value) {
        return VariableDefinition.variableType[i].localized;
      }
    }

    return '';
  }

  searchForVariable(varName) {
    if (this.implicitVariables.length == 0) {
      return null;
    }
    return (this.implicitVariables.some(varDef => varDef.Name == varName) ? true : null);
  }
}
