
# Azure Pipelines
- Se crea el azure pipelines que hace build & push de:
    - Docker image de executor
    - Docker image de intervals
    - Docker image de web
    - Docker image de worker
    - Helm chart de yFlow conteniendo los chart de las cuatro apps y common-helpers
- El pipeline se triggerea en la creacion del tag version/*
- Las versiones tanto para las docker image, como para el chart se obtendran del nombre del tag ej. para version/9.1.0, version/9.1.0-1, las versiones seran 9.1.0, 9.1.0-1 respectivamente.

# Helm chart
- Tendremos un Helm Chart padre o Umbrella que contendra como dependencia los charts para executor, intervals, web, worker y common-helpers.

## Estructura de Archivos
```
DevOps/Helm
├── Chart.yaml
├── templates/
│   └── utils.yaml              <-- Se define el template para el configmap y el secret
├── charts/
│   ├── executor/
│   │   ├── Chart.yaml          <-- Chart generico con nombre de la app (executor)
│   │   └── templates/
│   │       ├── deployment.yaml <-- Definimos el deployment para la aplicacion. Es generico, no requiere mayor manipulacion. Responsabilidad mixta equipos de dev y devops.
│   │       └── utils.yaml      <-- Definimos las dependencias que tendra el chart de executor de common-helpers. Responsabilidad equipo de DevOps.
│   ├── intervals/ (Igual que executor)
│   ├── web/ (Igual que executor)
│   └── worker/ (Igual que executor)
└── values.yaml                 <-- Archivo de values predefinidos para el chart. Se desarrolla mas adelante. Responsabilidad mixta equipos de dev y devops.
```

## values.yaml
- El values.yaml proveera todos los valores por defecto para el correcto funcionamiento de este chart.
- En instancia de despliegue se ampliaran valores de este mismo values, pero NO deberia sobreescribirse ninguno de los definidos en este repositorio.
- El mantenimiento del values.yaml sera responsabilidad mixta entre el equipo de dev y el equipo de devops. Debiendo ser mantenido para cada actualizacion de yflow, segun las variables de entorno esperadas para la aplicacion.

```yaml
global:
  productName: yflow                    #<--- Valor estatico yflow. Se utiliza para el nombramiento de los recursos de k8s.
  appVersion: {{chartversion}}          #<--- Valor estatico {{chartversion}}. Se utiliza como replace key dentro del pipeline.
executor:                               #<--- Se crea un nodo por cada app componente de yflow. (executor, intervals, web, worker).
  appName: executor                     #<--- appName se setea con el mismo nombre del nodo superior. Es la referencia que utiliza el chart para nombrar los recursos de k8s.
  requiredConfigMapEnv:                 #<--- requiredConfigMapEnv es una lista de las variables que son REQUERIDAS para el funcionamiento del producto. Si en tiempo de despliegue estas variables no son configuradas, el pod no sera creado.
    - client
    - [...]
  optionalConfigMapEnv:                 #<--- optionalConfigMapEnv Es una lista de las variables que son OPCIONALES. El implementador podra asignarles valor, como no.
    - NODE_ENV
    - [...]
  requiredSecretEnv:                    #<--- lista de las variables de entorno que deben ser SECRETOS (valores sensibles) y que son REQUERIDOS para el funcionamiento del producto.
    - ySocialAccessToken
    - [...]
  optionalSecretEnv:                    #<--- Lista de variables de entorno que deben ser SECRETOS (valores sensibles) y que NO son requeridas para el funcionamiento del producto.
    - [...]              
intervals:
  appName: intervals
  requiredConfigMapEnv:
    - client
    - [...]
  optionalConfigMapEnv:
    - NODE_ENV
    - [...]
  requiredSecretEnv:
    - dniServiceApiKey
    - [...]
web:
  [...]
worker:
  [...]
```