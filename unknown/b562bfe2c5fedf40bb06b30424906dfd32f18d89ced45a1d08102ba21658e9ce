import { Moment } from "moment";
import { intervalDate, intervalTime } from "../../../../../Yoizen.yFlow.Web/models/historical/interval";
import { Model } from "sequelize"

export enum HistoryDailyInfoTypes {
    /**
     * Información por intervalo diaria de colas y agentes
     */
    Normal = 1,

    /**
     * Información por intervalo diaria de flows
     */
    ByFlow = 2,

    /**
     * Información por intervalo diaria de bloques
     */
    Blocks = 3,

    /**
     * Información por intervalo diaria de commandos
     */
    Commnads = 4,

    /**
     * Información por intervalo diaria de integraciones
     */
    Integrations = 5,

    /**
     * Información por intervalo diaria de key de transferencias
     */
    DerivationKey = 6,

    /**
     * Información por intervalo diaria de respuestas por defecto
     */
    DefaultAnswers = 7,

    /**
     * Información por intervalo diaria de eventos estadísticos
     */
    StatisticEvent = 8,

    /**
     * Información por intervalo diaria de grupos
     */
    Groups = 9,

    /**
     * Información por intervalo de casos abandonados
     */
    AbandonedCases = 10,

    /**
     * Información por intervalo de bloques para gráfico de comportamiento
     */
    BlocksSequence = 11,

    /**
     * Información por intervalo de grupos para gráfico de comportamiento
     */
    GroupSequence = 12
};

export class HistoryDailyBase extends Model {
    dailyInfoTypes: any;
    declare datetime: Moment;
    declare interval: any;
    intervalDate: any;
    declare date: Moment;

    init(datetime: Moment, data?) {
        throw new Error('Invalid totalizer type');
    }

    initBase(datetime: Moment) {
        this.datetime = datetime;
        this.date = datetime;
        this.interval = intervalTime(datetime);
        this.intervalDate = typeof (intervalDate(datetime).replaceAll) === "undefined" ?
            intervalDate(datetime).replace(/-/g, '') :
            intervalDate(datetime).replaceAll('-', '');
    }

    type() {
        throw new Error('Invalid totalizer type');
    }
}