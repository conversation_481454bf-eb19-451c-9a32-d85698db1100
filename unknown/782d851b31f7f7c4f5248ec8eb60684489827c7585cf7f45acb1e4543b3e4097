import moment from "moment";
import { intervalDateTime } from "../../../../Yoizen.yFlow.Web/models/historical/interval";
import { SystemStatus, SystemStatusType } from "../../../../Yoizen.yFlow.Domain/src/models/statistics/SystemStatus";
import { ISystemStatusPort } from '../../../../Yoizen.yFlow.Infrastructure/src/ports/ISystemStatusPort';
import { IIntervalPort } from '../../../../Yoizen.yFlow.Infrastructure/src/ports/IIntervalPort';
import { IHistoryDailyPort } from "../../../../Yoizen.yFlow.Infrastructure/src/ports/IHistoryDailyPort";
import { IConfigurationPort } from "../../../../Yoizen.yFlow.Infrastructure/src/ports/IConfigurationPort";
import { IFlowPort } from '../../../../Yoizen.yFlow.Infrastructure/src/ports/IFlowPort';
import { Ftp } from '../../../../Yoizen.yFlow.Web/helpers/write-to-ftp-server';
import { Sftp } from '../../../../Yoizen.yFlow.Web/helpers/write-to-sftp-server';
import AbandonedCase from '../../../../Yoizen.yFlow.Web/models/historical/abandonedCase';
import DailyByBlocks from '../../../../Yoizen.yFlow.Web/models/historical/dailyByBlocks';
import DailyByCommands from '../../../../Yoizen.yFlow.Web/models/historical/dailyByCommands';
import DailyByDefaultAnswer from '../../../../Yoizen.yFlow.Web/models/historical/dailyByDefaultAnswer';
import DailyByDerivationKey from '../../../../Yoizen.yFlow.Web/models/historical/dailyByDerivationKey';
import DailyByStatisticEvent from '../../../../Yoizen.yFlow.Web/models/historical/dailyByStatisticEvent';
import DailyByFlow from '../../../../Yoizen.yFlow.Web/models/historical/dailyByFlow';
import DailyByGroups from '../../../../Yoizen.yFlow.Web/models/historical/dailyByGroups';
import DailyByIntegrations from '../../../../Yoizen.yFlow.Web/models/historical/dailyByIntegrations';
import DetailedStatisticEvent from '../../../../Yoizen.yFlow.Web/models/detailed/detailedStatisticEvent';
import DetailedEvent from '../../../../Yoizen.yFlow.Web/models/detailed/detailedEvent';
import Flow from '../../../../Yoizen.yFlow.Web/models/flow';
import UserSession from '../../../../Yoizen.yFlow.Web/models/userSession';
import fs from 'fs';
import { reportsFolder } from "../../../../Yoizen.yFlow.Web/helpers/folders";
import { GetModelName } from "../helpers/utils";
import { config } from "../../config";
import { config as globalConfig } from "../../../../Yoizen.yFlow.Helpers/src/Config";
import { logger } from "../../../../Yoizen.yFlow.Helpers/src/Logger";

type DailyProcessTypes = SystemStatusType.LastIntervalFTP;
type LastDailyInterval = {
  [key in DailyProcessTypes]: SystemStatus | null;
};

export class FtpService {
  systemStatusPort: ISystemStatusPort;
  intervalPort: IIntervalPort;
  historicalDailyPort: IHistoryDailyPort;
  configurationPort: IConfigurationPort;
  lastDailyInterval: LastDailyInterval;
  flowPort: IFlowPort;

  constructor(
    systemStatusPort: ISystemStatusPort,
    intervalPort: IIntervalPort,
    historicalDailyPort: IHistoryDailyPort,
    configurationPort: IConfigurationPort,
    flowPort: IFlowPort
  ) {
    this.systemStatusPort = systemStatusPort;
    this.intervalPort = intervalPort;
    this.historicalDailyPort = historicalDailyPort;
    this.configurationPort = configurationPort;
    this.flowPort = flowPort;

    // Inicializo los intervalos
    this.lastDailyInterval = {
      [SystemStatusType.LastIntervalFTP]: null
    };

  }

  private async Consolidate(lastInterval: SystemStatus, currentDateInterval: moment.Moment): Promise<SystemStatus> {
    // Verifico cual fue el ultimo intervalo en consolidarse, si falto uno o mas, voy consolidando hasta 
    while (lastInterval.Date.valueOf() <= currentDateInterval.valueOf()) {

      try {
        if (lastInterval.Date.hour() === config.hourCentralize &&
          lastInterval.Date.minute() === config.minuteCentralize) {

          let configReport = await this.configurationPort.Get();
          if (!configReport) {
            return lastInterval;
          }
          const flows = await this.flowPort.GetAllProductiveMaster();

          logger.info(`Voy a consolidar el intervalo ${lastInterval.Date.toLocaleString()} del tipo ${lastInterval.type}`);
          let initInterval = lastInterval.Date.clone();
          let endInterval = lastInterval.Date.clone();
          initInterval.add(-1, 'day').add(config.offset, 'minutes');
          endInterval.add(-1, 'millisecond').add(config.offset, 'minutes');

          const reportData = JSON.parse(configReport.content);
          if (!reportData?.enabled) {
            logger.info(`Report data se encuentra desactivado`);
          } else {
            const { FTPData, SFTPData } = reportData;
            let config;
            if (typeof (reportData.protocol) !== 'string') {
              reportData.protocol = 'ftp';
            }

            let ftp = null;
            if (reportData.protocol === 'ftp' && this.validateFTPCredentials(FTPData)) {
              ftp = new Ftp(FTPData);
              await ftp.init(SFTPData);
              config = FTPData;
            }
            else if (reportData.protocol === 'sftp' && this.validateFTPCredentials(SFTPData)) {
              ftp = new Sftp();
              await ftp.init(SFTPData);
              config = SFTPData;
            } else {
              logger.error(`Ignorando los reportes FTP por credenciales incompletas`);
            }
            console.log(`Valor userSessionsReport: ${reportData.userSessionsReport} Interval :  ${initInterval} ${endInterval}`);
            if (reportData.userSessionsReport) {
              try {
                logger.info(`Generando reporte de Login/Logout de usuarios para el intervalo ${initInterval} ${endInterval}`);
                if (reportData.protocol === 'ftp' || reportData.protocol === 'sftp') {
                  const fileName = `LoginLogout_${initInterval.format("YYYYMMDD")}.zip`;
                  const destFile = `${config.path}/${fileName}`;

                  let file;
                  if (config.saveInExcel) {
                    file = await UserSession.generateExcel(initInterval, endInterval, config.offset, globalConfig.defaultLanguaje);
                    await ftp.Send(config, destFile, file);
                  } else {
                    file = await UserSession.generateText(`LoginLogout_${initInterval.format("YYYYMMDD")}`,
                      initInterval, endInterval, config.offset, config.operator, globalConfig.defaultLanguaje);
                    await ftp.Send(config, destFile, fs.createReadStream(file, 'utf8'));
                  }
                  if (fs.existsSync(file)) {
                    fs.unlinkSync(file);
                  }

                  logger.info(`Reporte de Login/Logout de usuarios ha sido enviado por ftp de manera exitosa`);
                }
              } catch (e) {
                logger.error(`Ocurrió un error enviando el reporte de Login/Logout de usuarios: ${e}`);
              }
            }

            if (reportData.detailedEventReport) {
              try {
                console.log(`Generando reporte detallado de eventos global para el intervalo ${initInterval} ${endInterval}`);
                if (reportData.protocol === 'ftp' || reportData.protocol === 'sftp') {
                  const fileName = `DetailedEvents_${initInterval.format("YYYYMMDD")}.zip`;
                  const destFile = `${config.path}/${fileName}`;
                  
                  let file;
                  if (config.saveInExcel) {
                    file = await DetailedEvent.generateExcel(initInterval, endInterval, config.offset, globalConfig.defaultLanguaje);
                    await ftp.Send(config, destFile, file);
                  } else {
                    file = await DetailedEvent.generateText(
                      parseInt(`${initInterval.format("YYYYMMDD")}`), 
                      initInterval, 
                      endInterval, 
                      config.offset, 
                      config.operator, 
                      globalConfig.defaultLanguaje
                    );
                    await ftp.Send(config, destFile, fs.createReadStream(file, 'utf8'));
                  }
                  
                  if (fs.existsSync(file)) {
                    fs.unlinkSync(file);
                  }
                  
                  console.log(`Reporte detallado de eventos global ha sido enviado por ftp de manera exitosa`);
                }
              } catch (e) {
                console.error(`Ocurrió un error enviando el reporte detallado de eventos global: ${e}`);
              }
            }

            for (const element of flows) {
              let flow = element;
              logger.info(`Guardando por ftp las estadisticas del flow ${flow.name} en el intervalo ${initInterval} ${endInterval}`);
              try {
                if (reportData.blocksReport) {
                  await this.SendReport(DailyByBlocks, flow, config, initInterval, endInterval, config.offset, ftp);
                }
                if (reportData.commandsReport) {
                  await this.SendReport(DailyByCommands, flow, config, initInterval, endInterval, config.offset, ftp);
                }
                if (reportData.defaultAnswersReport) {
                  await this.SendReport(DailyByDefaultAnswer, flow, config, initInterval, endInterval, config.offset, ftp);
                }
                if (reportData.derivationsReport) {
                  await this.SendReport(DailyByDerivationKey, flow, config, initInterval, endInterval, config.offset, ftp);
                }
                if (reportData.eventsReport) {
                  await this.SendReport(DailyByStatisticEvent, flow, config, initInterval, endInterval, config.offset, ftp);
                }
                if (reportData.globalsReport) {
                  await this.SendReport(DailyByFlow, flow, config, initInterval, endInterval, config.offset, ftp);
                }
                if (reportData.groupsReport) {
                  await this.SendReport(DailyByGroups, flow, config, initInterval, endInterval, config.offset, ftp);
                }
                if (reportData.integrationsReport) {
                  await this.SendReport(DailyByIntegrations, flow, config, initInterval, endInterval, config.offset, ftp);
                }
                if (reportData.detailedStatisticEventReport) {
                  if (flow.ActiveProductionVersionId !== undefined &&
                    flow.ActiveProductionVersionId !== 0) {
                    let flowVersion = await Flow.findLastProductionById(flow.id);
                    if (flowVersion !== null &&
                      Array.isArray(flowVersion.ActiveProductionVersion.blob.StatisticEventList)) {
                      if (flowVersion.ActiveProductionVersion.blob.StatisticEventList.length != 0) {
                        let folder = `${flow.name.normalize("NFD").replace(/[\u0300-\u036f]/g, "").replace(/[/\\]/g, "-")}_${GetModelName(DetailedStatisticEvent)}_${initInterval.format("YYYYMMDD")}`;
                        if (!fs.existsSync(`${reportsFolder}/${folder}/`)) {
                          fs.mkdirSync(`${reportsFolder}/${folder}/`, { recursive: true });
                        }
                        let existsOneEventWithStructureData = flowVersion.ActiveProductionVersion.blob.StatisticEventList.find(event => event.StructuredDataEnabled) !== undefined;

                        if (existsOneEventWithStructureData) {
                          for (const statisticEvent of flowVersion.ActiveProductionVersion.blob.StatisticEventList) {
                            await this.SendReport(DetailedStatisticEvent, flow, config, initInterval, endInterval, config.offset, ftp, [statisticEvent]);
                          }
                        } else {
                          await this.SendReport(DetailedStatisticEvent, flow, config, initInterval, endInterval, config.offset, ftp, []);
                        }
                        if (!fs.existsSync(`${reportsFolder}/${folder}/`)) {
                          fs.rmdirSync(`${reportsFolder}/${folder}/`, { recursive: true });
                        }
                      }
                    }
                  }
                }
                if (reportData.abandonedCasesReport) {
                  await this.SendReport(AbandonedCase, flow, config, initInterval, endInterval, config.offset, ftp);
                }
              }
              catch (e) {
                logger.error(`Error durante el envio ftp: ${e}`);
              }
            }

            await ftp.Close();
          }
        }
      } catch (error) {
        logger.error(`Ocurrió un error al intentar consolidar el intervalo ${lastInterval.toLocaleString()} del tipo ${lastInterval.type} Error: ${error}`);
      }
      this.systemStatusPort.Save(lastInterval);

      lastInterval.Date = lastInterval.Date.add(30, 'minute');
    }

    return lastInterval;
  }

  private validateFTPCredentials(FTPData) {
    return typeof (FTPData) === 'object' && FTPData !== null && FTPData.name.trim() !== "" && FTPData.server.trim() !== "" && typeof (FTPData.port) === "number";
  }

  private async SendReport(model, flow, ftpData, start, end, offset, ftp, events: any = []) {
    let folder, fileName, file;
    if (events.length > 0) {
      fileName = `${flow.name.normalize("NFD").replace(/[\u0300-\u036f]/g, "").replace(/[/\\]/g, "-")}_${GetModelName(model)}_Event${events[0].Name}_${start.format("YYYYMMDD")}.zip`;
      folder = `${flow.name.normalize("NFD").replace(/[\u0300-\u036f]/g, "").replace(/[/\\]/g, "-")}_${GetModelName(model)}_${start.format("YYYYMMDD")}`;
    }
    else {
      fileName = `${flow.name.normalize("NFD").replace(/[\u0300-\u036f]/g, "").replace(/[/\\]/g, "-")}_${GetModelName(model)}_${start.format("YYYYMMDD")}.zip`;
      folder = fileName.replace('.zip', '');
    }

    try {
      let destFile = `${ftpData.path}/${fileName}`;
      logger.info(`Se subirá al ftp del flow ${flow.name} en el reporte de tipo ${GetModelName(model)} el archivo ${fileName} con destino ${destFile}`);

      if (ftpData.saveInExcel) {
        file = await model.generateExcel(flow.id, start, end, offset, globalConfig.defaultLanguaje, events);
        await ftp.Send(ftpData, destFile, file);
      }
      else {
        file = await model.generateText(folder, flow.id, start, end, offset, ftpData.operator, globalConfig.defaultLanguaje, events);
        await ftp.Send(ftpData, destFile, fs.createReadStream(file, 'utf8'));
      }

      if (fs.existsSync(file)) {
        fs.unlinkSync(file);
      }

      logger.info(`El flow ${flow.name} en el reporte de tipo ${GetModelName(model)} ha sido enviado por ftp de manera exitosa`);
    }
    catch (e) {
      logger.error(`Ocurrió un error enviando el reporte del flow ${flow.name} en el reporte de tipo ${GetModelName(model)}: ${e}`)
    }
  }

  async Process() {
    const now = moment();
    const currentDateInterval = intervalDateTime(now).add(-30, 'minute');

    for (let key in this.lastDailyInterval) {
      let value: SystemStatus = this.lastDailyInterval[key];
      logger.info(`voy a procesar el registro ${key} del intervalo ${value.Date.toLocaleString()}`);
      await this.Consolidate(value, currentDateInterval);
      logger.info(`terminé de procesar el registro ${key} del intervalo ${value.Date.toLocaleString()}`);
    }

    setTimeout(this.Process.bind(this), 60000 * 5);
  }

  async Init() {
    const now = moment();
    for (let key in this.lastDailyInterval) {
      await this.GetIntervalSystemStatus(now, key as SystemStatusType);
    }
  }

  private async GetIntervalSystemStatus(now: moment.Moment, systemStatusType: SystemStatusType) {
    // Obtengo la última fecha del intervalo ejecutado correctamente si no la tengo.
    if (!this.lastDailyInterval[systemStatusType]) {
      let status = await this.systemStatusPort.Get(systemStatusType);
      if (!status) {
        status = new SystemStatus();
        status.type = systemStatusType;
        let date = moment(now);
        date.set({ hour: 0, minute: 0, second: 0, millisecond: 0 });
        status.Date = date;
        logger.info(`No tengo registros en la bd, creo hoy el último intervalo ${this.lastDailyInterval[systemStatusType]} del tipo dailyByFlow`);
        this.lastDailyInterval[systemStatusType] = status;

        status = await this.systemStatusPort.Save(status);
      }
      logger.info(`Ultimo intervalo obtenido ${JSON.stringify(status)} del tipo ${systemStatusType}`);
      this.lastDailyInterval[systemStatusType] = status;
    }
  }
  //#endregion
}
