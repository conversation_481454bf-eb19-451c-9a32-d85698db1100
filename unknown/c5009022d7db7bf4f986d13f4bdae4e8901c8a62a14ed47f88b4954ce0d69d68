import { Model } from 'sequelize';

export enum IntegrationResultType {
    Success = 0,
    Error = 1
}

interface IntegrationAuditBase {
    type: IntegrationResultType;
    caseId: string;
    messageId: string;
    socialUserId: string;
    flowId: string;
    statusCode: number;
    wait: number;
    dns: number;
    tcp: number;
    tls: number;
    request: number;
    firstByte: number;
    download: number;
    total: number;
    yFlow: number;
    integrationId: string;
    integrationName: string;
    timestamp: number;
}

export class IntegrationAuditSequelize extends Model<IntegrationAuditBase> {
    declare type: IntegrationResultType;
    declare caseId: string;
    declare messageId: string;
    declare socialUserId: string;
    declare flowId: string;
    declare statusCode: number;
    declare wait: number;
    declare dns: number;
    declare tcp: number;
    declare tls: number;
    declare request: number;
    declare firstByte: number;
    declare download: number;
    declare total: number;
    declare yFlow: number;
    declare integrationId: string;
    declare integrationName: string;
    declare timestamp: number;
}


export class IntegrationAudit implements IntegrationAuditBase {
    constructor() { }
    caseId: string;
    messageId: string;
    socialUserId: string;
    flowId: string;
    statusCode: number;
    wait: number;
    dns: number;
    tcp: number;
    tls: number;
    request: number;
    firstByte: number;
    download: number;
    total: number;
    yFlow: number;
    integrationId: string;
    integrationName: string;
    type: IntegrationResultType;
    timestamp: number;
}