import moment from "moment";
import { intervalDateTime, intervalDate, intervalTime } from "../../../../Yoizen.yFlow.Web/models/historical/interval";
import { SystemStatus, SystemStatusType } from "../../../../Yoizen.yFlow.Domain/src/models/statistics/SystemStatus";
import { ISystemStatusPort } from '../../../../Yoizen.yFlow.Infrastructure/src/ports/ISystemStatusPort';
import { IIntervalPort } from '../../../../Yoizen.yFlow.Infrastructure/src/ports/IIntervalPort';
import { IHistoryDailyPort } from "../../../../Yoizen.yFlow.Infrastructure/src/ports/IHistoryDailyPort";
import { logger } from "../../../../Yoizen.yFlow.Helpers/src/Logger";

type DailyProcessTypes = SystemStatusType.LastIntervalTableCreation;

type LastDailyInterval = {
  [key in DailyProcessTypes]: SystemStatus;
};

export class TableCreationIntervalService {
  lastDailyInterval: LastDailyInterval;

  constructor(
    private systemStatusPort: ISystemStatusPort,
    private intervalPort: IIntervalPort,
    private historicalDailyPort: IHistoryDailyPort
  ) {

    // Inicializo los intervalos
    this.lastDailyInterval = {
      [SystemStatusType.LastIntervalTableCreation]: null
    };
  }

  async Consolidate(lastInterval: SystemStatus, currentDateInterval: moment.Moment): Promise<SystemStatus> {
    // Verifico cual fue el ultimo intervalo en consolidarse, si falto uno o mas, voy consolidando hasta 
    while (lastInterval.Date.valueOf() < currentDateInterval.clone().add(2, "day").valueOf()) {
      try {
        logger.info(`Voy a consolidar el intervalo ${lastInterval.Date.toLocaleString()} del tipo ${lastInterval.type}`);

        await this.intervalPort.executeStoredProcedure("daily_createtables", [intervalDateTime(lastInterval.Date.add(1, 'day')).format("YYYYMMDD"), intervalTime(lastInterval.Date).toFixed()]);
        lastInterval.Date = lastInterval.Date.utc().startOf('day').add(1, 'day');
        await this.systemStatusPort.Save(lastInterval);

      } catch (error) {
        logger.fatal(`CRITICAL - Ocurrió un error al intentar consolidar el intervalo ${lastInterval.Date.toLocaleString()} del tipo ${lastInterval.type} Error: ${error}`);
      }
    }

    return lastInterval;
  }

  async Process() {
    const now = moment();
    const currentDateInterval = intervalDateTime(now).add(-30, 'minute');

    for (let key in this.lastDailyInterval) {
      let value: SystemStatus = this.lastDailyInterval[key];
      logger.info(`voy a procesar el registro ${key} del intervalo ${value.Date.toLocaleString()}`);
      await this.Consolidate(value, currentDateInterval);
      logger.info(`terminé de procesar el registro ${key} del intervalo ${value.Date.toLocaleString()}`);
    }

    setTimeout(this.Process.bind(this), 60000 * 5);
  }

  async Init() {
    const now = moment();

    for (let key in this.lastDailyInterval) {
      await this.GetIntervalSystemStatus(now, key as SystemStatusType);
    }
  }

  private async GetIntervalSystemStatus(now: moment.Moment, systemStatusType: SystemStatusType) {
    if (!this.lastDailyInterval[systemStatusType]) {
      let status = await this.systemStatusPort.Get(systemStatusType);
      if (!status) {
        status = new SystemStatus();
        status.type = systemStatusType;
        let date = moment(now);
        status.Date = date.add(-1, "day");
        logger.info(`No tengo registros en la bd, creo hoy el último intervalo ${this.lastDailyInterval[systemStatusType]} del tipo ${systemStatusType}`);
        this.lastDailyInterval[systemStatusType] = status;
      }

      logger.info(`Ultimo intervalo obtenido ${JSON.stringify(status)} del tipo ${systemStatusType}`);
      this.lastDailyInterval[systemStatusType] = status;
    }
  }
}