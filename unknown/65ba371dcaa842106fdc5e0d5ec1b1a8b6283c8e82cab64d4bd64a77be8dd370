import { promises } from 'fs';
import { createConnection } from 'mysql2/promise';
import sql from 'mssql';
import redis from 'redis';

const storagePath = 'C:\\Source\\Yoizen\\yFlow\\Yoizen.yFlow.Scripts\\MigrateFlows\\test';
const host = '**********';
const username = 'oldversionyflowdbuser';
const password = 'Mastropiero!2018.';
const name = 'OldversionYflowDB';
const redisHost = 'yflow.redis.cache.windows.net';
const redisPort = 6380;
const redisKey = '+SLICqbsKg0tJQwyeSe2IHb4tLds7ScCpYMByHjvTdg=';
const saveToRedis = true; // Set this to false to save to MySQL instead
const client = 'ATT';

function formatDate(date) {
  const pad = (num, size) => ('000' + num).slice(size * -1);
  const dateStr = date.getFullYear() + '-' +
    pad(date.getMonth() + 1, 2) + '-' +
    pad(date.getDate(), 2) + ' ' +
    pad(date.getHours(), 2) + ':' +
    pad(date.getMinutes(), 2) + ':' +
    pad(date.getSeconds(), 2) + '.' +
    pad(date.getMilliseconds(), 3) + '0000';
  return dateStr;
}

(async () => {
  let connection;
  let redisClient;
  const date = formatDate(new Date);

  if (saveToRedis) {
    redisClient = redis.createClient({
      socket: {
        host: redisHost,
        port: redisPort,
        passphrase: redisKey,
        tls: true
      },
      password: redisKey,
      name: client
    });
    redisClient.on("error", function (error) {
      if (typeof (error) === 'object') {
        console.error(`Error en el cliente de redis dbcontext: ${JSON.stringify(error)}`);
      }
      else {
        console.error(`Error en el cliente de redis dbcontext: ${error.toString()}`);
      }
    });

    redisClient.on('ready', function () {
      console.log(`Cliente redis dbcontext ready`);
    });

    redisClient.on('connect', function () {
      console.log(`Cliente redis dbcontext connected`);
    });

    redisClient.on('reconnecting', function () {
      console.log(`Cliente redis dbcontext reconnecting`);
    });

    redisClient.on('end', function () {
      console.log(`Cliente redis dbcontext ended`);
    });
    console.log(`esperanding`);
    let pepe = await redisClient.connect();

  } else {
    try {
      connection = await sql.connect({
        server: host || '',
        user: username || '',
        password: password || '',
        database: name || '',
      });

      console.log('Conexión establecida a la base de datos MySQL');
    } catch (err) {
      console.error('Error de conexión:', err);
      return;
    }
  }

  try {
    let files = await promises.readdir(storagePath);
    for (let file of files) {
      const filePath = `${storagePath}/${file}`;
      file = file.replace("_yflow", "").replace(".json", "");
      try {
        const data = await promises.readFile(filePath, 'utf8');
        const jsonData = JSON.parse(data);

        if (saveToRedis) {
          // Save to Redis with 24-hour
          file = `${client}:yFlow:caseContext:${file}`
          await redisClient.set(file, JSON.stringify(jsonData), {
            EX: 86400
          });
          console.log(`Datos del caso #${file} insertados en Redis`);
        } else {
          const query = `INSERT INTO case_contexts (case_id, json, created_at, updated_at) VALUES ('${file}', '${JSON.stringify(jsonData)}', '${date}', '${date}')`;
          console.log(query);
          const request = connection.request();
          await request.query(query);
          console.log(`Datos del caso #${file} insertados en la base de datos`);
        }

      } catch (err) {
        console.error('Error al procesar el archivo:', err);
      }
    }
  } catch (err) {
    console.error('Error al leer el storagePath:', err);
  }
  process.exit(9);
})();
