import { Component, OnInit, EventEmitter, Input, Output } from '@angular/core';
import { LoginType, UserPublicInfo } from "../../../models/UserPublicInfo";
import { ModalService } from "../../../services/Tools/ModalService";
import { EditorService } from "../../../services/editor.service";
import { ServerService } from "../../../services/server.service";
import { TranslateService } from "@ngx-translate/core";
import { ToasterService } from "angular2-toaster";
import { StatusResponse } from "../../../models/StatusResponse";
import { finalize } from "rxjs/operators";
import { getTokenPayload, ySmartEnabled } from "../../../Utils/window";
import { DomSanitizer, SafeHtml } from '@angular/platform-browser';
import * as zxcvbn from 'zxcvbn';

@Component({
    selector: 'app-user-popup',
    templateUrl: './user-popup.component.html',
    styleUrls: ['./user-popup.component.scss']
})
export class UserPopupComponent implements OnInit {
    editing: boolean = false;
    superAdmin: boolean = false;
    confirmationPassword: string = null;
    passwordStrengthValue: number = 0;
    canEditFields: boolean = false;
    canValidatePasswords: boolean = false;
    ySmartEnabled: boolean = false;
    buttonPressed: boolean = false;
    loginTypes = LoginType;

    @Input() user: UserPublicInfo = null;
    @Input() validator: (name: string) => boolean = null;
    @Output() loading: EventEmitter<boolean> = new EventEmitter<boolean>();
    @Output() acceptAction: EventEmitter<UserPublicInfo> = new EventEmitter<UserPublicInfo>();

    constructor(
        private modalService: ModalService,
        private editorService: EditorService,
        private serverService: ServerService,
        private toasterService: ToasterService,
        private translateService: TranslateService,
        private sanitizer: DomSanitizer
    ) { }

    getTitleTranslation(): SafeHtml {
        if (this.editing) {
            const translatedText = this.translateService.instant('USER_DIALOG_TITLE_EDIT', { name: this.user.name });
            return this.sanitizer.bypassSecurityTrustHtml(translatedText);
        }
        return this.translateService.instant('USER_DIALOG_TITLE_NEW');
    }

    onNewPassword() {
        this.toasterService.clear();
        if (this.editing) {
            if (!this.validatePassword()) {
                return;
            }

            this.loading.emit(true);
            this.serverService.changePassword(null, this.user.password, this.user.id)
                .pipe(finalize(() => {
                    this.loading.emit(false);
                }))
                .subscribe(
                    (status: StatusResponse) => {
                        this.acceptAction.emit(this.user);
                        this.modalService.destroy();
                    },
                    error => {
                        this.toasterService.pop('error', this.translateService.instant('USER_DIALOG_ERROR'));
                    }
                );
        }
    }

    onChangeIsAdmin(event) {
        if (event) {
            this.user.can_edit = true;
            this.user.can_publish = true;
            this.user.can_see_statistics = true;
            this.user.can_validate_passwords = true;
            this.user.can_access_ysmart = true;
            this.user.can_delete = true
        }
    }

    onAccept() {
      this.buttonPressed = true;
      this.toasterService.clear();

      // Caso 1: Crear nuevo usuario
      if (!this.editing) {
        if (!this.validateNewUser()) {
          this.buttonPressed = false;
          return;
        }

        this.loading.emit(true);
        this.serverService.createUser(this.user)
          .pipe(finalize(() => {
            this.loading.emit(false);
          }))
          .subscribe(
            (status: StatusResponse) => {
              this.user.id = status.data.id;
              this.acceptAction.emit(this.user);
              this.modalService.destroy();
            },
            error => {
              this.toasterService.pop('error', this.translateService.instant('USER_DIALOG_ERROR'));
              this.buttonPressed = false;
            }
          );
      }
      // Caso 2: Editar usuario existente
      else {
        // Si hay nueva contraseña, primero actualizamos la contraseña
        if (this.user.password) {
          if (!this.validatePassword()) {
            this.buttonPressed = false;
            return;
          }

          this.loading.emit(true);
          // Indicar que es solo una confirmación, no un cambio real de contraseña
          this.serverService.changePassword(null, this.user.password, this.user.id, true) // true = confirmationOnly
            .pipe(finalize(() => {
              this.updateUserData();
            }))
            .subscribe(
              (status: StatusResponse) => {
                // Éxito al confirmar contraseña, continúa con updateUser
              },
              error => {
                this.toasterService.pop('error', this.translateService.instant('USER_DIALOG_ERROR'));
                this.buttonPressed = false;
                this.loading.emit(false);
              }
            );
        } else {
          // Si no hay cambio de contraseña, solo actualiza datos
          this.updateUserData();
        }
      }
    }
    private updateUserData() {
      this.loading.emit(true);
      this.serverService.updateUser(this.user)
        .pipe(finalize(() => {
          this.loading.emit(false);
        }))
        .subscribe(
          (status: StatusResponse) => {
            this.acceptAction.emit(this.user);
            this.modalService.destroy();
          },
          error => {
            this.toasterService.pop('error', this.translateService.instant('USER_DIALOG_ERROR'));
            this.buttonPressed = false;
          }
        );
    }


    private validateNewUser(): boolean {
        if (!this.validateUsername()) {
            return false;
        }

        if (this.user.login_type === LoginType.LOCAL) {
            if (!this.validatePassword()) {
                return false;
            }
        }

        return true;
    }

    private validateUsername(): boolean {
        if (!this.user.name || this.user.name.length < 3) {
            this.toasterService.pop('error', this.translateService.instant('USER_DIALOG_INVALID_NAME'));
            return false;
        }

        const regex = /^[a-zA-Z0-9_.-]{3,}$/;
        if (!regex.test(this.user.name)) {
            this.toasterService.pop('error', this.translateService.instant('USER_DIALOG_INVALID_NAME'));
            return false;
        }

        if (this.validator && !this.validator(this.user.name)) {
            this.toasterService.pop('error', this.translateService.instant('USER_DIALOG_INVALID_NAME_EXISTS'));
            return false;
        }

        return true;
    }

    private validatePassword(): boolean {
        if (!this.user.password || this.user.password.length < 6) {
            this.toasterService.pop('error', this.translateService.instant('USER_DIALOG_INVALID_PASSWORD'));
            return false;
        }

        if (typeof (zxcvbn) !== 'undefined') {
            let result = zxcvbn(this.user.password, [this.user.name]);
            if (result.score < 3) {
                this.toasterService.pop('error', this.translateService.instant('USER_DIALOG_INVALID_PASSWORD_UNSECURE'));
                return false;
            }
        }

        if (!this.confirmationPassword || this.confirmationPassword !== this.user.password) {
            this.toasterService.pop('error', this.translateService.instant('USER_DIALOG_INVALID_CONFIRMATION'));
            return false;
        }

        return true;
    }

    ngOnInit() {
      let tokenPayload = getTokenPayload();
      if (tokenPayload.uid == 1 || (tokenPayload.admin && (this.user === null || this.user.id !== tokenPayload.uid))) {
        this.canEditFields = true;
      }
      if (tokenPayload.canValidatePasswords) {
          this.canValidatePasswords = true;
      }
      if (this.user === null) {
          this.user = new UserPublicInfo();
          this.user.login_type = LoginType.LOCAL;
          this.editing = false;
      } else {
          this.editing = true;
          this.user = this.user.clone();
          this.superAdmin = (this.user.id === 1);
      }
      if (!this.user.enabled) {
          this.canValidatePasswords = false;
      }

      if (this.user.login_type === null || this.user.login_type === undefined) {
          this.user.login_type = LoginType.LOCAL;
      }

      this.ySmartEnabled = ySmartEnabled().toLocaleLowerCase() === "true";
    }

    onCancel() {
        this.modalService.destroy();
    }

    calcPasswordStrength($event: Event) {
        if (!this.user.password || this.user.password.length === 0) {
            this.passwordStrengthValue = 0;
            return;
        }

        if (typeof (zxcvbn) !== 'undefined') {
            let result = zxcvbn(this.user.password, [this.user.name]);
            this.passwordStrengthValue = result.score;
        }
    }
}
