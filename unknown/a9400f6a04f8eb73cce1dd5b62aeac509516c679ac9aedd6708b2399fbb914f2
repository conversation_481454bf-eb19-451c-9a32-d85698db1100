# Utilerías para DevOps

1. Para buildear local desde la raíz proyecto, ejecutar:
 ```sh
docker build -f DocumentValidator.Dockerfile -t dni-validator-image .
  ```

2. Para levantar el proyecto dockerizado post build:
 ```sh
docker run -d -p 3000:3000 --name dni-validator-container --env-file Yoizen.yFlow.DocumentValidator/.env.example dni-validator-image
  ```
  
3. Para probar localmente que haya levantado ok: [health](http://localhost:3000/api/health_check)