import { StringUtils } from "./StringUtils";
import { isStringValid } from "../urlutils.module";
import { EditorService } from "../services/editor.service";
import { VariableDefinition } from "../models/VariableDefinition";
import { TypeDefinition } from "../models/TypeDefinition";

export class ScriptDefinition {
  startPosition: number;
  endPosition: number;
}

export class ScriptSearchResult {
  scriptDefinitions: ScriptDefinition[] = [];
  isScriptTagOpen: boolean = false;
}

export class VariableInputValidator {
    
  variableFinder : Function;
  OpenVariableChar = '{';
  CloseVariableChar = '}';
  ScriptCharacter = '$';

  constructor( variableFinder) { 
    this.variableFinder = variableFinder;
  }

  getVariableNamesFromString(text : string) : Array<string>
  {
    var regexp = new RegExp(/{{.+?}}/g);
    var ret = [];
    var matches = regexp.exec(text);
    while(matches)
    {
      ret.push(matches[0].replace('{{', '').replace('}}',''));
      matches = regexp.exec(text);
    }
    
    return ret;
  }

  static getVariableNamesFromString(text : string) : Array<string>
  {
    var regexp = new RegExp(/{{.+?}}/g);
    var ret = [];
    var matches = regexp.exec(text);
    while(matches)
    {
      ret.push(matches[0].replace('{{', '').replace('}}',''));
      matches = regexp.exec(text);
    }
    
    return ret;
  }

  missingReferences(text : string) : Boolean {
    if(text == null || text.length == 0) {
      return false;
    }

    var references = this.getVariableNamesFromString(text);
    return references.some((varName) => {
      if(this.variableFinder(varName) == null) {
        return true;
      }
    });
  }

  isValidText(text : string) {
    return !this.missingReferences(text) && !this.getScriptPositionsInText(text).isScriptTagOpen;
  }

  getScriptPositionsInText( text: string) : ScriptSearchResult {
    let searchResult = new ScriptSearchResult;
    if(StringUtils.isEmpty(text)) {
      return searchResult;
    }
    var currentDefinition : ScriptDefinition;
    for(let i=2; i < text.length; i++) {
      
      if( currentDefinition == null && 
          text[i-2] === this.ScriptCharacter &&
          text[i-1] === this.OpenVariableChar) {
        currentDefinition = new ScriptDefinition();
        currentDefinition.startPosition = i;
        searchResult.isScriptTagOpen = true;
      }
      if( currentDefinition != null && 
          text[i] === this.ScriptCharacter &&
          text[i-1] === this.CloseVariableChar) {
        currentDefinition.endPosition = i-2;
        searchResult.scriptDefinitions.push(currentDefinition);
        currentDefinition = null;
        searchResult.isScriptTagOpen = false;
      }
    }
    return searchResult;
  }

  static mGlobalValidator = new VariableInputValidator(null);
  static validateStringWithScripts(str: string, editorService:EditorService, specialVar:VariableDefinition[] = null): boolean {
    if(!isStringValid(str)) {
      return false;
    }
    let variableList = VariableInputValidator.mGlobalValidator.getVariableNamesFromString(str);
    let allVariableValid = variableList.every( (vname,index) => {
      let variable = editorService.findVariableWithName( vname, true);
      if(variable == null && specialVar != null) {
        variable = specialVar.find( (v) => {return v.Name === vname; });
      }
      return variable != null;
    });
    if( !allVariableValid ) {
      return false;
    }
    let searchScript = VariableInputValidator.mGlobalValidator.getScriptPositionsInText(str);
    return !searchScript.isScriptTagOpen;
  }

  static validateStringNumberWithScripts(str: string, editorService: EditorService, specialVar:VariableDefinition[] = null): boolean {
    if(!isStringValid(str)) {
      return false;
    }
    let variableList = VariableInputValidator.mGlobalValidator.getVariableNamesFromString(str);
    let allVariableValid = variableList.every( (vname,index) => {
      let variable = editorService.findVariableWithName( vname, true);
      if(variable == null && specialVar != null) {
        variable = specialVar.find( (v) => {return v.Name === vname; });
      }
      return variable != null && (variable.Type === TypeDefinition.Number || variable.Type === TypeDefinition.Decimal);
    });
    if( !allVariableValid ) {
      return false;
    }
    let searchScript = VariableInputValidator.mGlobalValidator.getScriptPositionsInText(str);
    return !searchScript.isScriptTagOpen && (variableList.length !== 0 || !isNaN(Number(str)));
  }
}