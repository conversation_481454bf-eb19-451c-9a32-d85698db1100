import FlowTable from '../../../../Yoizen.yFlow.Web/models/imports/flowTable';
import FlowTableType from '../../../../Yoizen.yFlow.Web/models/imports/flowTableType';
import { IPersonalizedTablePort } from '../../ports/IPersonalizedTablePort';
import sequelize from '../../../../Yoizen.yFlow.Web/helpers/sequelize';

export class SequelizePersonalizedTableAdapter implements IPersonalizedTablePort {

    async getAllProcessing() {
        return FlowTable.getAllByStatus(FlowTableType.Processing);
    }

    async query(query: string, transaction?: any) {
        if (transaction) {
            return await sequelize.query(query, transaction);
        } else {
            return await sequelize.query(query);
        }

    }

    async getTransaction() {
        return await sequelize.transaction();
    }
}