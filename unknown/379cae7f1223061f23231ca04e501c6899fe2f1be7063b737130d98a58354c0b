<app-nav-bar [leftButtonLink]="homeUrl" [mainText]="'WELCOME' | translate" (onLogoutButtonClick)="logout()"
  [showLogoutButton]="true" [showSettingsButton]="false" [showTestButton]="false" [showConfigurationButton]="true"
  [showDashboardButton]="false" [showFilterButton]="true" [showLogsErrorButton]="isAdmin"
  [showGlobalStatisticButton]="canSeeStatistics" [showAccessYSmart]="canAccessYSmart"
  (onAccessYSmartButtonClick)="accessYSmart()" (onConfigurationButtonClick)="showSettings()"
  (onSettingsButtonClick)="showSettings()" (onGlobalStatisticButtonClick)="showGlobalStatistic()"
  (onFilterButtonClick)="openFilter()" (onLogsErrorButtonClick)="openLogsError()">
</app-nav-bar>
<main role="main" class="container">
  <div class="container" *ngIf="flowsLoaded">
    <div class="tags">
      <div *ngIf="keywordfilterTag" class="tag label label-info keyword">
        <span>{{keywordfilterTag}}</span>
        <a (click)="removeFilterKeyword()"><span class="fa fa-times"></span></a>
      </div>
      <div *ngIf="channelFilterTag" class="tag label label-info {{ getChannelClass(channelFilterTag) }}">
        <span>{{getChannelType(channelFilterTag)}}</span>
        <a (click)="removeFilterChannel()"><span class="fa fa-times"></span></a>
      </div>
      <div *ngIf="botTypeFilterTag" class="tag label label-info keyword">
        <span>{{getBotType(botTypeFilterTag)}}</span>
        <a (click)="removeFilterBotType()"><span class="fa fa-times"></span></a>
      </div>
    </div>
    <h2>{{ 'BOTS' | translate }}</h2>
    <div class="tiles">
      <div class="content development new" *ngIf="canEdit">
        <div class="new" (click)="newFlow()">
          <div class="contents new">
            <div class="add">
              <span class="fal fa-plus"></span>
            </div>
            <div class="text">
              {{ 'NEW_BOT' | translate }}
            </div>
          </div>
        </div>
      </div>
      <app-dashboard-flow *ngFor="let flow of stagingFlows" class="content development" [model]="flow"
        (onClone)="onClone($event)" (onUpload)="onUpload($event, false)" (onDownload)="onDownload($event, false)"
        (onDownloadProd)="onDownload($event, true)" (onPublish)="onPublish($event)" (onDelete)="onDelete($event, false)"
        (onselectFlow)="onselectFlow($event)">
      </app-dashboard-flow>
    </div>
    <div class="empty" *ngIf="(!stagingFlows || stagingFlows.length == 0) && !canEdit" role="alert">
      <div class="alert alert-info">
        <span class="fa fa-exclamation-circle icon"></span>
        {{ 'NO_FLOWS' | translate }}
      </div>
    </div>
  </div>
  <input type="file" hidden #fileField accept=".json">
</main>
<div class="overlay" *ngIf="loading">
  <action-spinner class="spinner"></action-spinner>
</div>