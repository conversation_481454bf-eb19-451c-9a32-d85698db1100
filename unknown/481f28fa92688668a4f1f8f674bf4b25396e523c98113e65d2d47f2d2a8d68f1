<mxfile host="Electron" agent="Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) draw.io/24.7.5 Chrome/126.0.6478.183 Electron/31.3.0 Safari/537.36" version="24.7.5" pages="3">
  <diagram name="Básico" id="834lR0Mk72IthSTk7eVe">
    <mxGraphModel dx="794" dy="511" grid="1" gridSize="10" guides="1" tooltips="1" connect="1" arrows="1" fold="1" page="1" pageScale="1" pageWidth="1169" pageHeight="827" math="0" shadow="0">
      <root>
        <mxCell id="0" />
        <mxCell id="1" parent="0" />
        <mxCell id="E8U5f5jHLggIwIybrH13-5" value="Integraciones Cliente" style="group" parent="1" vertex="1" connectable="0">
          <mxGeometry x="115" y="1038" width="214" height="290" as="geometry" />
        </mxCell>
        <mxCell id="E8U5f5jHLggIwIybrH13-3" value="" style="rounded=1;whiteSpace=wrap;html=1;dashed=1;dashPattern=1 1;fillColor=none;fontColor=#333333;strokeColor=#666666;arcSize=13;" parent="E8U5f5jHLggIwIybrH13-5" vertex="1">
          <mxGeometry width="214" height="240" as="geometry" />
        </mxCell>
        <mxCell id="E8U5f5jHLggIwIybrH13-1" value="" style="sketch=0;pointerEvents=1;shadow=0;dashed=0;html=1;strokeColor=none;fillColor=#DF8C42;labelPosition=center;verticalLabelPosition=bottom;verticalAlign=top;align=center;outlineConnect=0;shape=mxgraph.veeam2.restful_api;" parent="E8U5f5jHLggIwIybrH13-5" vertex="1">
          <mxGeometry x="27.33" y="41.89999999999998" width="50" height="54" as="geometry" />
        </mxCell>
        <mxCell id="E8U5f5jHLggIwIybrH13-2" value="" style="sketch=0;outlineConnect=0;fontColor=#232F3E;gradientColor=none;fillColor=#01A88D;strokeColor=none;dashed=0;verticalLabelPosition=bottom;verticalAlign=top;align=center;html=1;fontSize=12;fontStyle=0;aspect=fixed;shape=mxgraph.aws4.transfer_for_sftp_resource;" parent="E8U5f5jHLggIwIybrH13-5" vertex="1">
          <mxGeometry x="120" y="26.110000000000014" width="68" height="69.79" as="geometry" />
        </mxCell>
        <mxCell id="5fTfZW_KtLm9I_vtzrQ5-38" value="Identity Provider" style="shape=image;editableCssRules=.*;verticalLabelPosition=bottom;labelBackgroundColor=default;verticalAlign=top;aspect=fixed;imageAspect=0;image=data:image/svg+xml,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;" parent="E8U5f5jHLggIwIybrH13-5" vertex="1">
          <mxGeometry x="77.33" y="142" width="50" height="50" as="geometry" />
        </mxCell>
        <mxCell id="sANotFvRzP5cd9Ry8RsS-35" value="" style="rounded=1;whiteSpace=wrap;html=1;dashed=1;dashPattern=12 12;fillColor=#f5f5f5;fontColor=#333333;strokeColor=#666666;" parent="1" vertex="1">
          <mxGeometry x="390" y="110" width="380" height="490" as="geometry" />
        </mxCell>
        <mxCell id="sANotFvRzP5cd9Ry8RsS-4" value="Azure Storage&lt;div&gt;Adjuntos + Reportes&lt;/div&gt;" style="image;aspect=fixed;html=1;points=[];align=center;fontSize=12;image=img/lib/azure2/storage/Storage_Accounts_Classic.svg;" parent="1" vertex="1">
          <mxGeometry x="909.5" y="590" width="55" height="44" as="geometry" />
        </mxCell>
        <mxCell id="sANotFvRzP5cd9Ry8RsS-5" value="&lt;div&gt;SQL&lt;/div&gt;yflowDB" style="image;aspect=fixed;html=1;points=[];align=center;fontSize=12;image=img/lib/azure2/databases/SQL_Server.svg;" parent="1" vertex="1">
          <mxGeometry x="901" y="130" width="55" height="55" as="geometry" />
        </mxCell>
        <mxCell id="sANotFvRzP5cd9Ry8RsS-6" value="&lt;div&gt;MySQL&lt;/div&gt;intervalsDB" style="image;aspect=fixed;html=1;points=[];align=center;fontSize=12;image=img/lib/azure2/databases/Azure_Database_MySQL_Server.svg;" parent="1" vertex="1">
          <mxGeometry x="909.5" y="267.**************" width="38" height="50.67" as="geometry" />
        </mxCell>
        <mxCell id="sANotFvRzP5cd9Ry8RsS-7" value="Redis&lt;div&gt;Eventos de flujo + CaseContexts&lt;/div&gt;" style="image;sketch=0;aspect=fixed;html=1;points=[];align=center;fontSize=12;image=img/lib/mscae/Cache_Redis_Product.svg;" parent="1" vertex="1">
          <mxGeometry x="906" y="400" width="50" height="42" as="geometry" />
        </mxCell>
        <mxCell id="sANotFvRzP5cd9Ry8RsS-21" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;fillColor=#d5e8d4;strokeColor=#82b366;strokeWidth=2;" parent="1" source="sANotFvRzP5cd9Ry8RsS-8" target="sANotFvRzP5cd9Ry8RsS-6" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="sANotFvRzP5cd9Ry8RsS-30" style="rounded=0;orthogonalLoop=1;jettySize=auto;html=1;fillColor=#f8cecc;strokeColor=#b85450;strokeWidth=2;startArrow=none;startFill=0;endArrow=none;endFill=0;" parent="1" source="sANotFvRzP5cd9Ry8RsS-8" target="sANotFvRzP5cd9Ry8RsS-7" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="sANotFvRzP5cd9Ry8RsS-34" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;fillColor=#e1d5e7;strokeColor=#9673a6;strokeWidth=2;" parent="1" source="sANotFvRzP5cd9Ry8RsS-8" target="sANotFvRzP5cd9Ry8RsS-4" edge="1">
          <mxGeometry relative="1" as="geometry">
            <Array as="points">
              <mxPoint x="410" y="293" />
              <mxPoint x="410" y="620" />
            </Array>
          </mxGeometry>
        </mxCell>
        <mxCell id="sANotFvRzP5cd9Ry8RsS-8" value="&lt;b&gt;intervals&lt;/b&gt;" style="aspect=fixed;sketch=0;html=1;dashed=0;whitespace=wrap;verticalLabelPosition=bottom;verticalAlign=top;fillColor=#2875E2;strokeColor=#ffffff;points=[[0.005,0.63,0],[0.1,0.2,0],[0.9,0.2,0],[0.5,0,0],[0.995,0.63,0],[0.72,0.99,0],[0.5,1,0],[0.28,0.99,0]];shape=mxgraph.kubernetes.icon2;kubernetesLabel=1;prIcon=pod" parent="1" vertex="1">
          <mxGeometry x="530" y="269" width="50" height="48" as="geometry" />
        </mxCell>
        <mxCell id="sANotFvRzP5cd9Ry8RsS-25" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;exitX=0.5;exitY=1;exitDx=0;exitDy=0;exitPerimeter=0;dashed=1;" parent="1" source="sANotFvRzP5cd9Ry8RsS-9" edge="1">
          <mxGeometry relative="1" as="geometry">
            <mxPoint x="475" y="400" as="targetPoint" />
            <mxPoint x="475" y="220" as="sourcePoint" />
          </mxGeometry>
        </mxCell>
        <mxCell id="sANotFvRzP5cd9Ry8RsS-27" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;entryX=0.059;entryY=0.399;entryDx=0;entryDy=0;entryPerimeter=0;fillColor=#dae8fc;strokeColor=#6c8ebf;strokeWidth=2;startArrow=none;startFill=0;" parent="1" source="sANotFvRzP5cd9Ry8RsS-9" target="sANotFvRzP5cd9Ry8RsS-5" edge="1">
          <mxGeometry relative="1" as="geometry">
            <mxPoint x="890" y="160" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        <mxCell id="sANotFvRzP5cd9Ry8RsS-31" style="rounded=0;orthogonalLoop=1;jettySize=auto;html=1;fillColor=#f8cecc;strokeColor=#b85450;strokeWidth=2;startArrow=none;startFill=0;" parent="1" source="sANotFvRzP5cd9Ry8RsS-9" target="sANotFvRzP5cd9Ry8RsS-7" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="E8U5f5jHLggIwIybrH13-6" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;entryX=0.5;entryY=0;entryDx=0;entryDy=0;fillColor=#0050ef;strokeColor=#001DBC;strokeWidth=2;" parent="1" source="sANotFvRzP5cd9Ry8RsS-9" target="E8U5f5jHLggIwIybrH13-3" edge="1">
          <mxGeometry relative="1" as="geometry">
            <Array as="points">
              <mxPoint x="450" y="180" />
              <mxPoint x="222" y="180" />
            </Array>
          </mxGeometry>
        </mxCell>
        <mxCell id="5fTfZW_KtLm9I_vtzrQ5-37" value="https" style="rounded=0;orthogonalLoop=1;jettySize=auto;html=1;entryX=1;entryY=0.5;entryDx=0;entryDy=0;fillColor=#008a00;strokeColor=#005700;strokeWidth=2;" parent="1" source="sANotFvRzP5cd9Ry8RsS-9" target="5fTfZW_KtLm9I_vtzrQ5-28" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="sANotFvRzP5cd9Ry8RsS-9" value="&lt;b&gt;web&lt;/b&gt;" style="aspect=fixed;sketch=0;html=1;dashed=0;whitespace=wrap;verticalLabelPosition=bottom;verticalAlign=top;fillColor=#2875E2;strokeColor=#ffffff;points=[[0.005,0.63,0],[0.1,0.2,0],[0.9,0.2,0],[0.5,0,0],[0.995,0.63,0],[0.72,0.99,0],[0.5,1,0],[0.28,0.99,0]];shape=mxgraph.kubernetes.icon2;kubernetesLabel=1;prIcon=pod" parent="1" vertex="1">
          <mxGeometry x="450" y="128" width="50" height="48" as="geometry" />
        </mxCell>
        <mxCell id="sANotFvRzP5cd9Ry8RsS-14" value="" style="group" parent="1" vertex="1" connectable="0">
          <mxGeometry x="445" y="398" width="70" height="68" as="geometry" />
        </mxCell>
        <mxCell id="sANotFvRzP5cd9Ry8RsS-23" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;fillColor=#f8cecc;strokeColor=#b85450;strokeWidth=2;startArrow=none;startFill=0;" parent="sANotFvRzP5cd9Ry8RsS-14" edge="1">
          <mxGeometry relative="1" as="geometry">
            <mxPoint x="455" y="23.999999999999773" as="targetPoint" />
            <mxPoint x="65" y="24" as="sourcePoint" />
          </mxGeometry>
        </mxCell>
        <mxCell id="5fTfZW_KtLm9I_vtzrQ5-31" value="https" style="rounded=0;orthogonalLoop=1;jettySize=auto;html=1;strokeWidth=2;strokeColor=#00ffff;" parent="sANotFvRzP5cd9Ry8RsS-14" source="sANotFvRzP5cd9Ry8RsS-1" edge="1">
          <mxGeometry relative="1" as="geometry">
            <mxPoint x="-275" y="-108" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        <mxCell id="sANotFvRzP5cd9Ry8RsS-1" value="" style="aspect=fixed;sketch=0;html=1;dashed=0;whitespace=wrap;verticalLabelPosition=bottom;verticalAlign=top;fillColor=#2875E2;strokeColor=#ffffff;points=[[0.005,0.63,0],[0.1,0.2,0],[0.9,0.2,0],[0.5,0,0],[0.995,0.63,0],[0.72,0.99,0],[0.5,1,0],[0.28,0.99,0]];shape=mxgraph.kubernetes.icon2;kubernetesLabel=1;prIcon=pod" parent="sANotFvRzP5cd9Ry8RsS-14" vertex="1">
          <mxGeometry width="50" height="48" as="geometry" />
        </mxCell>
        <mxCell id="sANotFvRzP5cd9Ry8RsS-11" value="" style="aspect=fixed;sketch=0;html=1;dashed=0;whitespace=wrap;verticalLabelPosition=bottom;verticalAlign=top;fillColor=#2875E2;strokeColor=#ffffff;points=[[0.005,0.63,0],[0.1,0.2,0],[0.9,0.2,0],[0.5,0,0],[0.995,0.63,0],[0.72,0.99,0],[0.5,1,0],[0.28,0.99,0]];shape=mxgraph.kubernetes.icon2;kubernetesLabel=1;prIcon=pod" parent="sANotFvRzP5cd9Ry8RsS-14" vertex="1">
          <mxGeometry x="10" y="10" width="50" height="48" as="geometry" />
        </mxCell>
        <mxCell id="sANotFvRzP5cd9Ry8RsS-28" style="rounded=0;orthogonalLoop=1;jettySize=auto;html=1;entryX=0.08;entryY=0.998;entryDx=0;entryDy=0;entryPerimeter=0;fillColor=#dae8fc;strokeColor=#6c8ebf;strokeWidth=2;startArrow=none;startFill=0;" parent="sANotFvRzP5cd9Ry8RsS-14" target="sANotFvRzP5cd9Ry8RsS-5" edge="1">
          <mxGeometry relative="1" as="geometry">
            <mxPoint x="445" y="-228" as="targetPoint" />
            <mxPoint x="55" y="2" as="sourcePoint" />
          </mxGeometry>
        </mxCell>
        <mxCell id="sANotFvRzP5cd9Ry8RsS-12" value="&lt;b&gt;executor&lt;/b&gt;" style="aspect=fixed;sketch=0;html=1;dashed=0;whitespace=wrap;verticalLabelPosition=bottom;verticalAlign=top;fillColor=#2875E2;strokeColor=#ffffff;points=[[0.005,0.63,0],[0.1,0.2,0],[0.9,0.2,0],[0.5,0,0],[0.995,0.63,0],[0.72,0.99,0],[0.5,1,0],[0.28,0.99,0]];shape=mxgraph.kubernetes.icon2;kubernetesLabel=1;prIcon=pod" parent="sANotFvRzP5cd9Ry8RsS-14" vertex="1">
          <mxGeometry x="20" y="21" width="50" height="48" as="geometry" />
        </mxCell>
        <mxCell id="sANotFvRzP5cd9Ry8RsS-22" style="rounded=0;orthogonalLoop=1;jettySize=auto;html=1;entryX=0.037;entryY=0.666;entryDx=0;entryDy=0;entryPerimeter=0;fillColor=#dae8fc;strokeColor=#6c8ebf;strokeWidth=2;startArrow=none;startFill=0;" parent="1" source="sANotFvRzP5cd9Ry8RsS-8" target="sANotFvRzP5cd9Ry8RsS-5" edge="1">
          <mxGeometry relative="1" as="geometry">
            <mxPoint x="880" y="170" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        <mxCell id="sANotFvRzP5cd9Ry8RsS-26" style="rounded=0;orthogonalLoop=1;jettySize=auto;html=1;entryX=-0.061;entryY=0.846;entryDx=0;entryDy=0;entryPerimeter=0;fillColor=#d5e8d4;strokeColor=#82b366;strokeWidth=2;startArrow=none;startFill=0;" parent="1" target="sANotFvRzP5cd9Ry8RsS-6" edge="1">
          <mxGeometry relative="1" as="geometry">
            <mxPoint x="500" y="410" as="sourcePoint" />
          </mxGeometry>
        </mxCell>
        <mxCell id="sANotFvRzP5cd9Ry8RsS-33" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;entryX=0.988;entryY=0.58;entryDx=0;entryDy=0;entryPerimeter=0;fillColor=#e1d5e7;strokeColor=#9673a6;strokeWidth=2;startArrow=classicThin;startFill=1;endArrow=none;endFill=0;" parent="1" source="sANotFvRzP5cd9Ry8RsS-9" target="sANotFvRzP5cd9Ry8RsS-4" edge="1">
          <mxGeometry relative="1" as="geometry">
            <Array as="points">
              <mxPoint x="475" y="100" />
              <mxPoint x="1040" y="100" />
              <mxPoint x="1040" y="616" />
            </Array>
          </mxGeometry>
        </mxCell>
        <mxCell id="sANotFvRzP5cd9Ry8RsS-36" value="&lt;div&gt;Cliente&amp;nbsp;&lt;span style=&quot;background-color: initial;&quot;&gt;namespace&lt;/span&gt;&lt;/div&gt;" style="text;html=1;align=center;verticalAlign=middle;whiteSpace=wrap;rounded=0;fontStyle=1" parent="1" vertex="1">
          <mxGeometry x="479" y="570" width="210" height="30" as="geometry" />
        </mxCell>
        <mxCell id="sANotFvRzP5cd9Ry8RsS-38" value="Usuarios" style="sketch=0;outlineConnect=0;fontColor=#232F3E;gradientColor=none;fillColor=#232F3D;strokeColor=none;dashed=0;verticalLabelPosition=bottom;verticalAlign=top;align=center;html=1;fontSize=12;fontStyle=0;aspect=fixed;pointerEvents=1;shape=mxgraph.aws4.users;" parent="1" vertex="1">
          <mxGeometry x="80" y="118.84" width="77.33" height="77.33" as="geometry" />
        </mxCell>
        <mxCell id="sANotFvRzP5cd9Ry8RsS-39" value="https" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;entryX=0.005;entryY=0.63;entryDx=0;entryDy=0;entryPerimeter=0;strokeWidth=2;" parent="1" source="sANotFvRzP5cd9Ry8RsS-38" target="sANotFvRzP5cd9Ry8RsS-9" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="sANotFvRzP5cd9Ry8RsS-42" value="https" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;entryX=0.005;entryY=0.63;entryDx=0;entryDy=0;entryPerimeter=0;dashed=1;strokeWidth=2;startArrow=classicThin;startFill=1;" parent="1" source="sANotFvRzP5cd9Ry8RsS-41" target="sANotFvRzP5cd9Ry8RsS-1" edge="1">
          <mxGeometry relative="1" as="geometry">
            <mxPoint x="440" y="433.21500000000015" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        <mxCell id="sANotFvRzP5cd9Ry8RsS-41" value="" style="shape=image;verticalLabelPosition=bottom;labelBackgroundColor=default;verticalAlign=top;aspect=fixed;imageAspect=0;image=data:image/png,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;" parent="1" vertex="1">
          <mxGeometry x="100" y="394.00000000000006" width="72" height="70.43" as="geometry" />
        </mxCell>
        <mxCell id="E8U5f5jHLggIwIybrH13-4" value="&lt;b&gt;IP Pública&lt;/b&gt;&lt;div&gt;&lt;b&gt;*************&lt;/b&gt;&lt;br&gt;&lt;/div&gt;" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;fillColor=#0050ef;strokeColor=#001DBC;strokeWidth=2;" parent="1" source="sANotFvRzP5cd9Ry8RsS-12" target="E8U5f5jHLggIwIybrH13-3" edge="1">
          <mxGeometry x="0.5859" y="-2" relative="1" as="geometry">
            <mxPoint x="220" y="650" as="targetPoint" />
            <mxPoint as="offset" />
          </mxGeometry>
        </mxCell>
        <mxCell id="E8U5f5jHLggIwIybrH13-7" value="Request Metrics" style="aspect=fixed;sketch=0;html=1;dashed=1;whitespace=wrap;verticalLabelPosition=bottom;verticalAlign=top;fillColor=#2875E2;strokeColor=#ffffff;points=[[0.005,0.63,0],[0.1,0.2,0],[0.9,0.2,0],[0.5,0,0],[0.995,0.63,0],[0.72,0.99,0],[0.5,1,0],[0.28,0.99,0]];shape=mxgraph.kubernetes.icon2;kubernetesLabel=1;prIcon=hpa" parent="1" vertex="1">
          <mxGeometry x="680" y="500" width="50" height="48" as="geometry" />
        </mxCell>
        <mxCell id="E8U5f5jHLggIwIybrH13-8" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;entryX=0.995;entryY=0.63;entryDx=0;entryDy=0;entryPerimeter=0;dashed=1;" parent="1" source="E8U5f5jHLggIwIybrH13-7" target="sANotFvRzP5cd9Ry8RsS-12" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="5fTfZW_KtLm9I_vtzrQ5-1" value="" style="rounded=1;whiteSpace=wrap;html=1;dashed=1;" parent="1" vertex="1">
          <mxGeometry x="440" y="861.83" width="280" height="331.17" as="geometry" />
        </mxCell>
        <mxCell id="5fTfZW_KtLm9I_vtzrQ5-21" style="rounded=0;orthogonalLoop=1;jettySize=auto;html=1;fillColor=#f8cecc;strokeColor=#b85450;strokeWidth=2;" parent="1" source="5fTfZW_KtLm9I_vtzrQ5-2" target="5fTfZW_KtLm9I_vtzrQ5-13" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="5fTfZW_KtLm9I_vtzrQ5-24" style="rounded=0;orthogonalLoop=1;jettySize=auto;html=1;fillColor=#e1d5e7;strokeColor=#9673a6;strokeWidth=2;" parent="1" source="5fTfZW_KtLm9I_vtzrQ5-2" target="5fTfZW_KtLm9I_vtzrQ5-15" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="5fTfZW_KtLm9I_vtzrQ5-2" value="&lt;b&gt;intervals&lt;/b&gt;" style="aspect=fixed;sketch=0;html=1;dashed=0;whitespace=wrap;verticalLabelPosition=bottom;verticalAlign=top;fillColor=#2875E2;strokeColor=#ffffff;points=[[0.005,0.63,0],[0.1,0.2,0],[0.9,0.2,0],[0.5,0,0],[0.995,0.63,0],[0.72,0.99,0],[0.5,1,0],[0.28,0.99,0]];shape=mxgraph.kubernetes.icon2;kubernetesLabel=1;prIcon=pod" parent="1" vertex="1">
          <mxGeometry x="600" y="1053" width="50" height="48" as="geometry" />
        </mxCell>
        <mxCell id="5fTfZW_KtLm9I_vtzrQ5-3" value="&lt;b&gt;web&lt;/b&gt;" style="aspect=fixed;sketch=0;html=1;dashed=0;whitespace=wrap;verticalLabelPosition=bottom;verticalAlign=top;fillColor=#2875E2;strokeColor=#ffffff;points=[[0.005,0.63,0],[0.1,0.2,0],[0.9,0.2,0],[0.5,0,0],[0.995,0.63,0],[0.72,0.99,0],[0.5,1,0],[0.28,0.99,0]];shape=mxgraph.kubernetes.icon2;kubernetesLabel=1;prIcon=pod" parent="1" vertex="1">
          <mxGeometry x="476" y="881.83" width="50" height="48" as="geometry" />
        </mxCell>
        <mxCell id="5fTfZW_KtLm9I_vtzrQ5-18" style="rounded=0;orthogonalLoop=1;jettySize=auto;html=1;fillColor=#dae8fc;strokeColor=#6c8ebf;strokeWidth=2;" parent="1" source="5fTfZW_KtLm9I_vtzrQ5-4" target="5fTfZW_KtLm9I_vtzrQ5-11" edge="1">
          <mxGeometry relative="1" as="geometry">
            <mxPoint x="900" y="883" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        <mxCell id="5fTfZW_KtLm9I_vtzrQ5-26" value="https" style="rounded=0;orthogonalLoop=1;jettySize=auto;html=1;entryX=0.5;entryY=1;entryDx=0;entryDy=0;strokeWidth=2;fillColor=#eeeeee;strokeColor=#999999;startArrow=classic;startFill=1;dashed=1;dashPattern=1 1;" parent="1" source="5fTfZW_KtLm9I_vtzrQ5-4" target="sANotFvRzP5cd9Ry8RsS-41" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="5fTfZW_KtLm9I_vtzrQ5-4" value="&lt;b&gt;executor&lt;/b&gt;" style="aspect=fixed;sketch=0;html=1;dashed=0;whitespace=wrap;verticalLabelPosition=bottom;verticalAlign=top;fillColor=#2875E2;strokeColor=#ffffff;points=[[0.005,0.63,0],[0.1,0.2,0],[0.9,0.2,0],[0.5,0,0],[0.995,0.63,0],[0.72,0.99,0],[0.5,1,0],[0.28,0.99,0]];shape=mxgraph.kubernetes.icon2;kubernetesLabel=1;prIcon=pod" parent="1" vertex="1">
          <mxGeometry x="476" y="983" width="50" height="48" as="geometry" />
        </mxCell>
        <mxCell id="5fTfZW_KtLm9I_vtzrQ5-5" value="&lt;div&gt;Cliente&amp;nbsp;&lt;span style=&quot;background-color: initial;&quot;&gt;namespace-contingencia&lt;/span&gt;&lt;/div&gt;" style="text;html=1;align=center;verticalAlign=middle;whiteSpace=wrap;rounded=0;fontStyle=1" parent="1" vertex="1">
          <mxGeometry x="470" y="1153" width="210" height="30" as="geometry" />
        </mxCell>
        <mxCell id="5fTfZW_KtLm9I_vtzrQ5-10" style="rounded=0;orthogonalLoop=1;jettySize=auto;html=1;entryX=0.5;entryY=0;entryDx=0;entryDy=0;entryPerimeter=0;" parent="1" source="5fTfZW_KtLm9I_vtzrQ5-3" target="5fTfZW_KtLm9I_vtzrQ5-4" edge="1">
          <mxGeometry relative="1" as="geometry">
            <mxPoint x="501" y="961.83" as="sourcePoint" />
          </mxGeometry>
        </mxCell>
        <mxCell id="5fTfZW_KtLm9I_vtzrQ5-11" value="&lt;div&gt;SQL&lt;/div&gt;yflowDB-Contingencia" style="image;aspect=fixed;html=1;points=[];align=center;fontSize=12;image=img/lib/azure2/databases/SQL_Server.svg;" parent="1" vertex="1">
          <mxGeometry x="920" y="842" width="55" height="55" as="geometry" />
        </mxCell>
        <mxCell id="5fTfZW_KtLm9I_vtzrQ5-12" value="&lt;div&gt;MySQL&lt;/div&gt;intervalsDB-Contingencia" style="image;aspect=fixed;html=1;points=[];align=center;fontSize=12;image=img/lib/azure2/databases/Azure_Database_MySQL_Server.svg;" parent="1" vertex="1">
          <mxGeometry x="920" y="963" width="38" height="50.67" as="geometry" />
        </mxCell>
        <mxCell id="5fTfZW_KtLm9I_vtzrQ5-13" value="Redis&lt;div&gt;Eventos de flujo + CaseContexts&lt;/div&gt;" style="image;sketch=0;aspect=fixed;html=1;points=[];align=center;fontSize=12;image=img/lib/mscae/Cache_Redis_Product.svg;" parent="1" vertex="1">
          <mxGeometry x="920" y="1078" width="50" height="42" as="geometry" />
        </mxCell>
        <mxCell id="5fTfZW_KtLm9I_vtzrQ5-15" value="Azure Storage&lt;div&gt;Adjuntos + Reportes&lt;/div&gt;" style="image;aspect=fixed;html=1;points=[];align=center;fontSize=12;image=img/lib/azure2/storage/Storage_Accounts_Classic.svg;" parent="1" vertex="1">
          <mxGeometry x="911.5" y="1193" width="55" height="44" as="geometry" />
        </mxCell>
        <mxCell id="5fTfZW_KtLm9I_vtzrQ5-16" style="rounded=0;orthogonalLoop=1;jettySize=auto;html=1;entryX=-0.019;entryY=0.631;entryDx=0;entryDy=0;entryPerimeter=0;fillColor=#dae8fc;strokeColor=#6c8ebf;strokeWidth=2;startArrow=none;startFill=0;exitX=0.995;exitY=0.63;exitDx=0;exitDy=0;exitPerimeter=0;" parent="1" source="5fTfZW_KtLm9I_vtzrQ5-3" target="5fTfZW_KtLm9I_vtzrQ5-11" edge="1">
          <mxGeometry relative="1" as="geometry">
            <mxPoint x="914" y="285" as="targetPoint" />
            <mxPoint x="510" y="285" as="sourcePoint" />
          </mxGeometry>
        </mxCell>
        <mxCell id="5fTfZW_KtLm9I_vtzrQ5-17" style="rounded=0;orthogonalLoop=1;jettySize=auto;html=1;entryX=0.019;entryY=0.684;entryDx=0;entryDy=0;entryPerimeter=0;fillColor=#dae8fc;strokeColor=#6c8ebf;strokeWidth=2;" parent="1" source="5fTfZW_KtLm9I_vtzrQ5-2" target="5fTfZW_KtLm9I_vtzrQ5-11" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="5fTfZW_KtLm9I_vtzrQ5-19" style="rounded=0;orthogonalLoop=1;jettySize=auto;html=1;entryX=-0.012;entryY=0.491;entryDx=0;entryDy=0;entryPerimeter=0;fillColor=#d5e8d4;strokeColor=#82b366;strokeWidth=2;" parent="1" source="5fTfZW_KtLm9I_vtzrQ5-2" target="5fTfZW_KtLm9I_vtzrQ5-12" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="5fTfZW_KtLm9I_vtzrQ5-20" style="rounded=0;orthogonalLoop=1;jettySize=auto;html=1;entryX=-0.012;entryY=0.479;entryDx=0;entryDy=0;entryPerimeter=0;strokeWidth=2;fillColor=#d5e8d4;strokeColor=#82b366;" parent="1" source="5fTfZW_KtLm9I_vtzrQ5-4" target="5fTfZW_KtLm9I_vtzrQ5-12" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="5fTfZW_KtLm9I_vtzrQ5-22" style="rounded=0;orthogonalLoop=1;jettySize=auto;html=1;entryX=-0.021;entryY=0.417;entryDx=0;entryDy=0;entryPerimeter=0;fillColor=#f8cecc;strokeColor=#b85450;strokeWidth=2;" parent="1" source="5fTfZW_KtLm9I_vtzrQ5-4" target="5fTfZW_KtLm9I_vtzrQ5-13" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="5fTfZW_KtLm9I_vtzrQ5-23" style="rounded=0;orthogonalLoop=1;jettySize=auto;html=1;entryX=0.049;entryY=0.487;entryDx=0;entryDy=0;entryPerimeter=0;strokeWidth=2;fillColor=#f8cecc;strokeColor=#b85450;" parent="1" source="5fTfZW_KtLm9I_vtzrQ5-3" target="5fTfZW_KtLm9I_vtzrQ5-13" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="5fTfZW_KtLm9I_vtzrQ5-25" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;entryX=0.5;entryY=0;entryDx=0;entryDy=0;entryPerimeter=0;strokeWidth=2;fillColor=#e1d5e7;strokeColor=#9673a6;" parent="1" source="5fTfZW_KtLm9I_vtzrQ5-15" target="5fTfZW_KtLm9I_vtzrQ5-3" edge="1">
          <mxGeometry relative="1" as="geometry">
            <Array as="points">
              <mxPoint x="1040" y="1215" />
              <mxPoint x="1040" y="823" />
              <mxPoint x="501" y="823" />
            </Array>
          </mxGeometry>
        </mxCell>
        <mxCell id="5fTfZW_KtLm9I_vtzrQ5-27" value="https" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;entryX=0.005;entryY=0.63;entryDx=0;entryDy=0;entryPerimeter=0;strokeColor=#999999;dashed=1;dashPattern=1 1;strokeWidth=2;" parent="1" edge="1">
          <mxGeometry relative="1" as="geometry">
            <mxPoint x="80" y="160.42857142857144" as="sourcePoint" />
            <mxPoint x="476.25" y="915.0699999999997" as="targetPoint" />
            <Array as="points">
              <mxPoint x="70" y="160" />
              <mxPoint x="70" y="909" />
              <mxPoint x="476" y="909" />
            </Array>
          </mxGeometry>
        </mxCell>
        <mxCell id="5fTfZW_KtLm9I_vtzrQ5-29" value="&lt;div&gt;App Services&lt;/div&gt;ysmart" style="image;sketch=0;aspect=fixed;html=1;points=[];align=center;fontSize=12;image=img/lib/mscae/App_Services.svg;" parent="1" vertex="1">
          <mxGeometry x="111" y="250" width="50" height="50" as="geometry" />
        </mxCell>
        <mxCell id="5fTfZW_KtLm9I_vtzrQ5-30" value="https" style="rounded=0;orthogonalLoop=1;jettySize=auto;html=1;entryX=1.014;entryY=0.637;entryDx=0;entryDy=0;entryPerimeter=0;fillColor=#ffcccc;strokeColor=#00FFFF;strokeWidth=2;" parent="1" source="sANotFvRzP5cd9Ry8RsS-9" target="5fTfZW_KtLm9I_vtzrQ5-29" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="5fTfZW_KtLm9I_vtzrQ5-36" value="" style="group" parent="1" vertex="1" connectable="0">
          <mxGeometry x="230" y="20" width="114" height="90" as="geometry" />
        </mxCell>
        <mxCell id="5fTfZW_KtLm9I_vtzrQ5-28" value="" style="rounded=1;whiteSpace=wrap;html=1;dashed=1;" parent="5fTfZW_KtLm9I_vtzrQ5-36" vertex="1">
          <mxGeometry width="114" height="86" as="geometry" />
        </mxCell>
        <mxCell id="5fTfZW_KtLm9I_vtzrQ5-34" value="" style="group" parent="5fTfZW_KtLm9I_vtzrQ5-36" vertex="1" connectable="0">
          <mxGeometry x="16.5" y="12" width="79" height="78" as="geometry" />
        </mxCell>
        <mxCell id="5fTfZW_KtLm9I_vtzrQ5-32" value="" style="aspect=fixed;sketch=0;html=1;dashed=0;whitespace=wrap;verticalLabelPosition=bottom;verticalAlign=top;fillColor=#2875E2;strokeColor=#ffffff;points=[[0.005,0.63,0],[0.1,0.2,0],[0.9,0.2,0],[0.5,0,0],[0.995,0.63,0],[0.72,0.99,0],[0.5,1,0],[0.28,0.99,0]];shape=mxgraph.kubernetes.icon2;kubernetesLabel=1;prIcon=pod" parent="5fTfZW_KtLm9I_vtzrQ5-34" vertex="1">
          <mxGeometry x="14.5" width="50" height="48" as="geometry" />
        </mxCell>
        <mxCell id="5fTfZW_KtLm9I_vtzrQ5-33" value="&lt;div&gt;Keycloak&lt;/div&gt;" style="text;html=1;align=center;verticalAlign=middle;whiteSpace=wrap;rounded=0;fontStyle=1" parent="5fTfZW_KtLm9I_vtzrQ5-34" vertex="1">
          <mxGeometry y="48" width="79" height="30" as="geometry" />
        </mxCell>
        <mxCell id="5fTfZW_KtLm9I_vtzrQ5-39" value="saml" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;entryX=0;entryY=0.5;entryDx=0;entryDy=0;fillColor=#008a00;strokeColor=#005700;strokeWidth=2;" parent="1" source="5fTfZW_KtLm9I_vtzrQ5-28" target="5fTfZW_KtLm9I_vtzrQ5-38" edge="1">
          <mxGeometry relative="1" as="geometry">
            <Array as="points">
              <mxPoint x="40" y="63" />
              <mxPoint x="40" y="1205" />
            </Array>
          </mxGeometry>
        </mxCell>
        <mxCell id="5fTfZW_KtLm9I_vtzrQ5-44" value="" style="group" parent="1" vertex="1" connectable="0">
          <mxGeometry x="590" y="650" width="220" height="160" as="geometry" />
        </mxCell>
        <mxCell id="5fTfZW_KtLm9I_vtzrQ5-46" value="" style="rounded=1;whiteSpace=wrap;html=1;" parent="5fTfZW_KtLm9I_vtzrQ5-44" vertex="1">
          <mxGeometry x="-210" y="-10" width="430" height="170" as="geometry" />
        </mxCell>
        <mxCell id="5fTfZW_KtLm9I_vtzrQ5-7" value="" style="rounded=1;whiteSpace=wrap;html=1;" parent="5fTfZW_KtLm9I_vtzrQ5-44" vertex="1">
          <mxGeometry width="120" height="140" as="geometry" />
        </mxCell>
        <mxCell id="5fTfZW_KtLm9I_vtzrQ5-40" value="" style="aspect=fixed;sketch=0;html=1;dashed=0;whitespace=wrap;verticalLabelPosition=bottom;verticalAlign=top;fillColor=#2875E2;strokeColor=#ffffff;points=[[0.005,0.63,0],[0.1,0.2,0],[0.9,0.2,0],[0.5,0,0],[0.995,0.63,0],[0.72,0.99,0],[0.5,1,0],[0.28,0.99,0]];shape=mxgraph.kubernetes.icon2;kubernetesLabel=1;prIcon=pod" parent="5fTfZW_KtLm9I_vtzrQ5-44" vertex="1">
          <mxGeometry x="22" y="20" width="50" height="48" as="geometry" />
        </mxCell>
        <mxCell id="5fTfZW_KtLm9I_vtzrQ5-41" value="" style="aspect=fixed;sketch=0;html=1;dashed=0;whitespace=wrap;verticalLabelPosition=bottom;verticalAlign=top;fillColor=#2875E2;strokeColor=#ffffff;points=[[0.005,0.63,0],[0.1,0.2,0],[0.9,0.2,0],[0.5,0,0],[0.995,0.63,0],[0.72,0.99,0],[0.5,1,0],[0.28,0.99,0]];shape=mxgraph.kubernetes.icon2;kubernetesLabel=1;prIcon=pod" parent="5fTfZW_KtLm9I_vtzrQ5-44" vertex="1">
          <mxGeometry x="32" y="30" width="50" height="48" as="geometry" />
        </mxCell>
        <mxCell id="5fTfZW_KtLm9I_vtzrQ5-42" value="" style="aspect=fixed;sketch=0;html=1;dashed=0;whitespace=wrap;verticalLabelPosition=bottom;verticalAlign=top;fillColor=#2875E2;strokeColor=#ffffff;points=[[0.005,0.63,0],[0.1,0.2,0],[0.9,0.2,0],[0.5,0,0],[0.995,0.63,0],[0.72,0.99,0],[0.5,1,0],[0.28,0.99,0]];shape=mxgraph.kubernetes.icon2;kubernetesLabel=1;prIcon=pod" parent="5fTfZW_KtLm9I_vtzrQ5-44" vertex="1">
          <mxGeometry x="42" y="40" width="50" height="48" as="geometry" />
        </mxCell>
        <mxCell id="5fTfZW_KtLm9I_vtzrQ5-43" value="&lt;div&gt;yourls&lt;/div&gt;&lt;div&gt;(acortador url)&lt;/div&gt;" style="text;html=1;align=center;verticalAlign=middle;whiteSpace=wrap;rounded=0;fontStyle=1" parent="5fTfZW_KtLm9I_vtzrQ5-44" vertex="1">
          <mxGeometry x="12.5" y="100" width="95" height="30" as="geometry" />
        </mxCell>
        <mxCell id="5fTfZW_KtLm9I_vtzrQ5-60" value="" style="group" parent="1" vertex="1" connectable="0">
          <mxGeometry x="447" y="650" width="120" height="140" as="geometry" />
        </mxCell>
        <mxCell id="5fTfZW_KtLm9I_vtzrQ5-61" value="" style="rounded=1;whiteSpace=wrap;html=1;" parent="5fTfZW_KtLm9I_vtzrQ5-60" vertex="1">
          <mxGeometry width="120" height="140" as="geometry" />
        </mxCell>
        <mxCell id="5fTfZW_KtLm9I_vtzrQ5-62" value="" style="aspect=fixed;sketch=0;html=1;dashed=0;whitespace=wrap;verticalLabelPosition=bottom;verticalAlign=top;fillColor=#2875E2;strokeColor=#ffffff;points=[[0.005,0.63,0],[0.1,0.2,0],[0.9,0.2,0],[0.5,0,0],[0.995,0.63,0],[0.72,0.99,0],[0.5,1,0],[0.28,0.99,0]];shape=mxgraph.kubernetes.icon2;kubernetesLabel=1;prIcon=pod" parent="5fTfZW_KtLm9I_vtzrQ5-60" vertex="1">
          <mxGeometry x="22" y="20" width="50" height="48" as="geometry" />
        </mxCell>
        <mxCell id="5fTfZW_KtLm9I_vtzrQ5-63" value="" style="aspect=fixed;sketch=0;html=1;dashed=0;whitespace=wrap;verticalLabelPosition=bottom;verticalAlign=top;fillColor=#2875E2;strokeColor=#ffffff;points=[[0.005,0.63,0],[0.1,0.2,0],[0.9,0.2,0],[0.5,0,0],[0.995,0.63,0],[0.72,0.99,0],[0.5,1,0],[0.28,0.99,0]];shape=mxgraph.kubernetes.icon2;kubernetesLabel=1;prIcon=pod" parent="5fTfZW_KtLm9I_vtzrQ5-60" vertex="1">
          <mxGeometry x="32" y="30" width="50" height="48" as="geometry" />
        </mxCell>
        <mxCell id="5fTfZW_KtLm9I_vtzrQ5-64" value="" style="aspect=fixed;sketch=0;html=1;dashed=0;whitespace=wrap;verticalLabelPosition=bottom;verticalAlign=top;fillColor=#2875E2;strokeColor=#ffffff;points=[[0.005,0.63,0],[0.1,0.2,0],[0.9,0.2,0],[0.5,0,0],[0.995,0.63,0],[0.72,0.99,0],[0.5,1,0],[0.28,0.99,0]];shape=mxgraph.kubernetes.icon2;kubernetesLabel=1;prIcon=pod" parent="5fTfZW_KtLm9I_vtzrQ5-60" vertex="1">
          <mxGeometry x="42" y="40" width="50" height="48" as="geometry" />
        </mxCell>
        <mxCell id="5fTfZW_KtLm9I_vtzrQ5-65" value="&lt;div&gt;DNI Validator&lt;/div&gt;" style="text;html=1;align=center;verticalAlign=middle;whiteSpace=wrap;rounded=0;fontStyle=1" parent="5fTfZW_KtLm9I_vtzrQ5-60" vertex="1">
          <mxGeometry x="12.5" y="100" width="95" height="30" as="geometry" />
        </mxCell>
        <mxCell id="5fTfZW_KtLm9I_vtzrQ5-66" value="https" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;entryX=0;entryY=0.5;entryDx=0;entryDy=0;fillColor=#1ba1e2;strokeColor=#006EAF;strokeWidth=2;" parent="1" source="sANotFvRzP5cd9Ry8RsS-12" target="5fTfZW_KtLm9I_vtzrQ5-46" edge="1">
          <mxGeometry relative="1" as="geometry">
            <Array as="points">
              <mxPoint x="490" y="470" />
              <mxPoint x="360" y="470" />
              <mxPoint x="360" y="725" />
            </Array>
          </mxGeometry>
        </mxCell>
        <mxCell id="Uu1ZU-vipX-tH2mmgno4-1" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;entryX=0.75;entryY=0;entryDx=0;entryDy=0;fillColor=#0050ef;strokeColor=#001DBC;strokeWidth=2;" edge="1" parent="1" source="sANotFvRzP5cd9Ry8RsS-12" target="E8U5f5jHLggIwIybrH13-3">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
      </root>
    </mxGraphModel>
  </diagram>
  <diagram id="RdYVijUtldbmCbVGZt2t" name="Migración">
    <mxGraphModel dx="1222" dy="786" grid="1" gridSize="10" guides="1" tooltips="1" connect="1" arrows="1" fold="1" page="1" pageScale="1" pageWidth="1169" pageHeight="827" math="0" shadow="0">
      <root>
        <mxCell id="0" />
        <mxCell id="1" parent="0" />
        <mxCell id="V_O7RqRg4uyJ0K3OQh4G-4" value="Soporte" style="swimlane;horizontal=0;whiteSpace=wrap;html=1;fillColor=#dae8fc;strokeColor=#6c8ebf;" vertex="1" parent="1">
          <mxGeometry x="40" y="61" width="1120" height="240" as="geometry" />
        </mxCell>
        <mxCell id="V_O7RqRg4uyJ0K3OQh4G-5" value="" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;" edge="1" parent="V_O7RqRg4uyJ0K3OQh4G-4" target="V_O7RqRg4uyJ0K3OQh4G-7">
          <mxGeometry relative="1" as="geometry">
            <mxPoint x="123" y="80" as="sourcePoint" />
          </mxGeometry>
        </mxCell>
        <mxCell id="V_O7RqRg4uyJ0K3OQh4G-7" value="Solicitar alta K8s" style="whiteSpace=wrap;html=1;rounded=1;" vertex="1" parent="V_O7RqRg4uyJ0K3OQh4G-4">
          <mxGeometry x="63" y="154" width="120" height="60" as="geometry" />
        </mxCell>
        <mxCell id="V_O7RqRg4uyJ0K3OQh4G-8" value="&amp;lt;" style="swimlane;horizontal=0;whiteSpace=wrap;html=1;align=center;fillColor=#f5f5f5;fontColor=#333333;strokeColor=#666666;" vertex="1" parent="1">
          <mxGeometry x="40" y="301" width="1120" height="160" as="geometry" />
        </mxCell>
        <mxCell id="V_O7RqRg4uyJ0K3OQh4G-10" value="SLA: 3 días" style="rounded=0;orthogonalLoop=1;jettySize=auto;html=1;entryX=0.382;entryY=0.008;entryDx=0;entryDy=0;entryPerimeter=0;" edge="1" parent="1" source="V_O7RqRg4uyJ0K3OQh4G-7" target="V_O7RqRg4uyJ0K3OQh4G-9">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="V_O7RqRg4uyJ0K3OQh4G-14" value="Pre-Implementación v9.1 o superior" style="swimlane;whiteSpace=wrap;html=1;fillColor=#fff2cc;strokeColor=#d6b656;" vertex="1" parent="1">
          <mxGeometry x="63" y="41" width="377" height="420" as="geometry" />
        </mxCell>
        <mxCell id="V_O7RqRg4uyJ0K3OQh4G-12" value="- Ejecutar script intervalos DB&lt;div&gt;- Completar Variable Group&lt;/div&gt;" style="whiteSpace=wrap;html=1;align=left;rounded=1;" vertex="1" parent="V_O7RqRg4uyJ0K3OQh4G-14">
          <mxGeometry x="187" y="40" width="170" height="60" as="geometry" />
        </mxCell>
        <mxCell id="V_O7RqRg4uyJ0K3OQh4G-9" value="- Crear DB Intervals&amp;nbsp;&lt;div&gt;- Crear Variable Group de Azure DevOps&lt;/div&gt;&lt;div&gt;- Crear DNS (web +executor)&lt;/div&gt;&lt;div&gt;- Alta DNS Web en Google&lt;/div&gt;&lt;div&gt;- Alta de monitores&lt;/div&gt;" style="whiteSpace=wrap;html=1;rounded=1;align=left;" vertex="1" parent="V_O7RqRg4uyJ0K3OQh4G-14">
          <mxGeometry x="13" y="327" width="227" height="83" as="geometry" />
        </mxCell>
        <mxCell id="V_O7RqRg4uyJ0K3OQh4G-13" value="" style="rounded=0;orthogonalLoop=1;jettySize=auto;html=1;entryX=0.5;entryY=1;entryDx=0;entryDy=0;exitX=0.75;exitY=0;exitDx=0;exitDy=0;" edge="1" parent="V_O7RqRg4uyJ0K3OQh4G-14" source="V_O7RqRg4uyJ0K3OQh4G-9" target="V_O7RqRg4uyJ0K3OQh4G-12">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="V_O7RqRg4uyJ0K3OQh4G-15" value="Coordinar ventana&lt;div&gt;migración&amp;nbsp;&lt;/div&gt;&lt;div&gt;(nueva IP pública)&lt;/div&gt;" style="rounded=1;whiteSpace=wrap;html=1;" vertex="1" parent="1">
          <mxGeometry x="103" y="81" width="120" height="60" as="geometry" />
        </mxCell>
        <mxCell id="hNhSMg06BQchYeBZpVvr-1" value="Implementación" style="swimlane;whiteSpace=wrap;html=1;fillColor=#ffe6cc;strokeColor=#d79b00;" vertex="1" parent="1">
          <mxGeometry x="440" y="41" width="300" height="420" as="geometry" />
        </mxCell>
        <mxCell id="yNYZIpjw9lbDRPOIWvDi-5" value="" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;" edge="1" parent="hNhSMg06BQchYeBZpVvr-1" source="yNYZIpjw9lbDRPOIWvDi-3" target="yNYZIpjw9lbDRPOIWvDi-4">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="yNYZIpjw9lbDRPOIWvDi-3" value="- BKP DB + Instancia&lt;div&gt;&lt;div style=&quot;&quot;&gt;&lt;span style=&quot;background-color: initial;&quot;&gt;- Detener ySocial + yFlow&lt;/span&gt;&lt;/div&gt;&lt;div&gt;- Upgrade yFlow SQL DB&lt;/div&gt;&lt;div&gt;- Ejecutar proceso de migración de flujos&lt;/div&gt;&lt;/div&gt;&lt;div&gt;- Ejecutar proceso de migración de contexto&lt;/div&gt;" style="rounded=1;whiteSpace=wrap;html=1;align=left;" vertex="1" parent="hNhSMg06BQchYeBZpVvr-1">
          <mxGeometry x="25" y="40" width="240" height="90" as="geometry" />
        </mxCell>
        <mxCell id="yNYZIpjw9lbDRPOIWvDi-4" value="Ejecutar pipeline de CD para el Cliente" style="whiteSpace=wrap;html=1;align=center;rounded=1;" vertex="1" parent="hNhSMg06BQchYeBZpVvr-1">
          <mxGeometry x="32" y="340" width="225" height="40" as="geometry" />
        </mxCell>
        <mxCell id="yNYZIpjw9lbDRPOIWvDi-6" value="Post Implementación" style="swimlane;whiteSpace=wrap;html=1;fillColor=#d5e8d4;strokeColor=#82b366;" vertex="1" parent="1">
          <mxGeometry x="740" y="41" width="410" height="420" as="geometry" />
        </mxCell>
        <mxCell id="yNYZIpjw9lbDRPOIWvDi-7" value="" style="rounded=0;orthogonalLoop=1;jettySize=auto;html=1;exitX=0.5;exitY=1;exitDx=0;exitDy=0;" edge="1" parent="yNYZIpjw9lbDRPOIWvDi-6" source="yNYZIpjw9lbDRPOIWvDi-8">
          <mxGeometry relative="1" as="geometry">
            <mxPoint x="216" y="339" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        <mxCell id="yNYZIpjw9lbDRPOIWvDi-8" value="&lt;div style=&quot;&quot;&gt;&lt;span style=&quot;background-color: initial;&quot;&gt;Upgrade yFlow DBs&lt;/span&gt;&lt;/div&gt;" style="rounded=1;whiteSpace=wrap;html=1;align=center;" vertex="1" parent="yNYZIpjw9lbDRPOIWvDi-6">
          <mxGeometry x="140" y="196" width="150" height="38" as="geometry" />
        </mxCell>
        <mxCell id="yNYZIpjw9lbDRPOIWvDi-9" value="Ejecutar pipeline de CD para el Cliente" style="whiteSpace=wrap;html=1;align=center;rounded=1;" vertex="1" parent="yNYZIpjw9lbDRPOIWvDi-6">
          <mxGeometry x="131.5" y="340" width="225" height="40" as="geometry" />
        </mxCell>
        <mxCell id="yNYZIpjw9lbDRPOIWvDi-10" value="&lt;div style=&quot;&quot;&gt;&lt;span style=&quot;background-color: initial;&quot;&gt;- Visualización de logs en Loki&lt;/span&gt;&lt;/div&gt;&lt;div style=&quot;&quot;&gt;&lt;span style=&quot;background-color: initial;&quot;&gt;- Visualización de consumo Grafana&lt;/span&gt;&lt;/div&gt;" style="rounded=1;whiteSpace=wrap;html=1;align=left;" vertex="1" parent="yNYZIpjw9lbDRPOIWvDi-6">
          <mxGeometry x="20.75" y="40" width="202" height="60" as="geometry" />
        </mxCell>
        <mxCell id="yNYZIpjw9lbDRPOIWvDi-16" value="Si" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;" edge="1" parent="yNYZIpjw9lbDRPOIWvDi-6" source="yNYZIpjw9lbDRPOIWvDi-14" target="yNYZIpjw9lbDRPOIWvDi-15">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="yNYZIpjw9lbDRPOIWvDi-14" value="cambios en DB" style="rhombus;whiteSpace=wrap;html=1;" vertex="1" parent="yNYZIpjw9lbDRPOIWvDi-6">
          <mxGeometry x="20.75" y="112" width="127" height="80" as="geometry" />
        </mxCell>
        <mxCell id="yNYZIpjw9lbDRPOIWvDi-15" value="Detener pods" style="rounded=1;whiteSpace=wrap;html=1;" vertex="1" parent="yNYZIpjw9lbDRPOIWvDi-6">
          <mxGeometry x="24.25" y="284" width="120" height="36" as="geometry" />
        </mxCell>
        <mxCell id="yNYZIpjw9lbDRPOIWvDi-17" style="rounded=0;orthogonalLoop=1;jettySize=auto;html=1;entryX=0;entryY=1;entryDx=0;entryDy=0;exitX=0.75;exitY=0;exitDx=0;exitDy=0;" edge="1" parent="yNYZIpjw9lbDRPOIWvDi-6" source="yNYZIpjw9lbDRPOIWvDi-15" target="yNYZIpjw9lbDRPOIWvDi-8">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="2AlmBcd5_f4Vhdr6EyWk-6" value="Si" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;entryX=0.797;entryY=0.058;entryDx=0;entryDy=0;entryPerimeter=0;exitX=0.5;exitY=1;exitDx=0;exitDy=0;" edge="1" parent="yNYZIpjw9lbDRPOIWvDi-6" source="2AlmBcd5_f4Vhdr6EyWk-7" target="yNYZIpjw9lbDRPOIWvDi-9">
          <mxGeometry x="-0.5494" y="-1" relative="1" as="geometry">
            <mxPoint x="298.9999999999998" y="173" as="sourcePoint" />
            <mxPoint as="offset" />
          </mxGeometry>
        </mxCell>
        <mxCell id="2AlmBcd5_f4Vhdr6EyWk-7" value="&lt;span style=&quot;text-align: left;&quot;&gt;Modificar Variable Group&lt;/span&gt;" style="rhombus;whiteSpace=wrap;html=1;" vertex="1" parent="yNYZIpjw9lbDRPOIWvDi-6">
          <mxGeometry x="231.75" y="108" width="158.25" height="88" as="geometry" />
        </mxCell>
        <mxCell id="nlpWrtG1j-ynDPjYGOsy-1" value="Soporte" style="swimlane;horizontal=0;whiteSpace=wrap;html=1;fillColor=#dae8fc;strokeColor=#6c8ebf;" vertex="1" parent="1">
          <mxGeometry x="40" y="500" width="1120" height="150" as="geometry" />
        </mxCell>
        <mxCell id="nlpWrtG1j-ynDPjYGOsy-4" value="Rollback" style="swimlane;whiteSpace=wrap;html=1;fillColor=#f8cecc;strokeColor=#b85450;" vertex="1" parent="1">
          <mxGeometry x="63" y="473" width="1097" height="330" as="geometry" />
        </mxCell>
        <mxCell id="nlpWrtG1j-ynDPjYGOsy-8" value="Infra" style="swimlane;horizontal=0;whiteSpace=wrap;html=1;fillColor=#f5f5f5;strokeColor=#666666;fontColor=#333333;align=center;" vertex="1" parent="nlpWrtG1j-ynDPjYGOsy-4">
          <mxGeometry x="-23" y="178" width="1120" height="152" as="geometry" />
        </mxCell>
        <mxCell id="nlpWrtG1j-ynDPjYGOsy-16" value="" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;" edge="1" parent="nlpWrtG1j-ynDPjYGOsy-4" source="nlpWrtG1j-ynDPjYGOsy-10" target="nlpWrtG1j-ynDPjYGOsy-15">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="nlpWrtG1j-ynDPjYGOsy-13" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;entryX=0.5;entryY=0;entryDx=0;entryDy=0;" edge="1" parent="nlpWrtG1j-ynDPjYGOsy-4" source="nlpWrtG1j-ynDPjYGOsy-11" target="nlpWrtG1j-ynDPjYGOsy-12">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="nlpWrtG1j-ynDPjYGOsy-14" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;entryX=0;entryY=0.5;entryDx=0;entryDy=0;" edge="1" parent="nlpWrtG1j-ynDPjYGOsy-4" source="nlpWrtG1j-ynDPjYGOsy-11" target="nlpWrtG1j-ynDPjYGOsy-10">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="nlpWrtG1j-ynDPjYGOsy-15" value="Reapuntar DNS de web y executor a los puertos de la VM" style="whiteSpace=wrap;html=1;align=center;rounded=1;" vertex="1" parent="nlpWrtG1j-ynDPjYGOsy-4">
          <mxGeometry x="502" y="226" width="323" height="41" as="geometry" />
        </mxCell>
        <mxCell id="nlpWrtG1j-ynDPjYGOsy-12" value="Detener yFlow del Cliente" style="rounded=1;whiteSpace=wrap;html=1;" vertex="1" parent="nlpWrtG1j-ynDPjYGOsy-4">
          <mxGeometry x="247" y="230" width="173" height="33" as="geometry" />
        </mxCell>
        <mxCell id="nlpWrtG1j-ynDPjYGOsy-11" value="Solicitar Rollback a VM" style="rounded=1;whiteSpace=wrap;html=1;" vertex="1" parent="nlpWrtG1j-ynDPjYGOsy-4">
          <mxGeometry x="247" y="60.5" width="173" height="33" as="geometry" />
        </mxCell>
        <mxCell id="nlpWrtG1j-ynDPjYGOsy-10" value="- Instalar v9.1 o superior (versión VM) en path de la VM del Cliente&lt;div&gt;&lt;span style=&quot;background-color: initial;&quot;&gt;- Utilizar valores de Variable Group de Azure DevOps del Cliente&lt;/span&gt;&lt;/div&gt;" style="rounded=1;whiteSpace=wrap;html=1;align=left;" vertex="1" parent="nlpWrtG1j-ynDPjYGOsy-4">
          <mxGeometry x="477" y="44" width="373" height="66" as="geometry" />
        </mxCell>
      </root>
    </mxGraphModel>
  </diagram>
  <diagram name="Nuevo Cliente" id="lpt2VfNnOSbxgcUbuG76">
    <mxGraphModel dx="1134" dy="730" grid="1" gridSize="10" guides="1" tooltips="1" connect="1" arrows="1" fold="1" page="1" pageScale="1" pageWidth="1169" pageHeight="827" math="0" shadow="0">
      <root>
        <mxCell id="4F4YO4FFkAbSKTLpthfL-0" />
        <mxCell id="4F4YO4FFkAbSKTLpthfL-1" parent="4F4YO4FFkAbSKTLpthfL-0" />
        <mxCell id="4F4YO4FFkAbSKTLpthfL-2" value="Chatboot Trainer" style="swimlane;horizontal=0;whiteSpace=wrap;html=1;fillColor=#dae8fc;strokeColor=#6c8ebf;" vertex="1" parent="4F4YO4FFkAbSKTLpthfL-1">
          <mxGeometry x="40" y="120" width="860" height="240" as="geometry" />
        </mxCell>
        <mxCell id="4F4YO4FFkAbSKTLpthfL-5" value="Infra" style="swimlane;horizontal=0;whiteSpace=wrap;html=1;align=center;fillColor=#f5f5f5;fontColor=#333333;strokeColor=#666666;" vertex="1" parent="4F4YO4FFkAbSKTLpthfL-1">
          <mxGeometry x="40" y="360" width="860" height="200" as="geometry" />
        </mxCell>
        <mxCell id="4F4YO4FFkAbSKTLpthfL-9" value="Implementación 9.1 o superior" style="swimlane;whiteSpace=wrap;html=1;fillColor=#fff2cc;strokeColor=#d6b656;" vertex="1" parent="4F4YO4FFkAbSKTLpthfL-1">
          <mxGeometry x="63" y="100" width="537" height="460" as="geometry" />
        </mxCell>
        <mxCell id="3Qu7bDnvh18kGfuOOvC5-0" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;entryX=0.5;entryY=0;entryDx=0;entryDy=0;" edge="1" parent="4F4YO4FFkAbSKTLpthfL-9" source="4F4YO4FFkAbSKTLpthfL-11" target="4F4YO4FFkAbSKTLpthfL-17">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="4F4YO4FFkAbSKTLpthfL-11" value="- Ejecutar scripts DB&lt;div&gt;- Completar Variable Group en Azure DevOps&lt;/div&gt;" style="whiteSpace=wrap;html=1;align=left;rounded=1;" vertex="1" parent="4F4YO4FFkAbSKTLpthfL-9">
          <mxGeometry x="270" y="40" width="260" height="60" as="geometry" />
        </mxCell>
        <mxCell id="4F4YO4FFkAbSKTLpthfL-4" value="Solicitar alta K8s" style="whiteSpace=wrap;html=1;rounded=1;" vertex="1" parent="4F4YO4FFkAbSKTLpthfL-9">
          <mxGeometry x="38" y="40" width="120" height="60" as="geometry" />
        </mxCell>
        <mxCell id="4F4YO4FFkAbSKTLpthfL-6" value="- Crear DB Intervals&amp;nbsp;&lt;div&gt;- Asignar VM DB SQL&lt;br&gt;&lt;div&gt;- Crear Variable Group en Azure DevOps&lt;/div&gt;&lt;div&gt;- Crear DNS (web +executor)&lt;/div&gt;&lt;div&gt;- Alta DNS Web en Google&lt;/div&gt;&lt;div&gt;- Alta de monitores&lt;/div&gt;&lt;/div&gt;" style="whiteSpace=wrap;html=1;rounded=1;align=left;" vertex="1" parent="4F4YO4FFkAbSKTLpthfL-9">
          <mxGeometry x="13" y="320" width="247" height="120" as="geometry" />
        </mxCell>
        <mxCell id="4F4YO4FFkAbSKTLpthfL-7" value="SLA: 3 días" style="rounded=0;orthogonalLoop=1;jettySize=auto;html=1;entryX=0.5;entryY=0;entryDx=0;entryDy=0;" edge="1" parent="4F4YO4FFkAbSKTLpthfL-9" source="4F4YO4FFkAbSKTLpthfL-4" target="4F4YO4FFkAbSKTLpthfL-6">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="4F4YO4FFkAbSKTLpthfL-8" value="" style="rounded=0;orthogonalLoop=1;jettySize=auto;html=1;entryX=0;entryY=1;entryDx=0;entryDy=0;exitX=0.75;exitY=0;exitDx=0;exitDy=0;" edge="1" parent="4F4YO4FFkAbSKTLpthfL-9" source="4F4YO4FFkAbSKTLpthfL-6" target="4F4YO4FFkAbSKTLpthfL-11">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="4F4YO4FFkAbSKTLpthfL-17" value="Ejecutar pipeline de CD para el Cliente" style="whiteSpace=wrap;html=1;align=center;rounded=1;" vertex="1" parent="4F4YO4FFkAbSKTLpthfL-9">
          <mxGeometry x="287.5" y="360" width="225" height="40" as="geometry" />
        </mxCell>
        <mxCell id="4F4YO4FFkAbSKTLpthfL-18" value="Post Implementación" style="swimlane;whiteSpace=wrap;html=1;fillColor=#d5e8d4;strokeColor=#82b366;" vertex="1" parent="4F4YO4FFkAbSKTLpthfL-1">
          <mxGeometry x="600" y="100" width="280" height="460" as="geometry" />
        </mxCell>
        <mxCell id="4F4YO4FFkAbSKTLpthfL-22" value="&lt;div style=&quot;&quot;&gt;&lt;span style=&quot;background-color: initial;&quot;&gt;- Visualización de logs en Loki&lt;/span&gt;&lt;/div&gt;&lt;div style=&quot;&quot;&gt;&lt;span style=&quot;background-color: initial;&quot;&gt;- Visualización de consumo Grafana&lt;/span&gt;&lt;/div&gt;" style="rounded=1;whiteSpace=wrap;html=1;align=left;" vertex="1" parent="4F4YO4FFkAbSKTLpthfL-18">
          <mxGeometry x="40" y="40" width="202" height="60" as="geometry" />
        </mxCell>
      </root>
    </mxGraphModel>
  </diagram>
</mxfile>
