import {Component, ViewChild, Inject, OnInit, AfterViewInit, HostListener, ElementRef} from '@angular/core';
import { Router, ActivatedRoute } from '@angular/router';
import { SignaturePad } from 'angular2-signaturepad';
import { ServerService } from '../../services/ServerService';
import { finalize } from "rxjs/operators";
import { StatusResponse } from '../../models/StatusResponse';
import * as CryptoJS from 'crypto-js'
import { Title } from '@angular/platform-browser';
import { DOCUMENT } from '@angular/common';
import {ToasterService} from 'angular5-toaster/dist';

@Component({
  selector: 'app-account-test.component',
  templateUrl: './account-test.component.html',
  styleUrls: ['./account-test.component.scss'],
})
export class AccountTestComponent implements OnInit {
  constructor(private route: ActivatedRoute,
              private serverService: ServerService,
              private toasterService: ToasterService) {

  }

  Result : string;

  ngOnInit() {
    this.route.queryParams
      .subscribe(params => {
        this.serverService.accountTest(params.redirect_uri,params.token)
          .pipe(finalize(() => {
          }))
          .subscribe((res: StatusResponse) => {
            if (res.success === 1) {
              this.Result = 'todo bien';
            } else {
              this.Result = `error`;
            }
          }, error => {
            console.log(error);
            this.Result = `error`;
          });
      });
  }

  close() {
    window.close();
  }
}
