import { DbConfigBase } from "./ConfigDbBase";
import { isEnvironmentVariableValid, parseToInt } from "./Helpers";
import { logger } from "./Logger";

export class DbRedis extends DbConfigBase {
}

export const dbRedis = new DbRedis();
dbRedis.port = parseToInt(process.env.REDISPORT, 6380);
dbRedis.host = process.env.REDISCACHEHOSTNAME;
dbRedis.password = process.env.REDISCACHEKEY;
dbRedis.dialect = 'redis';
dbRedis.enable = true;
dbRedis.connectionString = process.env.redisConnectionString;


const validateAndFormatConfig = () => {

    dbRedis.parseConnectionString();

    if (!isEnvironmentVariableValid(dbRedis.host)) {
        logger.error(`faltan inicializar variables de entorno: REDISCACHEHOST`);
        process.exit(9);
    }

    if (!isEnvironmentVariableValid(dbRedis.password)) {
        logger.error(`faltan inicializar variables de entorno: REDISCACHEKEY`);
        process.exit(9);
    }
}

validateAndFormatConfig();