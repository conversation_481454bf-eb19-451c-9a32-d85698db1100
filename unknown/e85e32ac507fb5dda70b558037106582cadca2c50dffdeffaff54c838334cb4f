import { Injectable } from '@angular/core';
import { CanDeactivate } from '@angular/router';
import { ActivatedRouteSnapshot, RouterStateSnapshot } from '@angular/router';
import { EditorComponent } from '../components/editor/editor.component';
import { Observable } from 'rxjs';
import { EditorService } from '../services/editor.service';

@Injectable()
export class CanLeaveEditor implements CanDeactivate<EditorComponent> {

  constructor(private editorService: EditorService) {}

  canDeactivate(
    component: EditorComponent,
    currentRoute: ActivatedRouteSnapshot,
    currentState: RouterStateSnapshot,
    nextState: RouterStateSnapshot
  ): Observable<boolean>|Promise<boolean>|boolean {
    // Si el servicio acaba de guardar, permitir salir sin preguntar
    if (this.editorService['_justSaved'] === true) {
      return true;
    }

    // Verificar si estamos editando la versión productiva
    if (this.editorService.isEditingProductiveVersion()) {
      return true;
    }

    // Verificar si hay cambios pendientes
    const hasPendingChanges = this.editorService.pendingChanges();

    if (hasPendingChanges) {
      return confirm('Hay cambios pendientes de guardar que se perderán si sale, ¿desea continuar?');
    }

    return true;
  }
}
