@import '_variables';

.commands-tab {
  height: 100%;
  width: 100%;
  overflow-y: hidden;
  overflow-x: hidden;
  display: flex;
  flex-direction: row;

  .commands-list {
    background-color: $sidebarBackgroundColor;
    flex-grow: 0;
    flex-shrink: 0;
    width: 300px;
    padding: 20px 10px;
    max-height: 100%;
    overflow-y: auto;

    .command {

    }
  }

  .command-detail {
    flex-grow: 0;
    flex-shrink: 0;
    width: calc(100% - 300px);
    padding: 20px 10px;
    height: 100%;
    max-height: 100%;
    overflow-y: auto;
  }
}

.add{
	color: #337ab7;
	text-transform: uppercase;
	font-size: 12px;
	margin: 10px 0;
	width: max-content;
	cursor: pointer;
}