@import '_variables';
@import '_mixins';

.title {
  width: 100%;
  margin-bottom: 10px;

  input {
    border: solid 1px transparent;
    border-radius: 10px;
    background-color: transparent;
    padding: 0 26px 0 4px;
    line-height: 36px;
    font-size: 20px;
    width: 90%;

    &:not([disabled]):hover {
      border: solid 1px rgba(0, 0, 0, 0.09);
    }

    &:focus {
      color: #000000;
      background-color: #fafafa;
      box-shadow: inset 0 1px 3px 0 rgba(0, 0, 0, 0.07);
      border: solid 1px rgba(0, 0, 0, 0.09);
      margin-right: 6px;
    }
  }
}

.select-block {
  margin-bottom: 10px;

  .input {
    width: 50%;
  }

  label, input {
    margin-right: 10px;
  }
}
