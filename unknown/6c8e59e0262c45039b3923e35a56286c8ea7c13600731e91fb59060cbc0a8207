import { BrowserModule, Title } from '@angular/platform-browser';
import { NgModule } from '@angular/core';
import { FormsModule } from '@angular/forms';
import { HttpClientModule } from '@angular/common/http';
import { RouterModule } from '@angular/router';

import { AppComponent } from './app.component';
import { NavMenuComponent } from './components/nav-menu/nav-menu.component';
import { SignaturePadComponent } from './components/signature-pad/signature-pad.component';
import { SignaturePadModule } from 'angular2-signaturepad';

import { ServerService } from './services/ServerService';
import { HomeComponent } from './components/home/<USER>';
import {ActionSpinnerComponent} from './components/action-spinner/action-spinner';
import {BrowserAnimationsModule} from '@angular/platform-browser/animations';
import {ToasterModule} from 'angular5-toaster/dist';
import { AccountUnlinkingComponent } from './components/account-unlinking/account-unlinking.component';
import { AccountTestComponent } from './components/account-test/account-test.component';


@NgModule({
  declarations: [
    AppComponent,
    NavMenuComponent,
    SignaturePadComponent,
    HomeComponent,
    ActionSpinnerComponent,
    AccountUnlinkingComponent,
    AccountTestComponent
  ],
  imports: [
    BrowserModule.withServerTransition({ appId: 'ng-cli-universal' }),
    HttpClientModule,
    FormsModule,
    SignaturePadModule,
    BrowserAnimationsModule, ToasterModule,
    RouterModule.forRoot([
      { path: 'signature-pad', component: SignaturePadComponent },
      { path: 'account/unlinking', component: AccountUnlinkingComponent },
      { path: 'account/testing', component: AccountTestComponent },
      { path: '**', component: HomeComponent },
    ])
  ],
  providers: [
    ServerService,
    Title
  ],
  bootstrap: [AppComponent]
})
export class AppModule { }
