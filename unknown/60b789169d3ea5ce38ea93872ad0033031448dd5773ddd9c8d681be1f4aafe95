@import "variables";

@mixin flow {
  cursor: pointer;
  width: 250px;
  height: 160px;
  display: flex;
  flex-direction: column;
  background-color: $primary;
  padding: 10px;
  color: #fff;
  margin: 8px;
  border-radius: 8px;
  transition: transform 240ms ease 100ms;
  position: relative;
  font-size: 100%;

  &.new {
    background-color: #ffffff;
    color: rgb(20, 20, 20);

    &:hover {
      color: $linkActionColor;
    }
  }

  &::before {
    content: '';
    position: absolute;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    background-color: inherit;
    border-radius: inherit;
    z-index: 999;
    transition: transform 240ms ease 100ms;
  }

  &:hover::before {
    transform: scale(1.025);
  }

  .dropdown {
    position: absolute;
    right: 10px;
    top: 10px;
    z-index: 1001;

    .dropdown-toggle {
      border: 1px solid transparent;
      border-radius: 13px;
      background-color: transparent;
      color: #fff;
      cursor: pointer;
      padding: 0;

      & > span {
        line-height: 25px;
        width: 25px;
      }

      &::after, &::before {
        display: none;
      }

      &:hover {
        opacity: 0.7;
        border: 1px solid #fff;
      }
    }

    .dropdown-menu {
      min-width: 8rem;
      font-size: 100%;
      .dropdown-item {
        font-size: 90%;
        cursor: pointer;

        &:hover {
          background-color: #d3d3d3;
        }
      }
    }
  }

  .icon {
    font-size: 400%;
    color: #fff;
    position: absolute;
    bottom: 10px;
    right: 10px;
    opacity: 0.8;
    height: 60px;
    line-height: 60px;
    z-index: 1000;

    & > span {
      color: #fff;
    }
  }

  .contents {
    position: absolute;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    z-index: 1000;
    padding: 20px;

    &.new {
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      .add {
        font-size: 40px;
        line-height: 40px;
        margin-bottom: 10px;
      }
      .text {
        font-weight: bold;
        font-size: 120%;
      }
    }

    .name {
      font-size: 130%;
      font-weight: bold;
      max-width: 90%;
      p {
        word-break: break-all;
        overflow-x: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
      }

      .id {
        display: none;
        font-style: italic;
      }

      /*&:hover {
        .id {
          display: inline-block;
        }
      }*/
    }

    .info, .version {
      display: flex;
      font-size: 100%;
      font-family: $fontFamilyCondensed;
    }
  }

  &.development {
    color: rgb(20, 20, 20);

    .dropdown {
      .dropdown-toggle {
        color: rgb(20, 20, 20);

        &:hover {
          border-color: rgb(20, 20, 20);
        }
      }
    }
  }
}

@mixin box {
  width: 30%;
  text-align: center;
  border: 1px solid #eee;
  border-radius: 10px;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  margin-right: 7px;
  line-height: 24px;
  height: 36px;
  margin-bottom: 5px;
  font-weight: normal;
  vertical-align: bottom;
  background-color: white;
  cursor: pointer;
  padding: 2px 10px;
  position: relative;
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;

  .name {
    color: #222;
    width: 100%;
    text-overflow: ellipsis;
    display: block;
    overflow: hidden;
    line-height: 16px;
    font-size: 13px;
    max-height: 100%;
  }

  .invalid {
    display: none;
    position: absolute;
    right: 4px;
    top: 4px;
    color: red;
    font-size: 4px;
    line-height: 4px;
    height: 4px;

    & > span {
      line-height: 4px;
    }
  }

  .doesnt-return-message {
    display: none;
    position: absolute;
    right: 4px;
    bottom: 4px;
    color: blue;
    font-size: 4px;
    line-height: 4px;
    height: 4px;

    & > span {
      line-height: 4px;
    }
  }

  .highlighted-piece {
    display: none;
    position: absolute;
    left: 4px;
    bottom: 4px;
    color: $hightlightColor;
    font-size: 4px;
    line-height: 4px;
    height: 4px;

    & > span {
      line-height: 4px;
    }
  }

  &.invalid {
    .invalid {
      display: block;
    }
  }

  &.doesnt-return-message {
    .doesnt-return-message {
      display: block;
    }
  }

  &:hover {
    box-shadow: 0 2px 8px -2px rgba(0, 0, 0, 0.2);

    .name {
      color: #111;
    }
  }

  &.selected {
    background-color: darken($block-base, 10%);
    border-color: darken($block-border, 10%);

    .name {
      color: #000;
    }
  }

  &.active {
    border: 1px solid rgba(51, 122, 183, .5);
    color: #337ab7;
  }

  &.highlight {
    box-shadow: 0 0 0 0.2rem rgba($hightlightColor, .25);
    border-color: $hightlightColor;

    .name {
      &.highlighted {
        ::ng-deep.search-match {
          background-color: $hightlightColor;
        }
      }
    }

    .highlighted-piece {
      display: block;
    }
  }
}

@mixin variable-name {
  font-family: $fontFamilyMono;
}

@mixin variable-type {
  font-family: $fontFamilyMono;
  background-color: $flowColor;
  padding: 2px 4px;
  color: #fff;
  margin-left: 3px;
  font-size: 80%;
}

@mixin variable-channel {
  margin-left: 5px;
  font-size: 120%;
}

@mixin scrollbar {
  &::-webkit-scrollbar {
    width: 16px;
    height: 16px;
  }

  &::-webkit-scrollbar-button {
    width: 0;
    height: 0;
    display: none;
  }

  &::-webkit-scrollbar-corner {
    background-color: transparent;
  }

  &::-webkit-scrollbar-track:vertical {
    border-left: 6px solid transparent;
    border-right: 1px solid transparent;
    background-clip: padding-box;
  }

  &::-webkit-scrollbar-track:horizontal {
    border-top: 6px solid transparent;
    border-bottom: 1px solid transparent;
    background-clip: padding-box;
  }

  &::-webkit-scrollbar-track:vertical:hover {
    background-color: rgba(0, 0, 0, 0.035);
    -webkit-box-shadow: inset 1px 1px 0 rgba(0, 0, 0, 0.14), inset -1px -1px 0 rgba(0, 0, 0, 0.07);
  }

  &::-webkit-scrollbar-track:horizontal:hover {
    background-color: rgba(0, 0, 0, 0.035);
    -webkit-box-shadow: inset -1px 1px 0 rgba(0, 0, 0, 0.14), inset 1px -1px 0 rgba(0, 0, 0, 0.07);
  }

  &::-webkit-scrollbar-track:vertical:active {
    background-color: rgba(0, 0, 0, 0.05);
    -webkit-box-shadow: inset 1px 1px 0 rgba(0, 0, 0, 0.14), inset -1px -1px 0 rgba(0, 0, 0, 0.07);
  }

  &::-webkit-scrollbar-track:horizontal:active {
    background-color: rgba(0, 0, 0, 0.05);
    -webkit-box-shadow: inset -1px 1px 0 rgba(0, 0, 0, 0.14), inset 1px -1px 0 rgba(0, 0, 0, 0.07);
  }

  &::-webkit-scrollbar-thumb:vertical {
    background-clip: padding-box;
    border-top: 0 solid transparent;
    border-bottom: 0 solid transparent;
    border-left: 6px solid transparent;
    border-right: 1px solid transparent;
    min-height: 28px;
  }

  &::-webkit-scrollbar-thumb:horizontal {
    background-clip: padding-box;
    border-top: 6px solid transparent;
    border-bottom: 1px solid transparent;
    border-left: 0 solid transparent;
    border-right: 0 solid transparent;
    min-width: 28px;
  }

  &::-webkit-scrollbar-thumb {
    background-color: rgba(0, 0, 0, 0.2);
    -webkit-box-shadow: inset 1px 1px 0 rgba(0, 0, 0, 0.10), inset 0 -1px 0 rgba(0, 0, 0, 0.07);
  }

  &::-webkit-scrollbar-thumb:hover {
    background-color: rgba(0, 0, 0, 0.4);
    -webkit-box-shadow: inset 1px 1px 1px rgba(0, 0, 0, 0.25);
  }

  &::-webkit-scrollbar-thumb:active {
    background-color: rgba(0, 0, 0, 0.5);
    -webkit-box-shadow: inset 1px 1px 3px rgba(0, 0, 0, 0.35);
  }
}

@mixin trash {
  margin: 0;
  width: 26px;
  height: 26px;
  background: #ffffff none;
  box-shadow: 0 2px 4px 0 rgba(0, 0, 0, 0.07);
  border: solid 1px #aaa;
  border-radius: 13px;
  opacity: 0;
  font-size: 12px;
  text-align: center;
  color: #bbb;
  z-index: 1400;
  cursor: pointer;
  line-height: 24px;

  & > span {
    line-height: 24px;
  }

  &:hover {
    color: #555;
    border-color: #666;
  }
}

@mixin trashSmall {
  margin: 0;
  width: 20px;
  height: 20px;
  background: #ffffff none;
  box-shadow: 0 2px 4px 0 rgba(0, 0, 0, 0.07);
  border: solid 1px #aaa;
  border-radius: 10px;
  opacity: 0;
  font-size: 10px;
  text-align: center;
  color: #bbb;
  z-index: 1400;
  cursor: pointer;
  line-height: 18px;

  & > span {
    line-height: 18px;
  }

  &:hover {
    color: #555;
    border-color: #666;
  }
}

@mixin trashOver {
  opacity: 1;
}

@mixin configurable-item {
  position: relative;
  font-weight: normal;
  background-color: #ffffff;
  border-color: transparent;
  border-radius: 7px;
  box-shadow: 0 0 0 1px rgba(0, 0, 0, 0.04), 0 1px 0 0 rgba(0, 0, 0, 0.08);
  padding: 24px;
  margin-bottom: 20px;

  &.invalid {
    border: 1px solid red;
  }

  & > .title {
    font-family: $fontFamilyConfigurationTitles;
    font-size: $fontSizeConfigurationTitles;
    font-weight: bold;
    margin-bottom: 5px;
  }

  & > .description {
    font-family: $fontFamily;
    font-size: 16px;
    margin-bottom: 5px;
    color: #767676;
  }
}

@mixin addButton {
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: center;

  & > div {
    width: 40px;
    height: 40px;
    background-color: $linkActionColor;
    border-radius: 40px;
    text-align: center;
    color: #fff;
    cursor: pointer;
    box-shadow: 0 3px 5px -1px rgba(0, 0, 0, .2), 0 6px 10px 0 rgba(0, 0, 0, .14), 0 1px 18px 0 rgba(0, 0, 0, .12);
    font-size: 27px;
    display: flex;
    flex-direction: row;
    align-items: center;
    justify-content: center;

    &:hover {
      background-color: lighten($linkActionColor, 10%);
    }
  }
}

@mixin addLinkButton {
  text-transform: uppercase;
  cursor: pointer;
  font-size: 12px;
  font-weight: normal;
  color: $linkActionColor;
  width: max-content;

  &:hover {
    color: lighten($linkActionColor, 10%);
  }
}

@mixin addPieceButton {
  text-align: center;
  text-transform: uppercase;
  padding: 15px 0;
  cursor: pointer;
  font-size: 12px;
  position: relative;
  font-weight: normal;
  color: $linkActionColor;

  &:hover {
    color: lighten($linkActionColor, 10%);
  }

  span {
    display: inline-block;
    margin-right: 10px;
    position: absolute;
    left: -10px;
    top: 18px;
    margin-left: 30px;
  }
}

@mixin tooltip($color, $backgroundColor, $direction: 'top', $maxWidth: 420px) {
  width: auto;
  z-index: 100001101;

  .tooltip-inner {
    background-color: $backgroundColor;
    max-width: $maxWidth;
    height: auto;
    text-align: left;
    width: unset;
    margin: 0;
    font-size: 12px;
    line-height: 1.5;
    padding: 5px 10px;
    color: $color;
  }

  .arrow {
    @if $direction == right { border-right-color: $backgroundColor !important; }
    @if $direction == left { border-left-color: $backgroundColor !important; }
    @if $direction == top { border-top-color: $backgroundColor !important; }
    @if $direction == bottom { border-bottom-color: $backgroundColor !important; }
    color: $color;
    min-width: 0;
    max-width: unset;
    margin: 0;

    &::before {
      @if $direction == right { border-right-color: $backgroundColor !important; }
      @if $direction == left { border-left-color: $backgroundColor !important; }
      @if $direction == top { border-top-color: $backgroundColor !important; }
      @if $direction == bottom { border-bottom-color: $backgroundColor !important; }
    }
  }
}

@mixin actionButton($backgroundColor: #fff, $color: $defaultColor, $borderColor: $cardSeparatorBorderColor) {
  vertical-align: top;
  background: $backgroundColor;
  border: 1px solid $borderColor;
  text-align: center;
  padding: 10px;
  border-radius: 20px;
  text-transform: uppercase;
  font-size: 12px;
  cursor: pointer;
  color: $color;

  &:hover {
    background: darken($backgroundColor, 5%);
  }
}

@mixin alert-variant($background, $border, $color) {
  color: $color;
  border-color: $border;
  background-color: $background;
  display: flex;
  flex-direction: row;
  align-items: center;
  padding: 10px;

  hr {
    border-top-color: darken($border, 5%);
  }

  .icon {
    padding-left: 5px;
    padding-right: 5px;
  }

  .alert-link {
    color: darken($color, 10%);
  }

  .variable {
    background-color: #fff;
    border-color: darken(#fff, 5%);
    border-radius: 6px;
    padding: 2px 5px;
    color: $defaultColor;
  }
}

@mixin popupButton($borderColor: $delete-popup-border, $backgroundColor: #fff, $color: #000) {
  margin-left: 5px;
  border-radius: 5px;
  padding: 5px;
  border: 1px solid $delete-popup-border;
  min-width: 120px;
  text-align: center;
  cursor: pointer;
  text-transform: uppercase;
  background-color: $backgroundColor;
  color: $color;

  &.action-button-default {
    border-color: $popup-delete-button-background;
    background: $popup-delete-button-background;
    color: $block-base;
  }

  &.action-button-wide {
    width: 180px;
  }

  &[disabled] {
    background: $delete-popup-border;
    border: 1px solid $delete-popup-border;
    color: $block-base;
    cursor: not-allowed;
    pointer-events: none;
  }

  &:not(disabled) {
    &:hover {
      box-shadow: 0 2px 8px -2px rgba(0, 0, 0, 0.2);
      background: darken($backgroundColor, 5%);
    }

    &.action-button-default:hover {
      background: darken($popup-delete-button-background, 5%);
    }
  }
}
