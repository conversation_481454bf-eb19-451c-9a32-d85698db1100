<div class="configurable-item" [ngClass]="{ 'invalid': !isValid() }">
  <div class="title">
    {{ 'CONFIGURATION_GREETINGS_TITLE' | translate }}
  </div>
  <div class="description">
    {{ 'CONFIGURATION_GREETINGS_DESCRIPTION' | translate }}
  </div>
  <div class="implicit-variables" *ngIf="supportImplicitVariables">
    <div class="variables-info">{{'CONFIGURATION_GREETINGS_VARIABLELIST' | translate}}</div>
    <div class="variables-table">
      <div class="variables-header">
        <div>{{'NAME' | translate}}</div>
        <div>{{'DESCRIPTION' | translate}}</div>
      </div>
      <div class="variable-row" *ngFor="let variable of implicitVariables">
        <div class="variable-cell"><span class="variable-name">{{ variable.Name }}</span><span class="variable-type">{{ getVariableType(variable) | translate }}</span></div>
        <div class="variable-cell"><span class="variable-description">{{ variable.Description | translate }}</span></div>
      </div>
    </div>
  </div>
  <div class="greetings">
    <div class="greeting-info">{{'CONFIGURATION_GREETINGS_INFO' | translate}}</div>
    <div class="greeting" *ngFor="let greeting of model.Locales">
      <app-input-with-variables
        [isTextArea]="true"
        [placeholder]="'CONFIGURATION_GREETINGS_TEXT'"
        [(value)]="greeting.text"
        [disabled]="readOnly"
        [extendedStyles]="{ 'height.px': 100, 'max-height.px': 100, 'min-height.px': 100, 'resize': 'none', 'width.px': 300 }"
        [variableFinder]="searchForVariable.bind(this)"
        [customVariableList]="implicitVariables"></app-input-with-variables>
    </div>
  </div>
</div>
