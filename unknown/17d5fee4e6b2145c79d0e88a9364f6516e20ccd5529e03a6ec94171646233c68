DELIMITER $$

CREATE TABLE `integrations_audit` (
    `id` BIGINT AUTO_INCREMENT PRIMARY KEY,
    `type` INT,
    `message_id` VARCHAR(50),
    `case_id` VARCHAR(50),
    `social_user_id` VARCHAR(50),
    `flow_id` VARCHAR(50),
    `status_code` INT,
    `wait` BIGINT,
    `dns` BIGINT,
    `tcp` BIGINT,
    `tls` BIGINT,
    `request` BIGINT,
    `first_byte` BIGINT,
    `download` BIGINT,
    `total` BIGINT,
    `yFlow` BIGINT,
    `integration_id` VARCHAR(50),
    `integration_name` VARCHAR(250),
    `created_at` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
    `updated_at` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);$$

DELIMITER ;

DELIMITER $$

ALTER TABLE `integrations_audit` 
ADD COLUMN `timestamp` bigint DEFAULT NULL;

ALTER TABLE integrations_audit 
ADD INDEX Idx_created (created_at ASC) VISIBLE;

ALTER TABLE integrations_audit 
ADD INDEX IDX_Audit (timestamp ASC, integration_name ASC, status_code ASC) VISIBLE;
$$

DELIMITER ;

DELIMITER $$
CREATE EVENT `Depuration integrations_audit`
    ON SCHEDULE
        EVERY 1 DAY STARTS '2024-09-12 00:05:00'
    ON COMPLETION NOT PRESERVE
    ENABLE
    COMMENT 'Depura la tabla integrations_audit con los registros de mas de 3 dias'
    DO BEGIN

 DELETE FROM integrations_audit WHERE created_at < (DATE_SUB(DATE(NOW()), INTERVAL 3 DAY));
 
END$$
DELIMITER ;