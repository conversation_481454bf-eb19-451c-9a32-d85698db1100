const createError = require('http-errors');
const appRoot = require('app-root-path');
const express = require('express');
const path = require('path');
const cookieParser = require('cookie-parser');
const logger = require('morgan');
const winston = require('./config/winston');
const compression = require('compression');
const helmet = require('helmet');
const indexRouter = require('./routes/index');
const diaglogFlowRouter = require('./routes/dialogflow');
const apiGeeRouter = require('./routes/apigee');
const pjson = require('./package.json');

// Cargar variables de entorno si existe un archivo .env
require('dotenv').config();

const app = express();

app.use(compression());
app.use(helmet());

// view engine setup
app.set('views', path.join(__dirname, 'views'));
app.set('view engine', 'pug');

app.use(logger('combined', { stream: winston.stream }));
app.use(express.json());
app.use(express.urlencoded({ extended: false }));
app.use(cookieParser());
app.use(express.static(path.join(__dirname, 'public')));

app.use('/', indexRouter);
app.use('/diaglogflow', diaglogFlowRouter);
app.use('/apigee', apiGeeRouter);

// catch 404 and forward to error handler
app.use(function(req, res, next) {
  next(createError(404));
});

// error handler
app.use(function(err, req, res, next) {
  // set locals, only providing error in development
  res.locals.message = err.message;
  res.locals.error = req.app.get('env') === 'development' ? err : {};

  // add this line to include winston logging
  winston.error(`${err.status || 500} - ${err.message} - ${req.originalUrl} - ${req.method} - ${req.ip}`);

  // render the error page
  res.status(err.status || 500);
  res.render('error');
});

// Configurar credenciales de Google desde variables de entorno
process.env.GOOGLE_APPLICATION_CREDENTIALS = `${appRoot}/credential/google/${process.env.GOOGLE_APPLICATION_CREDENTIALS}`;

// Si se ejecuta directamente (no como módulo), iniciar el servidor
if (require.main === module) {
  const port = process.env.PORT || 3000;
  app.set('port', port);

  const server = app.listen(port, () => {
    console.log(`Servidor iniciado en http://localhost:${port}`);
  });
}

module.exports = app;
