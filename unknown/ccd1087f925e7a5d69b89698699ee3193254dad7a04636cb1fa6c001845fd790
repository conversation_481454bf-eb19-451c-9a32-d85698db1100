# Yoizen.yFlow.Extensions.Osde

Extensión para integrar yFlow con servicios de DialogFlow y Apigee para OSDE.

## Requisitos

- Node.js 18.20.3
- Docker (opcional, para contenedorización)

## Configuración

La aplicación se configura mediante variables de entorno. Ya no se utilizan los valores en `package.json`.

### Variables de entorno

Copia el archivo `.env.example` a `.env` y ajusta los valores según sea necesario:

```
PROJECT_ID=os-test-chatbotcorporativo
GOOGLE_APPLICATION_CREDENTIALS=os-test-chatbotcorporativo-0a6eecedd93e.json
CLIENT_CERTIFICATE_KEY=chatbotcorporativoyoizen.osde.ar.key
CLIENT_CERTIFICATE_CRL=chatbotcorporativoyoizen.osde.ar.pem
CLIENT_CERTIFICATE_PASSPHRASE=yoizen2019
PORT=3000
```

## Ejecución local

1. Instala las dependencias:
   ```
   npm install
   ```

2. Inicia la aplicación:
   ```
   npm start
   ```

## Dockerización

La aplicación utiliza un Dockerfile optimizado con las siguientes características:

- **Imagen base Alpine**: Más ligera y segura que la imagen estándar de Node.js.
- **Construcción multi-etapa**: Separa la etapa de construcción de la etapa de producción.
- **Usuario no root**: Ejecuta la aplicación con un usuario sin privilegios para mayor seguridad.
- **Optimización de capas**: Minimiza el número de capas y su tamaño.

### Construir y ejecutar con Docker

1. Construye la imagen:
   ```
   docker build -t yoizen-osde-extension .
   ```

2. Ejecuta el contenedor:
   ```
   docker run -p 3000:3000 \
     -e PROJECT_ID=os-test-chatbotcorporativo \
     -e GOOGLE_APPLICATION_CREDENTIALS=os-test-chatbotcorporativo-0a6eecedd93e.json \
     -e CLIENT_CERTIFICATE_KEY=chatbotcorporativoyoizen.osde.ar.key \
     -e CLIENT_CERTIFICATE_CRL=chatbotcorporativoyoizen.osde.ar.pem \
     -e CLIENT_CERTIFICATE_PASSPHRASE=yoizen2019 \
     -v $(pwd)/credential/google/os-test-chatbotcorporativo-0a6eecedd93e.json:/app/credential/google/os-test-chatbotcorporativo-0a6eecedd93e.json \
     -v $(pwd)/credential/client/chatbotcorporativoyoizen.osde.ar.key:/app/credential/client/chatbotcorporativoyoizen.osde.ar.key \
     -v $(pwd)/credential/client/chatbotcorporativoyoizen.osde.ar.pem:/app/credential/client/chatbotcorporativoyoizen.osde.ar.pem \
     yoizen-osde-extension
   ```

### Usando Docker Compose (Recomendado)

1. Prepara los directorios y permisos necesarios:

   En Linux/Mac:
   ```
   chmod +x setup-permissions.sh
   ./setup-permissions.sh
   ```

   En Windows:
   ```
   setup-permissions.bat
   ```

2. Asegúrate de tener los archivos de credenciales en los directorios correspondientes:
   - `credential/google/os-test-chatbotcorporativo-0a6eecedd93e.json`
   - `credential/client/chatbotcorporativoyoizen.osde.ar.key`
   - `credential/client/chatbotcorporativoyoizen.osde.ar.pem`

3. Ejecuta la aplicación con Docker Compose:
   ```
   docker-compose up -d
   ```

4. Para detener la aplicación:
   ```
   docker-compose down
   ```

5. Para reconstruir la imagen y reiniciar los contenedores:
   ```
   docker-compose down
   docker-compose build --no-cache
   docker-compose up -d
   ```

### Solución de problemas de permisos

Si encuentras errores de permisos al ejecutar la aplicación en Docker, especialmente relacionados con el directorio de logs, puedes intentar lo siguiente:

1. Asegúrate de que el directorio `logs` tenga permisos de escritura:
   ```
   chmod -R 777 logs  # En Linux/Mac
   ```

2. Si persisten los problemas, puedes modificar el Dockerfile para ajustar los permisos o ejecutar el contenedor como root (no recomendado para producción).

## Pruebas

La aplicación incluye varias herramientas para realizar pruebas:

### Usando el script de prueba

1. Asegúrate de que la aplicación esté en ejecución.

2. Ejecuta el script de prueba:
   ```
   npm test
   ```

### Usando cURL

Se proporcionan comandos cURL para probar los endpoints en `test/curl-commands.md`.

Ejemplo para probar el endpoint de DialogFlow:
```bash
curl -X GET "http://localhost:3000/diaglogflow/Hola"
```

### Usando Postman

1. Importa la colección `test/postman-collection.json` en Postman.
2. Configura la variable de entorno `baseUrl` si es necesario (por defecto es `http://localhost:3000`).
3. Ejecuta las solicitudes incluidas en la colección.

### Notas sobre las pruebas

- El caso especial de DialogFlow (`1 PLAN 210 Ciudad de Buenos Aires Boedo - Almagro - Congreso - San Cristóbal`) debería funcionar sin credenciales válidas.
- Las pruebas que requieren credenciales válidas pueden fallar si no se han configurado correctamente las variables de entorno.

## Estructura de directorios

- `app.js`: Punto de entrada principal de la aplicación
- `/credential`: Archivos de credenciales
  - `/google`: Credenciales de Google para DialogFlow (*.json)
  - `/client`: Certificados para Apigee (*.key, *.pem)
- `/routes`: Rutas de la API
- `/views`: Plantillas de vistas
- `/config`: Archivos de configuración
- `/logs`: Archivos de registro (generados en tiempo de ejecución)

### Archivos necesarios para las credenciales

Antes de ejecutar la aplicación, asegúrate de tener los siguientes archivos en los directorios correspondientes:

1. `credential/google/os-test-chatbotcorporativo-0a6eecedd93e.json`: Credenciales de Google para DialogFlow
2. `credential/client/chatbotcorporativoyoizen.osde.ar.key`: Clave del certificado para Apigee
3. `credential/client/chatbotcorporativoyoizen.osde.ar.pem`: Certificado para Apigee

## Endpoints

- `/diaglogflow/:text`: Envía consultas a DialogFlow
- `/apigee`: Realiza peticiones a Apigee
