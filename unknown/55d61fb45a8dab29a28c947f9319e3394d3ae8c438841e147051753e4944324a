import {Component, EventEmitter, Input, OnInit} from '@angular/core';
import { FormatDefinition, FormatDefinitionType } from 'src/app/models/FormatDefinition';
import { EditorService } from 'src/app/services/editor.service';
import { TypeDefinition } from 'src/app/models/TypeDefinition';
import { VariableDefinition } from 'src/app/models/VariableDefinition';
import * as moment from 'moment';

import * as numeral from 'numeral';
import { TranslateService } from '@ngx-translate/core';
import {DeleteGroupQuestionComponent} from "../../popups/delete-group-question/delete-group-question.component";
import {DeleteInput} from "../../../../models/UI/DeleteInput";
import {ModalService} from "../../../../services/Tools/ModalService";
import {
  WhatsappHSMTemplateByService,
  YSocialReturnFromAgentSettings,
  YSocialSettings,
  HSMTemplateHeaderTypes, HSMTemplateButtonsTypes, HSMTemplateParameter, HSMTemplateButton
} from "../../../../models/YSocialSettings";
import {BlockDefinition} from "../../../../models/BlockDefinition";
import {ChannelTypes} from "../../../../models/ChannelType";
import {finalize} from "rxjs/operators";
import {StatusResponse} from "../../../../models/StatusResponse";
import {ErrorPopupComponent} from "../../../error-popup/error-popup.component";
import {ServerService} from "../../../../services/server.service";
import { ToasterService } from 'angular2-toaster';
import { getTokenPayload } from 'src/app/Utils/window';

@Component({
  selector: 'app-ysocial-settings',
  templateUrl: './ysocial-settings.component.html',
  styleUrls: ['./ysocial-settings.component.scss']
})
export class YsocialSettingsComponent implements OnInit {
  @Input() readOnly: boolean = false;
  @Input() settings: YSocialSettings = null;
  showReturnsFromAgent : boolean = false;
  showWhatsappHSMs : boolean = false;
  isYoizenAdmin: boolean = false;
  loading: boolean = false;
  hsmTemplateHeaderTypes = HSMTemplateHeaderTypes;
  hsmButtonsType = HSMTemplateButtonsTypes;

  constructor(public editorService: EditorService,
              private translateService: TranslateService,
              private modalService: ModalService,
              private serverService: ServerService,
              private toasterService: ToasterService) {
  }

  ngOnInit() {
    if (this.settings === null) {
      this.settings = this.editorService.getYSocialSettings();
    }

    this.isYoizenAdmin = getTokenPayload().name === 'Administrador' || 
                         getTokenPayload().name === 'administrador';
                         
    
    let currentFlow = this.editorService.getCurrentFlow();
    this.showReturnsFromAgent = currentFlow.channel !== ChannelTypes.Chat;
    this.showWhatsappHSMs = currentFlow.channel === ChannelTypes.WhatsApp;
  }

  delete(record: YSocialReturnFromAgentSettings, index: number) {
    var emmitAction = new EventEmitter();
    emmitAction.subscribe(() => {
      this.settings.ReturnsFromAgent.splice(index, 1);
    });

    var deleteInfo: DeleteInput = new DeleteInput();
    deleteInfo.ElementName = record.Description;

    this.modalService.init(
      DeleteGroupQuestionComponent,
      {
        DeleteDetail: deleteInfo,
        Title: 'ARE_YOU_SURE_DELETE_RETURNFROMAGENT_QUESTION',
        HideAffected: true,
        HideConfirmation: true,
        deleteText: 'ACCEPT',
        DeleteTextExplanation: 'DELETE_RETURNFROMAGENT_EXPLANATION',
      }, {DeleteAction: emmitAction}
    );
  }

  add() {
    this.settings.ReturnsFromAgent.push(new YSocialReturnFromAgentSettings());
  }

  onSelectBlock(blockData: BlockDefinition, record: YSocialReturnFromAgentSettings) {
    record.BlockId = blockData.Id;
  }

  onDeleteBlock(blockData: BlockDefinition, record: YSocialReturnFromAgentSettings) {
    record.BlockId = null;
  }

  refreshWhatsappHSM() {
    this.loading = true;
    this.serverService.retrieveWhatsappHSMTemplates(this.settings.Url)
      .pipe(finalize(() => {
        this.loading = false;
      }))
      .subscribe((status: StatusResponse) => {
        if (status.data !== null && Array.isArray(status.data)) {
          this.editorService.loadySocialSettings(status.data);
        }
        },
        error => {
          console.log(error)
          this.toasterService.pop({
            type: 'error',
            body: this.translateService.instant('CANNOT_FIND_HSM'),
            timeout: 2000
          });
          
        });
  }
}
