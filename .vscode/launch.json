{
    // Use IntelliSense to learn about possible attributes.
    // Hover to view descriptions of existing attributes.
    // For more information, visit: https://go.microsoft.com/fwlink/?linkid=830387
    "version": "0.2.0",
    "configurations": [
        {
            "name": "Attach to Yoizen.yFlow.Web",
            "type": "node",
            "request": "attach",
            "port": 9229, // Puerto configurado con --inspect-brk en el script de lanzamiento
            "restart": true,
            "localRoot": "${workspaceFolder}/Yoizen.yFlow.Web",
            "remoteRoot": "${workspaceFolder}/Yoizen.yFlow.Web",
            "skipFiles": [
                "${workspaceFolder}/Yoizen.yFlow.Web/<node_internals>/**"
            ]
        },
        {
            "name": "Attach to Yoizen.yFlow.WebExecutor",
            "type": "node",
            "request": "attach",
            "port": 9230, // Puerto configurado con --inspect-brk en el script de lanzamiento
            "restart": true,
            "localRoot": "${workspaceFolder}/Yoizen.yFlow.WebExecutor",
            "remoteRoot": "${workspaceFolder}/Yoizen.yFlow.WebExecutor",
            "skipFiles": [
                "${workspaceFolder}/Yoizen.yFlow.WebExecutor/<node_internals>/**"
            ]
        },
        {
            "name": "Attach to Yoizen.yFlow.IntervalServices",
            "type": "node",
            "request": "attach",
            "port": 9230, // Puerto configurado con --inspect-brk en el script de lanzamiento
            "restart": true,
            "localRoot": "${workspaceFolder}/Yoizen.yFlow.IntervalServices",
            "remoteRoot": "${workspaceFolder}/Yoizen.yFlow.IntervalServices",
            "skipFiles": [
                "${workspaceFolder}/Yoizen.yFlow.IntervalServices/<node_internals>/**"
            ]
        }
    ]
}