import { DetailedBase } from "../../../../Yoizen.yFlow.Domain/src/models/statistics/detail/DetailedBase";
import { IIntervalPort } from "../../ports/IIntervalPort";
import { DailyBase } from '../../../../Yoizen.yFlow.Domain/src/models/statistics/daily/DailyBase';
import { config as configAux } from "../../../../Yoizen.yFlow.Helpers/src/Config";
import { logger } from "../../../../Yoizen.yFlow.Helpers/src/Logger";

export class SequelizeDetailedAdapter implements IIntervalPort {
    executeStoredProcedure(procedureName: string, params: string[]): Promise<any> {
        throw new Error('Method not implemented.');
    }
    dropTable(interval: DailyBase | DetailedBase): Promise<any> {
        throw new Error('Method not implemented.');
    }
    calculate(interval: DailyBase | DetailedBase): Promise<any> {
        throw new Error('Method not implemented.');
    }

    async connect(): Promise<void> {
        return;
    }

    async saveInDatabase(interval: DetailedBase): Promise<any> {
        try {
            await interval.save();
            return {
                failed: false,
                message: 'Saved in database'
            }
        } catch (error) {
            logger.fatal({ error: error }, `CRITICAL - Error al guardar estadísticas ${JSON.stringify(interval)} error`);
            return {
                failed: true,
                message: error
            };
        }
    }
}