import { Op, TableHints } from 'sequelize';
import Flow from '../../../../Yoizen.yFlow.Web/models/flow';
import { IFlowPort } from '../../ports/IFlowPort';
import { CaseContext, CaseContextSequelize, CaseContextStringSequelize } from '../../../../Yoizen.yFlow.Domain/src/models/statistics/flow/CaseContext';
import { DataTypes, Sequelize, Options as SequelizeOptions, Dialect as SequelizeDialect } from "sequelize";
import { config as configAux } from "../../../../Yoizen.yFlow.Helpers/src/Config";
import { dbContext } from '../../../../Yoizen.yFlow.Helpers/src/ConfigDbContext';
import { logger } from '../../../../Yoizen.yFlow.Helpers/src/Logger';


export class SequelizeFlowAdapter implements IFlowPort {

    private sequelize: Sequelize;
    public constructor() {
        const config: SequelizeOptions = {
            dialect: dbContext.dialect as SequelizeDialect,
            host: dbContext.host,
            port: dbContext.port,
            database: dbContext.name,
            username: dbContext.username,
            password: dbContext.password,
            ssl: dbContext.ssl,
            logging: configAux.isDebug ? function (str) {
                logger.trace(str);
            } : false,
            define: {
                underscored: true
            },
        };

        if (config.dialect === 'mssql') {
            config.dialectOptions = {
                options: {
                    requestTimeout: dbContext.dbtimeout,
                    cancelTimeout: dbContext.dbcanceltimeout
                },
                ssl: {
                    require: dbContext.ssl,
                }
            }
        } else if (config.dialect === 'mysql') {
            config.dialectOptions = {
                connectTimeout: dbContext.dbtimeout,
                ssl: {
                    require: dbContext.ssl,
                }
            }
        }

        if (!dbContext.ssl) {
            // @ts-ignore: Lo quito ya que no encontré otra forma elegante de quitar el warning
            delete config.dialectOptions.ssl;
        }

        if (typeof (dbContext.poolMaxSize) === 'number') {
            config.pool = {
                max: dbContext.poolMaxSize,
                min: 0,
                acquire: 30000,
                idle: 10000
            };
        }

        this.sequelize = new Sequelize(config);
        CaseContextSequelize.initialize(this.sequelize);
        CaseContextStringSequelize.initialize(this.sequelize);
    }

    async GetAllProductiveMaster() {
        let flows = await Flow.findAll({
            attributes: ['id', 'name', 'ActiveProductionVersionId'],
            where: {
                ActiveStagingVersionId: {
                    [Op.ne]: null
                },
                master_flow_id: {
                    [Op.eq]: null
                }
            },
            tableHint: TableHints.NOLOCK
        });
        return flows;
    }

    async connect(): Promise<void> {
        try {
            await this.sequelize.authenticate();
            logger.info('Database Connection has been established successfully  - dbContext');
        }
        catch (err) {
            logger.error({ error: err }, 'Unable to connect to the database - dbContext:');
            process.exit(9);
        }
    }

    private getModel(caseContext: CaseContext): { model: typeof CaseContextSequelize | typeof CaseContextStringSequelize, caseContext: CaseContext } {
        let model: typeof CaseContextSequelize | typeof CaseContextStringSequelize;
        if (typeof (caseContext.case_id) === 'number') {
            model = CaseContextSequelize;
        } else if (typeof (caseContext.case_id) === 'string') {
            const caseIdToNumber = parseInt(caseContext.case_id, 10);
            if (isNaN(caseIdToNumber)) {
                model = CaseContextStringSequelize;
            } else {
                caseContext.case_id = caseIdToNumber;
                model = CaseContextSequelize;
            }
        }

        return {
            model: model,
            caseContext: caseContext
        }
    }

    private getModelByCaseId(caseId: string | number): { model: typeof CaseContextSequelize | typeof CaseContextStringSequelize, caseId: string | number } {
        let model: typeof CaseContextSequelize | typeof CaseContextStringSequelize;
        if (typeof (caseId) === 'number') {
            model = CaseContextSequelize;
        } else if (typeof (caseId) === 'string') {
            const caseIdToNumber = parseInt(caseId, 10);
            if (isNaN(caseIdToNumber)) {
                model = CaseContextStringSequelize;
            } else {
                caseId = caseIdToNumber;
                model = CaseContextSequelize;
            }
        }

        return {
            model: model,
            caseId: caseId
        }
    }

    async SaveContext(caseContextToSave: CaseContext): Promise<CaseContext> {
        const { model, caseContext } = this.getModel(caseContextToSave);
        //@ts-ignore
        return await model.create({
            case_id: caseContext.case_id,
            json: JSON.stringify(caseContext.json)
        });
    }

    async UpdateContext(caseContextToSave: CaseContext): Promise<CaseContext> {
        const { model, caseContext } = this.getModel(caseContextToSave);
        //@ts-ignore
        const result = await model.update({
            json: JSON.stringify(caseContext.json)
        }, { where: { case_id: caseContext.case_id } });
        return caseContext;
    }

    async GetContextByCaseId(caseIdToSearch: string | number): Promise<CaseContext> {
        const { model, caseId } = this.getModelByCaseId(caseIdToSearch);
        //@ts-ignore
        let caseContext = await model.findOne({
            where: { case_id: caseId },
            //@ts-ignore
            tableHint: TableHints.NOLOCK
        });

        if (caseContext) {
            if (typeof caseContext.json === 'string') {
                caseContext.json = JSON.parse(caseContext.json);
            }
        }
        return caseContext;
    }
}