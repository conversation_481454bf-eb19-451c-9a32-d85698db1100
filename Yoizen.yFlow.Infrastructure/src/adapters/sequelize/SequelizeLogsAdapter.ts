import { Op, TableHints } from "sequelize";
import { Logs } from "../../../../Yoizen.yFlow.Domain/src/models/statistics/flow/Logs";
import { ILogPort } from "../../ports/ILogPort";
import { logger } from "../../../../Yoizen.yFlow.Helpers/src/Logger";

export class SequelizeLogsAdapter implements ILogPort {

    async Save(log: Logs): Promise<Logs> {
        try {
            if (log.stack && (typeof (log.stack) === 'object' || Array.isArray(log.stack))) {
                log.stack = JSON.stringify(log.stack);
            }
            return await log.save();
        } catch (error) {
            logger.error({ error: error }, `<PERSON>rror guardando el log '${log.type}'`);
            return null;
        }
    }

    async GetLogsByDateRange(startDate: Date, endDate: Date): Promise<any> {
        try {
            const where = {
                date: {
                    [Op.between]: [startDate, endDate]
                }
            };

            return Logs.findAll({
                where: where,
                //@ts-ignore
                tableHint: TableHints.NOLOCK
            });
        } catch (error) {
            logger.error({ error: error }, `Error al obtener los logs:`);
            return [];
        }
    }

}