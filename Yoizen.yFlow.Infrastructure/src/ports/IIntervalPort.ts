import { DailyBase } from "../../../Yoizen.yFlow.Domain/src/models/statistics/daily/DailyBase";
import { DetailedBase } from "../../../Yoizen.yFlow.Domain/src/models/statistics/detail/DetailedBase";

export interface IIntervalPort {
  saveInDatabase(interval: DailyBase | DetailedBase): Promise<any>;
  executeStoredProcedure(procedureName: string, params: string[]): Promise<any>
  dropTable(interval: DailyBase | DetailedBase): Promise<any>
  calculate(interval: DailyBase | DetailedBase): Promise<any>;
  connect(): Promise<void>

}