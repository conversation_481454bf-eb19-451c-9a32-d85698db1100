import { BlockBlobClient } from "@azure/storage-blob";

export interface IFilePort {
    init(): Promise<void>;
    /**
     * Sube un archivo al blob
     * @param blobName Nombre del blob
     * @param base64 Archivo en base64
     * @param filename Tipo de contenido del archivo
     * @param contentType Tipo de contenido del archivo
     * @returns Nombre temporal del archivo
     */
    uploadFile(blobName: string, base64: string, filename: string, contentType: string): Promise<string>;
    /**
     * Descarga un archivo del blob
     * @param blobName Nombre del blob
     * @param fileName Nombre del archivo
     */
    downloadFile(blobName: string, fileName: string): Promise<{ binaryData: string | null; filename: string | null; mimeType: string | null; }>;
    /**
     * 
     * @param blobName Nombre del blob
     * @param fileName Nombre del archivo
     */
    deleteFile(blobName: string, fileName: string): Promise<void>,
}