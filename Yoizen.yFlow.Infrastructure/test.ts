import { createClient, RedisClientType } from 'redis';

let client = createClient({
    socket: {
        host: 'yflow.redis.cache.windows.net',
        port: 6380,
        passphrase: '+SLICqbsKg0tJQwyeSe2IHb4tLds7ScCpYMByHjvTdg=',
        tls: true
    },
    password: '+SLICqbsKg0tJQwyeSe2IHb4tLds7ScCpYMByHjvTdg=',
    name: 'Yoizen'
});


(async () => {
    await client.connect();

    const documents = [
        { id: 1, category: 'A', value: 10 },
        { id: 2, category: 'B', value: 15 },
        { id: 3, category: 'A', value: 20 },
        { id: 4, category: 'C', value: 30 },
        { id: 5, category: 'B', value: 25 },
    ];

    for (const doc of documents) {
        await client.json.set(`doc:${doc.id}`, '$', doc);
    }

    console.log('Documents stored in Redis.');


})();