{"name": "yoizen.yflow.helpers", "version": "1.0.0", "description": "", "main": "index.js", "type": "module", "scripts": {"start": "node --env-file=.env --loader ts-node/esm src/index.ts", "dev": "node --env-file=.env --watch --loader ts-node/esm src/index.ts", "build": "tsc", "buildWeb": "tsc --build tsconfig.web.json", "lint": "eslint . --ext .ts", "lint:fix": "eslint . --fix --ext .ts"}, "keywords": [], "author": "", "license": "ISC", "dependencies": {"crypto-js": "4.1.1", "pino": "9.6.0", "pino-http": "^10.4.0", "pino-pretty": "^13.0.0"}, "devDependencies": {"@types/node": "^20.12.7", "ts-node": "^10.9.2", "typescript": "^4.9.5"}}