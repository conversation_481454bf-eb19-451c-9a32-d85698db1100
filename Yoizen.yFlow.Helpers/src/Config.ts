import { isEnvironmentVariableValid, parseToBoolean } from "./Helpers";
import CryptoJS from 'crypto-js';
import { logger } from "./Logger";

if (typeof (process.env.key) !== 'string') {
    process.env.key = "6+0WAvwNXuEtpst2l6ruHHBdHLUVfTVFJQhsECxNUPI=";
}
if (typeof (process.env.iv) !== 'string') {
    process.env.iv = "YJY2yfEyqHiGeS7dRnDSKw==";
}

export class Config {
    isDebug: boolean;
    client: string;
    generateFilesForDefaultAnswerBlock: boolean;
    key: string;
    iv: string;
    standAlone: boolean;
    gmt: string;
    virtualDirPath: string;
    salt: CryptoJS.lib.WordArray;
    defaultLanguaje: string;
    isContingencyBot: boolean;
}

export const config: Config = {
    isDebug: process.env.NODE_ENV === 'dev',
    client: process.env.client,
    generateFilesForDefaultAnswerBlock: parseToBoolean(process.env.generateFilesForDefaultAnswerBlock, false),
    key: process.env.key,
    iv: process.env.iv,
    standAlone: parseToBoolean(process.env.standAlone, false),
    gmt: process.env.gmt,
    virtualDirPath: process.env.virtualDirPath || '',
    salt: CryptoJS.enc.Utf8.parse("Y01z3n2020*"),
    defaultLanguaje: process.env.defaultLanguaje,
    isContingencyBot: parseToBoolean(process.env.isContingencyBot)
};

const validateAndFormatConfig = () => {
    if (!isEnvironmentVariableValid(config.client)) {
        logger.error(`faltan inicializar variables de entorno: client`);
        process.exit(9);
    }

    if (!isEnvironmentVariableValid(config.gmt)) {
        logger.error(`faltan inicializar variables de entorno: gmt`);
        process.exit(9);
    }

    if (!isEnvironmentVariableValid(config.defaultLanguaje)) {
        logger.error(`faltan inicializar variables de entorno: defaultLanguaje`);
        process.exit(9);
    }

    if (!isEnvironmentVariableValid(config.isContingencyBot)) {
        logger.error(`faltan inicializar variables de entorno: isContingencyBot`);
        process.exit(9);
    }

    if (config.virtualDirPath.length > 0 && !config.virtualDirPath.startsWith('/')) {
        config.virtualDirPath = '/' + config.virtualDirPath;
    }
}

validateAndFormatConfig();