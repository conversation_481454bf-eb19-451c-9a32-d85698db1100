import { logger, changeServiceName, expressMiddleware, responseBodyLoggerMiddleware } from '../../Yoizen.yFlow.Helpers/src/Logger';
changeServiceName('IntervalServices');
import { InitIntervalServices, InityFLowServices, InitCache } from '../../Yoizen.yFlow.Infrastructure/src/InitServices';
import { AxiosExternalApiAdapter } from '../../Yoizen.yFlow.Infrastructure/src/adapters/axios/AxiosExternalApiAdapter';
import { importReplaceAll } from '../../Yoizen.yFlow.Helpers/src/Helpers';
import { DailyIntervalService } from './application/services/DailyIntervalService';
import { ReportService } from './application/services/ReportService';
import { FtpService } from './application/services/FtpService';
import { CentralizeService } from './application/services/CentralizeService';
import { LogsService } from './application/services/LogsService';
import { TableCreationIntervalService } from './application/services/TableCreationIntervalService';
import { IIntervalPort } from '../../Yoizen.yFlow.Infrastructure/src/ports/IIntervalPort';
import { InsertPersonalizedTableService } from './application/services/InsertPersonalizedTableService';
import { config as configApp } from '../../Yoizen.yFlow.Helpers/src/Config';
import consoleStamp from 'console-stamp';

if (!configApp.isDebug) {
    consoleStamp(console, {
        format: ':date(dd/mm/yyyy HH:MM:ss.l) :label'
    });
}

let tableCreationService: TableCreationIntervalService;
let intervalService: DailyIntervalService;
let reportService: ReportService;
let ftpService: FtpService;
let centralizeService: CentralizeService;
let logService: LogsService;
let insertPersonalizedTableService: InsertPersonalizedTableService;

const initializeApp = async () => {
    // Inicializo la base de datos
    importReplaceAll();

    let { dailyIntervalPort } = await InitIntervalServices();
    let { systemStatusPort, historyDailyPort, configurationPort, flowPort, reportPort, logPort, personalizedTablePort } = await InityFLowServices();
    let { cache } = await InitCache();
    let apiPort = new AxiosExternalApiAdapter();

    await waitForConnection(dailyIntervalPort);

    // Inicializo los servicios
    logger.info('Inicializando servicios');
    tableCreationService = new TableCreationIntervalService(systemStatusPort, dailyIntervalPort, historyDailyPort);
    await tableCreationService.Init();

    intervalService = new DailyIntervalService(systemStatusPort, dailyIntervalPort, historyDailyPort);
    await intervalService.Init();

    reportService = new ReportService(reportPort, cache);

    insertPersonalizedTableService = new InsertPersonalizedTableService(personalizedTablePort, cache);

    ftpService = new FtpService(systemStatusPort, dailyIntervalPort, historyDailyPort, configurationPort, flowPort);
    await ftpService.Init();

    centralizeService = new CentralizeService(apiPort, systemStatusPort);
    await centralizeService.Init();

    logService = new LogsService(apiPort, systemStatusPort, logPort);
    await logService.Init();

    if (!configApp.isContingencyBot) {
        await tableCreationService.Process();
        await insertPersonalizedTableService.Process();
        await centralizeService.Process();
        await logService.Process();
        await intervalService.Process();
    }

    await ftpService.Process();
    await reportService.Process();
};

async function waitForConnection(repository: IIntervalPort) {
    await repository.connect();
}

initializeApp().catch(error => {
    logger.error('Error al inicializar la aplicación:', error);
    process.exit(9);
});
