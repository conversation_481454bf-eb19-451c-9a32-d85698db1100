import moment from "moment";
import { intervalDateTime } from "../../../../Yoizen.yFlow.Web/models/historical/interval";
import { SystemStatus, SystemStatusType } from "../../../../Yoizen.yFlow.Domain/src/models/statistics/SystemStatus";
import { ISystemStatusPort } from '../../../../Yoizen.yFlow.Infrastructure/src/ports/ISystemStatusPort';
import { IExternalApiPort } from '../../../../Yoizen.yFlow.Infrastructure/src/ports/IExternalApiPort';
import { config } from "../../config";
import { config as globalConfig } from "../../../../Yoizen.yFlow.Helpers/src/Config";
import DailyByFlow from "../../../../Yoizen.yFlow.Web/models/historical/dailyByFlow";
import CryptoJS from 'crypto-js';
import { logger } from "../../../../Yoizen.yFlow.Helpers/src/Logger";

type DailyProcessTypes = SystemStatusType.LastIntervalCentralize;
type LastDailyInterval = {
  [key in DailyProcessTypes]: SystemStatus | null;
};

export class CentralizeService {
  systemStatusPort: ISystemStatusPort;
  externalApiPort: IExternalApiPort;
  lastDailyInterval: LastDailyInterval;

  constructor(
    externalApiPort: IExternalApiPort,
    systemStatusPort: ISystemStatusPort
  ) {
    this.systemStatusPort = systemStatusPort;
    this.externalApiPort = externalApiPort;
    // Inicializo los intervalos
    this.lastDailyInterval = {
      [SystemStatusType.LastIntervalCentralize]: null
    };

  }

  private async Consolidate(systemStatus: SystemStatus, currentDateInterval: moment.Moment): Promise<SystemStatus> {
    // Verifico cual fue el ultimo intervalo en consolidarse, si falto uno o mas, voy consolidando hasta
    let lastInterval: moment.Moment = systemStatus.Date;

    while (lastInterval.valueOf() <= currentDateInterval.valueOf()) {
      try {
        if (lastInterval.hour() === config.hourCentralize &&
          lastInterval.minute() === config.minuteCentralize) {
          logger.info(`Voy a consolidar el intervalo ${lastInterval.toLocaleString()} del tipo ${systemStatus.type}`);
          const initInterval: moment.Moment = lastInterval.clone().add(-1, 'day');
          const endInterval: moment.Moment = lastInterval.clone().add(-1, 'millisecond');

          let infoToConsolidate = await DailyByFlow.getGroupDataByFlow(initInterval, endInterval)
            .then((records: DailyByFlow[]) => {
              let totals: DailyByFlow | any = new DailyByFlow();
              totals.newCase = 0;
              totals.transferredToYSocial = 0;
              totals.closedByYFlow = 0;
              totals.newMessage = 0;
              totals.hsmCase = 0;
              totals.caseAbandoned = 0;
              totals.monthlyUsers = 0;

              if (records.length > 0) {
                /*
                 Al `reduce` se le pasa como initialValue el registro con todos los valores en CERO, que se utilizará para empezar
                 a acumular. Luego, `accumulator` va a tener siempre el valor anterior y `currentValue` es el registro actual,
                 que se irá acumulando en `accumulator`
                 */
                totals = records.reduce((accumulator: DailyByFlow | any, currentValue: DailyByFlow | any) => {
                  if (typeof (currentValue.newCase) === 'number' && currentValue.newCase > 0) {
                    accumulator.newCase += currentValue.newCase;
                  }
                  if (typeof (currentValue.transferredToYSocial) === 'number' && currentValue.transferredToYSocial > 0) {
                    accumulator.transferredToYSocial += currentValue.transferredToYSocial;
                  }
                  if (typeof (currentValue.closedByYFlow) === 'number' && currentValue.closedByYFlow > 0) {
                    accumulator.closedByYFlow += currentValue.closedByYFlow;
                  }
                  if (typeof (currentValue.newMessage) === 'number' && currentValue.newMessage > 0) {
                    accumulator.newMessage += currentValue.newMessage;
                  }
                  if (typeof (currentValue.hsmCase) === 'number' && currentValue.hsmCase > 0) {
                    accumulator.hsmCase += currentValue.hsmCase;
                  }
                  if (typeof (currentValue.caseAbandoned) === 'number' && currentValue.caseAbandoned > 0) {
                    accumulator.caseAbandoned += currentValue.caseAbandoned;
                  }
                  if (typeof (currentValue.monthlyUsers) === 'number' && currentValue.monthlyUsers > 0) {
                    accumulator.monthlyUsers += currentValue.monthlyUsers;
                  }

                  return accumulator;
                }, totals);
              }

              return {
                date: initInterval.format(),
                type: "yFlow",
                client: globalConfig.client,
                flows: records.map((item: any) => {
                  return {
                    id: item.flow.id,
                    name: item.flow.name,
                    type: item.flow.channel,
                    flow_type: null, // Todo: Es el type de si es "bot" o si es "lite", despues hay que sumarlo para calcularlo
                    new_cases: item.newCase,
                    new_messages: item.newMessage,
                    closed_cases: item.closedByYFlow,
                    derived_cases: item.transferredToYSocial,
                    hsm_case: totals.hsmCase,
                    case_abandoned: totals.caseAbandoned,
                    monthly_users: totals.monthlyUsers
                  }
                }),
                global: {
                  new_cases: totals.newCase,
                  new_messages: totals.newMessage,
                  closed_cases: totals.closedByYFlow,
                  derived_cases: totals.transferredToYSocial,
                  hsm_case: totals.hsmCase,
                  case_abandoned: totals.caseAbandoned,
                  monthly_users: totals.monthlyUsers
                },
                counters: Object.entries(totals).map(([key, value]) => {
                  const snakeCase = key.replace(/([A-Z])/g, '_$1').toLowerCase();

                  return {
                    name: `yflow_${snakeCase}`,
                    value: value,
                    reference: 0
                  }
                })
              };
            })
            .catch((err) => {
              logger.info(`Ocurrió un error en getGroupDataByFlow, ${err}`);
              return null;
            });

          const json = JSON.stringify(infoToConsolidate);
          const saltedHash = CryptoJS.HmacSHA256(json, config.salt);
          const hashInBase64 = CryptoJS.enc.Hex.stringify(saltedHash).toLowerCase();
          const headers = {
            'User-Agent': "yFlow",
            'Content-Type': 'application/json',
            'x-social-hash': `sha256=${hashInBase64}`
          };

          try {
            const result = await this.externalApiPort.SendData(config.urlCentralize, json, { headers: headers });
            if (result.status === 200) {
              logger.info(`Se publicaron las estadísticas centralizadas del intervalo ${lastInterval.toLocaleString()} del tipo ${systemStatus.type}`);
            } else {
              logger.error(`Ocurrió un error al publicar las estadísticas centralizadas del intervalo ${lastInterval.toLocaleString()} del tipo ${systemStatus.type}. Error: ${result}`);
            }
          }
          catch (error) {
            logger.error(`Ocurrió un error publicando la información consolidada del intervalo ${lastInterval.toLocaleString()} del tipo ${systemStatus.type} enviando como JSON: ${json}. Error: ${error}`);
          }
        }
      }
      catch (error) {
        logger.error(`Ocurrió un error al intentar consolidar el intervalo ${lastInterval.toLocaleString()} del tipo ${systemStatus.type}. Error: ${error}`);
      }

      await this.systemStatusPort.Save(systemStatus);

      systemStatus.Date = systemStatus.Date.add(30, 'minute');
      lastInterval = systemStatus.Date;
    }

    return systemStatus;
  }

  async Process() {
    const now = moment();
    const currentDateInterval = intervalDateTime(now).add(-30, 'minute');

    for (let key in this.lastDailyInterval) {
      let value: SystemStatus = this.lastDailyInterval[key];
      logger.info(`voy a procesar el registro ${key} del intervalo ${value.Date.toLocaleString()}`);
      await this.Consolidate(value, currentDateInterval);
      logger.info(`terminé de procesar el registro ${key} del intervalo ${value.Date.toLocaleString()}`);
    }

    setTimeout(this.Process.bind(this), 60000 * 5);
  }

  async Init() {
    const now = moment();
    for (let key in this.lastDailyInterval) {
      await this.GetIntervalSystemStatus(now, key as SystemStatusType);
    }
  }

  private async GetIntervalSystemStatus(now: moment.Moment, systemStatusType: SystemStatusType) {
    // Obtengo la última fecha del intervalo ejecutado correctamente si no la tengo.
    if (!this.lastDailyInterval[systemStatusType]) {
      let status = await this.systemStatusPort.Get(systemStatusType);
      if (!status) {
        status = new SystemStatus();
        status.type = systemStatusType;
        let date = moment(now);
        date.set({ hour: 0, minute: 0, second: 0, millisecond: 0 });
        status.Date = date;
        logger.info(`No tengo registros en la bd, creo hoy el último intervalo ${this.lastDailyInterval[systemStatusType]} del tipo dailyByFlow`);
        this.lastDailyInterval[systemStatusType] = status;

        status = await this.systemStatusPort.Save(status);
      }
      logger.info(`Ultimo intervalo obtenido ${JSON.stringify(status)} del tipo ${systemStatusType}`);
      this.lastDailyInterval[systemStatusType] = status;
    }
  }
}