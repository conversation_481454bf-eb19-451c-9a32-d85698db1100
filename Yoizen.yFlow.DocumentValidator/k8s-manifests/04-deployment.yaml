apiVersion: apps/v1
kind: Deployment
metadata:
  name: yflow-dni-validator-qa
  namespace: yflow-dni-validator-qa
spec:
  replicas: 1
  selector:
    matchLabels:
      app: yflow-dni-validator-qa
  template:
    metadata:
      labels:
        app: yflow-dni-validator-qa
    spec:
      imagePullSecrets:
      - name: dockerhub-secret
      containers:
      - name: yflow-dni-validator-qa
        image: yoizensa/yflow-document-validator:1.0.0.0-rcx
        imagePullPolicy: Always
        env:
        - name: apiKey
          valueFrom:
            secretKeyRef:
              name: yflow-dni-validator-qa-secret
              key: API_KEY
        ports:
        - containerPort: 3000
        resources:
          requests:
            memory: "250Mi"
            cpu: "50m"
          limits:
            memory: "300Mi"
            cpu: "300m"
        readinessProbe:
          httpGet:
            path: /api/health_check
            port: 3000
          initialDelaySeconds: 10
          periodSeconds: 10
          timeoutSeconds: 5
          successThreshold: 1
          failureThreshold: 3
        livenessProbe:
          httpGet:
            path: /api/health_check
            port: 3000
          initialDelaySeconds: 30
          periodSeconds: 10
          timeoutSeconds: 5
          successThreshold: 1
          failureThreshold: 3
      nodeSelector:
        kubernetes.azure.com/agentpool: development
