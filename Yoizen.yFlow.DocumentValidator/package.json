{"name": "yoizen.yflow.documentvalidator", "version": "1.0.0", "description": "", "main": "index.js", "scripts": {"start": "node index.js", "startDocker": "node /home/<USER>/app/src/index.js", "test": "echo \"Error: no test specified\" && exit 1"}, "author": "", "license": "ISC", "dependencies": {"@zxing/library": "^0.21.0", "axios": "^1.6.8", "express": "^4.18.3", "mrz-detection": "0.4.1", "mrz-scanner": "^1.2.3", "text-encoding": "^0.7.0"}, "devDependencies": {"nodemon": "^3.1.0"}}