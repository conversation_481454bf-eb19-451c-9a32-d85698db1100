# Configuración de Compresión de Flow Versions

## Descripción

Este documento explica cómo controlar la compresión gzip de los JSONs de las versiones de flujo en yFlow.

## Variable de Entorno

### `COMPRESS_FLOW_VERSIONS`

**Valores permitidos:**
- `true` (por defecto): Los JSONs de flow versions se comprimen con gzip antes de guardarse en la base de datos
- `false`: Los JSONs de flow versions se guardan sin compresión

**Ejemplo de configuración:**

```bash
# Para desactivar la compresión
COMPRESS_FLOW_VERSIONS=false

# Para activar la compresión (comportamiento por defecto)
COMPRESS_FLOW_VERSIONS=true
```

## Comportamiento

### Con Compresión Activada (COMPRESS_FLOW_VERSIONS=true)
- Los JSONs de flow versions se comprimen con gzip y se codifican en base64 antes del almacenamiento
- Al leer, se detecta automáticamente si los datos están comprimidos y se descomprimen
- **Ventaja:** Menor uso de espacio en base de datos
- **Desventaja:** Ligero overhead de CPU para compresión/descompresión

### Con Compresión Desactivada (COMPRESS_FLOW_VERSIONS=false)
- Los JSONs de flow versions se almacenan directamente como texto plano
- No hay procesamiento adicional en lectura/escritura
- **Ventaja:** Mejor rendimiento, datos legibles directamente en la base de datos
- **Desventaja:** Mayor uso de espacio en base de datos

## Compatibilidad

El sistema mantiene compatibilidad hacia atrás:
- Las versiones de flujo existentes (comprimidas) se pueden leer correctamente
- Las nuevas versiones se guardan según la configuración actual
- El cambio no afecta flujos ya existentes

## Logs

El sistema registra en los logs cuando se guarda una nueva versión:
- `[COMPRESSION] Flow version comprimida con gzip` - cuando está activada
- `[COMPRESSION] Flow version guardada sin compresión` - cuando está desactivada

## Recomendaciones

- **Para desarrollo/testing:** Usar `COMPRESS_FLOW_VERSIONS=false` para facilitar debugging
- **Para producción:** Evaluar según el volumen de datos y recursos disponibles
- **Para migración:** Cambiar gradualmente, el sistema es compatible con ambos formatos
