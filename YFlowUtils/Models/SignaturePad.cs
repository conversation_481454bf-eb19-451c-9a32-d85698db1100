﻿using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Threading.Tasks;

namespace YFlowUtils.Models
{
	public class SignaturePad
	{
		public string Token { get; set; }
		public string Base64image { get; set; }

		[Newtonsoft.Json.JsonIgnore]
		public string SignatureGuidFile
		{
			get
			{
				var file = Path.Combine(Startup.ContentRoot	, @"../../guid.txt");
				return file;
			}
		}

		/// <summary>
		/// Devuelve el nombre y ruta completa del archivo con los casos respondidos por encuesta (este archivo es temporal para 
		/// poder ordenar los casos en el archivo)
		/// </summary>
		[Newtonsoft.Json.JsonIgnore]
		public string SignatureGuidTempFile
		{
			get
			{
				var file = Path.Combine(Startup.ContentRoot, @"../../guidtemp.txt");
				return file;
			}

		}
		#region Static Fields

		private static object shared = new object();

		#endregion

		/// <summary>
		/// Devuelve si una firma ya fue realizada
		/// </summary>
		/// <param name="guid">El código de la firma</param>
		/// <param name="caseId">El código del caso</param>
		/// <returns><code>true</code> si la firma ya fue respondido; en caso contrario, <code>false</code></returns>
		public bool IsGuidAlreadyAnswered(int messageId, string client)
		{
			if (!File.Exists(this.SignatureGuidFile))
				return false;

			using (var fs = new FileStream(this.SignatureGuidFile, FileMode.Open, FileAccess.Read, FileShare.Read))
			using (var sr = new StreamReader(fs))
			{
				while (!sr.EndOfStream)
				{
					try
					{
						string[] currentSignature = sr.ReadLine().Split(',');
						int currentMessageId = int.Parse(currentSignature[0]);
						string currentClient = currentSignature[1];

						if (currentMessageId == messageId)
							if (client.Equals(currentClient, StringComparison.InvariantCultureIgnoreCase))
								return true;
							else if (messageId < currentMessageId)
								break;
					} catch { }
				}
			}

			return false;
		}

		public void SaveSignature(int messageId, string client)
		{
			lock (shared)
			{
				if (!File.Exists(this.SignatureGuidFile))
				{
					using (StreamWriter sw = new StreamWriter(this.SignatureGuidFile, false))
					{
						sw.WriteLine($"{messageId.ToString()},{client}");
					}
				}
				else
				{
					using (StreamReader sr = new StreamReader(this.SignatureGuidFile))
					using (StreamWriter sw = new StreamWriter(this.SignatureGuidTempFile))
					{
						bool written = false;
						while (!sr.EndOfStream)
						{
							try 
							{
								string[] currentSignature = sr.ReadLine().Split(',');
								int currentMessageId = int.Parse(currentSignature[0]);
								string currentClient = currentSignature[1];

								if (messageId < currentMessageId && !written)
								{
									sw.WriteLine($"{messageId.ToString()},{client}");
									written = true;
								}

								sw.WriteLine(string.Join(",", currentSignature));
							} catch { }
						}

						if (!written)
							sw.WriteLine($"{messageId.ToString()},{client}");
					}

					File.Delete(this.SignatureGuidFile);
					File.Move(this.SignatureGuidTempFile, this.SignatureGuidFile);
				}
			}
		}
	}
}
