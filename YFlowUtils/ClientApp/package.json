{"name": "Y<PERSON><PERSON><PERSON><PERSON>s", "version": "0.0.0", "license": "MIT", "scripts": {"ng": "ng", "start": "ng serve --extract-css", "build": "ng build --extract-css", "build:ssr": "npm run build -- --app=ssr --output-hashing=media", "test": "ng test", "lint": "ng lint", "e2e": "ng e2e"}, "private": true, "dependencies": {"@angular/animations": "^5.2.0", "@angular/common": "^5.2.0", "@angular/compiler": "^5.2.0", "@angular/core": "^5.2.0", "@angular/forms": "^5.2.0", "@angular/http": "^5.2.0", "@angular/platform-browser": "^5.2.0", "@angular/platform-browser-dynamic": "^5.2.0", "@angular/platform-server": "^5.2.0", "@angular/router": "^5.2.0", "@nguniversal/module-map-ngfactory-loader": "^5.0.0-beta.5", "angular2-signaturepad": "^3.0.4", "angular5-toaster": "^1.0.2", "aspnet-prerendering": "^3.0.1", "bootstrap": "^3.4.1", "core-js": "^2.4.1", "crypto-js": "4.1.1", "rxjs": "^5.5.6", "zone.js": "^0.8.19"}, "devDependencies": {"@angular/cli": "~1.7.0", "@angular/compiler-cli": "^5.2.0", "@angular/language-service": "^5.2.0", "@types/jasmine": "~2.8.3", "@types/jasminewd2": "~2.0.2", "@types/node": "~6.0.60", "codelyzer": "^4.0.1", "jasmine-core": "~2.8.0", "jasmine-spec-reporter": "~4.2.1", "karma": "~2.0.0", "karma-chrome-launcher": "~2.2.0", "karma-coverage-istanbul-reporter": "^1.2.1", "karma-jasmine": "~1.1.0", "karma-jasmine-html-reporter": "^0.2.2", "protractor": "~5.1.2", "ts-node": "~4.1.0", "tslint": "~5.9.1", "typescript": "~2.5.3"}}