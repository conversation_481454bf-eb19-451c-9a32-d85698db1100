FROM node:20.14.0 AS builder

# Instala jq
RUN curl -L -o /usr/bin/jq https://github.com/stedolan/jq/releases/download/jq-1.6/jq-linux64 && \
    chmod +x /usr/bin/jq

WORKDIR /app

#Copio los repos necesarios para armar el executor
COPY Yoizen.yFlow.Domain ./Yoizen.yFlow.Domain
COPY Yoizen.yFlow.Helpers ./Yoizen.yFlow.Helpers
COPY Yoizen.yFlow.Infrastructure ./Yoizen.yFlow.Infrastructure
COPY Yoizen.yFlow.Web ./Yoizen.yFlow.Web
COPY Yoizen.yFlow.IntervalServices ./Yoizen.yFlow.IntervalServices
#Reemplazo el package.json de la web por el del executor
COPY Yoizen.yFlow.Web/packageForIntervalServices.json ./Yoizen.yFlow.Web/package.json
COPY Yoizen.yFlow.IntervalServices/packageDocker.json ./Yoizen.yFlow.IntervalServices/package.json
COPY Yoizen.yFlow.IntervalServices/tsconfig.docker.json ./Yoizen.yFlow.IntervalServices/tsconfig.docker.json

# Actualiza la versión en el package.json del executor
ARG versionName
RUN jq --arg version "$versionName" '.version = $version' ./Yoizen.yFlow.IntervalServices/package.json > ./Yoizen.yFlow.IntervalServices/tmp.package.json && mv ./Yoizen.yFlow.IntervalServices/tmp.package.json ./Yoizen.yFlow.IntervalServices/package.json

#Quito los type module de los package.json de todos los proyectos relacionados.
RUN sed '/"type": "module"/d' ./Yoizen.yFlow.Domain/package.json > ./Yoizen.yFlow.Domain/tmp.package.json && mv ./Yoizen.yFlow.Domain/tmp.package.json ./Yoizen.yFlow.Domain/package.json
RUN sed '/"type": "module"/d' ./Yoizen.yFlow.Helpers/package.json > ./Yoizen.yFlow.Helpers/tmp.package.json && mv ./Yoizen.yFlow.Helpers/tmp.package.json ./Yoizen.yFlow.Helpers/package.json
RUN sed '/"type": "module"/d' ./Yoizen.yFlow.Infrastructure/package.json > ./Yoizen.yFlow.Infrastructure/tmp.package.json && mv ./Yoizen.yFlow.Infrastructure/tmp.package.json ./Yoizen.yFlow.Infrastructure/package.json
RUN sed '/"type": "module"/d' ./Yoizen.yFlow.Web/package.json > ./Yoizen.yFlow.Web/tmp.package.json && mv ./Yoizen.yFlow.Web/tmp.package.json ./Yoizen.yFlow.Web/package.json
RUN sed '/"type": "module"/d' ./Yoizen.yFlow.IntervalServices/package.json > ./Yoizen.yFlow.IntervalServices/tmp.package.json && mv ./Yoizen.yFlow.IntervalServices/tmp.package.json ./Yoizen.yFlow.IntervalServices/package.json

RUN rm -rf ./Yoizen.yFlow.Domain/package-lock.json
RUN rm -rf ./Yoizen.yFlow.Helpers/package-lock.json
RUN rm -rf ./Yoizen.yFlow.Infrastructure/package-lock.json
RUN rm -rf ./Yoizen.yFlow.Web/package-lock.json
RUN rm -rf ./Yoizen.yFlow.IntervalServices/package-lock.json

#Instalo las dependencias de los proyectos relacionados.
RUN cd ./Yoizen.yFlow.Domain && npm install
RUN cd ./Yoizen.yFlow.Helpers && npm install
RUN cd ./Yoizen.yFlow.Infrastructure && npm install
RUN cd ./Yoizen.yFlow.Web && npm install
RUN cd ./Yoizen.yFlow.IntervalServices && npm install

#Compilo el proyecto executor
RUN cd ./Yoizen.yFlow.IntervalServices && npm run buildDocker

##############################################################################################
# Finalizado el build generamos la imagen final con un runtime más liviano de node
FROM node:20.14.0-slim AS runtime

# Creación de directorios para despliegue de dependencias de node y aplicación
# Se crean dentro de la home del usuario "node"
# El usuario "node" no root viene en la imagen de node y evita ejecutar contenedores como root por seguridad
# https://docs.docker.com/engine/security/#linux-kernel-capabilities
RUN mkdir -p /home/<USER>/app/node_modules

# Creación del directorio "/home/<USER>/StorageYflow" sin asignar permisos específicos
RUN mkdir -p /home/<USER>/StorageYflow

# Creación del directorio "/home/<USER>/app/storage" sin asignar permisos específicos
RUN mkdir -p /home/<USER>/app/storage

# Definición del directorio de trabajo de la aplicación
WORKDIR /home/<USER>/app

# Copiamos todas las dependecias de los proyectos relacionados.
COPY --from=builder /app/Yoizen.yFlow.Domain/node_modules/ /home/<USER>/app/node_modules
COPY --from=builder /app/Yoizen.yFlow.Helpers/node_modules/ /home/<USER>/app/node_modules
COPY --from=builder /app/Yoizen.yFlow.Infrastructure/node_modules/ /home/<USER>/app/node_modules
COPY --from=builder /app/Yoizen.yFlow.Web/node_modules/ /home/<USER>/app/node_modules
COPY --from=builder /app/Yoizen.yFlow.IntervalServices/node_modules/ /home/<USER>/app/node_modules

# Copiamos el código de la aplicación al directorio dentro del contenedor.
COPY --from=builder /app/Yoizen.yFlow.IntervalServices/dist/ /home/<USER>/app/dist
COPY --from=builder /app/Yoizen.yFlow.IntervalServices/package.json /home/<USER>/app/package.json
COPY --from=builder /app/Yoizen.yFlow.IntervalServices/package-lock.json /home/<USER>/app/package-lock.json
COPY --from=builder /app/Yoizen.yFlow.IntervalServices/locales/ /home/<USER>/app/locales

# Quito los type module de los package.json del executor
RUN sed '/"type": "module"/d' package.json > tmp.package.json && mv tmp.package.json package.json

#EXPOSE 3000
CMD [ "npm", "run" ,"startDocker" ]
#CMD ["tail", "-f", "/dev/null"]