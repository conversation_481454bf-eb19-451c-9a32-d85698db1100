const { Sequelize, Op } = require('sequelize');
const sequelize = require('../helpers/sequelize');
const myCache = require('../helpers/mycache');
const client = process.env.client;
const zlib = require('zlib');

/**
 * @classdesc Not a real class but an interface. Etc...
 *
 * @name Flow
 * @class
 * @typedef {Object} Flow
 * @property {String} name Nombre del flujo
 * @property {String} channel Red social
 * @property {Number} user_id - Usuario que lo creó
 * @property {Number} company_id - Companía asociada, cuando tengo multiples instancias
 * @property {String} type Tipo de flow
 * @property {Function} findAllByCompany {@link Flow#findAllByCompany}
 */
var Flow = sequelize.define('flow', {
    name: {
        type: Sequelize.STRING
    },
    channel: {
        type: Sequelize.STRING
    },
    user_id: {
        type: Sequelize.INTEGER
    },
    company_id: {
        type: Sequelize.INTEGER
    },
    type: {
        type: Sequelize.STRING
    },
    master_flow_id: {
        type: Sequelize.INTEGER
    }
}, {
    paranoid: true
});

/**
 * @method
 * @name Flow#findAllByCompany
 * @param {Number} companyId id de la companía
 * @param {Number} userId id del usuario logeado
 * @returns {Promise<Flow[]>} lista de flows
 */
Flow.findAllByCompany = async function (companyId, userId, includeModules) {
    let where = {
        company_id: companyId
    };

    if (!includeModules) {
        where.master_flow_id = {
            [Op.eq]: null
        };
    }

    return Flow.findAll({
        where: where,
        include: [
            {
                model: FlowVersion,
                as: 'ActiveStagingVersion',
                attributes: {
                    exclude: ['blob', 'stats']
                }
            },
            {
                model: FlowVersion,
                as: 'ActiveProductionVersion',
                attributes: {
                    exclude: ['blob', 'stats']
                }
            },
            {
                model: Permisssion,
                where: {
                    userId: userId/*,
                    [Op.or]: [{
                        canEdit: true
                    },{
                        canPublish: true
                    },{
                        canSeeStatistics: true
                    }]*/
                }
            }
        ],
        order: [
            ['updated_at', 'DESC']
        ]
    }).then((result) => {
        return result
    }).catch((err) => {
        console.error(`error en flow.findAllByCompany, ${err}`);
        throw err;
    });
}

/**
 * @method
 * @name Flow#findOneById
 * @param {Number} id id del flujo
 * @param {Number} companyId id de la companía
 * @param {Number} userId id del usuario logeado
 * @returns {Promise<Flow>} lista de flows
 */
Flow.findOneById = async function (id, companyId, userId, showPrivateVariables = false) {
    let where = {};
    let include = [
        {
            model: FlowVersion,
            as: 'ActiveStagingVersion',
        },
        {
            model: FlowVersion,
            as: 'ActiveProductionVersion',
        },
        {
            model: FlowVersion,
            as: 'ActiveProductionVersionMaster',
        }
    ];

    where.id = id;
    if (companyId !== undefined && companyId !== null) {
        where.company_id = parseInt(companyId);
    }

    if (userId !== undefined && userId !== null) {
        include.push({
            model: Permisssion,
            where: {
                userId: userId/*,
                [Op.or]: [{
                    canEdit: true
                },{
                    canPublish: true
                },{
                    canSeeStatistics: true
                }]*/
            }
        });
    }

    return await Flow.findOne({
        where: where,
        include: include
    }).then(async (result) => {
        if (!result)
            return null;

        if (result.ActiveStagingVersion !== null && result.ActiveStagingVersion !== undefined) {
            result.ActiveStagingVersion.blob = await FlowVersion.findByIdAndVersion(result.ActiveStagingVersion.blob, result.id, result.ActiveStagingVersion.number, showPrivateVariables);

            if (result.ActiveProductionVersion !== null && result.ActiveProductionVersion !== undefined) {
                if (result.ActiveStagingVersion.number === result.ActiveProductionVersion.number) {
                    result.ActiveProductionVersion.blob = result.ActiveStagingVersion.blob;
                } else {
                    result.ActiveProductionVersion.blob = await FlowVersion.findByIdAndVersion(result.ActiveProductionVersion.blob, result.id, result.ActiveProductionVersion.number, showPrivateVariables);
                }
            }

            if (result.ActiveProductionVersionMaster !== null && result.ActiveProductionVersionMaster !== undefined) {
                if (result.ActiveStagingVersion.number === result.ActiveProductionVersionMaster.number) {
                    result.ActiveProductionVersionMaster.blob = result.ActiveStagingVersion.blob;
                } else {
                    result.ActiveProductionVersionMaster.blob = await FlowVersion.findByIdAndVersion(result.ActiveProductionVersionMaster.blob, result.id, result.ActiveProductionVersionMaster.number, showPrivateVariables);
                }
            }
        }

        return result
    }).catch((err) => {
        console.error(`error en flow.findOneById, ${err}`);
        throw err;
    });
}

/**
 * @method
 * @name Flow#findOneById
 * @param {Number} id id del flujo
 * @param {Number} companyId id de la companía
 * @param {Number} userId id del usuario logeado
 * @returns {Promise<Flow>} lista de flows
 */
Flow.findAllModulesById = async function (id, companyId, userId, showPrivateVariables = false, includeVersion = true) {
    let include = [];
    let where = {
        master_flow_id: id
    }

    if (companyId !== undefined && companyId !== null) {
        where.company_id = companyId;
    }

    if (userId !== undefined && userId !== null) {
        include.push({
            model: Permisssion,
            where: {
                userId: userId/*,
                [Op.or]: [{
                    canEdit: true
                },{
                    canPublish: true
                },{
                    canSeeStatistics: true
                }]*/
            }
        });
    }

    if (includeVersion) {
        include.push({
            model: FlowVersion,
            as: 'ActiveStagingVersion',
        });
        include.push({
            model: FlowVersion,
            as: 'ActiveProductionVersion',
        });
        include.push({
            model: FlowVersion,
            as: 'ActiveProductionVersionMaster',
        })
    } else {
        include.push({
            model: FlowVersion,
            as: 'ActiveStagingVersion',
            attributes: {
                exclude: ['blob', 'stats']
            }
        });
        include.push({
            model: FlowVersion,
            as: 'ActiveProductionVersion',
            attributes: {
                exclude: ['blob', 'stats']
            }
        });
        include.push({
            model: FlowVersion,
            as: 'ActiveProductionVersionMaster',
            attributes: {
                exclude: ['blob', 'stats']
            }
        })
    }

    return await Flow.findAll({
        where: where,
        include: include
    }).then(async (results) => {
        if (includeVersion) {

            for (const result of results) {
                if (!result)
                    return null;

                if (result.ActiveStagingVersion !== null && result.ActiveStagingVersion !== undefined) {
                    result.ActiveStagingVersion.blob = await FlowVersion.findByIdAndVersion(result.ActiveStagingVersion.blob, result.id, result.ActiveStagingVersion.number, showPrivateVariables);

                    if (result.ActiveProductionVersion !== null && result.ActiveProductionVersion !== undefined) {
                        if (result.ActiveStagingVersion.number === result.ActiveProductionVersion.number) {
                            result.ActiveProductionVersion.blob = result.ActiveStagingVersion.blob;
                        } else {
                            result.ActiveProductionVersion.blob = await FlowVersion.findByIdAndVersion(result.ActiveProductionVersion.blob, result.id, result.ActiveProductionVersion.number, showPrivateVariables);
                        }
                    }

                    if (result.ActiveProductionVersionMaster !== null && result.ActiveProductionVersionMaster !== undefined) {
                        if (result.ActiveStagingVersion.number === result.ActiveProductionVersionMaster.number) {
                            result.ActiveProductionVersionMaster.blob = result.ActiveStagingVersion.blob;
                        } else {
                            result.ActiveProductionVersionMaster.blob = await FlowVersion.findByIdAndVersion(result.ActiveProductionVersionMaster.blob, result.id, result.ActiveProductionVersionMaster.number, showPrivateVariables);
                        }
                    }
                }
            }
        }
        return results;
    }).catch((err) => {
        console.error(`error en flow.findAllModulesById, ${err}`);
        throw err;
    });
}

Flow.findAllProductionModulesById = async function (id, showPrivateVariables = false, includeVersion = true) {
    let include = [];
    let where = {
        master_flow_id: id
    }

    if (includeVersion) {
        include.push({
            model: FlowVersion,
            as: 'ActiveProductionVersion',
        });
    }

    return await Flow.findAll({
        where: where,
        include: include
    }).then(async (results) => {
        if (includeVersion) {
            for (const result of results) {
                if (!result)
                    return null;

                if (result.ActiveProductionVersion === null || result.ActiveProductionVersion === undefined)
                    return null;

                result.ActiveProductionVersion.blob = await FlowVersion.findByIdAndVersion(result.ActiveProductionVersion.blob, result.id, result.ActiveProductionVersion.number, showPrivateVariables);
            }
        }
        return results;
    }).catch((err) => {
        console.error(`error en flow.findAllProductionModulesById, ${err}`);
        throw err;
    });
}

Flow.findAllTesterModulesById = async function (id, showPrivateVariables = false, includeVersion = true) {
    let include = [];
    let where = {
        master_flow_id: id
    }

    if (includeVersion) {
        include.push({
            model: FlowVersion,
            as: 'ActiveStagingVersion',
        });
    }

    return await Flow.findAll({
        where: where,
        include: include
    }).then(async (results) => {
        if (includeVersion) {
            for (const result of results) {
                if (!result)
                    return null;

                if (result.ActiveStagingVersion === null || result.ActiveStagingVersion === undefined)
                    return null;

                result.ActiveStagingVersion.blob = await FlowVersion.findByIdAndVersion(result.ActiveStagingVersion.blob, result.id, result.ActiveStagingVersion.number, showPrivateVariables);
            }
        }
        return results;
    }).catch((err) => {
        console.error(`error en flow.findAllTesterModulesById, ${err}`);
        throw err;
    });
}

/**
 * @method
 * @name Flow#findOneById
 * @param {Number} id id del flujo
 * @returns {Promise<Flow>} lista de flows
 */
Flow.findLastTesterById = async function (id, showPrivateVariables = false) {
    return await Flow.findOne({
        where: {
            id: id
        },
        include: [
            {
                model: FlowVersion,
                as: 'ActiveStagingVersion',
            }
        ]
    }).then(async (flow) => {
        if (!flow)
            return null;

        if (flow.ActiveStagingVersion === null || flow.ActiveStagingVersion === undefined)
            return null;


        flow.ActiveStagingVersion.blob = await FlowVersion.findByIdAndVersion(flow.ActiveStagingVersion.blob, flow.id, flow.ActiveStagingVersion.number, showPrivateVariables);

        if (typeof (flow.ActiveStagingVersion.blob) === 'string')
            flow.ActiveStagingVersion.blob = JSON.parse(flow.ActiveStagingVersion.blob);

        // Inicializar arrays si no existen para evitar errores de deserialización
        if (!flow.ActiveStagingVersion.blob.BlockGroups) {
            flow.ActiveStagingVersion.blob.BlockGroups = [];
        }
        if (!flow.ActiveStagingVersion.blob.BlockList) {
            flow.ActiveStagingVersion.blob.BlockList = [];
        }

        var modules = await Flow.findAllTesterModulesById(id, showPrivateVariables, true);
        modules.forEach(module => {
            if (module.ActiveStagingVersion !== null) {
                let moduleBlob = JSON.parse(module.ActiveStagingVersion.blob);
                // Verificar que los arrays del módulo existan antes de hacer push
                if (moduleBlob.BlockGroups && Array.isArray(moduleBlob.BlockGroups)) {
                    flow.ActiveStagingVersion.blob.BlockGroups.push(...moduleBlob.BlockGroups);
                }
                if (moduleBlob.BlockList && Array.isArray(moduleBlob.BlockList)) {
                    flow.ActiveStagingVersion.blob.BlockList.push(...moduleBlob.BlockList);
                }
            }
        });

        flow.isMasterModule = true;

        return flow
    }).catch((err) => {
        console.error(`error en flow.findLastTesterById, ${err}`);
        throw err;
    });
}

/**
 * @method
 * @name Flow#findOneById
 * @param {Number} id id del flujo
 * @returns {Promise<Flow>} lista de flows
 */
Flow.findLastProductionById = async function (id, showPrivateVariables = true) {

    let cacheKey = `${client}-${id}-prod`;
    if (myCache.has(cacheKey)) {
        let flow = myCache.get(cacheKey);
        return flow;
    }

    return await Flow.findOne({
        where: {
            id: id
        },
        include: [
            {
                model: FlowVersion,
                as: 'ActiveProductionVersion',
            }
        ]
    }).then(async (flow) => {
        if (!flow)
            return null;

        if (flow.ActiveProductionVersion === null || flow.ActiveProductionVersion === undefined)
            return null;

        flow.ActiveProductionVersion.blob = await FlowVersion.findByIdAndVersion(flow.ActiveProductionVersion.blob, flow.id, flow.ActiveProductionVersion.number, showPrivateVariables);

        if (typeof (flow.ActiveProductionVersion.blob) === 'string')
            flow.ActiveProductionVersion.blob = JSON.parse(flow.ActiveProductionVersion.blob);

        // Inicializar arrays si no existen para evitar errores de deserialización
        if (!flow.ActiveProductionVersion.blob.BlockGroups) {
            flow.ActiveProductionVersion.blob.BlockGroups = [];
        }
        if (!flow.ActiveProductionVersion.blob.BlockList) {
            flow.ActiveProductionVersion.blob.BlockList = [];
        }

        var modules = await Flow.findAllProductionModulesById(id, showPrivateVariables, true);
        modules.forEach(module => {
            if (module.ActiveProductionVersion !== null) {
                let moduleBlob = JSON.parse(module.ActiveProductionVersion.blob);
                // Verificar que los arrays del módulo existan antes de hacer push
                if (moduleBlob.BlockGroups && Array.isArray(moduleBlob.BlockGroups)) {
                    flow.ActiveProductionVersion.blob.BlockGroups.push(...moduleBlob.BlockGroups);
                }
                if (moduleBlob.BlockList && Array.isArray(moduleBlob.BlockList)) {
                    flow.ActiveProductionVersion.blob.BlockList.push(...moduleBlob.BlockList);
                }
            }
        });

        flow.isMasterModule = true;

        if (myCache.has(cacheKey)) {
            myCache.del(cacheKey);
        }
        myCache.set(cacheKey, flow);

        return flow
    }).catch((err) => {
        console.error(`error en flow.findLastProductionById, ${err}`);
        throw err;
    });
}

/**
 * @method
 * @name Flow#findOneById
 * @param {Number} id id del flujo
 * @returns {Promise<Flow>} lista de flows
 */
Flow.findOneByIdWithoutVersion = async function (id, companyId) {
    return await Flow.findOne({
        where: {
            id: id,
            company_id: companyId
        },
    }).then((result) => {
        return result;
    }).catch((err) => {
        console.error(`error en flow.findOneByIdWithoutVersion, ${err}`);
        throw err;
    });
}

const User = require('./user');
const FlowVersion = require('./flow-version');
Flow.belongsTo(User, {
    foreignKey: 'user_id'
});
Flow.hasMany(FlowVersion);
Flow.belongsTo(FlowVersion, { foreign_key: 'active_staging_version_id', as: 'ActiveStagingVersion' });
Flow.belongsTo(FlowVersion, { foreign_key: 'active_production_version_id', as: 'ActiveProductionVersion' });
Flow.belongsTo(FlowVersion, { foreign_key: 'active_production_version_master_id', as: 'ActiveProductionVersionMaster' });
const Permisssion = require('./user-permission-flow');
const { or } = require('sequelize');
Flow.hasMany(Permisssion, { foreignKey: 'flow_id' });
const Report = require('./report');
const { getFlowVersion } = require('../helpers/flowVersionManager');
Flow.hasMany(Report, { foreignKey: 'flow_id' })

module.exports = Flow;
