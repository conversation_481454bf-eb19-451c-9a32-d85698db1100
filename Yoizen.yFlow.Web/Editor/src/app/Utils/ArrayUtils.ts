export class ArrayUtils {
  static getUniqueName(baseName : string, array: any[], compareFcn : (value:string, instance) => boolean, separator: string = " ") : string {
    if (typeof(separator) === 'undefined' || separator === null) {
      separator = " ";
    }

    var exist = false;
    var currentName = baseName;
    var iterationNumber = 1;
    do {
      exist = false;
      array.forEach(element => {
        exist = exist || compareFcn(currentName, element);
      });
      if(exist) {
        currentName = baseName + separator + iterationNumber.toString();
        iterationNumber += 1;
      }
    }
    while( exist );
    return currentName;
  }

  static getUniqueNameStartingAt(baseName : string, array: any[], startAt: number = 1, compareFcn : (value: string, instance) => boolean) : string {
    var exist = false;
    var currentName = baseName;
    var iterationNumber = startAt;
    do {
      exist = false;
      array.forEach(element => {
        exist = exist || compareFcn(currentName, element);
      });
      if(exist) {
        currentName = baseName + " " + iterationNumber.toString();
        iterationNumber += 1;
      }
    }
    while( exist );
    return currentName;
  }
}
