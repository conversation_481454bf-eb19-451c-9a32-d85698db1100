import { environment } from '../../environments/environment';
import jwtDecode from "jwt-decode";
import * as moment from 'moment';

export function getBaseUrl(): string {
  return window['virtual_dir_path'] ? window['virtual_dir_path'] : '/';
}

export function getToken(): string {
  if (environment.production || environment.staging) {
    return window['access_token'];
  }
  else {
    // usuario administrador
    return 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJodHRwOi8veWZsb3cueW9pemVuLmNvbSIsIm5hbWUiOiJBZG1pbmlzdHJhZG9yIiwidWlkIjoiMSIsImNpZCI6IjEiLCJjbmFtZSI6ImRlZmF1bHQiLCJoYXNoIjoiIiwic3VwZXIiOnRydWUsImFkbWluIjp0cnVlLCJlZGl0Ijp0cnVlLCJwdWJsaXNoIjp0cnVlLCJzZWVTdGF0aXN0aWNzIjp0cnVlLCJjYW5WYWxpZGF0ZVBhc3N3b3JkcyI6dHJ1ZSwiY2FuQWNjZXNzWVNtYXJ0Ijp0cnVlLCJsYW5nIjoiZXMiLCJpYXQiOjE2MTgyNDA1NjF9.TNTGNv6h_YsnfcWE5Qm96epFaoXw83ecyHyuJUs9gpE';
    // Usuario con permiso de edición y publicación
    //return 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJodHRwOi8veWZsb3cueW9pemVuLmNvbSIsIm5hbWUiOiJ0ZXN0IiwidWlkIjoyLCJjaWQiOjEsImNuYW1lIjoiZGVmYXVsdCIsImFkbWluIjpmYWxzZSwiZWRpdCI6dHJ1ZSwicHVibGlzaCI6dHJ1ZSwiaWF0IjoxNTQ3NTY0NjYxfQ.fr-vuPOy4BwXK0gq0kl6zMxYruxEiYosqyqMr6KnG9E';
    // Usuario sin permisos de edición ni publicación
    //return 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJodHRwOi8veWZsb3cueW9pemVuLmNvbSIsIm5hbWUiOiJ2aXNvciIsInVpZCI6NCwiY2lkIjoxLCJjbmFtZSI6ImRlZmF1bHQiLCJhZG1pbiI6ZmFsc2UsImVkaXQiOmZhbHNlLCJwdWJsaXNoIjpmYWxzZSwiaWF0IjoxNTQ3NTc4NzAwfQ.z1Fgebj93H4n4OmDvhc1leF7qRk33V8waGculZ1-paQ';
    // Usuario sin permiso de ver estatisticas
    //return 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJodHRwOi8veWZsb3cueW9pemVuLmNvbSIsIm5hbWUiOiJtaWV6emkyIiwidWlkIjoxOCwiY2lkIjoxLCJjbmFtZSI6ImRlZmF1bHQiLCJzdXBlciI6ZmFsc2UsImFkbWluIjpmYWxzZSwiZWRpdCI6ZmFsc2UsInB1Ymxpc2giOmZhbHNlLCJzZWVTdGFkaXN0aWNzIjpmYWxzZSwiaWF0IjoxNTkwMTgyMDQ5fQ._29-fvFLkEw1pwqnC-Q4iy36kFjZjzGEzJkLZUXIU_E';
    // Usuario con permiso de blanqueo de contraseñas
    //return 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJodHRwOi8veWZsb3cueW9pemVuLmNvbSIsIm5hbWUiOiJDYWN0aW1hbiIsInVpZCI6MTksImNpZCI6MSwiY25hbWUiOiJkZWZhdWx0Iiwic3VwZXIiOmZhbHNlLCJhZG1pbiI6ZmFsc2UsImVkaXQiOnRydWUsInB1Ymxpc2giOnRydWUsInNlZVN0YXRpc3RpY3MiOnRydWUsImNhblZhbGlkYXRlUGFzc3dvcmRzIjp0cnVlLCJpYXQiOjE1OTU5NzM0NTZ9.5rJfGNjm-zhfPQ3n9OyG7IjZkZISbc9MQEhfOzj6rNk'
    // Usuario para probar idiomas
    //return "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJodHRwOi8veWZsb3cueW9pemVuLmNvbSIsIm5hbWUiOiJVc2VySWRpb21hcyIsInVpZCI6MTcsImNpZCI6MSwiY25hbWUiOiJkZWZhdWx0IiwiaGFzaCI6Ijk4MTJhYjNiZjg0NTcwMDJiYzUyZjg5OThlMmRjZTkzIiwic3VwZXIiOmZhbHNlLCJhZG1pbiI6ZmFsc2UsImVkaXQiOnRydWUsInB1Ymxpc2giOnRydWUsInNlZVN0YXRpc3RpY3MiOnRydWUsImNhblZhbGlkYXRlUGFzc3dvcmRzIjp0cnVlLCJsYW5nIjoiZXMiLCJpYXQiOjE1OTY1ODAxNDd9.-VdXmzVd96dqu4RXYEZgvzf56FqaG7_kux8I7HtjCuY";
    //return "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJodHRwOi8veWZsb3cueW9pemVuLmNvbSIsIm5hbWUiOiJudWV2byIsInVpZCI6MTksImNpZCI6MSwiY25hbWUiOiJkZWZhdWx0Iiwic3VwZXIiOmZhbHNlLCJhZG1pbiI6ZmFsc2UsImVkaXQiOnRydWUsInB1Ymxpc2giOnRydWUsInNlZVN0YXRpc3RpY3MiOnRydWUsImNhblZhbGlkYXRlUGFzc3dvcmRzIjp0cnVlLCJsYW5nIjpudWxsLCJpYXQiOjE2MDM4MzMwMzR9.JVkr2P4ojrsDiaeKhsJ_cvPfZ7ZLJf60ufpz1zfbAHc";
    // usuario administrador con acceso a YSmart
    //return "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJodHRwOi8veWZsb3cueW9pemVuLmNvbSIsIm5hbWUiOiJhZG1pbllTbWFydCIsInVpZCI6IjI0IiwiY2lkIjoiMSIsImNuYW1lIjoiRGVmYXVsdCIsImhhc2giOiIiLCJzdXBlciI6dHJ1ZSwiYWRtaW4iOnRydWUsImVkaXQiOnRydWUsInB1Ymxpc2giOnRydWUsInNlZVN0YXRpc3RpY3MiOnRydWUsImNhbkFjY2Vzc1lTbWFydCI6dHJ1ZSwiY2FuVmFsaWRhdGVQYXNzd29yZHMiOnRydWUsImxhbmciOiJlcyIsImlhdCI6MTYxMzA2NjI4Mn0.pnDrw5pzLTJWfnaZXd9YHyAN5XrUJUGgvcDpXnfkAVo";
    //usuario solo lectura
    //return 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJodHRwOi8veWZsb3cueW9pemVuLmNvbSIsIm5hbWUiOiJPdHJvdXN1YXJpbyIsInVpZCI6IjIxIiwiY2lkIjoiMSIsImNuYW1lIjoiRGVmYXVsdCIsImhhc2giOiIiLCJzdXBlciI6ZmFsc2UsImFkbWluIjpmYWxzZSwiZWRpdCI6ZmFsc2UsInB1Ymxpc2giOmZhbHNlLCJzZWVTdGF0aXN0aWNzIjp0cnVlLCJjYW5WYWxpZGF0ZVBhc3N3b3JkcyI6ZmFsc2UsImxhbmciOiJlcyIsImlhdCI6MTYxMjM3MzkyMH0.QYODpsVLxJEhsmP3fIsAb2-AlbU7yBawpzuRQIfAYIk'
    //solo estadisticas
    //return 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJodHRwOi8veWZsb3cueW9pemVuLmNvbSIsIm5hbWUiOiJBZG1pbmlzdHJhZG9yIiwidWlkIjoiNjMiLCJjaWQiOiIxIiwiY25hbWUiOiJEZWZhdWx0IiwiaGFzaCI6IiIsInN1cGVyIjpmYWxzZSwiYWRtaW4iOmZhbHNlLCJlZGl0IjpmYWxzZSwicHVibGlzaCI6ZmFsc2UsInNlZVN0YXRpc3RpY3MiOnRydWUsImNhblZhbGlkYXRlUGFzc3dvcmRzIjpmYWxzZSwiY2FuQWNjZXNzWVNtYXJ0IjpmYWxzZSwibGFuZyI6ImVzIiwiaWF0IjoxNjE3ODgzNjM3fQ.cW7MbeSMbLg9EMxgNf-w3TH3nNalhzeBQ8UAo8fl_DI';
    //todos los permisos
    //return 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJodHRwOi8veWZsb3cueW9pemVuLmNvbSIsIm5hbWUiOiJ0b2RvIiwidWlkIjoiNjkiLCJjaWQiOiIxIiwiY25hbWUiOiJkZWZhdWx0IiwiaGFzaCI6IiIsInN1cGVyIjpmYWxzZSwiYWRtaW4iOnRydWUsImVkaXQiOnRydWUsInB1Ymxpc2giOnRydWUsInNlZVN0YXRpc3RpY3MiOnRydWUsImNhblZhbGlkYXRlUGFzc3dvcmRzIjp0cnVlLCJjYW5BY2Nlc3NZU21hcnQiOnRydWUsImxhbmciOiJlcyIsImlhdCI6MTYyNTA4MTcwMn0.skqYJ9G0ZAr_4gD6w-XTqsbFjho1exLqV6dj2hWId_4';
    //todos los permisos menos blanqueo
    //return 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJodHRwOi8veWZsb3cueW9pemVuLmNvbSIsIm5hbWUiOiJzb2xvYWRtaW4iLCJ1aWQiOiI3MCIsImNpZCI6IjEiLCJjbmFtZSI6ImRlZmF1bHQiLCJoYXNoIjoiIiwic3VwZXIiOmZhbHNlLCJhZG1pbiI6dHJ1ZSwiZWRpdCI6dHJ1ZSwicHVibGlzaCI6dHJ1ZSwic2VlU3RhdGlzdGljcyI6dHJ1ZSwiY2FuVmFsaWRhdGVQYXNzd29yZHMiOmZhbHNlLCJjYW5BY2Nlc3NZU21hcnQiOnRydWUsImxhbmciOiJlcyIsImlhdCI6MTYyNTA4MTY4M30.faLTkwe3W0veQuBQGZOBbxNUye_FSizIVtT7lM20lV0';
    //solo blanqueo
    //return 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJodHRwOi8veWZsb3cueW9pemVuLmNvbSIsIm5hbWUiOiJzb2xvYmxhbnF1ZW8iLCJ1aWQiOiI3MSIsImNpZCI6IjEiLCJjbmFtZSI6ImRlZmF1bHQiLCJoYXNoIjoiIiwic3VwZXIiOmZhbHNlLCJhZG1pbiI6ZmFsc2UsImVkaXQiOmZhbHNlLCJwdWJsaXNoIjpmYWxzZSwic2VlU3RhdGlzdGljcyI6ZmFsc2UsImNhblZhbGlkYXRlUGFzc3dvcmRzIjp0cnVlLCJjYW5BY2Nlc3NZU21hcnQiOmZhbHNlLCJsYW5nIjoiZXMiLCJpYXQiOjE2MjUwODE3NTl9.oD4OGtT7GkhxETxmWyY2pbLbFB_xLC24anoW96tjA40';
    //nada
    //return 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJodHRwOi8veWZsb3cueW9pemVuLmNvbSIsIm5hbWUiOiJuYWRhIiwidWlkIjoiNzIiLCJjaWQiOiIxIiwiY25hbWUiOiJkZWZhdWx0IiwiaGFzaCI6IiIsInN1cGVyIjpmYWxzZSwiYWRtaW4iOmZhbHNlLCJlZGl0IjpmYWxzZSwicHVibGlzaCI6ZmFsc2UsInNlZVN0YXRpc3RpY3MiOmZhbHNlLCJjYW5WYWxpZGF0ZVBhc3N3b3JkcyI6ZmFsc2UsImNhbkFjY2Vzc1lTbWFydCI6ZmFsc2UsImxhbmciOiJlcyIsImlhdCI6MTYyNTA4MTc5MH0.lGDmFBmO3VomnarPuA-AGZWJyWqyVDPjrdOdoGB6xn8'
    // Tester con permisos de administrador
    //return 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJodHRwOi8veWZsb3cueW9pemVuLmNvbSIsIm5hbWUiOiJ0ZXN0ZXIiLCJ1aWQiOjMsImNpZCI6MSwiY25hbWUiOiJkZWZhdWx0IiwiaGFzaCI6IjkxZmQxMmM5NGFjMjMyNTc4OGI5YjQxMzhlMmViNDU3Iiwic3VwZXIiOmZhbHNlLCJhZG1pbiI6MSwiZWRpdCI6MSwicHVibGlzaCI6MSwic2VlU3RhdGlzdGljcyI6MSwiY2FuQWNjZXNzWVNtYXJ0IjoxLCJjYW5WYWxpZGF0ZVBhc3N3b3JkcyI6MSwibGFuZyI6bnVsbCwiaWF0IjoxNjY0OTk0NDc1fQ.ciaw7Iw2HVXypjlVxx3YUSCRlq520YKq8FEMZ93fNPY'

  }
}

export function getTokenPayload(): any {
  let token = getToken();
  return jwtDecode(token);
}

export function updateToken(token: string): void {
  window['access_token'] = token;
}

export function getDefaultLanguaje(): string {
  if (environment.production || environment.staging) {
    return window['defaultLanguaje'];
  }
  else {
    return 'es';
  }
}

export function getGMT(): string {
  if (window['gmt']) {
    return window['gmt'];
  } else {
    let momentValue = moment().utcOffset() / 60;
    const negative = momentValue < 0;
    momentValue = Math.abs(momentValue);
    let momentValueString = momentValue < 10 ? ('0' + momentValue.toString()) : momentValue.toString();
    momentValueString = negative ? ('-' + momentValueString) : momentValueString;
    return momentValueString + ':00';

  }
}

export function geflowsTypes(): string {
  if (environment.production || environment.staging) {
    return window['flowType'];
  }
  else {
    return 'Lite,Bot';
  }
}

export function getEnabledLanguajes(): string {
  if (environment.production || environment.staging) {
    return window['enabledLanguajes'];
  }
  else {
    return "default_languaje,es,en,pt";
  }
}

export function ySmartEnabled(): string {
  if (environment.production || environment.staging) {
    return window['ySmartEnabled'];
  }
  else {
    return "true";
  }
}

export function yBiometricEnabled(): string {
  if (environment.production || environment.staging) {
    return window['yBiometricEnabled'];
  }
  else {
    return "true";
  }
}

export function yFlowUrl(): string {
  if (environment.production || environment.staging) {
    return window['yFlowUrl'];
  }
  else {
    return "http://localhost:3000";
  }
}

export function ySocialUrl(): string {
  if (environment.production || environment.staging) {
    return window['ySocialUrl'];
  }
  else {
    return "https://dev.ysocial.net/social/";
  }
}
export function getUrlApiCognitiveServices(): string {
  if (environment.production || environment.staging) {
    return window['urlApiCognitiveServices'];
  }
  else {
    return "http://dev.ysocial.net/ysmartcore/api/";
  }
}


export function updateCasePieceEnabledInChat(): boolean {
  if (environment.production || environment.staging) {
    if (typeof (window['updateCasePieceEnabledInChat']) === 'string') {
      return window['updateCasePieceEnabledInChat'].toLowerCase() === 'true';
    }
    return false;
  }
  else {
    return true;
  }
}

export function downloadReportTypes(): string[] {
  if (environment.production || environment.staging) {
    if (typeof (window['downloadReportTypes']) === 'string') {
      return window['downloadReportTypes'].toLocaleLowerCase().split(',');
    }
    return ['rt'];
  }
  else {
    return ['rt', 'background'];
  }
}

export function isContingencyBot(): boolean {
  if (typeof (window['isContingencyBot']) === 'string') {
    return window['isContingencyBot'].toLowerCase() === 'true';
  }

  return false;
}
