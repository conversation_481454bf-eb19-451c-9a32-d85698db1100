<div class="button-content">
    <div class="addName" (click)="onClick(); $event.stopPropagation();" [ngClass]="{'invalid': isInvalid()}">
      <div class="trash" (click)="deleteElement(); $event.stopPropagation();" *ngIf="!readOnly && !(IsFirst && IsLast)"
           data-toggle="tooltip" ngbTooltip="{{ 'QUICKREPLY_DELETE_TEXT' | translate }}" placement="top"
           container="body" tooltipClass="tooltip-trash">
        <span class="fa fa-trash-alt"></span>
      </div>
      <div class="reorder left" (click)="moveLeft(); $event.stopPropagation();" *ngIf="!IsFirst && !readOnly"
           data-toggle="tooltip" ngbTooltip="{{ 'QUICKREPLY_REORDER_LEFT' | translate }}" placement="top"
           container="body" tooltipClass="tooltip-trash">
        <span class="fa fa-caret-left"></span>
      </div>
      <div class="reorder right" (click)="moveRight(); $event.stopPropagation();" *ngIf="!IsLast && !readOnly"
           data-toggle="tooltip" ngbTooltip="{{ 'QUICKREPLY_REORDER_RIGHT' | translate }}" placement="top"
           container="body" tooltipClass="tooltip-trash">
        <span class="fa fa-caret-right"></span>
      </div>
      <div class="name" [ngClass]="{ 'invalid': !withName() }">
        <span class="fa fa-location-arrow icon" *ngIf="Model.Type === quickReplyTypes.Location"></span>
        <span class="fa fa-map-marker icon" *ngIf="Model.Type === quickReplyTypes.LocationWithMap"></span>
        <span class="fa fa-phone icon" *ngIf="Model.Type === quickReplyTypes.UserPhoneNumber"></span>
        <span class="fa fa-at icon" *ngIf="Model.Type === quickReplyTypes.UserEmail"></span>
        <span class="fa fa-text icon" *ngIf="Model.Type === quickReplyTypes.Text"></span>
        <span class="text">{{ getButtonText() }}</span>
      </div>
      <div class="block" *ngIf="withRedirect()">
        <span class="fa fa-table"></span> {{ getBlockName() }}
        <div class="goto" (click)="goToBlock(); $event.stopPropagation();" *ngIf="!readOnly"
             data-toggle="tooltip" ngbTooltip="{{ 'GO_TO_BLOCK' | translate }}" placement="bottom"
             container="body" tooltipClass="tooltip-gotoblock">
          <span class="fas fa-external-link-alt"></span>
        </div>
      </div>
      <div class="stats" *ngIf="withRedirect()">
        <div class="title"><span class="fa fa-chart-line"></span>{{ 'BUTTON_STATS_TITLE' | translate }}</div>
        <div class="stats-empty" *ngIf="stats === null">
          <div class="alert alert-info">
            {{ 'BUTTON_STATS_DESC_EMPTY' | translate }}
          </div>
        </div>
        <div class="stats-info" *ngIf="stats !== null">
          <ul>
            <li>
              <span class="title">{{ 'BLOCK_STATS_FIRSTEXECUTION' | translate }}:</span>
              <span class="value">{{ stats.firstExecution | dateFormat: 'LLL' }}</span>
            </li>
            <li>
              <span class="title">{{ 'BLOCK_STATS_LASTEXECUTION' | translate }}:</span>
              <span class="value">{{ stats.lastExecution | dateFormat: 'LLL' }}</span>
            </li>
            <li>
              <span class="title">{{ 'BLOCK_STATS_COUNT' | translate }}:</span>
              <span class="value">{{ stats.count }}</span>
            </li>
          </ul>
        </div>
      </div>
      <div class="url" *ngIf="withUrl()"><span class="fa fa-link"></span> {{ Model.Url }}</div>
    </div>
    <app-add-quick-reply #buttonDetail class="button-details"
                         (onClose)="closePopup()"
                         [Model]="Model"
                         [readOnly]="readOnly"
                         *ngIf="!HideDetail"></app-add-quick-reply>
</div>
