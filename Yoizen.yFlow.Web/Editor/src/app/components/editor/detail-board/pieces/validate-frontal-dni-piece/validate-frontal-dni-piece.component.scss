@import '_variables';
@import '_mixins';

.validate-dni {
	background-color: #fff;
	padding: 10px;
	width: 800px;

  .destvariable, .destvariableparseformat, .regex, .errormessage, .retries, .next, .command {
    display: flex;
    flex-direction: row;
    margin-bottom: 10px;
    width: 100%;

    .title {
      font-family: $fontFamilyTitles;
      font-weight: bold;
      margin-right: 10px;
      justify-self: center;
      align-self: center;
      text-align: center;
      flex-grow: 0;
      flex-shrink: 0;
    }
  }

  .validationerror {
    padding: 10px 0px 10px 0px;

    .messages {
      display: flex;
      flex-direction: row;

      app-text-list-error-message {
        width: 218px;
      }

      .addText {
        @include addButton;
        width: 40px;
        flex-grow: 0;
        flex-shrink: 0;
      }
    }

    .errormessage {
      .inputerrormessage {
        flex-grow: 1;
        flex-shrink: 1;
      }
    }

    .retries {
      .tries {
        width: 80px;
      }
    }
  }

  .validationerror, .commands {
    border-bottom: 1px solid $gray;
    margin-bottom: 10px;
  }
}
