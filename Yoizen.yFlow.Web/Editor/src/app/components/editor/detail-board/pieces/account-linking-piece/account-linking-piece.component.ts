import { Component, OnInit, ElementRef, Renderer2, ViewChild } from '@angular/core';
import { BlockDefinition } from 'src/app/models/BlockDefinition';
import { AccountLinkingPiece, EncryptModeType, EncryptPaddingType, EncryptType, HashType } from 'src/app/models/pieces/AccountLinkingPiece';
import { BusinessData } from 'src/app/models/pieces/UpdateCasePiece';
import { EditorService } from 'src/app/services/editor.service';
import { ModalService } from 'src/app/services/Tools/ModalService';
import { isUrlValid } from 'src/app/urlutils.module';
import { BasePieceVM } from '../BasePieceVM';

@Component({
  selector: 'app-account-linking-piece',
  templateUrl: './account-linking-piece.component.html',
  styleUrls: ['./account-linking-piece.component.scss']
})
export class AccountLinkingPieceComponent extends BasePieceVM implements OnInit {
  model: AccountLinkingPiece;
  SuccessBlockData: BlockDefinition;
  searchSuccessBlockString : String;
  encryptModeType = EncryptModeType;
  encryptPaddingType = EncryptPaddingType;
  encryptType = EncryptType;
  hashType = HashType;

  @ViewChild('successBlockPicker', { static: false }) SuccessBlockPicker : ElementRef;

  constructor(editorService: EditorService, public modalService: ModalService, private renderer: Renderer2) {
    super(editorService, modalService);
  }

  ngOnInit() {
    this.model = this.context as AccountLinkingPiece;
  }

  isUrlValid(str): Boolean {
    return isUrlValid(str);
  }

  onSuccessInputFocusIn() {
    this.renderer.removeClass(this.SuccessBlockPicker.nativeElement, 'hide');
  }

  onSuccessInputFocusOut() {

    setTimeout(() => {
      this.renderer.addClass(this.SuccessBlockPicker.nativeElement, 'hide');
    }, 500);
  }

  deleteBussinessData(i: number) {
    this.model.BusinessData.splice(i, 1);
  }

  addBussinessData() {
    this.model.BusinessData.push(new BusinessData());
  }

  isValidValue(str : string){
    if (str === null||
      str === '' ||
      str === undefined) {
        return false;
      }

    return true;
  }
}
