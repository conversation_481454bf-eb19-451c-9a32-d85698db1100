<div class="authentication card" [ngClass]="{'invalid-piece': !model.isValid(editorService)}">
  <div class="trash" (click)="deleteAction()" *ngIf="!readOnly"
       data-toggle="tooltip" ngbTooltip="{{ 'CARD_DELETE_TEXT' | translate }}" placement="top" container="body" tooltipClass="tooltip-trash">
    <span class="fa fa-trash-alt"></span>
  </div>
  <div class="clone" (click)="cloneAction()" *ngIf="!readOnly"
       data-toggle="tooltip" ngbTooltip="{{ 'CARD_CLONE_TEXT' | translate }}" placement="top" container="body" tooltipClass="tooltip-trash">
    <span class="fa fa-clone"></span>
  </div>
  <div class="export" (click)="exportAction()" *ngIf="!readOnly"
       data-toggle="tooltip" ngbTooltip="{{ 'CARD_EXPORT_TEXT' | translate }}" placement="top" container="body" tooltipClass="tooltip-trash">
    <span class="fa fa-file-export"></span>
  </div>
  <div class="card-title">
    <span class="fab fa-app-store-ios"></span> {{ 'PIECE_APPLE_INTERATIVE_MESSAGE_IMESSAGE_APP' | translate }}
  </div>
  <div class="options">
    <div class="option">
      <span class="title">{{'APPLE_INTERATIVE_MESSAGE_IMESSAGE_APP_ICON' | translate}}:</span>
      <input type="text"
             [(ngModel)]="model.appIcon"
             [ngClass]="{'invalid-input': !model.isAppIconValid() }"
             [disabled]="readOnly"
             class="input" spellcheck="false" autocomplete="off" />
    </div>
    <div class="option">
      <span class="title">{{'APPLE_INTERATIVE_MESSAGE_IMESSAGE_APP_ID' | translate}}:</span>
      <input type="text"
             [(ngModel)]="model.appId"
             [ngClass]="{'invalid-input': !model.isAppIdValid() }"
             [disabled]="readOnly"
             class="input" spellcheck="false" autocomplete="off" />
    </div>
    <div class="option">
      <span class="title">{{'APPLE_INTERATIVE_MESSAGE_IMESSAGE_APP_NAME' | translate}}:</span>
      <input type="text"
             [(ngModel)]="model.appName"
             [ngClass]="{'invalid-input': !model.isAppNameValid() }"
             [disabled]="readOnly"
             class="input" spellcheck="false" autocomplete="off" />
    </div>
    <div class="option">
      <span class="title">{{'APPLE_INTERATIVE_MESSAGE_IMESSAGE_APP_TEAM_ID' | translate}}:</span>
      <input type="text"
             [(ngModel)]="model.teamId"
             [ngClass]="{'invalid-input': !model.isTeamIdValid() }"
             [disabled]="readOnly"
             class="input" spellcheck="false" autocomplete="off" />
    </div>
    <div class="option">
      <span class="title">{{'APPLE_INTERATIVE_MESSAGE_IMESSAGE_APP_BUNDLE_ID' | translate}}:</span>
      <input type="text"
             [(ngModel)]="model.bundleId"
             [ngClass]="{'invalid-input': !model.isBundleIdValid() }"
             [disabled]="readOnly"
             class="input" spellcheck="false" autocomplete="off" />
    </div>
    <div class="option">
      <span class="title">{{'APPLE_INTERATIVE_MESSAGE_IMESSAGE_APP_URL' | translate}}:</span>
      <app-input-with-variables
        [placeholder]="'APPLE_INTERATIVE_MESSAGE_IMESSAGE_APP_URL' | translate"
        [(value)]="model.url"
        [isTextArea]="false"
        [wideInput]="true"
        [validator]="model.isUrlValid.bind(model)"
        [disabled]="readOnly"></app-input-with-variables>
    </div>
    <div class="option">
      <span class="title">{{'APPLE_INTERATIVE_MESSAGE_IMESSAGE_APP_USE_LIVE_LAYOUT' | translate}}:</span>
      <ui-switch [(ngModel)]="model.useLiveLayout" [disabled]="readOnly"
                 color="#45c195" size="small" defaultBgColor="#e0e0e0" switchColor="#ffffff"></ui-switch>
    </div>
  </div>
  <app-apple-interactive-received-and-reply
    [receivedMessage]="model.receivedMessage"
    [replyMessage]="model.replyMessage"
    [readOnly]="readOnly"></app-apple-interactive-received-and-reply>
</div>
