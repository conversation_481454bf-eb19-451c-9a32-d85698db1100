import { Component, OnInit } from '@angular/core';
import { BasePieceVM } from '../BasePieceVM';
import { EditorService } from '../../../../../services/editor.service';
import { ModalService } from '../../../../../services/Tools/ModalService';
import { OperatorDefinitions } from '../../../../../models/OperatorType';
import { VariableDefinition } from '../../../../../models/VariableDefinition';
import { TypeDefinition } from "../../../../../models/TypeDefinition";
import { SetVariableFromEntity } from 'src/app/models/pieces/SetVariableFromEntity';
import { Entity } from 'src/app/models/cognitivity/Entity';

@Component({
  selector: 'app-set-variable-from-entity',
  templateUrl: './set-variable-from-entity.component.html',
  styleUrls: ['./set-variable-from-entity.component.scss']
})
export class SetVariableFromEntityComponent extends BasePieceVM implements OnInit {
  variableFilter: TypeDefinition[] = [TypeDefinition.Bool, TypeDefinition.Text, TypeDefinition.StringDate,
  TypeDefinition.Timestamp, TypeDefinition.Decimal, TypeDefinition.Number];
  model: SetVariableFromEntity;
  selectedEntity: Entity;

  get curretVariable(): VariableDefinition {
    return this.editorService.getVariableWithId(this.model.VariableId);
  }
  constructor(editorService: EditorService, public modalService: ModalService) {
    super(editorService, modalService);
  }

  ngOnInit() {
    this.model = this.context as SetVariableFromEntity;
    if (this.model.EntityId !== null) {
      let availableEntities = this.editorService.getEntities();
      let entity = availableEntities.find(e => e.cognitiveServiceId === this.model.EntityId);
      if (entity === undefined) {
        this.model.EntityId = null;
        this.model.EntityName = null;
      } else {
        this.setEntity(entity);
      }
    }
  }

  setEntity(entity: Entity) {
    if (entity === null) {
      this.model.EntityId = null;
      this.model.EntityName = null;
      this.selectedEntity = null;
      return;
    }
    this.model.EntityId = entity.cognitiveServiceId;
    this.model.EntityName = entity.name;
    this.selectedEntity = entity;
  }

  getOperators() {
    return OperatorDefinitions.Operators;
  }

  setVariableOnOutput(variable: VariableDefinition) {
    if (variable != null) {
      this.model.VariableId = variable.Id;
    }
    else {
      this.model.VariableId = null;
    }
  }

  showOperand(): boolean {
    let op = OperatorDefinitions.Operators.find(op => op.value == this.model.Operator);
    if (op == null) {
      return false;
    }
    return op.requiresOperand;
  }
}

