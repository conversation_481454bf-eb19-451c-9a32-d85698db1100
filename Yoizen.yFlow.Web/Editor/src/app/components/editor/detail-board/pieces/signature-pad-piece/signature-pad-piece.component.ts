import { Component, OnInit, ElementRef, Renderer2, ViewChild } from '@angular/core';
import { BlockDefinition } from 'src/app/models/BlockDefinition';
import { SignaturePadPiece } from 'src/app/models/pieces/SignaturePadPiece';
import { EditorService } from 'src/app/services/editor.service';
import { ModalService } from 'src/app/services/Tools/ModalService';
import { isUrlValid } from 'src/app/urlutils.module';
import { BasePieceVM } from '../BasePieceVM';

@Component({
  selector: 'app-signature-pad-piece',
  templateUrl: './signature-pad-piece.component.html',
  styleUrls: ['./signature-pad-piece.component.scss']
})
export class SignaturePadPieceComponent extends BasePieceVM implements OnInit {
  model: SignaturePadPiece;
  SuccessBlockData: BlockDefinition;
  searchSuccessBlockString : String;

  @ViewChild('successBlockPicker', { static: false }) SuccessBlockPicker : ElementRef;

  constructor(editorService: EditorService, public modalService: ModalService, private renderer: Renderer2) {
    super(editorService, modalService);
  }

  ngOnInit() {
    this.model = this.context as SignaturePadPiece;
    this.SuccessBlockData = this.editorService.findBlockWithId(this.model.SuccessBlockId);
  }

  hasSuccessBlock() {
    if (!this.SuccessBlockData) {
      return false;
    }
    return true;
  }

  isUrlValid(str): Boolean {
    return isUrlValid(str);
  }

  onSuccessBlockSelect(blockData) {
    this.model.SuccessBlockId = blockData.Id;
    this.searchSuccessBlockString = blockData.Name;
    this.SuccessBlockData = this.editorService.findBlockWithId( this.model.SuccessBlockId);
  }

  deleteSuccessBlock() {
    this.SuccessBlockData = null;
    this.model.SuccessBlockId = "-1";
    this.searchSuccessBlockString = "";
  }

  onSuccessInputFocusIn() {
    this.renderer.removeClass(this.SuccessBlockPicker.nativeElement, 'hide');
  }

  onSuccessInputFocusOut() {

    setTimeout(() => {
      this.renderer.addClass(this.SuccessBlockPicker.nativeElement, 'hide');
    }, 500);
  }

}
