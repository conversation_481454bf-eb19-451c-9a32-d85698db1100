<div class="action card" [ngClass]="{'invalid-piece': !model.isValid(editorService)}">
  <div class="trash" (click)="deleteAction()" *ngIf="!readOnly"
       data-toggle="tooltip" ngbTooltip="{{ 'CARD_DELETE_TEXT' | translate }}" placement="top" container="body" tooltipClass="tooltip-trash">
    <span class="fa fa-trash-alt"></span>
  </div>
  <div class="clone" (click)="cloneAction()" *ngIf="!readOnly"
       data-toggle="tooltip" ngbTooltip="{{ 'CARD_CLONE_TEXT' | translate }}" placement="top" container="body" tooltipClass="tooltip-trash">
    <span class="fa fa-clone"></span>
  </div>
  <div class="export" (click)="exportAction()" *ngIf="!readOnly"
       data-toggle="tooltip" ngbTooltip="{{ 'CARD_EXPORT_TEXT' | translate }}" placement="top" container="body" tooltipClass="tooltip-trash">
    <span class="fa fa-file-export"></span>
  </div>
  <div class="card-title">
    <span class="fa fa-credit-card"></span> {{ 'PAYMENT_GATEWAY_BLOCK_TITLE' | translate }}
  </div>
  <div class="card-info">
    {{ 'PAYMENT_GATEWAY_BLOCK_INFO' | translate }}
  </div>

  <p>Expiracion del token: {{model.TokenExpiration}} hs</p>
  <input class="slider" type="range" min="1" max="24" [(ngModel)]="model.TokenExpiration" [disabled]="readOnly" />

  <div class="next block-picker">
    <label class="title">{{ 'PAYMENT_GATEWAY_BLOCK_SUCCESS' | translate }}</label>
    <app-block-picker class="input" 
                      [blockId]="model.SuccessBlockId" 
                      (onSelectNewBlock)="onSelectBlock($event)"
                      [readOnly]="readOnly" 
                      (onDeleteBlock)="onDeleteBlock($event)" 
                      [isInvalid]="!model.isSuccessBlockValid(editorService)">
    </app-block-picker>
  </div>

  <div class="next block-picker">
    <label class="title">{{ 'PAYMENT_GATEWAY_BLOCK_ERROR' | translate }}</label>
    <app-block-picker class="input" 
                      [blockId]="model.ErrorBlockId" 
                      (onSelectNewBlock)="onSelectErrorBlock($event)"
                      [readOnly]="readOnly" 
                      (onDeleteBlock)="onDeleteErrorBlock($event)" 
                      [isInvalid]="!model.isErrorBlockValid(editorService)">
    </app-block-picker>
  </div>
</div>