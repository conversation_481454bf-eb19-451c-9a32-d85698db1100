import {Component, Input, OnInit} from '@angular/core';
import {FormItemSelect, FormPage, FormPiece, FormSelectPage} from "../../../../../../models/pieces/FormPiece";
import {VariableDefinition} from "../../../../../../models/VariableDefinition";
import {TypeDefinition} from "../../../../../../models/TypeDefinition";
import {EditorService} from "../../../../../../services/editor.service";

@Component({
  selector: 'app-form-page-select',
  templateUrl: './form-page-select.component.html',
  styleUrls: ['./form-page-select.component.scss']
})
export class FormPageSelectComponent implements OnInit {
  @Input() basePage: FormPage;
  @Input() pageIndex: number;
  @Input() form: FormPiece;
  @Input() readOnly : boolean = false;
  page: FormSelectPage;
  variableTypes = TypeDefinition;

  get variableData(): VariableDefinition {
    return this.editorService.getVariableWithId(this.page.variableId);
  }

  constructor(public editorService: EditorService) { }

  ngOnInit() {
    this.page = <FormSelectPage> this.basePage;
  }

  addNewItem() {
    this.page.items.push(new FormItemSelect());
  }

  deleteItem(index: number) {
    this.page.items.splice(index, 1);
  }

  canAddItem() : boolean {
    return true;
  }

  getVariableTypes() : TypeDefinition[] {
    if (this.page.multipleSelection) {
      return new Array<TypeDefinition>(TypeDefinition.Array);
    }

    return new Array<TypeDefinition>(TypeDefinition.Text);
  }

  setVariable(variableData: VariableDefinition) {
    this.page.variableId = variableData ? variableData.Id : -1;
  }

  multipleSelecionChanged() {
    this.page.variableId = -1;
  }
}
