import { Component, OnInit, ElementRef, ViewChild, Renderer2 } from '@angular/core';
import { BasePieceVM } from '../BasePieceVM';
import { EditorService } from '../../../../../services/editor.service';
import { ModalService } from '../../../../../services/Tools/ModalService';
import { StatisticEventDefinition } from 'src/app/models/StatisticEventDefinition';
import { StatisticEventPiece } from 'src/app/models/pieces/StatisticEventPiece';


@Component({
  selector: 'app-statistic-event-piece',
  templateUrl: './statistic-event-piece.component.html',
  styleUrls: ['./statistic-event-piece.component.scss']
})
export class StatisticEventPieceComponent extends BasePieceVM implements OnInit {
  model : StatisticEventPiece;
  StatisticEventData : StatisticEventDefinition;
  searchBlockString : String;

  @ViewChild('blockPicker', { static: false }) BlockPicker : ElementRef;

  constructor( editorService : EditorService, public modalService : ModalService, private renderer: Renderer2  ) {
    super(editorService, modalService);
  }

  ngOnInit() {
    this.model = this.context as StatisticEventPiece;
    const event = this.editorService.findStatisticEventWithId(this.model.StatisticEventID);
    if (event){
      if (event.Enabled){
        this.StatisticEventData = event;
        this.model.compareStructuredData(event);
      } else {
        this.model.StatisticEventID = -1;
        this.StatisticEventData = undefined;
      }
    }
  }

  hasStatisticEvent() {
    if(!this.StatisticEventData) {
      return false;
    }
    return true;
  }

  onStatisticEventSelect(StatisticEventData) {
    this.model.StatisticEventID = StatisticEventData.Id;
    this.searchBlockString = StatisticEventData.Name;
    this.StatisticEventData = this.editorService.findStatisticEventWithId( this.model.StatisticEventID);
    this.model.compareStructuredData(StatisticEventData)
  }

  deleteStatisticEvent() {
    this.StatisticEventData = null;
    this.model.StatisticEventID = -1;
    this.searchBlockString = "";
  }

  onInputFocusIn() {
    this.renderer.removeClass(this.BlockPicker.nativeElement, 'hide');
  }

  onInputFocusOut() {
    setTimeout(() => {
      this.renderer.addClass(this.BlockPicker.nativeElement, 'hide');
    }, 500);
  }
}
