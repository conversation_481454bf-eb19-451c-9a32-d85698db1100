import { async, ComponentFixture, TestBed } from '@angular/core/testing';

import { QuickRepliesPieceComponent } from './quick-replies-piece.component';

describe('QuickRepliesPieceComponent', () => {
  let component: QuickRepliesPieceComponent;
  let fixture: ComponentFixture<QuickRepliesPieceComponent>;

  beforeEach(async(() => {
    TestBed.configureTestingModule({
      declarations: [ QuickRepliesPieceComponent ]
    })
    .compileComponents();
  }));

  beforeEach(() => {
    fixture = TestBed.createComponent(QuickRepliesPieceComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });
});
