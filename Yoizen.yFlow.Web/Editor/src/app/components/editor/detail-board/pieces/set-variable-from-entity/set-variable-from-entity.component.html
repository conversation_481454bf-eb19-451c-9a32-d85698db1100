<div class="condition card" [ngClass]="{'invalid-piece': !model.isValid(editorService)}">
  <div class="trash" (click)="deleteAction()" *ngIf="!readOnly"
       data-toggle="tooltip" ngbTooltip="{{ 'CARD_DELETE_TEXT' | translate }}" placement="top" container="body" tooltipClass="tooltip-trash">
    <span class="fa fa-trash-alt"></span>
  </div>
  <div class="clone" (click)="cloneAction()" *ngIf="!readOnly"
       data-toggle="tooltip" ngbTooltip="{{ 'CARD_CLONE_TEXT' | translate }}" placement="top" container="body" tooltipClass="tooltip-trash">
    <span class="fa fa-clone"></span>
  </div>
  <div class="export" (click)="exportAction()" *ngIf="!readOnly"
       data-toggle="tooltip" ngbTooltip="{{ 'CARD_EXPORT_TEXT' | translate }}" placement="top" container="body" tooltipClass="tooltip-trash">
    <span class="fa fa-file-export"></span>
  </div>
  <div class="card-title">
    <span class="fa fa-sort-numeric-down"></span> {{ 'CARD_SET_VARIABLE_FROM_ENTITY_TITLE' | translate }}
  </div>
  <div class="add-condition" (click)="model.HasCondition = true" *ngIf="!model.HasCondition && !readOnly">
    <span class="fa fa-plus"></span> {{ 'CARD_ADD_CONDITION' | translate }}
  </div>
  <div class="data condition" *ngIf="model.HasCondition">
    <app-input-with-variables [placeholder]="'VALUE' | translate"
                              [(value)]="model.FirstValue"
                              [disabled]="readOnly"
                              [ngClass]="{'validation-error': !model.isFirstValueValid(editorService)}"></app-input-with-variables>
    <select class="select"
            [(ngModel)]="model.Operator"
            [disabled]="readOnly"
            [ngClass]="{'validation-error': !model.isOperatorValid()}">
      <option *ngFor="let operator of getOperators()" [ngValue]="operator.value">{{ operator.localized | translate }}</option>
    </select>
    <app-input-with-variables *ngIf="showOperand()"
                              [placeholder]="'VALUE' | translate"
                              [(value)]="model.SecondValue"
                              [disabled]="readOnly"
                              [ngClass]="{'validation-error': !model.isSecondValueValid(editorService)}"></app-input-with-variables>
    <div class="trash" (click)="model.HasCondition=false" *ngIf="!readOnly"
         data-toggle="tooltip" ngbTooltip="{{ 'CARD_REMOVE_CONDITION' | translate }}" placement="top" container="body"
         tooltipClass="tooltip-trash">
      <span class="fa fa-trash-alt"></span>
    </div>
  </div>
  <div class="data">
    <span class="title">{{'ENTITY' | translate}}:</span>
    <app-entity-selector-input
      [readOnly]="readOnly"
      [entity]="selectedEntity"
      (setEntity)="setEntity($event)">
    </app-entity-selector-input>
  </div>
  <div class="data">
    <span class="title">{{'SAVE_INTO' | translate}}:</span>
    <app-variable-selector-input [VariableData]="curretVariable"
                                 (setVariable)="setVariableOnOutput($event)"
                                 [typeFilters]="variableFilter"
                                 [readOnly]="readOnly"
                                 class="input-variable-area"></app-variable-selector-input>
  </div>
  <div class="data">
      <span class="title">{{'DEFAULT_VALUE_TITLE' | translate}}:</span>
      <app-input-with-variables [placeholder]="'VALUE' | translate"
                                [(value)]="model.DefaultValue"
                                [disabled]="readOnly"
                                class="input-variable-area" ></app-input-with-variables>
  </div>
</div>
