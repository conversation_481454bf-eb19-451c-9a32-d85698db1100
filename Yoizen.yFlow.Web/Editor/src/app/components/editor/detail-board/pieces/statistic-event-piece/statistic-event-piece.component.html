<div class="storemessage card" [ngClass]="{'invalid-piece': !model.isValid(editorService)}">
  <div class="trash" (click)="deleteAction()" *ngIf="!readOnly"
       data-toggle="tooltip" ngbTooltip="{{ 'CARD_DELETE_TEXT' | translate }}" placement="top" container="body" tooltipClass="tooltip-trash">
    <span class="fa fa-trash-alt"></span>
  </div>
  <div class="clone" (click)="cloneAction()" *ngIf="!readOnly"
       data-toggle="tooltip" ngbTooltip="{{ 'CARD_CLONE_TEXT' | translate }}" placement="top" container="body" tooltipClass="tooltip-trash">
    <span class="fa fa-clone"></span>
  </div>
  <div class="export" (click)="exportAction()" *ngIf="!readOnly"
       data-toggle="tooltip" ngbTooltip="{{ 'CARD_EXPORT_TEXT' | translate }}" placement="top" container="body" tooltipClass="tooltip-trash">
    <span class="fa fa-file-export"></span>
  </div>
  <div class="card-title">
    <span class="fa fa-chart-bar"></span> {{ 'CARD_STATISTIC_EVENT_TITLE' | translate }}
  </div>
  <div class="next block-picker">
    <div [ngClass]="{'hide': hasStatisticEvent()}" *ngIf="!readOnly">
      <input class="input" type="text" placeholder="{{'REDIRECT_TO_STATISTIC_EVENT_PLACEHOLDER' | translate}}"
        [(ngModel)]="searchBlockString" LimitLength (focusin)="onInputFocusIn()" (focusout)="onInputFocusOut()">
      <div #blockPicker class="hide block-selection-anchor">
        <app-statistic-event-selector class="select-block" [StatisticEventName]=searchBlockString
          (onSelectStatisticEvent)="onStatisticEventSelect($event)"></app-statistic-event-selector>
      </div>
    </div>

    <span class="title" [ngClass]="{'hide': !hasStatisticEvent()}">{{'STATISTIC_EVENT_KEY_TITLE' | translate}}:</span>
    <span class="block-selected" [ngClass]="{'hide': !hasStatisticEvent()}">
      <span class="block-display">{{StatisticEventData?.Name}}</span>
      <span class="fa fa-unlink trash" (click)="deleteStatisticEvent()" *ngIf="!readOnly" data-toggle="tooltip"
        ngbTooltip="{{ 'STATISTIC_EVENT_REMOVESELECTED' | translate }}" placement="top" container="body"
        tooltipClass="tooltip-trash"></span>

    </span>
  </div>
  <div class="contents" *ngIf="!model.isStructuredDataEnabled(editorService)">
    <app-input-with-variables [placeholder]="'MESSAGE' | translate" [(value)]="model.Message" [isTextArea]="true"
      [wideInput]="true" [disabled]="readOnly">
    </app-input-with-variables>
  </div>

  <div class="structured-data" *ngIf="model.isStructuredDataEnabled(editorService)">
    <div class="title">
      {{ 'STATISTICS_EVENTS_CONFIGURATION_STRUCTURED_DATA' | translate }}
    </div>
    <div class="structured-data-table">
      <div class="header">
        <div>{{ 'STATISTICS_EVENTS_CONFIGURATION_HEADER_NAME' | translate }}</div>
        <div>{{ 'STATISTICS_EVENTS_CONFIGURATION_HEADER_VALUE' | translate }}</div>
        <div></div>
      </div>
      <div class="structured-data-row" *ngFor="let StructuredData of model.StructuredData let i = index">
        <div class="structured-data-value">
          <app-input-with-variables [placeholder]="'STATISTICS_EVENTS_CONFIGURATION_HEADER_NAME' | translate"
            [(value)]="StructuredData.Key" [disabled]="true" id="Key_{{i}}"
            [ngClass]="{'validation-error': !StructuredData.isValidKey()}" [wideInput]="true">
          </app-input-with-variables>
        </div>
        <div class="structured-data-value">
          <app-input-with-variables [placeholder]="'STATISTICS_EVENTS_CONFIGURATION_HEADER_VALUE' | translate"
            [(value)]="StructuredData.Value" [disabled]="readOnly" id="Value_{{i}}"
            [ngClass]="{'validation-error': !StructuredData.isValidValue()}" [wideInput]="true">
          </app-input-with-variables>
        </div>
        <div class="structured-data-info" ngbTooltip="{{StructuredData.Description}}" id="Description_{{i}}"
          data-toggle="tooltip" placement="top" container="body" tooltipClass="tooltip-help">
          <span class="fa fa-question-circle"></span>
        </div>
      </div>
    </div>
    <div class="empty" role="alert" *ngIf="model.StructuredData.length == 0">
      <div class="alert alert-info">
        {{ 'STATISTICS_EVENTS_CONFIGURATION_STRUCTURED_DATA_EMPTY' | translate }}
      </div>
    </div>
  </div>

</div>