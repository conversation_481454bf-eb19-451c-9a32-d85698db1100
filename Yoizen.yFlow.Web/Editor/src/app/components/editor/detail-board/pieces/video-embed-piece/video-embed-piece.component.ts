import {Component, OnInit} from '@angular/core';
import {BasePieceVM} from '../BasePieceVM';
import {EditorService} from '../../../../../services/editor.service';
import {ModalService} from '../../../../../services/Tools/ModalService';
import {isStringValid, isUrlValid} from '../../../../../urlutils.module'
import {VideoEmbedPiece} from "../../../../../models/pieces/VideoEmbedPiece";
import {BasePiece} from "../../../../../models/pieces/BasePiece";
import {ChannelTypes} from "../../../../../models/ChannelType";

@Component({
  selector: 'app-video-embed-piece',
  templateUrl: './video-embed-piece.component.html',
  styleUrls: ['./video-embed-piece.component.scss']
})
export class VideoEmbedPieceComponent extends BasePieceVM implements OnInit {

  model: VideoEmbedPiece;

  constructor(editorService: EditorService, public modalService: ModalService) {
    super(editorService, modalService);
  }

  ngOnInit() {
    this.model = this.context as VideoEmbedPiece;
  }

  isStringValid(str): boolean {
    return isStringValid(str);
  }

  addQuickReplyPiece() {
    this.editorService.addNewPiece(
      this.editorService.createPiece('PIECE_QUICKREPLIES', 'fa-ellipsis-h', 'quick-reply-piece', this.editorService.createQuickReply),
      this.model
    );
  }

  getNextPiece(): BasePiece {
    return this.editorService.getEditorState().SelectedBlock.Pieces[this.index + 1];
  }

  canCreateQuickReply() {
    if (this.readOnly) {
      return false;
    }

    let next = this.getNextPiece();
    if (next != null) {
      if (next.type == 'quick-reply-piece') {
        return false;
      }
    }
    return true;
  }
}
