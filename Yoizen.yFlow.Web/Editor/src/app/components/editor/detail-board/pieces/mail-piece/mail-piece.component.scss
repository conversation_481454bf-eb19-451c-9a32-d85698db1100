@import "_variables";
@import "_mixins";

.mail{
	min-width: 500px;
  max-width: 500px;
	background-color: #fff;
	margin-bottom: 20px;

  .option {
    display: flex;
    flex-direction: row;
    align-items: center;
    margin-bottom: 10px;
    width: 100%;

    .title {
      font-family: $fontFamilyTitles;
      font-weight: bold;
      margin-right: 10px;
      justify-self: center;
      align-self: center;
      text-align: center;
      flex-grow: 0;
      flex-shrink: 0;
    }

    app-input-with-variables {
      flex-grow: 1;
      flex-shrink: 1;
    }

    ui-switch {
      height: 20px;
    }

    &.separator {
      border-top: 1px solid $cardSeparatorBorderColor;
      padding-top: 5px;
      margin-bottom: 5px;
    }

    &:last-child {
      margin-bottom: 0;
    }
  }

  .attachments {
    border-top: 1px solid $gray;

    & > .title {
      font-family: $fontFamilyTitles;
      font-weight: bold;
      margin: 5px 0;
    }

    .info {
      font-style: italic;
      color: lightgrey;
    }

    .attachments-container {
      display: flex;
      flex-direction: column;
      padding-top: 5px;

      @include scrollbar;

      .attachment-row {
        display: flex;
        flex-direction: column;
        border: solid 1px #ebebeb;
        padding: 10px;
        position: relative;

        .trash {
          @include trash;
          position: absolute;
          right: -13px;
          top: -13px;
          cursor: pointer;

          &:hover {
            color: #555;
          }
        }

        &:hover {
          .trash {
            @include trashOver;
          }
        }

        & > .option {
          margin-bottom: 0;
        }
      }
    }

    .attachment-add {
      @include addPieceButton;
      border: solid 1px #ebebeb;
      border-top-style: none;
    }
  }
}
