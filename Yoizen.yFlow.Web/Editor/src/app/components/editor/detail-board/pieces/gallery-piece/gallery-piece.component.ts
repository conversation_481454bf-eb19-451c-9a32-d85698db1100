import { Component, OnInit } from '@angular/core';
import { BasePieceVM } from '../BasePieceVM';
import { EditorService } from '../../../../../services/editor.service';
import { ModalService } from '../../../../../services/Tools/ModalService';
import { GalleryPiece, GalleryImage } from '../../../../../models/pieces/GalleryPiece';
import * as _ from 'lodash';
import { BasePiece } from '../../../../../models/pieces/BasePiece';
import { EventEmitter } from "@angular/core";
import { DeleteCardQuestionComponent } from '../../../popups/delete-card-question/delete-card-question.component';
import { isUrlValid, isStringValid } from '../../../../../urlutils.module'
import { ButtonPiece } from '../../../../../models/pieces/ButtonPiece';
import {ChannelTypes} from "../../../../../models/ChannelType";

@Component({
  selector: 'app-gallery-piece',
  templateUrl: './gallery-piece.component.html',
  styleUrls: ['./gallery-piece.component.scss']
})
export class GalleryPieceComponent extends BasePieceVM implements OnInit {

  model : GalleryPiece;
  expandButton : ButtonPiece;
  channelTypes = ChannelTypes;

  constructor( editorService : EditorService, public modalService : ModalService ) {
    super(editorService, modalService);
  }

  ngOnInit() {
    this.model = this.context as GalleryPiece;
  }

  isStringValid(url) {
    return isStringValid(url);
  }

  isUrlValid(url) {
    if (url === null || url.length === 0) {
      return true;
    }
    return isUrlValid(url);
  }

  addImage() {
    this.model.images.push(new GalleryImage());
  }

  canAddImage() {
    if (this.readOnly) {
      return false;
    }
    return this.model.images.length < 10;
  }

  removeImage(image : GalleryImage) {
    let emmitAction = new EventEmitter();
    emmitAction.subscribe( () => {
      _.remove(this.model.images, (im) => {return im == image; });
      if(this.model.images.length == 0) {
          this.editorService.removePieceForBlockInEdition( this.context as BasePiece );
      }
    });
    this.modalService.init( DeleteCardQuestionComponent, { }, { DeleteAction : emmitAction} );
  }

  addButton(img : GalleryImage) {
    this.expandButton = new ButtonPiece();
    img.buttons.push(this.expandButton);
  }

   canAddButton(img : GalleryImage) : boolean {
    if (this.readOnly) {
      return false;
    }
    var value = img.buttons.length < 20;
    return value;
  }

   deleteButtonElement(img : GalleryImage, element) {
    img.buttons.splice(element, 1);
  }

  onShowButtonDetail(btn : ButtonPiece) {
    this.expandButton = btn;
  }

  addQuickReplyPiece() {
    this.editorService.addNewPiece(
      this.editorService.createPiece('PIECE_QUICKREPLIES', 'fa-ellipsis-h', 'quick-reply-piece', this.editorService.createQuickReply),
      this.model
    );
  }

  getNextPiece() : BasePiece {
    return this.editorService.getEditorState().SelectedBlock.Pieces[this.index +1];
  }

  canCreateQuickReply() {
    if (this.readOnly) {
      return false;
    }

    if (this.model.Channel === ChannelTypes.GoogleRBM) {
      return false;
    }

    let next = this.getNextPiece();
    if( next != null ) {
      if(next.type == 'quick-reply-piece') {
        return false;
      }
    }
    return true;
  }

  moveLeft(index: number) {
    let option = this.model.images[index - 1];
    this.model.images[index - 1] = this.model.images[index];
    this.model.images[index] = option;
  }

  moveRight(index: number) {
    let option = this.model.images[index + 1];
    this.model.images[index + 1] = this.model.images[index];
    this.model.images[index] = option;
  }
}
