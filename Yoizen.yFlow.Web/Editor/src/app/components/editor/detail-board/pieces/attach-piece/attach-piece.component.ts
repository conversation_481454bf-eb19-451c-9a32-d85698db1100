import {Component, OnInit} from '@angular/core';
import {BasePieceVM} from '../BasePieceVM';
import {EditorService} from '../../../../../services/editor.service';
import {ModalService} from '../../../../../services/Tools/ModalService';
import {isUrlValid, isStringValid} from '../../../../../urlutils.module'
import {AttachmentPiece, SourceTypes} from '../../../../../models/pieces/AttachmentPiece';
import {OutputVariableMap} from '../../../../../models/pieces/IntegrationPiece';
import {VariableDefinition} from '../../../../../models/VariableDefinition';
import {TypeDefinition} from '../../../../../models/TypeDefinition';
import {BasePiece} from "../../../../../models/pieces/BasePiece";
import {ChannelTypes} from "../../../../../models/ChannelType";
import {environment} from "../../../../../../environments/environment";

@Component({
  selector: 'app-attach-piece',
  templateUrl: './attach-piece.component.html',
  styleUrls: ['./attach-piece.component.scss']
})
export class AttachPieceComponent extends BasePieceVM implements OnInit {
  model: AttachmentPiece;
  variableFilter = [TypeDefinition.ByteArray, TypeDefinition.Base64];
  ActiveIdString: string;
  showIsPublicToggle: boolean = true;

  get isWhatsappChannel(): boolean {
    return this.model.Channel === ChannelTypes.WhatsApp;
  }

  get isTelegramChannel(): boolean {
    return this.model.Channel === ChannelTypes.Telegram;
  }

  get variableDefinition(): VariableDefinition {
    return this.editorService.findVariableWithId(this.model.FileDataStorageId);
  }

  get isTwitterFlow(): boolean {
    return this.flow.channel == ChannelTypes.Twitter;
  }

  constructor(editorService: EditorService, public modalService: ModalService) {
    super(editorService, modalService);
  }

  ngOnInit() {
    this.model = this.context as AttachmentPiece;
    this.ActiveIdString = this.getActiveTab();
    this.flow = this.editorService.getCurrentFlow();

    // @ts-ignore
    if (typeof(environment.onPremise) !== 'boolean' ||
      // @ts-ignore
      !environment.onPremise) {
      if (this.model.IsPublicUrl) {
        this.showIsPublicToggle = false;
      }
    }
  }

  ngOnChanges() {
    this.ActiveIdString = this.getActiveTab();
  }

  getActiveTab(): string {
    switch (this.model.Source) {
      case SourceTypes.Variable:
        return "tab-variable";
      case SourceTypes.Url:
        return "tab-url";
    }
  }

  isUrlValid(str): Boolean {
    return isUrlValid(str);
  }

  isNameValid(str): Boolean {
    return isStringValid(str);
  }

  isMimeTypeValid(str) {
    if (!isStringValid(str)) {
      return false;
    }

    if (str.indexOf('{{') >= 0 &&
      str.indexOf('}}') >= 0) {
      return true;
    }

    if (str.indexOf('${') >= 0 &&
      str.indexOf('}$') >= 0) {
      return true;
    }

    const regex = new RegExp('^[-a-z]{1,127}/[-a-z0-9\+]+(\.[-a-z0-9\+]+)*$');
    if (!regex.test(str.toString())) {
      return false;
    }

    return true;
  }

  addQuickReplyPiece() {
    this.editorService.addNewPiece(
      this.editorService.createPiece('PIECE_QUICKREPLIES', 'fa-ellipsis-h', 'quick-reply-piece', this.editorService.createQuickReply),
      this.model
    );
  }

  setVariableOnOutput(output: OutputVariableMap, variable: VariableDefinition) {
    if (variable != null) {
      this.model.FileDataStorageId = variable.Id;
    }
    else {
      this.model.FileDataStorageId = null;
    }
  }

  onTabChange(eventInfo) {
    switch (eventInfo.nextId) {
      case "tab-url":
        this.model.Source = SourceTypes.Url;
        return;
      case "tab-variable":
        this.model.Source = SourceTypes.Variable;
        return
    }
  }

  getFileVaraibleValidator() {
    return this.validateFileVariable.bind(this);
  }

  validateFileVariable() {
    return this.model.isFileDataValid(this.editorService);
  }

  getNextPiece(): BasePiece {
    return this.editorService.getEditorState().SelectedBlock.Pieces[this.index + 1];
  }

  canCreateQuickReply() {
    if (this.readOnly) {
      return false;
    }

    if (this.flow.channel !== ChannelTypes.FacebookMessenger &&
      this.flow.channel !== ChannelTypes.Instagram &&
      this.flow.channel !== ChannelTypes.Chat &&
      this.flow.channel !== ChannelTypes.Twitter &&
      this.flow.channel !== ChannelTypes.Skype) {
      return false;
    }

    let next = this.getNextPiece();
    if (next != null) {
      if (next.type == 'quick-reply-piece') {
        return false;
      }
    }
    return true;
  }
}
