﻿<div class="button" [ngClass]="{ 'persistent': IsPersistentMenuButton, 'with-condition': Model.HasVisibilityCondition }" (clickOutside)="onClose.emit()" [delayClickOutsideInit]="true">
  <div>
    <div class="name">
      <input #nameInput autofocus class="input" [disabled]="readOnly" type="text"
             placeholder="{{'BUTTON_TEXT_INPUT' | translate}}" [ngClass]="{'invalid-input': !isStringValid(Model.Name)}"
             [maxLength]="MaxLength"
             [(ngModel)]="Model.Name" (ngModelChange)="updateName($event)" />
    </div>
    <div class="add-condition visibility" (click)="Model.HasVisibilityCondition = true; $event.stopPropagation();"
         *ngIf="!Model.HasVisibilityCondition && !IsPersistentMenuButton && !IsIceBreaker && !readOnly">
      <span class="fa fa-plus"></span> {{ 'CARD_MESSAGE_ADD_VISIBILITY_CONDITION' | translate }}
    </div>
    <div class="data visibility" *ngIf="Model.HasVisibilityCondition && !IsPersistentMenuButton && !IsIceBreaker">
      <span class="title">{{ 'BUTTON_VISIBILITY_CONDITION' | translate}}</span>
      <app-input-with-variables class="condition"
                                [placeholder]="'VALUE' | translate"
                                [(value)]="Model.FirstOperand"
                                [validator]="getFirstValidator()"
                                [customVariableList]="customVariableList"
                                [JoinCustomVariable]="joinCustomVariablesList"
                                [variableFinder]="searchForVariable.bind(this)"
                                [disabled]="readOnly"></app-input-with-variables>
      <select class="select" name="" id="" [(ngModel)]="Model.Operator"
              [ngClass]="{'validation-error': !Model.isOperatorValid()}"
              [disabled]="readOnly">
        <option [ngValue]="null">{{ 'Operando' | translate}}</option>
        <option *ngFor="let operator of getOperators()" [ngValue]="operator.value">{{ operator.localized | translate }}</option>
      </select>
      <app-input-with-variables class="condition" *ngIf="showOperand()"
                                [placeholder]="'VALUE' | translate"
                                [(value)]="Model.SecondOperand"
                                [validator]="getSecondValidator()"
                                [customVariableList]="customVariableList"
                                [JoinCustomVariable]="joinCustomVariablesList"
                                [variableFinder]="searchForVariable.bind(this)"
                                [disabled]="readOnly"></app-input-with-variables>
      <div class="trash" (click)="Model.HasVisibilityCondition=false; $event.stopPropagation();" *ngIf="!readOnly"
           data-toggle="tooltip" ngbTooltip="{{ 'BUTTON_VISIBILITY_CONDITION_DELETE_TEXT' | translate }}"
           placement="top" container="body" tooltipClass="tooltip-trash">
        <span class="fa fa-trash-alt"></span>
      </div>
    </div>
    <div class="tabs">
      <ngb-tabset [activeId]="ActiveIdString" class="tabsbutton" (tabChange)="onTabChange($event)">
        <ngb-tab id="tab-redirect-to-block" *ngIf="isButtonTypeAllowed('redirect')" [disabled]="readOnly">
          <ng-template ngbTabTitle><span class="fa fa-table"></span> {{ 'BUTTON_BLOCK' | translate }}</ng-template>
          <ng-template ngbTabContent>
            <div class="block">
              <label>{{ 'REDIRECT_TO_BLOCK' | translate }}:</label>
              <div class="block-picker">
                <div [ngClass]="{'hide': hasBlock()}"  (clickOutside)="hideBlockSelector = true">
                  <input #blockInput class="input" type="text" placeholder="{{'REDIRECT_TO_BLOCK_PLACEHOLDER' | translate}}"
                         [(ngModel)]="SearchBlockString" LimitLength (focus)="hideBlockSelector = false" />
                  <div class="block-selection-anchor">
                    <app-block-selector *ngIf="!hideBlockSelector" class="select-block" [BlockName]="SearchBlockString"
                                        (onSelectBlock)="onBlockSelect($event)"></app-block-selector>
                  </div>
                </div>
                <span class="block-selected" [ngClass]="{'hide': !hasBlock()}">
                  <span class="block-display">{{BlockData?.Name}}</span>
                  <span class="fa fa-unlink trash" (click)="deleteBlock()" *ngIf="!readOnly"
                        data-toggle="tooltip" ngbTooltip="{{ 'BLOCK_REMOVESELECTED' | translate }}"
                        placement="top" container="body" tooltipClass="tooltip-trash"></span>
                </span>
              </div>
            </div>
            <div class="add-condition condition" (click)="Model.AssignValueToVariable = true; $event.stopPropagation();"
                 *ngIf="!Model.AssignValueToVariable && !IsPersistentMenuButton && !readOnly && allowedToSetValueToVariable">
              <span class="fa fa-plus"></span> {{ 'CARD_MESSAGE_ADD_ASSIGN_VALUE_TO' | translate }}
            </div>
            <div class="data condition" *ngIf="Model.AssignValueToVariable && !IsPersistentMenuButton && allowedToSetValueToVariable">
              <div class="input-row">
                <label class="field-label">{{'EXPRESION' | translate}}:</label>
                <app-input-with-variables [placeholder]="'VALUE' | translate"
                                          [(value)]="Model.AssignValue"
                                          class="expresion-input"
                                          [wideInput]="true"
                                          [disabled]="readOnly"
                                          [customVariableList]="customVariableList"
                                          [JoinCustomVariable]="joinCustomVariablesList"
                                          [variableFinder]="searchForVariable.bind(this)"
                                          [validator]="getVariableValidator()"></app-input-with-variables>
              </div>
              <div class="input-row">
                <label class="field-label">{{'SAVE_INTO' | translate}}:</label>
                <app-variable-selector-input [VariableData]="currentVariable"
                                             (setVariable)="setVariableOnOutput($event)"
                                             [validator]="getAssignVariableIdValid()"
                                             [readOnly]="readOnly"
                                             class="expresion-input"></app-variable-selector-input>
              </div>
              <div class="trash" (click)="Model.AssignValueToVariable=false; $event.stopPropagation();"
                   *ngIf="!readOnly"
                   data-toggle="tooltip" ngbTooltip="{{ 'CARD_MESSAGE_ADD_ASSIGN_VALUE_TO_DELETE_TEXT' | translate }}"
                   placement="top" container="body" tooltipClass="tooltip-trash">
                <span class="fa fa-trash-alt"></span>
              </div>
            </div>
          </ng-template>
        </ngb-tab>
        <ngb-tab id="tab-url-input" *ngIf="isButtonTypeAllowed('url')" [disabled]="readOnly">
          <ng-template ngbTabTitle><span class="fa fa-link"></span> {{ 'BUTTON_URL' | translate }}</ng-template>
          <ng-template ngbTabContent>
            <div class="data url">
              <div class="input-row">
                <label class="field-label">{{'INPUT_URL' | translate}}:</label>
                <app-input-with-variables class="expresion-input"
                                          [placeholder]="'INPUT_URL' | translate"
                                          [(value)]="Model.Url"
                                          [validator]="isValidURL"
                                          [wideInput]="true"
                                          [customVariableList]="customVariableList"
                                          [JoinCustomVariable]="joinCustomVariablesList"
                                          [variableFinder]="searchForVariable.bind(this)"
                                          [disabled]="readOnly"></app-input-with-variables>
              </div>
              <div class="input-row" *ngIf="!hideWebViewSize">
                <label class="field-label">{{'BUTTON_URL_SIZE' | translate}}:</label>
                <select class="select" [(ngModel)]="Model.WebViewSize" [disabled]="readOnly">
                  <option *ngFor="let size of getWebViewSizes()" [ngValue]="size.value">{{ size.localized | translate }}
                  </option>
                </select>
              </div>
            </div>
          </ng-template>
        </ngb-tab>
        <ngb-tab id="tab-auth-select" *ngIf="!IsPersistentMenuButton && isButtonTypeAllowed('auth')" [disabled]="readOnly">
          <ng-template ngbTabTitle><span class="fa fa-certificate"></span> {{ 'BUTTON_LINK' | translate }}</ng-template>
          <ng-template ngbTabContent>
            <div class="option">
              <span class="title">{{ 'BUTTON_AUTH_URL' | translate }}:</span><br/>
              <app-input-with-variables [placeholder]="'INPUT_URL' | translate"
                                        [(value)]="Model.AuthLoginUrl"
                                        [validator]="isValidURL"
                                        [wideInput]="true"
                                        [customVariableList]="customVariableList"
                                        [JoinCustomVariable]="joinCustomVariablesList"
                                        [variableFinder]="searchForVariable.bind(this)"
                                        [disabled]="readOnly"></app-input-with-variables>
            </div>
            <div class="option" *ngIf="isAuthAlternativeMethodAllowed">
              <span class="title">{{ 'BUTTON_AUTH_USE_ALTERNATIVE' | translate }}:</span><br/>
              <ui-switch [(ngModel)]="Model.AuthUseAlternativeMethod" [disabled]="readOnly"
                         color="#45c195" size="small" defaultBgColor="#e0e0e0" switchColor="#ffffff"></ui-switch>
              <div class="info" ngbTooltip="{{ 'BUTTON_AUTH_USE_ALTERNATIVE_INFO' | translate }}"
                   placement="right" container="body" tooltipClass="tooltip-persistent">
                <span class="fa fa-question-circle"></span>
              </div>
            </div>
          </ng-template>
        </ngb-tab>
        <ngb-tab id="tab-unlink" *ngIf="!IsPersistentMenuButton && isButtonTypeAllowed('unlink')" [disabled]="readOnly">
          <ng-template ngbTabTitle><span class="fa fa-ban"></span> {{ 'BUTTON_UNLINK' | translate }}</ng-template>
          <ng-template ngbTabContent>
            <div class="empty" role="alert">
              <div class="alert alert-info">
                <span class="fa fa-lg fa-info-circle icon"></span>
                {{ 'BUTTON_UNLINK_DESCRIPTION' | translate }}
              </div>
            </div>
            <div class="option" *ngIf="isAuthAlternativeMethodAllowed" style="margin-top: 5px">
              <span class="title">{{ 'BUTTON_AUTH_USE_ALTERNATIVE' | translate }}:</span><br/>
              <ui-switch [(ngModel)]="Model.AuthUseAlternativeMethodForUnlink" [disabled]="readOnly"
                         color="#45c195" size="small" defaultBgColor="#e0e0e0" switchColor="#ffffff"></ui-switch>
              <div class="info" ngbTooltip="{{ 'BUTTON_AUTH_USE_ALTERNATIVE_INFO' | translate }}"
                   placement="right" container="body" tooltipClass="tooltip-persistent">
                <span class="fa fa-question-circle"></span>
              </div>
            </div>
          </ng-template>
        </ngb-tab>
        <ngb-tab id="tab-create-submenu" *ngIf="DisplayCreateMenuOption" [disabled]="readOnly">
          <ng-template ngbTabTitle><span class="fa fa-align-left"></span> {{ 'BUTTON_SUBMENU' | translate}}</ng-template>
          <ng-template ngbTabContent>
            <div class="empty" role="alert">
              <div class="alert alert-info">
                <span class="fa fa-lg fa-info-circle icon"></span>
                {{ 'BUTTON_SUBMENU_DESCRIPTION' | translate }}
              </div>
            </div>
          </ng-template>
        </ngb-tab>
        <ngb-tab id="tab-location" *ngIf="isButtonTypeAllowed('location')" [disabled]="readOnly">
          <ng-template ngbTabTitle><span class="fa fa-location-arrow"></span> {{ 'BUTTON_LOCATION' | translate }}</ng-template>
          <ng-template ngbTabContent>
            <div class="block">
              <label>{{ 'REDIRECT_TO_BLOCK' | translate }}:</label>
              <div class="block-picker">
                <div [ngClass]="{'hide': hasBlock()}" (clickOutside)="hideBlockSelector = true" *ngIf="!readOnly">
                  <input #blockInput LimitLength
                         class="input"
                         type="text"
                         placeholder="{{'REDIRECT_TO_BLOCK_PLACEHOLDER' | translate}}"
                         [(ngModel)]="SearchBlockString"
                         (focus)="hideBlockSelector = false" />
                  <div class="block-selection-anchor">
                    <app-block-selector *ngIf="!hideBlockSelector" class="select-block" [BlockName]="SearchBlockString"
                                        (onSelectBlock)="onBlockSelect($event)"></app-block-selector>
                  </div>
                </div>
                <span class="block-selected" [ngClass]="{'hide': !hasBlock()}">
                  <span class="block-display">{{BlockData?.Name}}</span>
                  <span class="fa fa-unlink trash" (click)="deleteBlock()" *ngIf="!readOnly"
                        data-toggle="tooltip" ngbTooltip="{{ 'BLOCK_REMOVESELECTED' | translate }}"
                        placement="top" container="body" tooltipClass="tooltip-trash"></span>
                </span>
              </div>
            </div>
            <div class="add-condition condition" (click)="Model.AssignValueToVariable = true; $event.stopPropagation();"
                 *ngIf="!Model.AssignValueToVariable && !IsPersistentMenuButton && !readOnly">
              <span class="fa fa-plus"></span> {{ 'CARD_MESSAGE_ADD_ASSIGN_VALUE_TO' | translate }}
            </div>
            <div class="data condition" *ngIf="Model.AssignValueToVariable && !IsPersistentMenuButton">
              <div class="input-row">
                <label class="field-label">{{'EXPRESION' | translate}}:</label>
                <app-input-with-variables [placeholder]="'VALUE' | translate"
                                          [(value)]="Model.AssignValue"
                                          class="expresion-input"
                                          [wideInput]="true"
                                          [disabled]="readOnly"
                                          [customVariableList]="customVariableList"
                                          [JoinCustomVariable]="joinCustomVariablesList"
                                          [variableFinder]="searchForVariable.bind(this)"
                                          [validator]="getVariableValidator()"></app-input-with-variables>
              </div>
              <div class="input-row">
                <label class="field-label">{{'SAVE_INTO' | translate}}:</label>
                <app-variable-selector-input [VariableData]="currentVariable"
                                             (setVariable)="setVariableOnOutput($event)"
                                             [validator]="getAssignVariableIdValid()"
                                             [readOnly]="readOnly"
                                             class="expresion-input"></app-variable-selector-input>
              </div>
              <div class="trash" (click)="Model.AssignValueToVariable=false; $event.stopPropagation();"
                   *ngIf="!readOnly"
                   data-toggle="tooltip" ngbTooltip="{{ 'CARD_MESSAGE_ADD_ASSIGN_VALUE_TO_DELETE_TEXT' | translate }}"
                   placement="top" container="body" tooltipClass="tooltip-trash">
                <span class="fa fa-trash-alt"></span>
              </div>
            </div>
          </ng-template>
        </ngb-tab>
        <ngb-tab id="tab-locationwithmap" *ngIf="isButtonTypeAllowed('location_with_map')" [disabled]="readOnly">
          <ng-template ngbTabTitle><span class="fa fa-map-marker"></span> {{ 'BUTTON_LOCATION_WITH_MAP' | translate }}</ng-template>
          <ng-template ngbTabContent>
            <div class="block">
              <label>{{ 'REDIRECT_TO_BLOCK' | translate }}:</label>
              <div class="block-picker">
                <div [ngClass]="{'hide': hasBlock()}" *ngIf="!readOnly">
                  <input #blockInput LimitLength
                         class="input"
                         type="text"
                         placeholder="{{'REDIRECT_TO_BLOCK_PLACEHOLDER' | translate}}"
                         autocomplete="nopongasninguno"
                         spellcheck="false"
                         [(ngModel)]="SearchBlockString"
                         (focus)="hideBlockSelector = false" />
                  <div class="block-selection-anchor">
                    <app-block-selector *ngIf="!hideBlockSelector" class="select-block" [BlockName]="SearchBlockString"
                                        (onSelectBlock)="onBlockSelect($event)"></app-block-selector>
                  </div>
                </div>
                <span class="block-selected" [ngClass]="{'hide': !hasBlock()}">
                  <span class="block-display">{{BlockData?.Name}}</span>
                  <span class="fa fa-unlink trash" (click)="deleteBlock()" *ngIf="!readOnly"
                        data-toggle="tooltip" ngbTooltip="{{ 'BLOCK_REMOVESELECTED' | translate }}"
                        placement="top" container="body" tooltipClass="tooltip-trash"></span>
                </span>
              </div>
            </div>
            <div class="add-condition condition" (click)="Model.AssignValueToVariable = true; $event.stopPropagation();"
                 *ngIf="!Model.AssignValueToVariable && !IsPersistentMenuButton && !readOnly">
              <span class="fa fa-plus"></span> {{ 'CARD_MESSAGE_ADD_ASSIGN_VALUE_TO' | translate }}
            </div>
            <div class="data condition" *ngIf="Model.AssignValueToVariable && !IsPersistentMenuButton">
              <div class="input-row">
                <label class="field-label">{{'EXPRESION' | translate}}:</label>
                <app-input-with-variables [placeholder]="'VALUE' | translate"
                                          [(value)]="Model.AssignValue"
                                          class="expresion-input"
                                          [wideInput]="true"
                                          [disabled]="readOnly"
                                          [customVariableList]="customVariableList"
                                          [JoinCustomVariable]="joinCustomVariablesList"
                                          [variableFinder]="searchForVariable.bind(this)"
                                          [validator]="getVariableValidator()"></app-input-with-variables>
              </div>
              <div class="input-row">
                <label class="field-label">{{'SAVE_INTO' | translate}}:</label>
                <app-variable-selector-input [VariableData]="currentVariable"
                                             (setVariable)="setVariableOnOutput($event)"
                                             [validator]="getAssignVariableIdValid()"
                                             [readOnly]="readOnly"
                                             class="expresion-input"></app-variable-selector-input>
              </div>
              <div class="trash" (click)="Model.AssignValueToVariable=false; $event.stopPropagation();"
                   *ngIf="!readOnly"
                   data-toggle="tooltip" ngbTooltip="{{ 'CARD_MESSAGE_ADD_ASSIGN_VALUE_TO_DELETE_TEXT' | translate }}"
                   placement="top" container="body" tooltipClass="tooltip-trash">
                <span class="fa fa-trash-alt"></span>
              </div>
            </div>
          </ng-template>
        </ngb-tab>
        <ngb-tab id="tab-dial" *ngIf="isButtonTypeAllowed('dial')" [disabled]="readOnly">
          <ng-template ngbTabTitle><span class="fa fa-phone"></span> {{ 'BUTTON_DIAL' | translate }}</ng-template>
          <ng-template ngbTabContent>
            <div class="data url">
              <div class="input-row">
                <label class="field-label">{{'BUTTON_DIAL_PHONENUMBER' | translate}}:</label>
                <app-input-with-variables class="expresion-input"
                                          [placeholder]="'BUTTON_DIAL_PHONENUMBER' | translate"
                                          [(value)]="Model.PhoneNumber"
                                          [validator]="Model.phoneNumberValid.bind(Model)"
                                          [wideInput]="true"
                                          [customVariableList]="customVariableList"
                                          [JoinCustomVariable]="joinCustomVariablesList"
                                          [variableFinder]="searchForVariable.bind(this)"
                                          [disabled]="readOnly"></app-input-with-variables>
              </div>
            </div>
          </ng-template>
        </ngb-tab>
        <ngb-tab id="tab-send-location" *ngIf="isButtonTypeAllowed('send-location')" [disabled]="readOnly">
          <ng-template ngbTabTitle><span class="fa fa-map"></span> {{ 'BUTTON_SEND_LOCATION' | translate }}</ng-template>
          <ng-template ngbTabContent>
            <div class="data url">
              <div class="input-row">
                <label class="field-label">{{'COORDINATES_LATITUDE' | translate}}:</label>
                <app-input-with-variables class="expresion-input"
                                          [placeholder]="'COORDINATES_LATITUDE' | translate"
                                          [(value)]="Model.Latitude"
                                          [validator]="Model.latitudeValid.bind(Model, editorService)"
                                          [wideInput]="true"
                                          [customVariableList]="customVariableList"
                                          [JoinCustomVariable]="joinCustomVariablesList"
                                          [variableFinder]="searchForVariable.bind(this)"
                                          [disabled]="readOnly"></app-input-with-variables>
              </div>
              <div class="input-row">
                <label class="field-label">{{'COORDINATES_LONGITUDE' | translate}}:</label>
                <app-input-with-variables class="expresion-input"
                                          [placeholder]="'COORDINATES_LONGITUDE' | translate"
                                          [(value)]="Model.Longitude"
                                          [validator]="Model.longitudeValid.bind(Model, editorService)"
                                          [wideInput]="true"
                                          [customVariableList]="customVariableList"
                                          [JoinCustomVariable]="joinCustomVariablesList"
                                          [variableFinder]="searchForVariable.bind(this)"
                                          [disabled]="readOnly"></app-input-with-variables>
              </div>
            </div>
          </ng-template>
        </ngb-tab>
        <ngb-tab id="tab-calendar" *ngIf="isButtonTypeAllowed('calendar')" [disabled]="readOnly">
          <ng-template ngbTabTitle><span class="fa fa-calendar"></span> {{ 'BUTTON_CALENDAR' | translate }}</ng-template>
          <ng-template ngbTabContent>

          </ng-template>
        </ngb-tab>
      </ngb-tabset>
    </div>
    <div class="close-button" (click)="onClose.emit()">
      <span class="fa fa-window-close"></span>
    </div>
  </div>
</div>
