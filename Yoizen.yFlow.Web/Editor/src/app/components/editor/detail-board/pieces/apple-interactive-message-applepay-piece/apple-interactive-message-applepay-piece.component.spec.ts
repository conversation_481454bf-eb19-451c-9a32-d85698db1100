import { async, ComponentFixture, TestBed } from '@angular/core/testing';

import { AppleInteractiveMessageApplePayPieceComponent } from './apple-interactive-message-applepay-piece.component';

describe('AppleInteractiveMessageApplePayPieceComponent', () => {
  let component: AppleInteractiveMessageApplePayPieceComponent;
  let fixture: ComponentFixture<AppleInteractiveMessageApplePayPieceComponent>;

  beforeEach(async(() => {
    TestBed.configureTestingModule({
      declarations: [ AppleInteractiveMessageApplePayPieceComponent ]
    })
    .compileComponents();
  }));

  beforeEach(() => {
    fixture = TestBed.createComponent(AppleInteractiveMessageApplePayPieceComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });
});
