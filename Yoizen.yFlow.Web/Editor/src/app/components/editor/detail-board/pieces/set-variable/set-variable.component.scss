@import '_variables';
@import '_mixins';

.condition {
  background-color: #fff;
  width: 600px;

  .add-condition {
    text-align: center;
    text-transform: uppercase;
    padding: 15px 0;
    cursor: pointer;
    font-size: 12px;
    position: relative;
    font-weight: normal;
    color: $linkActionColor;
    border-bottom: $cardSeparator;
    margin-bottom: 10px;

    &:hover {
      color: lighten($linkActionColor, 10%);
    }

    span {
      display: inline-block;
      margin-right: 10px;
      position: absolute;
      left: -10px;
      top: 18px;
      margin-left: 30px;
    }
  }

  .data {
    display: flex;
    flex-direction: row;
    margin-bottom: 10px;
    width: 100%;

    &.condition {
      border-bottom: $cardSeparator;
      position: relative;
      padding: 10px 0;

      .select {
        margin-left: 5px;
        margin-right: 5px;
      }

      .trash {
        @include trash;
        position: absolute;
        right: -16px;
        top: 50%;
        transform: translateY(-50%);

        &:hover {
          color: #555;
        }
      }

      &:hover {
        .trash {
          @include trashOver;
        }
      }
    }

    .title {
      font-family: $fontFamilyTitles;
      font-weight: bold;
      margin-right: 10px;
      justify-self: center;
      align-self: center;
      text-align: center;
      flex-grow: 0;
      flex-shrink: 0;
    }

    .input-variable-area {
      flex-grow: 1;
    }

    &:last-child {
      margin-bottom: 0;
    }
  }
}
