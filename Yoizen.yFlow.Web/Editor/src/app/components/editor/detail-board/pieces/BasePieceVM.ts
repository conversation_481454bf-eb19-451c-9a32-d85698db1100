import { BaseDynamicComponent } from "../../../utils/component-holder/BaseDynamicComponent";
import { EditorService } from "../../../../services/editor.service";
import { BasePiece } from "../../../../models/pieces/BasePiece";
import { EventEmitter } from "@angular/core";
import { ModalService } from "../../../../services/Tools/ModalService";
import { DeletePieceQuestionComponent } from "../../popups/delete-piece-question/delete-piece-question.component";
import {BlockSelectorDialogComponent} from "../../popups/block-selector-dialog/block-selector-dialog.component";
import {BlockDefinition} from "../../../../models/BlockDefinition";

export class BasePieceVM extends BaseDynamicComponent {
  public stats : any = null;

  constructor(public editorService : EditorService, public modalService : ModalService) {
    super();
  }

  deleteAction() {
    let emmitAction = new EventEmitter();
    emmitAction.subscribe( () => {
      this.editorService.removePieceForBlockInEdition( this.context as BasePiece );
    });
    this.modalService.init( DeletePieceQuestionComponent, { }, { DeleteAction : emmitAction} );
  }

  cloneAction() {
    this.editorService.clonePiece( this.context as BasePiece );
  }

  exportAction() {
    let emitAction = new EventEmitter<BlockDefinition>();
    emitAction.subscribe( (block: BlockDefinition) => {
      this.editorService.clonePiece( this.context as BasePiece, block );
    });
    this.modalService.init( BlockSelectorDialogComponent, { }, { AcceptAction : emitAction} );
  }
}
