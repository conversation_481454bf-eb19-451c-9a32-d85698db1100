import { Component, OnInit } from '@angular/core';
import { BlockDefinition } from 'src/app/models/BlockDefinition';
import { ErrorMessage, ValidateBackDniPiece } from 'src/app/models/pieces/ValidateBackDniPiece';
import { EditorService } from 'src/app/services/editor.service';
import { ModalService } from 'src/app/services/Tools/ModalService';
import { BasePieceVM } from '../BasePieceVM';

@Component({
  selector: 'app-validate-back-dni-piece',
  templateUrl: './validate-back-dni-piece.component.html',
  styleUrls: ['./validate-back-dni-piece.component.scss']
})
export class ValidateBackDniPieceComponent extends BasePieceVM implements OnInit {
  model: ValidateBackDniPiece;

  constructor(editorService: EditorService, public modalService: ModalService) {
    super(editorService, modalService);
  }

  ngOnInit() {
    this.model = this.context as ValidateBackDniPiece;
  }


  onSelectBlock(blockData: BlockDefinition) {
    this.model.ErrorBlockId = blockData.Id;
  }

  onDeleteBlock(blockData: BlockDefinition) {
    this.model.ErrorBlockId = null;
  }

  addNewText() {
    this.model.ErrorMessages.push(new ErrorMessage());
  }

  canAddTextOptions(): boolean {
    var value = this.model.ErrorMessages.length < 3;
    return value;
  }

  deleteErrorMessage(element) {
    this.model.ErrorMessages.splice(element, 1);
  }


}
