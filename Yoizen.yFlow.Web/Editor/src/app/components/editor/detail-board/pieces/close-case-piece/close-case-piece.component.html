<div class="update-case card">
  <div class="trash" (click)="deleteAction()" *ngIf="!readOnly"
       data-toggle="tooltip" ngbTooltip="{{ 'CARD_DELETE_TEXT' | translate }}" placement="top" container="body" tooltipClass="tooltip-trash">
    <span class="fa fa-trash-alt"></span>
  </div>
  <div class="clone" (click)="cloneAction()" *ngIf="!readOnly"
       data-toggle="tooltip" ngbTooltip="{{ 'CARD_CLONE_TEXT' | translate }}" placement="top" container="body" tooltipClass="tooltip-trash">
    <span class="fa fa-clone"></span>
  </div>
  <div class="export" (click)="exportAction()" *ngIf="!readOnly"
       data-toggle="tooltip" ngbTooltip="{{ 'CARD_EXPORT_TEXT' | translate }}" placement="top" container="body" tooltipClass="tooltip-trash">
    <span class="fa fa-file-export"></span>
  </div>
  <div class="card-title">
    <span class="fa fa-edit"></span> {{ 'PIECE_CLOSECASE' | translate }}
  </div>
  <div class="card-info">
    {{ 'CARD_UPDATECASE_INFO' | translate }}
  </div>
  <div class="close-case">
    <div class="alert alert-warning" *ngIf="model.Channel === channelTypes.Generic">
      <span class="fa fa-lg fa-exclamation-triangle icon"></span>
      {{ 'UPDATECASE_CLOSECASE_WARNING_GENERIC_CHANEL' | translate }}
    </div>
    <div class="alert alert-info">
      <span class="fa fa-lg fa-exclamation-triangle icon"></span>
      {{ 'UPDATECASE_CLOSECASE_WARNING' | translate }}
    </div>
    <div class="option" >
      <span class="title">{{'UPDATECASE_CLOSECASE_TITLE' | translate}}:</span>
      <ui-switch [(ngModel)]="model.closeCase" [disabled]="readOnly"
                 color="#45c195" size="small" defaultBgColor="#e0e0e0" switchColor="#ffffff"></ui-switch>
    </div>
  </div>
  <div class="mark-as-pending-reply" *ngIf="!model.closeCase && model.Channel !== channelTypes.Chat">
    <div class="alert alert-info">
      <span class="fa fa-lg fa-exclamation-circle icon"></span>
      {{ 'UPDATECASE_MARKASPENDINGREPLY_INFO' | translate }}
    </div>
    <div class="option" >
      <span class="title">{{'DATAENTRY_MARKASPENDINGREPLY_TITLE' | translate}}:</span>
      <select class="select" [(ngModel)]="model.markAsPendingReplyFromCustomer">
        <option [ngValue]="markAsPendingReplyFromCustomerTypes.No">{{ 'NO' | translate }}</option>
        <option [ngValue]="markAsPendingReplyFromCustomerTypes.YesAndJump">{{ 'DATAENTRY_MARKASPENDINGREPLY_YES_AND_JUMP' | translate }}</option>
        <option [ngValue]="markAsPendingReplyFromCustomerTypes.YesAndSendMessage">{{ 'DATAENTRY_MARKASPENDINGREPLY_YES_AND_SENDMESSAGE' | translate }}</option>
      </select>
    </div>
    <div class="option" *ngIf="model.markAsPendingReplyFromCustomer === markAsPendingReplyFromCustomerTypes.YesAndJump">
      <span class="title">{{'DATAENTRY_MARKASPENDINGREPLY_BLOCK_TITLE' | translate}}:</span>
      <app-block-picker class="input"
                        [blockId]="model.pendingReplyFromCustomerBlockId"
                        (onSelectNewBlock)="onSelectPendingReplyFromCustomerBlock($event)"
                        (onDeleteBlock)="onDeletePendingReplyFromCustomerBlock($event)"
                        [readOnly]="readOnly"
                        [isInvalid]="!model.isPendingReplyFromCustomerBlockValid(editorService)"></app-block-picker>
    </div>
    <div class="option" *ngIf="model.markAsPendingReplyFromCustomer === markAsPendingReplyFromCustomerTypes.YesAndSendMessage">
      <span class="title">{{'DATAENTRY_MARKASPENDINGREPLY_MESSAGE_TITLE' | translate}}:</span>
      <app-input-with-variables
        [placeholder]="'MESSAGE' | translate"
        [(value)]="model.customMessageForPendingReply"
        [isTextArea]="true"
        [wideInput]="true"
        [validator]="isCustomMessageForPendingReplyValid()"
        [extendedStyles]="{'height': '100px', 'min-height': '100px', 'max-height': '100px'}"
        [disabled]="readOnly">
      </app-input-with-variables>
    </div>
    <div class="option" *ngIf="model.markAsPendingReplyFromCustomer !== markAsPendingReplyFromCustomerTypes.No">
      <span class="title">{{'DATAENTRY_MARKASPENDINGREPLY_MINUTES_TITLE' | translate}}:</span>
      <input type="number"
             class="input"
             min="0"
             max="1440"
             step="10"
             spellcheck="false"
             [(ngModel)]="model.pendingReplyFromCustomerMinutes"
             [readOnly]="readOnly"
             [ngClass]="{'invalid-input': !model.isPendingReplyFromCustomerMinutesValid()}" />
    </div>
  </div>
</div>
