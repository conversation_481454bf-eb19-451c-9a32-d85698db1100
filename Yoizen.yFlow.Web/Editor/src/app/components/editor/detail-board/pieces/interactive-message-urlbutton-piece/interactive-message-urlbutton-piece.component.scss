@import "_variables";
@import "_mixins";

.text {
  min-width: 300px;
  background: #fff;
  position: relative;
  max-width: 680px;

  .more-buttons-than-allowed {
    display: none;
  }

  &.buttons-warning {
    .more-buttons-than-allowed {
      display: block;
    }
  }

  .max-length {
    display: none;

    .messenger, .twitter, .whatsapp, .telegram {
      display: none;
    }
  }

  .messages {
    display: flex;
    flex-direction: row;
    border-bottom: solid 1px #ebebeb;

    app-text-list-message {
      width: 218px;
    }

    .addText {
      @include addButton;
      width: 40px;
      flex-grow: 0;
      flex-shrink: 0;
    }
  }

  &.messenger {
    .max-length {
      display: block;

      .messenger {
        display: block;
      }
    }
  }

  &.twitter {
    .max-length {
      display: block;

      .twitter {
        display: block;
      }
    }
  }

  &.whatsapp {
    .max-length {
      display: block;

      .whatsapp {
        display: block;
      }
    }

    .messages {
      border-bottom-style: none;
    }
  }

  .quick, .addButton {
    @include addPieceButton;
  }

  .addName, .addButton {
    border-bottom: solid 1px #ebebeb;
  }
  .addName {
    padding: 10px;
  }

  textarea {
    width: 100%;
    background: transparent;
    white-space: pre-wrap;
    height: 33px;
    font-weight: normal;
    border-radius: 10px;
    border: 1px solid #9a9a9a;
    padding-left: 10px;
  }



  .option {
    display: flex;
    flex-direction: row;
    align-items: center;
    margin-bottom: 10px;
    width: 100%;

    .title {
      font-family: $fontFamilyTitles;
      font-weight: bold;
      margin-right: 10px;
      justify-self: center;
      align-self: center;
      text-align: center;
      flex-grow: 0;
      flex-shrink: 0;
    }

    .input {
      flex-grow: 1;
    }
  }

  .header, .body, .footer, .summary, .button {
    border-bottom: 1px solid $gray;
    margin-bottom: 10px;

    & > .title {
      font-family: $fontFamilyTitles;
      font-size: 120%;
      font-weight: bold;
      margin: 5px 0;
    }
  }

  .header {
    .multimedia {
      padding: 10px;

      .name, .mimetype, .url, .publicurl {
        display: flex;
        flex-direction: row;
        margin-bottom: 10px;
        width: 100%;

        .title {
          font-family: $fontFamilyTitles;
          font-weight: bold;
          margin-right: 10px;
          justify-self: center;
          align-self: center;
          text-align: center;
          flex-grow: 0;
          flex-shrink: 0;
        }

        .input-variable-area {
          flex-grow: 1;
        }
      }

      .mimetype, .publicurl {
        margin-bottom: 0;
      }
    }
  }

  .summary {
    border-bottom-style: none;
  }
}
