@import "_variables";
@import "_mixins";

.update-profile {
  background-color: #fff;
  min-width: 500px;

  .option {
    display: flex;
    flex-direction: row;
    margin-bottom: 10px;
    width: 100%;

    .title {
      font-family: $fontFamilyTitles;
      font-weight: bold;
      margin-right: 10px;
      justify-self: center;
      align-self: center;
      text-align: center;
      flex-grow: 0;
      flex-shrink: 0;
    }

    app-input-with-variables {
      flex-grow: 1;
      flex-shrink: 1;
    }
  }

  .extended-profile {
    margin-top: 10px;

    & > .title {
      font-family: $fontFamilyTitles;
      font-weight: bold;
      font-size: 120%;
      margin-bottom: 5px;
    }

    .extended-profile-table {
      display: table;
      width: 100%;

      .header {
        display: table-header-group;
        font-family: $fontFamilyTitles;

        & > div {
          display: table-cell;
          vertical-align: middle;
          border-bottom: 1px solid $sidebarBorderColor;
        }
      }

      .extended-profile-row {
        display: table-row;
        width: 100%;
        margin-top: 5px;
        height: 40px;
        min-height: 40px;

        & > div {
          display: table-cell;
          vertical-align: middle;
          border-bottom: 1px solid $sidebarBorderColor;
          padding-left: 3px;
          padding-right: 3px;
        }

        .extended-profile-value {
          width: 150px;
        }

        .extended-profile-variable {
          width: 200px;
        }

        .trash {
          width: 30px;

          & > div {
            @include trash;
            cursor: pointer;

            &:hover {
              color: #555;
            }
          }
        }

        &:hover {
          .trash {
            & > div {
              @include trashOver;
            }
          }
        }
      }
    }
    .add {
      color: $linkActionColor;
      text-transform: uppercase;
      font-size: 12px;
      margin: 20px 10px;
      width: max-content;
      cursor: pointer;

      &:hover {
        color: lighten($linkActionColor, 10%);
      }
    }
  }
}
