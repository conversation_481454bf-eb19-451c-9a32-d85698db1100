import { Component, OnInit } from '@angular/core';
import { BaseDynamicComponent } from '../../../../utils/component-holder/BaseDynamicComponent';
import { BasePieceVM } from '../BasePieceVM';
import { EditorService } from '../../../../../services/editor.service';
import { ModalService } from '../../../../../services/Tools/ModalService';
import { BlockDefinition } from '../../../../../models/BlockDefinition';
import {JumpToBlockPiece} from "../../../../../models/pieces/JumpToBlockPiece";

@Component({
  selector: 'app-jump-to-block-piece',
  templateUrl: './jump-to-block-piece.component.html',
  styleUrls: ['./jump-to-block-piece.component.scss']
})
export class JumpToBlockPieceComponent extends BasePieceVM implements OnInit {

  model: JumpToBlockPiece;

  constructor( editorService : EditorService, public modalService : ModalService ) {
    super(editorService, modalService);
   }

   ngOnInit() {
    this.model = this.context as JumpToBlockPiece;
  }

  onSelectBlock(blockData : BlockDefinition) {
    this.model.BlockId = blockData.Id;
  }

  onDeleteBlock(blockData : BlockDefinition) {
    this.model.BlockId = null;
  }
}
