import { Component, OnInit } from '@angular/core';
import { BaseDynamicComponent } from '../../../../utils/component-holder/BaseDynamicComponent';
import { BasePieceVM } from '../BasePieceVM';
import { EditorService } from '../../../../../services/editor.service';
import { ModalService } from '../../../../../services/Tools/ModalService';
import { BlockDefinition } from '../../../../../models/BlockDefinition';
import { VariableDefinition } from "../../../../../models/VariableDefinition";
import { TypeDefinition } from "../../../../../models/TypeDefinition";
import { GeocoderGooglePiece } from "../../../../../models/pieces/GeocoderGooglePiece";
import { ChannelTypes } from "../../../../../models/ChannelType";
import { Concatenate } from "../../../../../models/pieces/Concatenate";

@Component({
  selector: 'app-geocoder-google-piece',
  templateUrl: './geocoder-google-piece.component.html',
  styleUrls: ['./geocoder-google-piece.component.scss']
})
export class GeocoderGooglePieceComponent extends BasePieceVM implements OnInit {
  model: GeocoderGooglePiece;
  addressComponentFilter = [TypeDefinition.Text, TypeDefinition.Object];
  variableObjectFilter = [TypeDefinition.Object];
  addressFilter = [TypeDefinition.Text];
  LatLongFilter = [TypeDefinition.Decimal,TypeDefinition.Text];

  constructor(editorService: EditorService, public modalService: ModalService) {
    super(editorService, modalService);
  }

  VariableData(variable: number): VariableDefinition {
    return this.editorService.getVariableWithId(variable);
  }

  ngOnInit() {
    this.model = this.context as GeocoderGooglePiece;
    let flow = this.editorService.getCurrentFlow();

    if (this.model.StreetNumberVariableId !== -1) {
      this.setVariableOnInit(this.model.StreetNumberVariableId);
    }
    if (this.model.StreetVariableId !== -1) {
      this.setVariableOnInit(this.model.StreetVariableId);
    }
    if (this.model.LocalityVariableId !== -1) {
      this.setVariableOnInit(this.model.LocalityVariableId);
    }
    if (this.model.ProvinceVariableId !== -1) {
      this.setVariableOnInit(this.model.ProvinceVariableId);
    }
    if (this.model.CountryVariableId !== -1) {
      this.setVariableOnInit(this.model.CountryVariableId);
    }
    if (this.model.ZipCodeVariableId !== -1) {
      this.setVariableOnInit(this.model.ZipCodeVariableId);
    }
    if (this.model.ZipCodeSuffixVariableId !== -1) {
      this.setVariableOnInit(this.model.ZipCodeSuffixVariableId);
    }
    if (this.model.AddressVariableId !== -1) {
      this.setVariableOnInit(this.model.AddressVariableId);
    }
    if (this.model.ObjectVariableId !== -1) {
      this.setVariableOnInit(this.model.ObjectVariableId);
    }
    if (this.model.PlaceIdVariableId !== -1) {
      this.setVariableOnInit(this.model.PlaceIdVariableId);
    }    
    if (this.model.SublocalityVariableId !== -1) {
      this.setVariableOnInit(this.model.SublocalityVariableId);
    }
    if (this.model.DepartmentVariableId !== -1){
      this.setVariableOnInit(this.model.DepartmentVariableId);
    }    
  }

  setVariableOnInit(variable: number) {
    var variableDefinition = this.VariableData(variable);
    if (variableDefinition != null) {
      variable = variableDefinition.Id;
    }
    else {
      variable = -1;
    }
  }

  setVariable(variable: string, variableDefinition: VariableDefinition) {
    var value = -1;
    if (variableDefinition != null) {
      value = variableDefinition.Id;
    }

    switch(variable){
      case 'LatitudeVariableId':
        this.model.LatitudeVariableId = value;
        break;
      case 'LongitudeVariableId':
        this.model.LongitudeVariableId = value;
        break;
      case 'StreetNumberVariableId':
        this.model.StreetNumberVariableId = value;
        break;
      case 'StreetVariableId':
        this.model.StreetVariableId = value;
        break;
      case 'LocalityVariableId':
        this.model.LocalityVariableId = value;
        break;
      case 'ProvinceVariableId':
        this.model.ProvinceVariableId = value;
        break;
      case 'CountryVariableId':
        this.model.CountryVariableId = value;
        break;
      case 'ZipCodeVariableId':
        this.model.ZipCodeVariableId = value;
        break;
      case 'ZipCodeSuffixVariableId':
        this.model.ZipCodeSuffixVariableId = value;
        break;
      case 'AddressVariableId':
        this.model.AddressVariableId = value;
        break;
      case 'ObjectVariableId':
        this.model.ObjectVariableId = value;
        break;
      case 'PlaceIdVariableId':
        this.model.PlaceIdVariableId = value;
        break;
      case 'SublocalityVariableId':
        this.model.SublocalityVariableId = value;
        break;
      case 'DepartmentVariableId':
        this.model.DepartmentVariableId = value;
        break;
      default:
    }
  }

  onSelectBlock(blockData : BlockDefinition) {
    this.model.ErrorBlockId = blockData.Id;
  }

  onDeleteBlock(blockData : BlockDefinition) {
    this.model.ErrorBlockId = null;
  }

  searchForVariable(varName) {
    return (this.customVariables.some(varDef => varDef.Name == varName) ? true : this.editorService.findVariablesAndImplicitsWithName(varName));
  }

  get customVariables(): VariableDefinition[] {
    return Concatenate.SpecialVar;
  }
}
