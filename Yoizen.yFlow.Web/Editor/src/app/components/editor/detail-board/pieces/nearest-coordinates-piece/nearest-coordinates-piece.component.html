<div class="nearest-coordinates card" [ngClass]="{'invalid-piece': !model.isValid(editorService)}">
    <div class="trash" (click)="deleteAction()" *ngIf="!readOnly"
         data-toggle="tooltip" ngbTooltip="{{ 'CARD_DELETE_TEXT' | translate }}" placement="top"
         container="body" tooltipClass="tooltip-trash">
      <span class="fa fa-trash-alt"></span>
    </div>
    <div class="clone" (click)="cloneAction()" *ngIf="!readOnly"
         data-toggle="tooltip" ngbTooltip="{{ 'CARD_CLONE_TEXT' | translate }}" placement="top" container="body" tooltipClass="tooltip-trash">
      <span class="fa fa-clone"></span>
    </div>
    <div class="export" (click)="exportAction()" *ngIf="!readOnly"
         data-toggle="tooltip" ngbTooltip="{{ 'CARD_EXPORT_TEXT' | translate }}" placement="top" container="body" tooltipClass="tooltip-trash">
      <span class="fa fa-file-export"></span>
    </div>
    <div class="card-title">
      <span class="fa fa-map-marked"></span> {{ 'CARD_NEARESTCOORDINATES_TITLE' | translate }}
    </div>
    <div class="card-info">
      {{ 'CARD_NEARESTCOORDINATES_INFO' | translate }}
    </div>
    <div class="source">
      <span class="title">{{'NEARESTCOORDINATES_SOURCE_VARIABLE' | translate}}:</span>
      <app-variable-selector-input
        [VariableData]="VariableData"
        [includeImplicit]="false"
        (setVariable)="setVariable($event)"
        [readOnly]="readOnly"
        [typeFilters]="variableFilter">
      </app-variable-selector-input>
    </div>
    <div *ngIf="model.VariableId !== -1">
      <div class="latitude-prop">
        <span class="title">{{'NEARESTCOORDINATES_LATITUDE_PROP' | translate}}:</span>
        <input type="text" class="input" [(ngModel)]="model.LatitudeProp" [ngClass]="{'invalid-input': !model.isPropValid(model.LatitudeProp) }" />
      </div>
      <div class="longitude-prop">
        <span class="title">{{'NEARESTCOORDINATES_LONGITUDE_PROP' | translate}}:</span>
        <input type="text" class="input" [(ngModel)]="model.LongitudeProp" [ngClass]="{'invalid-input': !model.isPropValid(model.LongitudeProp) }" />
      </div>
      <div class="distance-prop">
        <span class="title">{{'NEARESTCOORDINATES_DISTANCE_PROP' | translate}}:</span>
        <input type="number" class="input" [(ngModel)]="model.DistanceProp" [ngClass]="{'invalid-input': !model.isPropNumberValid(model.DistanceProp) }" />
      </div>
      <div class="total-prop">
        <span class="title">{{'NEARESTCOORDINATES_TOTAL_PROP' | translate}}:</span>
        <input type="number" class="input" [(ngModel)]="model.TotalProp" [ngClass]="{'invalid-input': !model.isPropNumberValid(model.TotalProp) }" />
      </div>
      <div class="dest">
        <span class="title">{{'NEARESTCOORDINATES_DEST_VARIABLE' | translate}}:</span>
        <app-variable-selector-input
          [VariableData]="StoreVariableData"
          [includeImplicit]="false"
          (setVariable)="setStoreVariable($event)"
          [readOnly]="readOnly"
          [typeFilters]="storeVariableFilter">
        </app-variable-selector-input>
      </div>
    </div>
  </div>
  