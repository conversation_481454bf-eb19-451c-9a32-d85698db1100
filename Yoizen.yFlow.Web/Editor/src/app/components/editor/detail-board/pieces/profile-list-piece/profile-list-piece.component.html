<div class="profile-list card" [ngClass]="{'invalid-piece': !isValid()}">
  <div class="trash" (click)="deleteAction()" *ngIf="!readOnly" data-toggle="tooltip"
    ngbTooltip="{{ 'CARD_DELETE_TEXT' | translate }}" placement="top" container="body" tooltipClass="tooltip-trash">
    <span class="fa fa-trash-alt"></span>
  </div>
  <div class="clone" (click)="cloneAction()" *ngIf="!readOnly" data-toggle="tooltip"
    ngbTooltip="{{ 'CARD_CLONE_TEXT' | translate }}" placement="top" container="body" tooltipClass="tooltip-trash">
    <span class="fa fa-clone"></span>
  </div>
  <div class="export" (click)="exportAction()" *ngIf="!readOnly" data-toggle="tooltip"
    ngbTooltip="{{ 'CARD_EXPORT_TEXT' | translate }}" placement="top" container="body" tooltipClass="tooltip-trash">
    <span class="fa fa-file-export"></span>
  </div>

  <div class="card-title">
    <span class="fa fa-user-edit"></span> {{ 'CARD_PROFILELIST_TITLE' | translate }}
  </div>

  <div class="card-info mb-1">
    {{ 'CARD_PROFILELIST_INFO' | translate }}
  </div>

  <div class="add-profile-list">
    <div class="title">
      {{ 'PROFILELIST_ADDPROFILELIST_TITLE' | translate }}
    </div>
    <div class="add-profile-list-table">
      <div class="header">
        <div>{{ 'PROFILELIST_ACTIONS_TITLE' | translate }}</div>
        <div>{{ 'PROFILELIST_KEY_TITLE' | translate }}</div>
        <div></div>
      </div>
      <div class="add-profile-list-row" *ngFor="let profileListData of model.ProfileListData; let i = index">
        <div class="option">
          <select class="select"
            [(ngModel)]="profileListData.Action"
            [disabled]="readOnly">
            <option [value]="profileListActionTypes.Add">{{ 'PROFILELIST_ACTIONS_ADD' | translate }}</option>
            <option [value]="profileListActionTypes.Remove">{{ 'PROFILELIST_ACTIONS_DELETE' | translate }}</option>
          </select>
        </div>
        <div class="option">
          <app-input-with-variables
            [placeholder]="'PROFILELIST_KEY_TITLE' | translate"
            [(value)]="profileListData.Key"
            [disabled]="readOnly"
            [validator]="isKeyValid">
          </app-input-with-variables>
        </div>
        <div class="trash" *ngIf="!readOnly && model.ProfileListData.length > 1">
          <div (click)="deleteProfileListData(i)"
               tooltipClass="tooltip-trash-left"
               data-toggle="tooltip"
               ngbTooltip="{{ 'PROFILELIST_DELETEBUTTON_TITLE' | translate }}"
               placement="left">
            <span class="fa fa-trash-alt"></span>
          </div>
        </div>
      </div>
    </div>
  </div>

  <div class="add" (click)="addProfileListData()" *ngIf="!readOnly">
    <span class="fa fa-plus"></span> {{ 'PROFILELIST_ADDBUTTON_TITLE' | translate }}
  </div>
</div>
