import {Component, Input, OnInit} from '@angular/core';
import {
  FormItemPicker,
  FormItemSelect,
  FormPage,
  FormPickerPage,
  FormPiece,
  FormSelectPage
} from "../../../../../../models/pieces/FormPiece";
import {VariableDefinition} from "../../../../../../models/VariableDefinition";
import {TypeDefinition} from "../../../../../../models/TypeDefinition";
import {EditorService} from "../../../../../../services/editor.service";

@Component({
  selector: 'app-form-page-picker',
  templateUrl: './form-page-picker.component.html',
  styleUrls: ['./form-page-picker.component.scss']
})
export class FormPagePickerComponent implements OnInit {
  @Input() basePage: FormPage;
  @Input() pageIndex: number;
  @Input() form: FormPiece;
  @Input() readOnly : boolean = false;
  page: FormPickerPage;
  variableTypes = TypeDefinition;

  get variableData(): VariableDefinition {
    return this.editorService.getVariableWithId(this.page.variableId);
  }

  constructor(public editorService: EditorService) { }

  ngOnInit() {
    this.page = <FormPickerPage> this.basePage;
  }

  addNewItem() {
    this.page.items.push(new FormItemPicker());
  }

  deleteItem(index: number) {
    this.page.items.splice(index, 1);
  }

  canAddItem() : boolean {
    return true;
  }

  getVariableTypes() : TypeDefinition[] {
    return new Array<TypeDefinition>(TypeDefinition.Text);
  }

  setVariable(variableData: VariableDefinition) {
    this.page.variableId = variableData ? variableData.Id : -1;
  }

  multipleSelecionChanged() {
    this.page.variableId = -1;
  }
}
