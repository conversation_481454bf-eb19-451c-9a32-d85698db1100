@import "_variables";
@import "_mixins";

.get-element-from-array {
  background-color: #fff;
  min-width: 500px;

  .source {
    margin-top: 5px;
    display: flex;
    flex-direction: row;
    margin-bottom: 10px;
  }

  .item-container {
    padding-top: 10px;
    border-top: 1px solid $gray;
  }

  .implicit-variables {
    margin-bottom: 10px;

    .title {
      font-family: $fontFamilyTitles;
      font-weight: bold;
      margin-right: 10px;
      justify-self: center;
      align-self: center;
      text-align: center;
    }

    .variables-info {
      font-family: $fontFamily;
      margin-bottom: 5px;
      color: #767676;
    }

    .variables-table {
      display: table;
      padding: 0 10px;
      width: 100%;

      .variables-header, .variable-row {
        height: 30px;
      }

      .variables-header {
        display: table-row;
        font-family: $fontFamilyTitles;

        div {
          display: table-cell;
          vertical-align: middle;
          border-bottom: 1px solid $sidebarBorderColor;
          padding-left: 3px;
          padding-right: 3px;
        }
      }

      .variable-row {
        display: table-row;

        .variable-cell {
          display: table-cell;
          vertical-align: middle;
          border-bottom: 1px solid $sidebarBorderColor;
          padding-left: 3px;
          padding-right: 3px;

          .variable-name {
            @include variable-name;
          }

          .variable-type {
            @include variable-type;
          }
        }

        &:last-child {
          .variable-cell {
            border-bottom-style: none;
          }
        }
      }
    }
  }
  .source, .longitude-prop, .distance-prop, .total-prop, .next, .dest {
    display: flex;
    flex-direction: row;
    align-items: center;
    margin-bottom: 10px;
    width: 100%;

    .title {
      font-family: $fontFamilyTitles;
      font-weight: bold;
      margin-right: 10px;
      justify-self: center;
      align-self: center;
      text-align: center;
      flex-grow: 0;
      flex-shrink: 0;
    }

    .input {
      flex-grow: 1;
    }
  }
  .where-prop {
    flex-direction: row;
    align-items: center;
    width: 100%;
    margin-bottom: 10px;
    padding-bottom: 10px;
    border-bottom: 1px solid $gray;

    ::ng-deep textarea, ::ng-deep .textarea-mask {
      font-family: $fontFamilyMono;
    }
  }
}
