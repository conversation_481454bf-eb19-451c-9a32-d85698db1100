<div class="multiple-message card" [ngClass]="{'invalid-piece': !model.isValid(editorService)}">
  <div class="trash" (click)="deleteAction()" *ngIf="!readOnly"
       data-toggle="tooltip" ngbTooltip="{{ 'CARD_DELETE_TEXT' | translate }}" placement="top" container="body" tooltipClass="tooltip-trash">
    <span class="fa fa-trash-alt"></span>
  </div>
  <div class="clone" (click)="cloneAction()" *ngIf="!readOnly"
       data-toggle="tooltip" ngbTooltip="{{ 'CARD_CLONE_TEXT' | translate }}" placement="top" container="body" tooltipClass="tooltip-trash">
    <span class="fa fa-clone"></span>
  </div>
  <div class="export" (click)="exportAction()" *ngIf="!readOnly"
       data-toggle="tooltip" ngbTooltip="{{ 'CARD_EXPORT_TEXT' | translate }}" placement="top" container="body" tooltipClass="tooltip-trash">
    <span class="fa fa-file-export"></span>
  </div>
  <div class="card-title">
    <span class="fa fa-sitemap"></span> {{ 'CARD_MULTIPLEMESSAGE' | translate }}
  </div>
  <div class="card-info">
    {{ 'CARD_MULTIPLEMESSAGE_INFO' | translate }}
  </div>
  <div class="sourcearray">
    <span class="title">{{'MULTIPLEMESSAGES_SOURCE_ARRAY' | translate}}:</span>
    <app-variable-selector-input
      [VariableData]="InputVariableData"
      (setVariable)="setInputVariable($event)"
      [validator]="validateVariable"
      [readOnly]="readOnly"
      [typeFilters]="variableFilter">
    </app-variable-selector-input>
  </div>
  <div class="body">
    <label>{{'MULTIPLEMESSAGES_TEMPLATE' | translate}}</label>
    <div class="implicit-variables">
      <div class="variables-info">{{'MULTIPLEMESSAGES_VARIABLELIST' | translate}}</div>
      <div class="variables-table">
        <div class="variables-header">
          <div>{{'NAME' | translate}}</div>
          <div>{{'DESCRIPTION' | translate}}</div>
        </div>
        <div class="variable-row" *ngFor="let variable of customVariables">
          <div class="variable-cell"><span class="variable-name">{{ variable.Name }}</span><span class="variable-type">{{ getVariableType(variable) | translate }}</span></div>
          <div class="variable-cell"><span class="variable-description">{{ variable.Description | translate }}</span></div>
        </div>
      </div>
    </div>
    <app-input-with-variables
      [isTextArea]="true"
      [wideInput]="true"
      [placeholder]="''"
      [validator]="getValidator()"
      [variableFinder]="searchForVariable.bind(this)"
      [extendedStyles]="{'height': '100px', 'min-height': '100px', 'max-height': '400px'}"
      [customVariableList]="customVariables"
      [JoinCustomVariable]="true"
      [disabled]="readOnly"
      [(value)]="model.Template"></app-input-with-variables>
  </div>
</div>
