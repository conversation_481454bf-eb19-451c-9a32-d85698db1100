import { async, ComponentFixture, TestBed } from '@angular/core/testing';

import { GetElementFromArrayPieceComponent } from './get-element-from-array-piece.component';

describe('GetElementFromArrayPieceComponent', () => {
  let component: GetElementFromArrayPieceComponent;
  let fixture: ComponentFixture<GetElementFromArrayPieceComponent>;

  beforeEach(async(() => {
    TestBed.configureTestingModule({
      declarations: [ GetElementFromArrayPieceComponent ]
    })
    .compileComponents();
  }));

  beforeEach(() => {
    fixture = TestBed.createComponent(GetElementFromArrayPieceComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });
});
