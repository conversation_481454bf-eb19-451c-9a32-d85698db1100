import { Component, OnInit } from '@angular/core';
import { BasePieceVM } from '../BasePieceVM';
import { CharacterReplacement, DecryptPiece } from 'src/app/models/pieces/DecryptPiece';
import { EditorService } from 'src/app/services/editor.service';
import { ModalService } from 'src/app/services/Tools/ModalService';
import { EncryptModeType, EncryptPaddingType, EncryptType } from 'src/app/models/pieces/AccountLinkingPiece';
import { TypeDefinition } from 'src/app/models/TypeDefinition';
import { VariableDefinition } from 'src/app/models/VariableDefinition';
import { BlockDefinition } from 'src/app/models/BlockDefinition';

@Component({
  selector: 'app-decrypt-piece',
  templateUrl: './decrypt-piece.component.html',
  styleUrls: ['./decrypt-piece.component.scss']
})
export class DecryptPieceComponent extends BasePieceVM implements OnInit {
  model: DecryptPiece;
  encryptTypes = [
    { value: EncryptType.AES, label: 'ENCRYPT_TYPE_AES' },
    { value: EncryptType.TripleDES, label: 'ENCRYPT_TYPE_TRIPLEDES' },
    { value: EncryptType.Rabbit, label: 'ENCRYPT_TYPE_RABBIT' },
  ];

  decryptModeTypes = [
    { value: EncryptModeType.CBC, label: 'ENCRYPT_MODE_CBC' },
    { value: EncryptModeType.CFB, label: 'ENCRYPT_MODE_CFB' },
    { value: EncryptModeType.CTR, label: 'ENCRYPT_MODE_CTR' },
    { value: EncryptModeType.OFB, label: 'ENCRYPT_MODE_OFB' },
    { value: EncryptModeType.ECB, label: 'ENCRYPT_MODE_ECB' },
  ];

  decryptPaddingTypes = [
    { value: EncryptPaddingType.Pkcs7, label: 'ENCRYPT_PADDING_PKCS7' },
    { value: EncryptPaddingType.Iso97971, label: 'ENCRYPT_PADDING_ISO97971' },
    { value: EncryptPaddingType.AnsiX923, label: 'ENCRYPT_PADDING_ANSIX923' },
    { value: EncryptPaddingType.Iso10126, label: 'ENCRYPT_PADDING_ISO10126' },
    { value: EncryptPaddingType.ZeroPadding, label: 'ENCRYPT_PADDING_ZEROPADDING' },
    { value: EncryptPaddingType.NoPadding, label: 'ENCRYPT_PADDING_NOPADDING' },
  ];

  ivLengthTypes = [
    { value: 16, label: '16 bytes (128 bits)' },
    { value: 24, label: '24 bytes (192 bits)' },
    { value: 32, label: '32 bytes (256 bits)' },
  ];

  public delimiterTypes = [
    { value: '|', label: '| (Pipe)' },
    { value: null, label: 'ENCRYPT_DELIMITER_NO_USE' }
  ];

  variableTypes = TypeDefinition;
  EncryptType = EncryptType;

  constructor(public editorService: EditorService, public modalService: ModalService) {
    super(editorService, modalService);
  }

  ngOnInit() {
    this.model = this.context as DecryptPiece;
    if (!this.model.DecryptType) {
      this.model.DecryptType = EncryptType.AES;
    }

    // Initialize custom method properties if not present
    if (this.model.CustomMethod === undefined || this.model.CustomMethod === null) {
      this.model.CustomMethod = false;
    }

    if (!this.model.CharactersToReplace) {
      this.model.CharactersToReplace = [];
    }
  }

  onSelectVariableToDecrypt(variableData: VariableDefinition) {
    this.model.VariableToDecryptId = variableData ? variableData.Id : null;
  }

  onSelectVariableDecrypted(variableData: VariableDefinition) {
    this.model.VariableDecryptedId = variableData ? variableData.Id : null;
  }

  onSelectErrorBlock(blockData: BlockDefinition) {
    this.model.ErrorBlockId = blockData.Id;
  }

  onDeleteErrorBlock() {
    this.model.ErrorBlockId = "-1";
  }

  addCharacterReplacement(): void {
    if (!this.model.CharactersToReplace) {
      this.model.CharactersToReplace = [];
    }
    this.model.CharactersToReplace.push(new CharacterReplacement());
  }

  removeCharacterReplacement(index: number): void {
    if (this.model.CharactersToReplace && index >= 0 && index < this.model.CharactersToReplace.length) {
      this.model.CharactersToReplace.splice(index, 1);
    }
  }

  requiresIV(): boolean {
    return this.model.DecryptType === EncryptType.AES || this.model.DecryptType === EncryptType.TripleDES;
  }

  isValid(): boolean {
    return this.isDecryptKeyValid() &&
      this.isVariableToDecryptValid() &&
      this.isVariableDecryptedValid() &&
      this.isErrorBlockValid() &&
      this.isCustomMethodConfigurationValid();
  }

  isDecryptKeyValid(): boolean {
    if (this.model.CustomMethod) {
      if (!this.model.DecryptKey || this.model.DecryptKey.trim() === '') {
        return false;
      }

      if (this.model.type === "rabbit") {
        // Rabbit encryption does not use a key, so we can skip this validation
        return true;
      }

      if (this.model.DecryptType === EncryptType.AES || this.model.DecryptType === EncryptType.TripleDES) {
        if (this.model.DecryptKey.length < 16) {
          // AES and TripleDES require a key of at least 16 characters
          return false;
        }
      }

      // Validate IV if present and EncryptType is AES or TripleDES
      if (this.model.DecryptType === EncryptType.AES || this.model.DecryptType === EncryptType.TripleDES) {
        if (this.model.DecryptKey && this.model.DecryptKey.trim() !== '') {
          if (!this.model.IvBytesLength) {
            return false; // IV Length must be specified if IV is provided
          }

          if (this.model.DecryptKey.length !== this.model.IvBytesLength) {
            return false; // IV length must match specified IVLength
          }

        } else if (this.model.IvBytesLength) {
          return false; // IV must be provided if IvBytesLength is specified
        }
      }
    }

    return this.model.DecryptKey && this.model.DecryptKey.trim().length > 0;
  }

  isVariableToDecryptValid(): boolean {
    const variable = this.editorService.findVariableWithId(this.model.VariableToDecryptId);
    return variable != null && variable.Type === this.variableTypes.Text;
  }

  isVariableDecryptedValid(): boolean {
    const variable = this.editorService.findVariableWithId(this.model.VariableDecryptedId);
    return variable != null && (
      variable.Type === this.variableTypes.Text ||
      variable.Type === this.variableTypes.Number ||
      variable.Type === this.variableTypes.Decimal ||
      variable.Type === this.variableTypes.Bool ||
      variable.Type === this.variableTypes.Array
    );
  }

  isErrorBlockValid(): boolean {
    const block = this.editorService.findBlockWithId(this.model.ErrorBlockId);
    return this.model.ErrorBlockId !== "-1" && block != null && block.validateCurrentModuleBlock(this.editorService);
  }

  isIvBytesLengthValid(): boolean {
    if (!this.model.CustomMethod || !this.requiresIV()) {
      return true;
    }

    return this.model.IvBytesLength != null &&
      this.model.IvBytesLength > 0 &&
      this.model.IvBytesLength % 4 === 0;
  }

  isCustomMethodConfigurationValid(): boolean {
    if (!this.model.CustomMethod) {
      return true;
    }

    // Validate IV bytes length for algorithms that require it
    if (this.requiresIV() && !this.isIvBytesLengthValid()) {
      return false;
    }

    // Validate character replacement configuration
    if (this.model.ReplaceCharacters) {
      if (!this.model.CharactersToReplace || this.model.CharactersToReplace.length === 0) {
        return false;
      }

      // Validate each character replacement entry
      for (let i = 0; i < this.model.CharactersToReplace.length; i++) {
        const replacement = this.model.CharactersToReplace[i];
        if (!replacement.value || !replacement.replaceWith) {
          return false;
        }
      }
    }

    return true;
  }
}