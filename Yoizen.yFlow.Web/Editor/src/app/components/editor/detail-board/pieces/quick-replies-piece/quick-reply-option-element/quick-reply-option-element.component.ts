import { Component, OnInit, Input, Output, EventEmitter, ViewChild, ElementRef } from '@angular/core';
import { ButtonPiece, ButtonType } from '../../../../../../models/pieces/ButtonPiece';
import {TranslateService} from "@ngx-translate/core";
import {EditorService} from "../../../../../../services/editor.service";
import {QuickReplyOption, QuickReplyType} from "../../../../../../models/pieces/QuickReplyPiece";

@Component({
  selector: 'app-quick-reply-option-element',
  templateUrl: './quick-reply-option-element.component.html',
  styleUrls: ['./quick-reply-option-element.component.scss']
})
export class QuickReplyOptionElementComponent implements OnInit {
  quickReplyTypes = QuickReplyType;
  @ViewChild('buttonDetail', {read: ElementRef, static: false}) ButtonDetail : ElementRef;
  @Input() Model : QuickReplyOption;
  @Input() Index : number;
  @Input() IsFirst: boolean = false;
  @Input() IsLast: boolean = false;
  @Input() expandedBtn : QuickReplyOption;
  @Input() stats : any = null;
  @Input() readOnly : boolean = false;
  @Output() onDelete : EventEmitter<number> = new EventEmitter<number>();
  @Output() onMoveLeft : EventEmitter<number> = new EventEmitter<number>();
  @Output() onMoveRight : EventEmitter<number> = new EventEmitter<number>();
  @Output() onShowDetail : EventEmitter<QuickReplyOption> = new EventEmitter<QuickReplyOption>();

  public get HideDetail(): boolean {
    return this.expandedBtn != this.Model;
  }

  constructor(private translateService: TranslateService, private editorService: EditorService) {

  }

  ngOnInit() {

  }

  onClick() {
    this.onShowDetail.emit(this.Model);
  }

  deleteElement() {
  	this.onDelete.emit(this.Index);
  }

  moveLeft() {
    this.onMoveLeft.emit(this.Index);
  }

  moveRight() {
    this.onMoveRight.emit(this.Index);
  }

  closePopup() {
    this.onShowDetail.emit(null);
  }

  getButtonText() : string {
    if (typeof(this.Model.Text) !== 'undefined' &&
      this.Model.Text !== null) {
      return this.Model.Text;
    }

    return this.translateService.instant('BUTTON_MISSING_NAME');
  }

  withName() : boolean {
    if (typeof(this.Model.Text) !== 'undefined' &&
      this.Model.Text !== null &&
      this.Model.Text.length > 0) {
      return true;
    }

    return false;
  }

  withUrl() : boolean {
    if (this.Model.Type === QuickReplyType.Url &&
      typeof(this.Model.Url) !== 'undefined' &&
      this.Model.Url !== null &&
      this.Model.Url.length > 0) {
      return true;
    }

    return false;
  }

  withRedirect() : boolean {
    if (typeof(this.Model.BlockId) === 'undefined' ||
      this.Model.BlockId === "-1") {
      return false;
    }

    return true;
  }

  isInvalid() : boolean {
    return !this.Model.isValid(this.editorService);
  }

  getBlockName() : string {
    let block = this.editorService.findBlockWithId(this.Model.BlockId);
    if (typeof(block) !== 'undefined' &&
      block !== null) {
      return block.Name;
    }
    return '';
  }

  goToBlock() {
    let block = this.editorService.findBlockWithId(this.Model.BlockId);
    if (typeof(block) !== 'undefined' &&
      block !== null) {
      this.editorService.selectedBlock(block);
    }
  }
}
