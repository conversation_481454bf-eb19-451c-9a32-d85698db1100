<div class="operator card" [ngClass]="{'invalid-piece': !model.isValid(editorService)}">
  <div class="trash" (click)="deleteAction()" *ngIf="!readOnly" data-toggle="tooltip"
    ngbTooltip="{{ 'CARD_DELETE_TEXT' | translate }}" placement="top" container="body" tooltipClass="tooltip-trash">
    <span class="fa fa-trash-alt"></span>
  </div>
  <div class="clone" (click)="cloneAction()" *ngIf="!readOnly" data-toggle="tooltip"
    ngbTooltip="{{ 'CARD_CLONE_TEXT' | translate }}" placement="top" container="body" tooltipClass="tooltip-trash">
    <span class="fa fa-clone"></span>
  </div>
  <div class="export" (click)="exportAction()" *ngIf="!readOnly" data-toggle="tooltip"
    ngbTooltip="{{ 'CARD_EXPORT_TEXT' | translate }}" placement="top" container="body" tooltipClass="tooltip-trash">
    <span class="fa fa-file-export"></span>
  </div>
  <div class="card-title">
    <span class="fa fa-headset"></span> {{ 'CARD_OPERATOR_TITLE' | translate }}
  </div>
  <div class="card-info">
    {{ 'CARD_OPERATOR_INFO' | translate }}
  </div>
  <div class="derivation-key">
    <span class="title">{{ 'DERIVATION_KEY' | translate }}:</span>
    <app-input-with-variables class="input-variable-area" [placeholder]="'DERIVATION_KEY' | translate"
      [(value)]="model.Message" [disabled]="readOnly" [wideInput]="true">
    </app-input-with-variables>
  </div>
  <div class="vim">
    <span class="title">{{ 'DERIVATION_MASKASVIM' | translate }}:</span>
    <ui-switch [(ngModel)]="model.MarkAsVim" [disabled]="readOnly" color="#45c195" size="small" defaultBgColor="#e0e0e0"
      switchColor="#ffffff"></ui-switch>
  </div>
  <div class="alert-message">
    <span class="title">{{ 'DERIVATION_ALERTMESSAGE' | translate }}:</span>
    <app-input-with-variables class="input-variable-area" [placeholder]="'DERIVATION_ALERTMESSAGE' | translate"
      [(value)]="model.AlertMessage" [disabled]="readOnly" [wideInput]="true">
    </app-input-with-variables>
  </div>
  <div class="alert-message">
    <span class="title">{{ 'DERIVATION_CONTEXTMESSAGE' | translate }}:</span>
    <app-input-with-variables class="input-variable-area" [placeholder]="'DERIVATION_CONTEXTMESSAGE' | translate"
      [(value)]="model.Context" [disabled]="readOnly" [isTextArea]="true" [wideInput]="true">
    </app-input-with-variables>
  </div>
  <div *ngIf="showRequiresVoiceCall">
    <div class="vim">
      <span class="title">{{ 'DERIVATION_REQUIREVOICECALL' | translate }}:</span>
      <ui-switch [(ngModel)]="model.RequiresVoiceCall" [disabled]="readOnly" color="#45c195" size="small"
        defaultBgColor="#e0e0e0" switchColor="#ffffff"></ui-switch>
    </div>
    <div class="alert alert-info">
      <span class="fa fa-lg fa-info-circle icon"></span>
      <span>{{ 'DERIVATION_REQUIREVOICECALL_TIP' | translate }}</span>
    </div>
  </div>
  <div class="summary-section" *ngIf="showSummaryOption">
    <div class="vim">
      <span class="title">{{ 'DERIVATION_REQUIRESUMMARY' | translate }}:</span>
      <ui-switch [(ngModel)]="model.RequiresSummary" [disabled]="readOnly" color="#45c195" size="small"
        defaultBgColor="#e0e0e0" switchColor="#ffffff">
      </ui-switch>
    </div>

    <div *ngIf="model.RequiresSummary" class="summary-options">
      <div class="summary-type">
        <span class="title">{{ 'DERIVATION_SUMMARYTYPE' | translate }}:</span>
        <select [(ngModel)]="model.SummaryType" [disabled]="readOnly" class="form-control">
          <option value="brief">{{ 'SUMMARY_TYPE_BRIEF' | translate }}</option>
          <option value="detailed">{{ 'SUMMARY_TYPE_DETAILED' | translate }}</option>
        </select>
      </div>

      <div class="alert alert-info">
        <span class="fa fa-lg fa-info-circle icon"></span>
        <span>{{ 'DERIVATION_SUMMARY_INFO' | translate }}</span>
      </div>
    </div>
  </div>
</div>
