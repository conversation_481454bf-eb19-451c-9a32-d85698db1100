import { Component, OnInit } from '@angular/core';
import { BaseDynamicComponent } from '../../../../utils/component-holder/BaseDynamicComponent';
import { BasePieceVM } from '../BasePieceVM';
import { EditorService } from '../../../../../services/editor.service';
import { ModalService } from '../../../../../services/Tools/ModalService';
import { JsonPiece } from '../../../../../models/pieces/JsonPiece';
import {StoreMessagePiece} from "../../../../../models/pieces/StoreMessagePiece";
import {LogPiece} from "../../../../../models/pieces/LogPiece";

@Component({
  selector: 'app-log-piece',
  templateUrl: './log-piece.component.html',
  styleUrls: ['./log-piece.component.scss']
})
export class LogPieceComponent extends BasePieceVM implements OnInit {
  model : LogPiece;

  constructor( editorService : EditorService, public modalService : ModalService ) {
    super(editorService, modalService);
  }

  ngOnInit() {
    this.model = this.context as LogPiece;
  }
}
