<div class="option">
  <span class="title">{{'FORM_PAGE_TYPE_DATEPICKER_DATEFORMAT' | translate}}:</span>
  <select [(ngModel)]="page.dateFormat" [disabled]="readOnly" class="select">
    <option value="MM/dd/yyyy">MM/dd/yyyy</option>
    <option value="dd/MM/yyyy">dd/MM/yyyy</option>
  </select>
</div>
<div class="option">
  <span class="title">{{'FORM_PAGE_TYPE_DATEPICKER_STARTDATE_NOW' | translate}}:</span>
  <ui-switch [(ngModel)]="page.startDateIsNow" [disabled]="readOnly"
             color="#45c195" size="small" defaultBgColor="#e0e0e0" switchColor="#ffffff"></ui-switch>
</div>
<div class="option with-info" *ngIf="!page.startDateIsNow">
  <span class="title">{{'FORM_PAGE_TYPE_DATEPICKER_STARTDATE' | translate}}:</span>
  <app-input-with-variables
    [(value)]="page.startDate"
    [validator]="page.isStartDateValid.bind(page)"
    [wideInput]="true"
    [isTextArea]="false"
    [disabled]="readOnly"
    class="input">
  </app-input-with-variables>
  <div class="info" ngbTooltip="{{ 'FORM_PAGE_TYPE_DATEPICKER_STARTDATE_TIP' | translate }}"
       placement="right" container="body" tooltipClass="tooltip-persistent">
    <span class="fa fa-question-circle"></span>
  </div>
</div>
<div class="option">
  <span class="title">{{'FORM_PAGE_TYPE_DATEPICKER_MAXIMUMDATE_NOW' | translate}}:</span>
  <ui-switch [(ngModel)]="page.maximumDateIsNow" [disabled]="readOnly"
             color="#45c195" size="small" defaultBgColor="#e0e0e0" switchColor="#ffffff"></ui-switch>
</div>
<div class="option with-info" *ngIf="!page.maximumDateIsNow">
  <span class="title">{{'FORM_PAGE_TYPE_DATEPICKER_MAXIMUMDATE' | translate}}:</span>
  <app-input-with-variables
    [(value)]="page.maximumDate"
    [validator]="page.isMaximumDateValid.bind(page)"
    [wideInput]="true"
    [isTextArea]="false"
    [disabled]="readOnly"
    class="input">
  </app-input-with-variables>
  <div class="info" ngbTooltip="{{ 'FORM_PAGE_TYPE_DATEPICKER_MAXIMUMDATE_TIP' | translate }}"
       placement="right" container="body" tooltipClass="tooltip-persistent">
    <span class="fa fa-question-circle"></span>
  </div>
</div>
<div class="option with-info">
  <span class="title">{{'FORM_PAGE_TYPE_DATEPICKER_MINIMUMDATE' | translate}}:</span>
  <app-input-with-variables
    [(value)]="page.minimumDate"
    [validator]="page.isMinimumDateValid.bind(page)"
    [wideInput]="true"
    [isTextArea]="false"
    [disabled]="readOnly"
    class="input">
  </app-input-with-variables>
  <div class="info" ngbTooltip="{{ 'FORM_PAGE_TYPE_DATEPICKER_MINIMUMDATE_TIP' | translate }}"
       placement="right" container="body" tooltipClass="tooltip-persistent">
    <span class="fa fa-question-circle"></span>
  </div>
</div>
<div class="option">
  <span class="title">{{'FORM_PAGE_TYPE_DATEPICKER_LABELTEXT' | translate}}:</span>
  <app-input-with-variables
    [(value)]="page.labelText"
    [validator]="page.isLabelTextValid.bind(page)"
    [wideInput]="true"
    [isTextArea]="false"
    [disabled]="readOnly"
    class="input">
  </app-input-with-variables>
</div>
<div class="option">
  <span class="title">{{'FORM_PAGE_TYPE_DATEPICKER_VARIABLE' | translate}}:</span>
  <app-variable-selector-input class="input"
                               [VariableData]="variableData"
                               (setVariable)="setVariable($event)"
                               [canSelectConstants]="false"
                               [readOnly]="readOnly"
                               [typeFilters]="[ variableTypes.StringDate, variableTypes.Timestamp ]"
                               [validator]="page.isVariableIdValid.bind(page, editorService)"></app-variable-selector-input>
</div>
