import {Component, OnInit, Input, OnDestroy} from '@angular/core';
import { MessagePieceType, Text } from '../../../../../models/pieces/MessagePieceType';
import {ButtonPiece, ButtonType} from '../../../../../models/pieces/ButtonPiece';
import { BasePieceVM } from '../BasePieceVM';
import { EditorService } from '../../../../../services/editor.service';
import { ModalService } from '../../../../../services/Tools/ModalService';
import { ButtonElementComponent } from '../message-piece/button-element/button-element.component';
import {ChannelTypes} from "../../../../../models/ChannelType";
import { BasePiece } from 'src/app/models/pieces/BasePiece';
import { SingleOverlayService } from 'src/app/services/SingleOverlay.service';
import { DragulaService } from 'ng2-dragula';
import {TypeDefinition} from "../../../../../models/TypeDefinition";
import {VariableDefinition} from "../../../../../models/VariableDefinition";
import {SourceTypes} from "../../../../../models/pieces/AttachmentPiece";
import {isUrlValid, isStringValid} from "../../../../../urlutils.module";
import {OutputVariableMap} from "../../../../../models/pieces/IntegrationPiece";
import {environment} from "../../../../../../environments/environment";
import {
  InteractiveMessageUrlButtonHeaderTypes,
  InteractiveMessageUrlButtonPiece
} from "../../../../../models/pieces/InteractiveMessageUrlButtonPiece";


@Component({
  selector: 'app-interactive-message-urlbutton-piece',
  templateUrl: './interactive-message-urlbutton-piece.component.html',
  styleUrls: ['./interactive-message-urlbutton-piece.component.scss']
})
export class InteractiveMessageUrlbuttonPieceComponent extends BasePieceVM implements OnInit, OnDestroy {
  constructor( editorService : EditorService, public modalService : ModalService, private singleOverlay: SingleOverlayService, private dragulaService : DragulaService ) {
    super(editorService, modalService);
  }

  expandButton : ButtonPiece;
  model : InteractiveMessageUrlButtonPiece;
  currentBtnDetail : ButtonElementComponent;
  HeaderTypes = InteractiveMessageUrlButtonHeaderTypes;
  variableFilter = [TypeDefinition.ByteArray, TypeDefinition.Base64];
  ActiveIdString: string;
  channelTypes = ChannelTypes;
  showIsPublicToggle: boolean = true;

  get variableDefinition(): VariableDefinition {
    return this.editorService.findVariableWithId(this.model.Header.FileDataStorageId);
  }

  ngOnInit() {
    this.model = this.context as InteractiveMessageUrlButtonPiece;
    this.flow = this.editorService.getCurrentFlow();

    // @ts-ignore
    if (typeof(environment.onPremise) !== 'boolean' ||
    // @ts-ignore
    !environment.onPremise) {
      if (this.model.IsPublicUrl) {
        this.showIsPublicToggle = false;
      }
    }
  }

  ngOnDestroy() {
  }

  ngOnChanges() {
    this.ActiveIdString = this.getActiveTab();
  }

  getActiveTab(): string {
    switch (this.model.Header.Source) {
      case SourceTypes.Variable:
        return "tab-variable";
      case SourceTypes.Url:
        return "tab-url";
    }
  }

  addNewText() {
  	this.model.TextList.push(new Text());
  }

  canAddTextOptions() : boolean {
  	return this.model.TextList.length < 3;
  }

  isTextValid(index: number) {
    return str => { return this.model.isTextValid(index, this.editorService) }
  }

  isHeaderTextValid() {
    return str => { return this.model.Header.isTextValid(this.editorService) }
  }

  isFooterTextValid() {
    return str => { return this.model.isFooterTextValid(this.editorService) }
  }

  deleteElement(element) {
    this.model.TextList.splice(element, 1);
  }

  onShowButtonDetail(btn : ButtonPiece) {
    this.singleOverlay.closeCurrentAction = ()=>{ this.expandButton = null;};
    this.expandButton = btn;
  }

  getNextPiece() : BasePiece {
    let pieces = this.editorService.getEditorState().SelectedBlock.Pieces;

    if (pieces.length === this.index + 1) {
      return null;
    }

    return pieces[this.index + 1];
  }

  isUrlValid(str): Boolean {
    return isUrlValid(str);
  }

  isNameValid(str) {
    return str => { return this.model.Header.isNameValid(this.editorService); };
  }

  isButtonDisplayNameValid() {
    return str => { return this.model.Button.nameValid() };
  }

  isButtonUrlValid() {
    return str => { return this.model.Button.urlValid() };
  }

  isMimeTypeValid(str) {
    if (!isStringValid(str)) {
      return false;
    }

    if (str.indexOf('{{') >= 0 &&
      str.indexOf('}}') >= 0) {
      return true;
    }

    if (str.indexOf('${') >= 0 &&
      str.indexOf('}$') >= 0) {
      return true;
    }

    const regex = new RegExp('^[-a-z]{1,127}/[-a-z0-9\+]+(\.[-a-z0-9\+]+)*$');
    if (!regex.test(str.toString())) {
      return false;
    }

    return true;
  }

  setVariableOnOutput(output: OutputVariableMap, variable: VariableDefinition) {
    if (variable != null) {
      this.model.Header.FileDataStorageId = variable.Id;
    }
    else {
      this.model.Header.FileDataStorageId = null;
    }
  }

  onTabChange(eventInfo) {
    switch (eventInfo.nextId) {
      case "tab-url":
        this.model.Header.Source = SourceTypes.Url;
        return;
      case "tab-variable":
        this.model.Header.Source = SourceTypes.Variable;
        return
    }
  }

  getFileVaraibleValidator() {
    return this.validateFileVariable.bind(this);
  }

  validateFileVariable() {
    return this.model.Header.isFileDataValid(this.editorService);
  }
}
