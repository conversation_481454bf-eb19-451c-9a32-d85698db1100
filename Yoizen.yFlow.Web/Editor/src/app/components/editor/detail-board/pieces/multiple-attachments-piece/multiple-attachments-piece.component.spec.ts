import { async, ComponentFixture, TestBed } from '@angular/core/testing';

import { MultipleAttachmentsPieceComponent } from './multiple-attachments-piece.component';

describe('MultipleAttachmentsPieceComponent', () => {
  let component: MultipleAttachmentsPieceComponent;
  let fixture: ComponentFixture<MultipleAttachmentsPieceComponent>;

  beforeEach(async(() => {
    TestBed.configureTestingModule({
      declarations: [ MultipleAttachmentsPieceComponent ]
    })
    .compileComponents();
  }));

  beforeEach(() => {
    fixture = TestBed.createComponent(MultipleAttachmentsPieceComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });
});
