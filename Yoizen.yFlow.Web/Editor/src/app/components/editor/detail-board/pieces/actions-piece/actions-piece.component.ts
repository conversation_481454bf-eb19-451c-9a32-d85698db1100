import { Component, OnInit, ElementRef, ViewChild, Renderer2 } from '@angular/core';
import { BasePieceVM } from '../BasePieceVM';
import { EditorService } from '../../../../../services/editor.service';
import { ModalService } from '../../../../../services/Tools/ModalService';
import { ActionsPiece } from '../../../../../models/pieces/ActionsPiece';
import { BlockDefinition } from '../../../../../models/BlockDefinition';

@Component({
  selector: 'app-actions-piece',
  templateUrl: './actions-piece.component.html',
  styleUrls: ['./actions-piece.component.scss']
})
export class ActionsPieceComponent extends BasePieceVM implements OnInit {

  model : ActionsPiece;
  searchBlockString : String;
  BlockData : BlockDefinition;

  @ViewChild('blockPicker', { static: false }) BlockPicker : ElementRef;

  constructor(public editorService : EditorService, public modalService : ModalService, private renderer: Renderer2 ) {
    super(editorService, modalService);
  }


  ngOnInit() {
    this.model = this.context as ActionsPiece;
    this.BlockData = this.editorService.findBlockWithId( this.model.BlockID);
  }

  hasBlock() {
    if( !this.BlockData) {
      return false;
    }
    return true;
  }

  onBlockSelect(blockData) {
    this.model.BlockID = blockData.Id;
    this.searchBlockString = blockData.Name;
    this.BlockData = this.editorService.findBlockWithId( this.model.BlockID);
  }

  deleteBlock() {
    this.BlockData = null;
    this.model.BlockID = "-1";
    this.searchBlockString = "";
  }

  onInputFocusIn() {

    this.renderer.removeClass(this.BlockPicker.nativeElement, 'hide');
  }

  onInputFocusOut() {

    setTimeout(() => {
      this.renderer.addClass(this.BlockPicker.nativeElement, 'hide');
    }, 500);
  }
}
