import { async, ComponentFixture, TestBed } from '@angular/core/testing';

import { MultiMediaEntryPieceComponent } from './multimedia-entry-piece.component';

describe('MultiMediaEntryPieceComponent', () => {
  let component: MultiMediaEntryPieceComponent;
  let fixture: ComponentFixture<MultiMediaEntryPieceComponent>;

  beforeEach(async(() => {
    TestBed.configureTestingModule({
      declarations: [ MultiMediaEntryPieceComponent ]
    })
    .compileComponents();
  }));

  beforeEach(() => {
    fixture = TestBed.createComponent(MultiMediaEntryPieceComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });
});
