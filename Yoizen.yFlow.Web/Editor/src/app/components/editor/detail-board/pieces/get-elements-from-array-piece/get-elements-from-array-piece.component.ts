import {Component, OnInit} from '@angular/core';
import {BasePieceVM} from '../BasePieceVM';
import {EditorService} from '../../../../../services/editor.service';
import {ModalService} from '../../../../../services/Tools/ModalService';
import {VariableDefinition} from '../../../../../models/VariableDefinition';
import {TypeDefinition} from '../../../../../models/TypeDefinition';
import {GetElementFromArrayPiece} from '../../../../../models/pieces/GetElementFromArrayPiece';
import { Concatenate } from 'src/app/models/pieces/Concatenate';
import { GetElementsFromArrayPiece } from 'src/app/models/pieces/GetElementsFromArrayPiece';

@Component({
  selector: 'app-get-elements-from-array-piece',
  templateUrl: './get-elements-from-array-piece.component.html',
  styleUrls: ['./get-elements-from-array-piece.component.scss']
})
export class GetElementsFromArrayPieceComponent extends BasePieceVM implements OnInit {
  model: GetElementsFromArrayPiece;
  variableFilter = [TypeDefinition.Array];
  storeVariableFilter = [TypeDefinition.Array];

  get customVariables(): VariableDefinition[] {
    return GetElementFromArrayPiece.SpecialVar;
  }

  getVariableType(variable: VariableDefinition): string {
    for (let i = 0; i < VariableDefinition.variableType.length; i++) {
      if (variable.Type === VariableDefinition.variableType[i].value) {
        return VariableDefinition.variableType[i].localized;
      }
    }
    return '';
  }

  get VariableData(): VariableDefinition {
    return this.editorService.getVariableWithId(this.model.VariableId);
  }

  get StoreVariableData(): VariableDefinition {
    return this.editorService.getVariableWithId(this.model.StoreVariableId);
  }

  constructor(editorService: EditorService, public modalService: ModalService) {
    super(editorService, modalService);
  }

  searchForVariable(varName) {
    if (varName.includes('${') || varName.includes('}$')) {
      return false;
    }
    const substringVarName = varName.indexOf('.') === -1 ? varName : varName.substring(0, varName.indexOf('.'));
    return (this.customVariables.some(varDef => varDef.Name === substringVarName)
            ? true :
            this.editorService.findVariablesAndImplicitsWithName(varName));
  }

  ngOnInit() {
    this.model = this.context as GetElementsFromArrayPiece;

    if (this.model.VariableId !== -1) {
      this.setVariable(this.VariableData);
    }
    if (this.model.StoreVariableId !== -1) {
      this.setStoreVariable(this.StoreVariableData);
    }
  }

  setVariable(variable: VariableDefinition) {
    if (variable != null) {
      this.model.VariableId = variable.Id;
      this.model.VariableName = variable.Name;
    }
    else {
      this.model.VariableId = -1;
      this.model.VariableName = null;
    }
  }

  setStoreVariable(variable: VariableDefinition) {
    if (variable != null) {
      this.model.StoreVariableId = variable.Id;
    } else {
      this.model.StoreVariableId = -1;
    }
  }
}
