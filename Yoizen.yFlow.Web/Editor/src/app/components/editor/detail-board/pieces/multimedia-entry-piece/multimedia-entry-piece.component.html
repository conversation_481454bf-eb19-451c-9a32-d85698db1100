<div class="multimediaEntry card" [ngClass]="{'invalid-piece': !model.isValid(editorService)}">
  <div class="trash" (click)="deleteAction()" *ngIf="!readOnly" data-toggle="tooltip"
    ngbTooltip="{{ 'CARD_DELETE_TEXT' | translate }}" placement="top" container="body" tooltipClass="tooltip-trash">
    <span class="fa fa-trash-alt"></span>
  </div>
  <div class="clone" (click)="cloneAction()" *ngIf="!readOnly"
       data-toggle="tooltip" ngbTooltip="{{ 'CARD_CLONE_TEXT' | translate }}" placement="top" container="body" tooltipClass="tooltip-trash">
    <span class="fa fa-clone"></span>
  </div>
  <div class="export" (click)="exportAction()" *ngIf="!readOnly"
       data-toggle="tooltip" ngbTooltip="{{ 'CARD_EXPORT_TEXT' | translate }}" placement="top" container="body" tooltipClass="tooltip-trash">
    <span class="fa fa-file-export"></span>
  </div>
  <div class="card-title">
    <span class="fa fa-file-image"></span> {{ 'CARD_MULTIMEDIAENTRY_TITLE' | translate }}
  </div>
  <div class="mime-type">
    <div class="mime-type-available">
      <div class="title">
        {{ 'MIME_TYPE_AVAILABLE_TITLE' | translate }}
      </div>
      <div class="pickers" dragula="{{MIME_TYPE_ITEMS}}" id="MimeTypeAvailable" [(dragulaModel)]='model.MimeTypeAvailable'>
        <app-mime-type-picker *ngFor="let mimeTypeDefinition of model.MimeTypeAvailable"
          [MimeTypeDefinition]="mimeTypeDefinition">
        </app-mime-type-picker>
      </div>
    </div>
    <div class="mime-type-enabled">
        <div class="title">
          {{ 'MIME_TYPE_ENABLED_TITLE' | translate }}
        </div>
        <div class="pickers" dragula="{{MIME_TYPE_ITEMS}}" id="MimeTypeEnabled" [(dragulaModel)]='model.MimeTypeEnabled'>
            <app-mime-type-picker *ngFor="let mimeTypeDefinition of model.MimeTypeEnabled"
            [MimeTypeDefinition]="mimeTypeDefinition">
          </app-mime-type-picker>
        </div>
    </div>
  </div>
  <!--quito el agregar condicion, ya que todavía no esta definido como hacerlo-->
  <div *ngIf="false">
    <div class="add-condition" (click)="model.HasCondition = true" *ngIf="!model.HasCondition && !readOnly">
      <span class="fa fa-plus"></span> {{ 'MULTIMEDIAENTRY_ADD_VALIDATION' | translate }}
    </div>
    <div class="condition" *ngIf="model.HasCondition">
      <label>{{ 'MULTIMEDIAENTRY_VALIDATION_INFO' | translate }}</label>
      <div class="info">
        <app-input-with-variables class="value" [placeholder]="'VALUE' | translate" [(value)]="model.FirstValue"
          [wideInput]="true" [disabled]="readOnly" [ngClass]="{'validation-error': !model.isFirstValueValid()}">
        </app-input-with-variables>
        <select class="select" name="" id="" [disabled]="readOnly" [(ngModel)]="model.Operator"
          [ngClass]="{'validation-error': !model.isOperatorValid()}">
          <option *ngFor="let operator of getOperators()" [ngValue]="operator.value">{{ operator.localized | translate }}
          </option>
        </select>
        <app-input-with-variables *ngIf="showOperand()" class="value" [placeholder]="'VALUE' | translate"
          [(value)]="model.SecondValue" [wideInput]="true" [disabled]="readOnly"
          [ngClass]="{'validation-error': !model.isSecondValueValid()}"></app-input-with-variables>
      </div>
      <div class="trash" (click)="model.HasCondition=false" *ngIf="!readOnly" data-toggle="tooltip"
        ngbTooltip="{{ 'MULTIMEDIAENTRY_REMOVE_VALIDATION' | translate }}" placement="top" container="body"
        tooltipClass="tooltip-trash">
        <span class="fa fa-trash-alt"></span>
      </div>
    </div>
  </div>
  <div class="commands"
    *ngIf="(model.Channel === channelTypes.WhatsApp || model.Channel === channelTypes.MercadoLibre)">
    <label>{{'MULTIMEDIAENTRY_VALIDATIONERROR_COMMANDS' | translate}}</label>
    <div class="command">
      <span class="title">{{'MULTIMEDIAENTRY_CHECKCOMMANDS' | translate}}:</span>
      <ui-switch [(ngModel)]="model.CheckCommands" [disabled]="readOnly" color="#45c195" size="small"
        defaultBgColor="#e0e0e0" switchColor="#ffffff"></ui-switch>
    </div>
  </div>
  <div class="validationerror">
    <label>{{'MULTIMEDIAENTRY_VALIDATIONERROR_DESC' | translate}}</label>
    <div class="errormessage">
      <span class="title">{{ 'MULTIMEDIAENTRY_VALIDATIONERROR_MESSAGES' | translate }}:</span>
      <div class="messages">
        <app-text-list-error-message *ngFor="let text of model?.ErrorMessages let i = index" [(Text)]="text.text"
          [Index]="i" [CanDelete]="!readOnly && model?.ErrorMessages.length > 1" [readOnly]="readOnly"
          (onDelete)="deleteErrorMessage($event)"></app-text-list-error-message>
        <div class="addText" [ngClass]="{'hide': !canAddTextOptions() }" *ngIf="!readOnly">
          <div (click)="addNewText()" data-toggle="tooltip" ngbTooltip="{{ 'CARD_MESSAGE_ADD_TEXT' | translate }}"
            placement="right" tooltipClass="tooltip-add">
            <span class="fa fa-plus"></span>
          </div>
        </div>
      </div>
    </div>
    <div class="retries">
      <span class="title">{{ 'MULTIMEDIAENTRY_RETRIES' | translate }}:</span>
      <input class="input tries" type="number" placeholder="0" min="0" [disabled]="readOnly"
        [(ngModel)]="model.TryLimit" [ngClass]="{'invalid-input': !model.isTryLimitValid()}">
    </div>
  </div>
  <div class="validationerror">
    <div class="command">
      <span class="title">{{'MULTIMEDIAENTRY_TEXT_EXCEPTION_ENABLED' | translate}}:</span>
      <ui-switch [(ngModel)]="model.TextExceptionEnabled" [disabled]="readOnly" color="#45c195" size="small"
        defaultBgColor="#e0e0e0" switchColor="#ffffff"></ui-switch>
    </div>
    <div class="command" *ngIf="model.TextExceptionEnabled">
      <span class="title">{{'MULTIMEDIAENTRY_TEXT_EXCEPTION_JUMP' | translate}}:</span>
      <ui-switch [(ngModel)]="model.TextExceptionJumpEnabled" [disabled]="readOnly" color="#45c195" size="small"
        defaultBgColor="#e0e0e0" switchColor="#ffffff"></ui-switch>
    </div>
    <div class="next" *ngIf="model.TextExceptionEnabled && model.TextExceptionJumpEnabled">
      <span class="title">{{'MULTIMEDIAENTRY_TEXT_EXCEPTION' | translate}}:</span>
      <app-block-picker class="input" [blockId]="model.TextExceptionBlockId" (onSelectNewBlock)="onSelectTextExceptionBlock($event)"
        (onDeleteBlock)="onDeleteTextExceptionBlock($event)" [readOnly]="readOnly"
        [isInvalid]="!model.isTextExceptionBlockValid(editorService)"></app-block-picker>
    </div>
  </div>
  <div class="next">
    <span class="title">{{'ON_ERROR_GO_TO' | translate}}:</span>
    <app-block-picker class="input" [blockId]="model.ErrorBlockId" (onSelectNewBlock)="onSelectBlock($event)"
      (onDeleteBlock)="onDeleteBlock($event)" [readOnly]="readOnly"
      [isInvalid]="!model.isErrorBlockValid(editorService)"></app-block-picker>
  </div>
</div>
