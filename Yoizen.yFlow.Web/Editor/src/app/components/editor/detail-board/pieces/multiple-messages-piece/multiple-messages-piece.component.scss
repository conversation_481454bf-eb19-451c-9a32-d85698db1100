@import '_variables';
@import '_mixins';

.multiple-message {
	background-color: $block-base;
	width: 600px;

  .body {
    width: 100%;
    padding-bottom: 10px;
    padding-top: 10px;
    border-top: 1px solid $gray;

    label {
      font-family: $fontFamilyTitles;
      font-weight: bold;
    }

    select{
      margin-right: 10px;
      width: 29%;
    }
    textarea{
      width: 100%;
      background: $block-base;
      white-space: pre-wrap;
      height: 180px;
    }
    label{
      display: block;
    }
  }

  .body {
    .implicit-variables {
      padding: 0 10px;
      margin-bottom: 10px;

      .variables-info {
        font-family: $fontFamily;
        margin-bottom: 5px;
        color: #767676;
      }

      .variables-table {
        display: table;
        width: 100%;

        .variables-header, .variable-row {
          height: 30px;
        }

        .variables-header {
          display: table-row;
          font-family: $fontFamilyTitles;

          div {
            display: table-cell;
            vertical-align: middle;
            border-bottom: 1px solid $sidebarBorderColor;
            padding-left: 3px;
            padding-right: 3px;
          }
        }

        .variable-row {
          display: table-row;

          .variable-cell {
            display: table-cell;
            vertical-align: middle;
            border-bottom: 1px solid $sidebarBorderColor;
            padding-left: 3px;
            padding-right: 3px;

            .variable-name {
              @include variable-name;
            }

            .variable-type {
              @include variable-type;
            }
          }

          &:last-child {
            .variable-cell {
              border-bottom-style: none;
            }
          }
        }
      }
    }
  }

  .sourcearray {
    display: flex;
    flex-direction: row;

    .title {
      font-family: $fontFamilyTitles;
      font-weight: bold;
      margin-right: 10px;
      justify-self: center;
      align-self: center;
      text-align: center;
    }
  }

  .sourcearray {
    margin-bottom: 10px;
  }
}
