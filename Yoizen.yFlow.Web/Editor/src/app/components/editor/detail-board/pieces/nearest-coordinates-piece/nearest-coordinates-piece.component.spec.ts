import { async, ComponentFixture, TestBed } from '@angular/core/testing';

import { NearestCoordinatesPieceComponent } from './nearest-coordinates-piece.component';

describe('NearestCoordinatesPieceComponent', () => {
  let component: NearestCoordinatesPieceComponent;
  let fixture: ComponentFixture<NearestCoordinatesPieceComponent>;

  beforeEach(async(() => {
    TestBed.configureTestingModule({
      declarations: [ NearestCoordinatesPieceComponent ]
    })
    .compileComponents();
  }));

  beforeEach(() => {
    fixture = TestBed.createComponent(NearestCoordinatesPieceComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });
});
