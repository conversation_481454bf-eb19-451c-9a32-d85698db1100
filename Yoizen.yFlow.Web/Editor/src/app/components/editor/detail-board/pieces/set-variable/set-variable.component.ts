import {Component, OnInit} from '@angular/core';
import {BasePieceVM} from '../BasePieceVM';
import {EditorService} from '../../../../../services/editor.service';
import {ModalService} from '../../../../../services/Tools/ModalService';
import {OperatorDefinitions} from '../../../../../models/OperatorType';
import {SetVariable} from '../../../../../models/pieces/SetVariable';
import {VariableDefinition} from '../../../../../models/VariableDefinition';
import {TypeDefinition} from "../../../../../models/TypeDefinition";

@Component({
  selector: 'app-set-variable',
  templateUrl: './set-variable.component.html',
  styleUrls: ['./set-variable.component.scss']
})
export class SetVariableComponent extends BasePieceVM implements OnInit {
  variableFilter: TypeDefinition[] = [ TypeDefinition.Bool, TypeDefinition.Text, TypeDefinition.StringDate,
    TypeDefinition.Timestamp, TypeDefinition.Decimal, TypeDefinition.Number, TypeDefinition.Array, TypeDefinition.Object, TypeDefinition.Base64 ];
  model : SetVariable;

  get curretVariable(): VariableDefinition {
    return this.editorService.getVariableWithId( this.model.VariableId);
  }
  constructor( editorService : EditorService, public modalService : ModalService ) {
    super(editorService, modalService);
   }

   ngOnInit() {
    this.model = this.context as SetVariable;
  }

  getOperators() {
    return OperatorDefinitions.Operators;
  }

  setVariableOnOutput(variable : VariableDefinition) {
    if( variable != null) {
      this.model.VariableId = variable.Id;
    }
    else {
      this.model.VariableId = null;
    }
  }

  showOperand() : boolean {
    let op = OperatorDefinitions.Operators.find( op => op.value == this.model.Operator);
    if(op == null) {
      return false;
    }
    return op.requiresOperand;
  }
}

