@import "_variables";
@import "_mixins";

.invoke-flow {
  background-color: #fff;
  width: 600px;

  .flow-column {
    flex-direction: column;
    margin: 6px 0;
    display: flex;
    width: 100%;
    
    .title {
      display: flex;
      align-items: center;
      font-family: $fontFamilyTitles;
      font-weight: bold;
      margin-right: 10px;
      font-size: 120%;
    }
  }

  .flow-row {
    margin: 6px 0;
    display: flex;
    width: 100%;

    .input {
      margin: 0 4px;
      width: 100%;
    }

    .title {
      display: flex;
      align-items: center;
      font-family: $fontFamilyTitles;
      font-weight: bold;
      margin-right: 10px;
    }
  }

  .flow-data {
    padding: 8px 0;
    border-bottom: 1px solid $sidebarBorderColor;
    border-top: 1px solid $sidebarBorderColor;

    .title {
      display: flex;
      align-items: center;
      font-family: $fontFamilyTitles;
      font-weight: bold;
      margin-right: 10px;
    }
  }
  
}