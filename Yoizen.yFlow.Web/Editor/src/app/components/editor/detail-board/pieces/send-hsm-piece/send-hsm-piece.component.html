<div class="send-hsm card" [ngClass]="{'invalid-piece': !model.isValid(editorService)}">
  <div class="trash" (click)="deleteAction()" *ngIf="!readOnly"
       data-toggle="tooltip" ngbTooltip="{{ 'CARD_DELETE_TEXT' | translate }}" placement="top" container="body" tooltipClass="tooltip-trash">
    <span class="fa fa-trash-alt"></span>
  </div>
  <div class="clone" (click)="cloneAction()" *ngIf="!readOnly"
       data-toggle="tooltip" ngbTooltip="{{ 'CARD_CLONE_TEXT' | translate }}" placement="top" container="body" tooltipClass="tooltip-trash">
    <span class="fa fa-clone"></span>
  </div>
  <div class="export" (click)="exportAction()" *ngIf="!readOnly"
       data-toggle="tooltip" ngbTooltip="{{ 'CARD_EXPORT_TEXT' | translate }}" placement="top" container="body" tooltipClass="tooltip-trash">
    <span class="fa fa-file-export"></span>
  </div>
  <div class="card-title">
    <span class="fas fa-paper-plane"></span> {{ 'SEND_HSM_TITLE' | translate }}
  </div>
  <div class="card-info">
    {{ 'SEND_HSM_INFO' | translate }}
  </div>

  <div class="reload" *ngIf="!readOnly">
    <button type="button" class="action-button action-button-default" (click)="refreshWhatsappHSM()">{{'CONFIGURATION_YSOCIAL_SETTINGS_WHATSAPPHSM_RELOAD' | translate}}</button>
  </div>

  <div class="description">
    <div class="title">
      {{ 'SEND_HSM_YSOCIALSERVICE' | translate }}
    </div>
    <select [disabled]="readOnly" [ngClass]="{'invalid-piece': !model.HSMTemplate.Name === null}" *ngIf="socialServices.length > 0" class="select" [(ngModel)]="selectedService" (change)="setDescriptions(true)">
      <option *ngFor="let socialService of socialServices" [ngValue]="socialService">{{socialService}}</option>
    </select>

    <div *ngIf="socialServices.length == 0" class="alert alert-info">{{ 'SEND_HSM_SERVICES_EMPTY' | translate }}</div>
  </div>

  <div class="description" *ngIf="model.HSMTemplate.Name">
    <div class="title">
      {{ 'SEND_HSM_TEMPLATE' | translate }}
    </div>
    <select [disabled]="readOnly" class="select" [(ngModel)]="model.selectedElementName" (change)="setTemplate()">
      <option *ngFor="let template of templates" [ngValue]="template.elementName">{{template.description}}</option>
    </select>
  </div>

  <div class="description" *ngIf="model.selectedElementName">
    <div class="title">
      {{ 'SEND_HSM_PARAMETERS_USER_PHONE_NUMBER' | translate }}
    </div>
      <app-input-with-variables [placeholder]="'SEND_HSM_PARAMETERS_USER_PHONE_NUMBER' | translate"
                              [(value)]="model.userPhoneNumber"
                              [disabled]="readOnly"
                              class="input"
                              [wideInput]="true"
                              [validator]="model.isPhoneNumberValid.bind(model)"></app-input-with-variables>
  </div>

  <div class="header" *ngIf="model.selectedElementName">
    <div class="title">{{ 'SEND_HSM_HEADER' | translate }}</div>

    <textarea *ngIf="model.HSMTemplate.HeaderText" [disabled]="true">{{model.HSMTemplate.HeaderText}}</textarea>

    <div *ngIf="model.HSMTemplate.HeaderMediaType !== 0" class="card attachment">
      <div class="row">
        <span class="title">{{'SEND_HSM_HEADER_TYPE' | translate}}: </span>
        <p>{{'SEND_HSM_HEADER_TYPE_MULTIMEDIA' | translate}}</p>
      </div>

      <div class="row">
        <span class="title">{{'SEND_HSM_HEADER_MULTIMEDIA_TYPE' | translate}}: </span>
        <p *ngIf="model.HSMTemplate.HeaderMediaType === 1">{{'SEND_HSM_HEADER_MULTIMEDIA_TYPE_DOCUMENT' | translate}}</p>
        <p *ngIf="model.HSMTemplate.HeaderMediaType === 2">{{'SEND_HSM_HEADER_MULTIMEDIA_TYPE_IMAGE' | translate}}</p>
        <p *ngIf="model.HSMTemplate.HeaderMediaType === 3">{{'SEND_HSM_HEADER_MULTIMEDIA_TYPE_VIDEO' | translate}}</p>
      </div>

      <div class="definition">
        <ngb-tabset [activeId]="ActiveIdString" class="tabsbutton" (tabChange)="onTabChange($event)">
          <ngb-tab id="tab-url" [disabled]="readOnly">
            <ng-template ngbTabTitle><span class="fa fa-link"></span>{{ 'URL' | translate}}</ng-template>
            <ng-template ngbTabContent>
              <div class="contents">
                <div class="url">
                  <span class="title">{{ 'URL' | translate}}:</span>
                  <app-input-with-variables
                    [placeholder]="'URL'"
                    [(value)]="model.attachment.Url"
                    [validator]="isUrlValid"
                    [wideInput]="true"
                    [disabled]="readOnly"
                    class="input-variable-area">
                  </app-input-with-variables>
                </div>
                <div class="publicurl" *ngIf="showIsPublicToggle && (isWhatsappChannel || isTelegramChannel)">
                  <span class="title">{{ 'ATTACHMENT_IS_PUBLIC_URL' | translate}}:</span>
                  <ui-switch [(ngModel)]="model.attachment.IsPublicUrl" [disabled]="readOnly"
                             color="#45c195" size="small" defaultBgColor="#e0e0e0" switchColor="#ffffff"></ui-switch>
                </div>
              </div>
            </ng-template>
          </ngb-tab>
          <ngb-tab id="tab-variable" [disabled]="readOnly">
            <ng-template ngbTabTitle><span class="fa fa-database"></span> {{'VARIABLE' | translate}}</ng-template>
            <ng-template ngbTabContent>
              <div class="contents">
                <div class="url">
                  <span class="title">{{ 'VARIABLE' | translate}}:</span>
                  <app-variable-selector-input
                    [VariableData]="variableDefinition"
                    (setVariable)="setVariableOnOutput($event)"
                    [typeFilters]="variableFilter"
                    [readOnly]="readOnly"
                    [validator]="getFileVaraibleValidator()">
                  </app-variable-selector-input>
                </div>
              </div>
            </ng-template>
          </ngb-tab>
        </ngb-tabset>
        <div class="name">
          <span class="title">{{ 'ATTACHMENT_FILENAME' | translate}}:</span>
          <app-input-with-variables
            class="input-variable-area"
            [placeholder]="'ATTACHMENT_NAME' | translate"
            [(value)]="model.attachment.Filename"
            [validator]="isNameValid"
            [disabled]="readOnly"
            [wideInput]="true">
          </app-input-with-variables>
        </div>

        <div class="mimetype">
          <span class="title">{{ 'ATTACHMENT_MIMETYPE' | translate}}:</span>
          <app-input-with-variables
            class="input-variable-area"
            [placeholder]="'ATTACHMENT_MIMETYPE' | translate"
            [(value)]="model.attachment.MimeType"
            [validator]="isMimeTypeValid"
            [wideInput]="true"
            [list]="'knowncontenttypes'"
            [disabled]="readOnly"
            [spellCheck]="false">
          </app-input-with-variables>
        </div>
      </div>

    </div>

    <div *ngIf="!model.HSMTemplate.HeaderText && model.HSMTemplate.HeaderMediaType === 0" class="alert alert-info">{{ 'SEND_HSM_HEADER_EMPTY' | translate }}</div>
  </div>

  <div class="description" *ngIf="model.selectedElementName">
    <div class="title">{{ 'SEND_HSM_BODY' | translate }}</div>
    <textarea *ngIf="model.HSMTemplate.Template" [disabled]="true">{{model.HSMTemplate.Template}}</textarea>
    <div *ngIf="!model.HSMTemplate.Template" class="alert alert-info">{{ 'SEND_HSM_DESCRIPTION_EMPTY' | translate }}</div>
  </div>

  <div class="footer" *ngIf="model.selectedElementName">
    <div class="title">{{ 'SEND_HSM_FOOTER' | translate }}</div>
    <textarea *ngIf="model.HSMTemplate.FooterText" [disabled]="true">{{model.HSMTemplate.FooterText}}</textarea>
    <div *ngIf="!model.HSMTemplate.FooterText" class="alert alert-info">{{ 'SEND_HSM_FOOTER_EMPTY' | translate }}</div>
  </div>

  <div class="buttons-area" *ngIf="model.selectedElementName">
    <div class="title">{{ 'SEND_HSM_BUTTONS' | translate }}</div>

    <div class="card buttons" *ngIf="model.HSMTemplate.ButtonsType !== 0">
      <div *ngIf="model.HSMTemplate.ButtonsType === 1">
        <div class="row">
          <span class="title">{{'SEND_HSM_BUTTONS_TYPE' | translate}}: </span>
          <p>{{'SEND_HSM_BUTTONS_TYPE_QUICK_REPLY' | translate}}</p>
        </div>
        <div *ngFor="let button of model.HSMTemplate.Buttons; let i = index">
          <div class="row">
            <span class="title">{{ 'SEND_HSM_BUTTON' | translate}} {{i+1}}: </span>
            <p>{{ button.Text }}</p>
          </div>
        </div>
      </div>

      <div *ngIf="model.HSMTemplate.ButtonsType === 2">
        <div class="row">
          <span class="title">{{'SEND_HSM_BUTTONS_TYPE' | translate}}: </span>
          <p>{{'SEND_HSM_BUTTONS_TYPE_CALL_TO_ACTION' | translate}}</p>
        </div>
        <div *ngFor="let button of model.HSMTemplate.Buttons; let i = index">
          <div class="row">
            <span class="title">{{ 'SEND_HSM_BUTTON' | translate}} {{i+1}}: </span>
            <span *ngIf="button.CallToActionButtonType === 4" class="fa fa-layer-group"><p>{{ button.Text }}</p></span>
            <span *ngIf="button.CallToActionButtonType === 3" class="fa fa-copy"><p>{{ button.Text }}</p></span>
            <span *ngIf="button.CallToActionButtonType === 2" class="fa fa-phone"><p>{{ button.Text }}</p></span>
            <span *ngIf="button.CallToActionButtonType === 1" class="fa fa-external-link"><p>{{ getButtonText(button) }}</p></span>
          </div>
        </div>
      </div>

      <div *ngIf="model.HSMTemplate.ButtonsType === 3">
        <div class="row">
          <span class="title">{{'SEND_HSM_BUTTONS_TYPE' | translate}}: </span>
          <p>{{'SEND_HSM_BUTTONS_TYPE_MIXED' | translate}}</p>
        </div>
        <div *ngFor="let button of model.HSMTemplate.Buttons; let i = index">
          <div class="row">
            <span class="title">{{ 'SEND_HSM_BUTTON' | translate}} {{i+1}}: </span>
            <span *ngIf="button.QuickReplyParameter !== null"><p>{{ button.Text }}</p></span>
            <span *ngIf="button.CallToActionButtonType === 4" class="fa fa-layer-group"><p>{{ button.Text }}</p></span>
            <span *ngIf="button.CallToActionButtonType === 3" class="fa fa-copy"><p>{{ button.Text }}</p></span>
            <span *ngIf="button.CallToActionButtonType === 2" class="fa fa-phone"><p>{{ button.Text }}</p></span>
            <span *ngIf="button.CallToActionButtonType === 1" class="fa fa-external-link"><p>{{ getButtonText(button) }}</p></span>
          </div>
        </div>
      </div>
    </div>
    <div *ngIf="model.HSMTemplate.ButtonsType === 0" class="alert alert-info">{{ 'SEND_HSM_BUTTONS_EMPTY' | translate }}</div>
  </div>

  <div class="parameters" *ngIf="haveParameters && model.selectedElementName">
    <div class="title">
      {{ 'SEND_HSM_PARAMETERS_TITLE' | translate }}
    </div>
    <div class="parameters-table">
      <div class="header">
        <div>{{ 'SEND_HSM_PARAMETERS_KEY' | translate }}</div>
        <div>{{ 'SEND_HSM_PARAMETERS_VALUE' | translate }}</div>
        <div></div>
      </div>
      <div class="parameters-row" *ngFor="let parameter of model.HeaderParameter let i = index">
        <div class="parameters-key">
          <p>{{ parameter.Key }}</p>
          <!--<app-input-with-variables [placeholder]="'SEND_HSM_PARAMETERS_KEY' | translate"
            [(value)]="Parameter.Key"
            [disabled]="true"></app-input-with-variables>-->
        </div>

        <div class="parameters-value">
          <app-input-with-variables [placeholder]="'SEND_HSM_PARAMETERS_VALUE' | translate"
                            [(value)]="parameter.Value"
                            [disabled]="readOnly"
                            class="input"
                            [wideInput]="true"
                            [validator]="parameter.isValidValue.bind(parameter)">
          </app-input-with-variables>
        </div>
      </div>

      <div class="parameters-row" *ngFor="let parameter of model.BodyParameters let i = index">
        <div class="parameters-key">
          <p>{{ parameter.Key }}</p>
          <!--<app-input-with-variables [placeholder]="'SEND_HSM_PARAMETERS_KEY' | translate"
            [(value)]="Parameter.Key"
            [disabled]="true"></app-input-with-variables>-->
        </div>

        <div class="parameters-value">
          <app-input-with-variables [placeholder]="'SEND_HSM_PARAMETERS_VALUE' | translate"
                            [(value)]="parameter.Value"
                            [disabled]="readOnly"
                            class="input"
                            [wideInput]="true"
                            [validator]="parameter.isValidValue.bind(parameter)"></app-input-with-variables>
        </div>
      </div>

      <div *ngIf="!model.templateHasFlow">
        <div class="parameters-row" *ngFor="let parameter of model.ButtonsParameters let i = index">
          <div class="parameters-key">
            <span class="fa fa-rectangle-wide"></span>
            <p>{{ parameter.Key }}</p>
            <!--<app-input-with-variables [placeholder]="'SEND_HSM_PARAMETERS_KEY' | translate"
              [(value)]="Parameter.Key"
              [disabled]="true"></app-input-with-variables>-->
          </div>
          <div class="parameters-value">
            <app-input-with-variables [placeholder]="'SEND_HSM_PARAMETERS_VALUE' | translate"
                              [(value)]="parameter.Value"
                              [disabled]="readOnly"
                              class="input"
                              [wideInput]="true"
                              [validator]="parameter.isValidValue.bind(parameter)"></app-input-with-variables>
          </div>
        </div>
      </div>
      
      <div *ngIf="model.templateHasFlow">
        <div class="parameters-row" *ngFor="let parameter of model.FlowParameters let i = index">
          <div class="parameters-key">
            <span class="fa fa-rectangle-wide"></span>
            <p>{{ parameter.Name }}</p>
            <!--<app-input-with-variables [placeholder]="'SEND_HSM_PARAMETERS_KEY' | translate"
              [(value)]="Parameter.Key"
              [disabled]="true"></app-input-with-variables>-->
          </div>

          <div class="parameters-value">
            <app-variable-selector-input  [VariableData]="getVariableData(parameter)"
                                          (setVariable)="setFlowVariableOnOutput($event, parameter)"
                                          [validator]="getAssignVariableIdValid()"
                                          [typeFilters]="getVariableType(parameter)"
                                          [readOnly]="readOnly"
                                          class="input"></app-variable-selector-input>
          </div>
        </div>
      </div>
    
    </div>

    <div class="example" *ngIf="model.selectedElementName">
      <div class="title">
        {{ 'SEND_HSM_PARAMETERS_EXAMPLE' | translate }}
      </div>
      <div class="example-card">
        <div class="card-title" *ngIf="model.HSMTemplate.HeaderText">{{ getExampleHeader() }}</div>
        <div class="card-info" *ngIf="model.HSMTemplate.Template">{{ getExampleDescription() }}</div>
        <div class="card-footer" *ngIf="model.HSMTemplate.FooterText">{{ model.HSMTemplate.FooterText }}</div>
        <div class="card-buttons" *ngIf="model.HSMTemplate.ButtonsType !== 0 && model.HSMTemplate.Buttons.length > 0">
          <div class="quick-reply" *ngIf="model.HSMTemplate.ButtonsType === 1">
            <div *ngFor="let button of model.HSMTemplate.Buttons">
              <div class="row">{{ button.Text }}</div>
            </div>
          </div>
          <div class="call-to-action" *ngIf="model.HSMTemplate.ButtonsType === 2">
            <div *ngFor="let button of model.HSMTemplate.Buttons">
              <div class="row">{{ button.Text }}</div>
            </div>
          </div>
        </div>
      </div>
    </div>
    
  </div>

</div>
