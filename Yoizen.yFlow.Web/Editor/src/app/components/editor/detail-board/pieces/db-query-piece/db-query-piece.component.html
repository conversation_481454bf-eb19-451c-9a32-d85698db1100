<div class="db-query card" [ngClass]="{'invalid-piece': !model.isValid(editorService) }">
  <div class="trash" (click)="deleteAction()" *ngIf="!readOnly"
       data-toggle="tooltip" ngbTooltip="{{ 'CARD_DELETE_TEXT' | translate }}" placement="top" container="body" tooltipClass="tooltip-trash">
    <span class="fa fa-trash-alt"></span>
  </div>
  <div class="clone" (click)="cloneAction()" *ngIf="!readOnly"
       data-toggle="tooltip" ngbTooltip="{{ 'CARD_CLONE_TEXT' | translate }}" placement="top" container="body" tooltipClass="tooltip-trash">
    <span class="fa fa-clone"></span>
  </div>
  <div class="export" (click)="exportAction()" *ngIf="!readOnly"
       data-toggle="tooltip" ngbTooltip="{{ 'CARD_EXPORT_TEXT' | translate }}" placement="top" container="body" tooltipClass="tooltip-trash">
    <span class="fa fa-file-export"></span>
  </div>
  <div class="card-title">
    <span class="fa fa-database"></span> {{ 'PIECE_DB_QUERY' | translate }}
  </div>
  <div class="table-name" *ngIf="tables.length > 0">
    <span class="title">{{ 'PIECE_DB_QUERY_SELECT_DATABASE' | translate }}:</span>
    <select class="select" name="" id=""
            [disabled]="readOnly"
            [(ngModel)]="selectedTableName"
            [ngClass]="{'invalid-input': selectedTableName === '' }"
            (ngModelChange)="onSelectTable($event)">
      <option *ngFor="let table of tables" [ngValue]="table.name" >{{ getTableName(table.name) }}</option>
    </select>
  </div>
  <div class="alert alert-info" *ngIf="tables.length == 0">
    <span class="fa fa-lg fa-exclamation-triangle icon"></span>
    {{ 'PIECE_DB_QUERY_EMPTY' | translate }}
  </div>
  <div class="table-columns" *ngIf="selectedTableName != '' && tables.length > 0">
    <span class="title">{{'PIECE_DB_QUERY_SELECT_COLUMN' | translate}}:</span>
    <select class="select" name="" id="" 
            [disabled]="readOnly" 
            [ngClass]="{'invalid-input': selectedColumnName === '' }"
            [(ngModel)]="selectedColumnName" 
            (ngModelChange)="onSelectColumn($event)">
      <option *ngFor="let header of filteredHeaders" [ngValue]="header.variable.Name">
        <span class="variable-name">{{ header.variable.Name }}</span>
      </option>
    </select>
  </div>
  <div *ngIf="selectedColumnName != '' && tables.length > 0">
    <div class="operator">
      <span class="title">{{'VARIABLECONDITION_OPERATOR' | translate}}:</span>
      <select class="select" name="" id="" [disabled]="readOnly" [(ngModel)]="model.Operator">
        <option *ngFor="let operator of getOperators()" [ngValue]="operator.value">{{ operator.localized | translate }}</option>
      </select>
    </div>
    <div class="value" *ngIf="showOperand() && tables.length > 0">
      <span class="title">{{'VARIABLECONDITION_VALUETOCOMPARE' | translate}}:</span>
      <app-input-with-variables [placeholder]="'CONDITION_OPERAND' | translate"
                                [(value)]="model.CompareValue"
                                [disabled]="readOnly"
                                [wideInput]="true"
                                class="input-variable-area"></app-input-with-variables>
    </div>
    <div class="value" *ngIf="showOperand() && tables.length > 0">
      <span class="title">{{'SAVE_INTO' | translate}}:</span>
      <app-variable-selector-input [VariableData]="curretVariable"
                                   (setVariable)="setVariableOnOutput($event)"
                                   [typeFilters]="variableFilter"
                                   [readOnly]="readOnly"
                                   class="input-variable-area"></app-variable-selector-input>
    </div>
  </div>

</div>
