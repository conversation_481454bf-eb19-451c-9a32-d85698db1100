import {Component, Input, OnInit} from '@angular/core';
import {FormDatePickerPage, FormPage, FormPiece} from "../../../../../../models/pieces/FormPiece";
import {VariableDefinition} from "../../../../../../models/VariableDefinition";
import {TypeDefinition} from "../../../../../../models/TypeDefinition";
import {EditorService} from "../../../../../../services/editor.service";

@Component({
  selector: 'app-form-page-datepicker',
  templateUrl: './form-page-datepicker.component.html',
  styleUrls: ['./form-page-datepicker.component.scss']
})
export class FormPageDatepickerComponent implements OnInit {
  @Input() basePage: FormPage;
  @Input() pageIndex: number;
  @Input() form: FormPiece;
  @Input() readOnly : boolean = false;
  page: FormDatePickerPage;
  variableTypes = TypeDefinition;

  get variableData(): VariableDefinition {
    return this.editorService.getVariableWithId(this.page.variableId);
  }

  constructor(public editorService: EditorService) { }

  ngOnInit() {
    this.page = <FormDatePickerPage> this.basePage;
  }

  setVariable(variableData: VariableDefinition) {
    this.page.variableId = variableData ? variableData.Id : -1;
  }
}
