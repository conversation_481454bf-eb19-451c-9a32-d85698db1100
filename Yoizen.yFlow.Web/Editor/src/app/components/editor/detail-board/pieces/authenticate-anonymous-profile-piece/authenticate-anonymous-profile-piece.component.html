<div class="attach card" [ngClass]="{'invalid-piece': !model.isValid(editorService)}">
  <div class="trash" (click)="deleteAction()" *ngIf="!readOnly"
       data-toggle="tooltip" ngbTooltip="{{ 'CARD_DELETE_TEXT' | translate }}" placement="top" container="body" tooltipClass="tooltip-trash">
    <span class="fa fa-trash-alt"></span>
  </div>
  <div class="clone" (click)="cloneAction()" *ngIf="!readOnly"
       data-toggle="tooltip" ngbTooltip="{{ 'CARD_CLONE_TEXT' | translate }}" placement="top" container="body" tooltipClass="tooltip-trash">
    <span class="fa fa-clone"></span>
  </div>
  <div class="export" (click)="exportAction()" *ngIf="!readOnly"
       data-toggle="tooltip" ngbTooltip="{{ 'CARD_EXPORT_TEXT' | translate }}" placement="top" container="body" tooltipClass="tooltip-trash">
    <span class="fa fa-file-export"></span>
  </div>
  <div class="card-title">
    <span class="fa fa-paperclip"></span> {{ 'AUTHENTICATE_ANONYMOUS_PROFILE_TITLE' | translate }}
  </div>
  <div class="card-info">
    {{ 'AUTHENTICATE_ANONYMOUS_PROFILE_INFO' | translate }}
  </div>
  <div class="alert alert-info">
    <span class="fa fa-lg fa-exclamation-triangle icon"></span>
    {{ 'AUTHENTICATE_ANONYMOUS_PROFILE_ALERT' | translate }}
  </div>
  <div class="definition">
    <div class="code">
      <span class="title">{{ 'AUTHENTICATE_ANONYMOUS_PROFILE_ID' | translate}}:</span>
      <app-variable-selector-input
        [VariableData]="variableDefinition('AuthClientCode')"
        (setVariable)="setVariableOnOutput('AuthClientCode', $event)"
        [typeFilters]="variableFilter"
        [readOnly]="readOnly">
      </app-variable-selector-input>
    </div>

    <div class="name">
      <span class="title">{{ 'AUTHENTICATE_ANONYMOUS_PROFILE_NAME' | translate}}:</span>
      <app-variable-selector-input
        [VariableData]="variableDefinition('AuthName')"
        (setVariable)="setVariableOnOutput('AuthName', $event)"
        [typeFilters]="variableFilter"
        [readOnly]="readOnly">
      </app-variable-selector-input>
    </div>

    <div class="email">
      <span class="title">{{ 'AUTHENTICATE_ANONYMOUS_PROFILE_EMAIL' | translate}}:</span>
      <app-variable-selector-input
        [VariableData]="variableDefinition('AuthEmail')"
        (setVariable)="setVariableOnOutput('AuthEmail', $event)"
        [typeFilters]="variableFilter"
        [readOnly]="readOnly">
      </app-variable-selector-input>
    </div>

    <div class="phone">
      <span class="title">{{ 'AUTHENTICATE_ANONYMOUS_PROFILE_PHONE' | translate}}:</span>
      <app-variable-selector-input
        [VariableData]="variableDefinition('AuthPhone')"
        (setVariable)="setVariableOnOutput('AuthPhone', $event)"
        [typeFilters]="variableFilter"
        [readOnly]="readOnly">
      </app-variable-selector-input>
    </div>
    
  </div>
  <div class="next">
    <span class="title">{{'ON_ERROR_GO_TO' | translate}}:</span>
    <app-block-picker class="input"
                      [blockId]="model.ErrorBlockId"
                      (onSelectNewBlock)="onSelectBlock($event)"
                      (onDeleteBlock)="onDeleteBlock($event)"
                      [readOnly]="readOnly"
                      [isInvalid]="!model.isErrorBlockValid(editorService)"></app-block-picker>
  </div>
</div>
