import { Component, OnInit } from '@angular/core';
import { SourceTypes } from 'src/app/models/pieces/AttachmentPiece';
import { OutputVariableMap } from 'src/app/models/pieces/IntegrationPiece';
import { StickerPiece } from 'src/app/models/pieces/StickerPiece';
import { TypeDefinition } from 'src/app/models/TypeDefinition';
import { VariableDefinition } from 'src/app/models/VariableDefinition';
import { EditorService } from 'src/app/services/editor.service';
import { ModalService } from 'src/app/services/Tools/ModalService';
import { isStringValid, isUrlValid } from 'src/app/urlutils.module';
import { BasePieceVM } from '../BasePieceVM';

@Component({
  selector: 'app-sticker-piece',
  templateUrl: './sticker-piece.component.html',
  styleUrls: ['./sticker-piece.component.scss']
})
export class StickerPieceComponent extends BasePieceVM implements OnInit {
  model: StickerPiece;
  variableFilter = [TypeDefinition.ByteArray, TypeDefinition.Base64];
  ActiveIdString: string;
  previewUrl: string = null;

  constructor(editorService: EditorService, public modalService: ModalService) {
    super(editorService, modalService);
  }

  ngOnInit() {
    this.model = this.context as StickerPiece;
    this.ActiveIdString = this.getActiveTab();
    this.flow = this.editorService.getCurrentFlow();
    if (isUrlValid(this.model.Url)) {
      this.previewUrl = this.model.Url;
    }
  }

  isNameValid(str): Boolean {
    return isStringValid(str);
  }

  ngOnChanges() {
    this.ActiveIdString = this.getActiveTab();
  }

  getActiveTab(): string {
    switch (this.model.Source) {
      case SourceTypes.Variable:
        return "tab-variable";
      case SourceTypes.Url:
        return "tab-url";
    }
  }

  onFocusOutEvent(event: any) {
    if (isUrlValid(this.model.Url)) {
      this.previewUrl = this.model.Url;
    } else {
      this.previewUrl = null;
    }
  }

  showPreview(str): Boolean {
    return isUrlValid(str, false);
  }

  isMimeTypeValid(str) {
    if (!isStringValid(str)) {
      return false;
    }

    if (str.indexOf('{{') >= 0 &&
      str.indexOf('}}') >= 0) {
      return true;
    }

    if (str.indexOf('${') >= 0 &&
      str.indexOf('}$') >= 0) {
      return true;
    }

    const regex = new RegExp('^[-a-z]{1,127}/[-a-z0-9\+]+(\.[-a-z0-9\+]+)*$');
    if (!regex.test(str.toString())) {
      return false;
    }

    return true;
  }

  setVariableOnOutput(output: OutputVariableMap, variable: VariableDefinition) {
    if (variable != null) {
      this.model.FileDataStorageId = variable.Id;
    }
    else {
      this.model.FileDataStorageId = null;
    }
  }

  onTabChange(eventInfo) {
    switch (eventInfo.nextId) {
      case "tab-url":
        this.model.Source = SourceTypes.Url;
        return;
      case "tab-variable":
        this.model.Source = SourceTypes.Variable;
        return
    }
  }

  getFileVaraibleValidator() {
    return this.validateFileVariable.bind(this);
  }

  validateFileVariable() {
    return this.model.isFileDataValid(this.editorService);
  }

}
