import { Component, OnInit, Input, Output , EventEmitter } from '@angular/core';


@Component({
  selector: 'app-text-list-error-message',
  templateUrl: './text-list-error-message.component.html',
  styleUrls: ['./text-list-error-message.component.scss']
})
export class TextListErrorMessageComponent implements OnInit {


  @Input() set Text(value) {
    this.value = value;
  }
  @Input() Index : number;
  @Input() CanDelete : boolean;
  @Input() Validator : Function;
  @Input() readOnly : boolean = false;
  @Output() onChange = new EventEmitter();

  @Output() onDelete : EventEmitter<number> = new EventEmitter<number>();
  @Output() TextChange : EventEmitter<string> = new EventEmitter<string>();

  value : string;

  constructor() { }

  ngOnInit() {
  }

  deleteElement() {
  	this.onDelete.emit(this.Index);
  }
}
