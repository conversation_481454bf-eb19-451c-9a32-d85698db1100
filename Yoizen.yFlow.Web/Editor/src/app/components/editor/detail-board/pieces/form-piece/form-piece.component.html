<div class="form card" [ngClass]="{'invalid-piece': !model.isValid(editorService)}">
  <div class="trash" (click)="deleteAction()" *ngIf="!readOnly"
       data-toggle="tooltip" ngbTooltip="{{ 'CARD_DELETE_TEXT' | translate }}" placement="top" container="body" tooltipClass="tooltip-trash">
    <span class="fa fa-trash-alt"></span>
  </div>
  <div class="clone" (click)="cloneAction()" *ngIf="!readOnly"
       data-toggle="tooltip" ngbTooltip="{{ 'CARD_CLONE_TEXT' | translate }}" placement="top" container="body" tooltipClass="tooltip-trash">
    <span class="fa fa-clone"></span>
  </div>
  <div class="export" (click)="exportAction()" *ngIf="!readOnly"
       data-toggle="tooltip" ngbTooltip="{{ 'CARD_EXPORT_TEXT' | translate }}" placement="top" container="body" tooltipClass="tooltip-trash">
    <span class="fa fa-file-export"></span>
  </div>
  <div class="card-title">
    <span class="fa fa-ballot"></span> {{ 'CARD_FORM_TITLE' | translate }}
  </div>
  <div class="card-info">
    {{ 'CARD_FORM_INFO' | translate }}
  </div>
  <div class="contents">
    <div class="option">
      <span class="title">{{'FORM_SPLASH_USE' | translate}}:</span>
      <ui-switch [(ngModel)]="model.useSplash" [disabled]="readOnly"
                 color="#45c195" size="small" defaultBgColor="#e0e0e0" switchColor="#ffffff"></ui-switch>
    </div>
    <div class="option with-info">
      <span class="title">{{'FORM_STARTPAGEIDENTIFIER' | translate}}:</span>
      <input type="text"
             [(ngModel)]="model.startPageIdentifier"
             [ngClass]="{'invalid-input': !model.isStartPageIdentifierValid() }"
             [disabled]="readOnly"
             class="input" spellcheck="false" autocomplete="off" />
      <div class="info" ngbTooltip="{{ 'FORM_STARTPAGEIDENTIFIER_TIP' | translate }}"
           placement="right" container="body" tooltipClass="tooltip-persistent">
        <span class="fa fa-question-circle"></span>
      </div>
    </div>
    <div class="option with-info">
      <span class="title">{{'FORM_PRIVATE' | translate}}:</span>
      <ui-switch [(ngModel)]="model.private" [disabled]="readOnly"
                 color="#45c195" size="small" defaultBgColor="#e0e0e0" switchColor="#ffffff"></ui-switch>
      <div class="info" ngbTooltip="{{ 'FORM_PRIVATE_TIP' | translate }}"
           placement="right" container="body" tooltipClass="tooltip-persistent">
        <span class="fa fa-question-circle"></span>
      </div>
    </div>
    <div class="option with-info">
      <span class="title">{{'FORM_SHOWSUMMARY' | translate}}:</span>
      <ui-switch [(ngModel)]="model.showSummary" [disabled]="readOnly"
                 color="#45c195" size="small" defaultBgColor="#e0e0e0" switchColor="#ffffff"></ui-switch>
      <div class="info" ngbTooltip="{{ 'FORM_SHOWSUMMARY_TIP' | translate }}"
           placement="right" container="body" tooltipClass="tooltip-persistent">
        <span class="fa fa-question-circle"></span>
      </div>
    </div>
    <div class="option">
      <span class="title">{{'FORM_BLOCK' | translate}}:</span>
      <app-block-picker class="input" [blockId]="model.blockId" (onSelectNewBlock)="onSelectBlock($event)"
                        [readOnly]="readOnly"
                        (onDeleteBlock)="onDeleteBlock()"
                        [isInvalid]="!model.isBlockValid(editorService)"></app-block-picker>
    </div>
    <div class="pages">
      <div class="title">{{'FORM_PAGES_TITLE' | translate}}</div>
      <div class="info">{{'FORM_PAGES_INFO' | translate}}</div>
      <div class="pages-container">
        <div class="page splash" *ngIf="model.useSplash"
             [ngClass]="{'invalid-page': !model.splash.isValid() }">
          <div class="title">{{'FORM_SPLASH_EDIT_TITLE' | translate}}</div>
          <div class="options">
            <div class="option">
              <span class="title">{{'FORM_SPLASH_HEADER' | translate}}:</span>
              <app-input-with-variables
                [placeholder]="'FORM_SPLASH_HEADER' | translate"
                [(value)]="model.splash.header"
                [isTextArea]="false"
                [wideInput]="true"
                [validator]="model.splash.isHeaderValid.bind(model.splash)"
                [disabled]="readOnly">
              </app-input-with-variables>
            </div>
            <div class="option">
              <span class="title">{{'FORM_SPLASH_SPLASHTEXT' | translate}}:</span>
              <app-input-with-variables
                [placeholder]="'FORM_SPLASH_SPLASHTEXT' | translate"
                [(value)]="model.splash.splashText"
                [isTextArea]="false"
                [wideInput]="true"
                [validator]="model.splash.isSplashTextValid.bind(model.splash)"
                [disabled]="readOnly">
              </app-input-with-variables>
            </div>
            <div class="option">
              <span class="title">{{'FORM_SPLASH_BUTTONTITLE' | translate}}:</span>
              <app-input-with-variables
                [placeholder]="'FORM_SPLASH_BUTTONTITLE' | translate"
                [(value)]="model.splash.buttonTitle"
                [isTextArea]="false"
                [wideInput]="true"
                [validator]="model.splash.isButtonTitleValid.bind(model.splash)"
                [disabled]="readOnly">
              </app-input-with-variables>
            </div>
            <div class="option">
              <span class="title">{{'FORM_SPLASH_IMAGE' | translate}}:</span>
              <app-input-with-variables
                [placeholder]="'FORM_SPLASH_IMAGE' | translate"
                [(value)]="model.splash.image"
                [isTextArea]="false"
                [wideInput]="true"
                [validator]="model.splash.isImageValid.bind(model.splash)"
                [disabled]="readOnly">
              </app-input-with-variables>
            </div>
          </div>
        </div>
        <div class="page" *ngFor="let page of model.pages; index as i;"
             [ngClass]="{
              'invalid-page': !page.isValid(editorService),
              'select': page.type === formPageTypes.select,
              'datepicker': page.type === formPageTypes.datePicker,
              'picker': page.type === formPageTypes.picker,
              'input': page.type === formPageTypes.input
             }">
          <div class="trash" (click)="removePage(page)"
               *ngIf="model.pages !== null && model.pages.length > 1 && !readOnly"
               data-toggle="tooltip" ngbTooltip="{{ 'FORM_PAGES_DELETE' | translate }}" placement="top"
               container="body" tooltipClass="tooltip-trash">
            <span class="fa fa-trash-alt"></span>
          </div>
          <div class="reorder left" (click)="moveLeft(i); $event.stopPropagation();" *ngIf="i > 0 && !readOnly"
               data-toggle="tooltip" ngbTooltip="{{ 'QUICKREPLY_REORDER_LEFT' | translate }}" placement="top"
               container="body" tooltipClass="tooltip-trash">
            <span class="fa fa-caret-left"></span>
          </div>
          <div class="reorder right" (click)="moveRight(i); $event.stopPropagation();" *ngIf="i < (model.pages.length - 1) && !readOnly"
               data-toggle="tooltip" ngbTooltip="{{ 'QUICKREPLY_REORDER_RIGHT' | translate }}" placement="top"
               container="body" tooltipClass="tooltip-trash">
            <span class="fa fa-caret-right"></span>
          </div>
          <div class="title" *ngIf="page.type === formPageTypes.select">{{'FORM_PAGE_TYPE_SELECT' | translate}}</div>
          <div class="title" *ngIf="page.type === formPageTypes.datePicker">{{'FORM_PAGE_TYPE_DATEPICKER' | translate}}</div>
          <div class="title" *ngIf="page.type === formPageTypes.input">{{'FORM_PAGE_TYPE_INPUT' | translate}}</div>
          <div class="title" *ngIf="page.type === formPageTypes.picker">{{'FORM_PAGE_TYPE_PICKER' | translate}}</div>

          <div class="option with-info">
            <span class="title">{{'FORM_PAGE_IDENTIFIER' | translate}}:</span>
            <input type="text"
                   [(ngModel)]="page.pageIdentifier"
                   [ngClass]="{'invalid-input': !page.isPageIdentifierValid() }"
                   [disabled]="readOnly"
                   maxlength="5"
                   class="input" spellcheck="false" autocomplete="off" />
            <div class="info" ngbTooltip="{{ 'FORM_PAGE_IDENTIFIER_TIP' | translate }}"
                 placement="right" container="body" tooltipClass="tooltip-persistent">
              <span class="fa fa-question-circle"></span>
            </div>
          </div>
          <div class="option">
            <span class="title">{{'FORM_PAGE_TITLE' | translate}}:</span>
            <app-input-with-variables
              [(value)]="page.title"
              [validator]="page.isPageTitleValid.bind(page)"
              [wideInput]="true"
              [isTextArea]="false"
              [disabled]="readOnly"
              class="input">
            </app-input-with-variables>
          </div>
          <div class="option">
            <span class="title">{{'FORM_PAGE_SUBTITLE' | translate}}:</span>
            <app-input-with-variables
              [(value)]="page.subtitle"
              [validator]="page.isPageSubtitleValid.bind(page)"
              [wideInput]="true"
              [isTextArea]="false"
              [disabled]="readOnly"
              class="input">
            </app-input-with-variables>
          </div>

          <app-form-page-select *ngIf="page.type === formPageTypes.select"
                                [basePage]="page"
                                [pageIndex]="i"
                                [form]="model"
                                [readOnly]="readOnly"></app-form-page-select>

          <app-form-page-picker *ngIf="page.type === formPageTypes.picker"
                                [basePage]="page"
                                [pageIndex]="i"
                                [form]="model"
                                [readOnly]="readOnly"></app-form-page-picker>

          <app-form-page-datepicker *ngIf="page.type === formPageTypes.datePicker"
                                    [basePage]="page"
                                    [pageIndex]="i"
                                    [form]="model"
                                    [readOnly]="readOnly"></app-form-page-datepicker>

          <app-form-page-input *ngIf="page.type === formPageTypes.input"
                               [basePage]="page"
                               [pageIndex]="i"
                               [form]="model"
                               [readOnly]="readOnly"></app-form-page-input>

          <div class="option">
            <span class="title">{{'FORM_PAGE_SUBMITFORM' | translate}}:</span>
            <ui-switch [(ngModel)]="page.submitForm" [disabled]="readOnly"
                       color="#45c195" size="small" defaultBgColor="#e0e0e0" switchColor="#ffffff"></ui-switch>
          </div>
          <div class="option" *ngIf="page.supportsNextPage()">
            <span class="title">{{'FORM_PAGE_NEXTPAGEIDENTIFIER' | translate}}:</span>
            <input type="text"
                   [(ngModel)]="page.nextPageIdentifier"
                   [ngClass]="{'invalid-input': !page.isNextPageIdentifierValid() }"
                   [disabled]="readOnly"
                   class="input" spellcheck="false" autocomplete="off" />
          </div>
        </div>
        <div class="addItem" *ngIf="canAddPage()">
          <div data-toggle="tooltip" (click)="addPage()"
               ngbTooltip="{{ 'FORM_PAGES_ADD' | translate }}" placement="right" tooltipClass="tooltip-add">
            <span class="fa fa-plus"></span>
          </div>
        </div>
      </div>
    </div>
    <app-apple-interactive-received-and-reply
      [receivedMessage]="model.receivedMessage"
      [replyMessage]="model.replyMessage"
      [readOnly]="readOnly"></app-apple-interactive-received-and-reply>
  </div>
</div>
