import { Component, OnInit } from '@angular/core';
import { BlockDefinition } from 'src/app/models/BlockDefinition';
import { ErrorMessage, ValidateFrontalDniPiece } from 'src/app/models/pieces/ValidateFrontalDniPiece';
import { EditorService } from 'src/app/services/editor.service';
import { ModalService } from 'src/app/services/Tools/ModalService';
import { BasePieceVM } from '../BasePieceVM';


@Component({
  selector: 'app-validate-frontal-dni-piece',
  templateUrl: './validate-frontal-dni-piece.component.html',
  styleUrls: ['./validate-frontal-dni-piece.component.scss']
})
export class ValidateFrontalDniPieceComponent extends BasePieceVM implements OnInit {
  model: ValidateFrontalDniPiece;

  constructor(editorService: EditorService, public modalService: ModalService) {
    super(editorService, modalService);
  }

  ngOnInit() {
    this.model = this.context as ValidateFrontalDniPiece;
  }

  onSelectBlock(blockData: BlockDefinition) {
    this.model.ErrorBlockId = blockData.Id;
  }

  onDeleteBlock(blockData: BlockDefinition) {
    this.model.ErrorBlockId = null;
  }

  addNewText() {
    this.model.ErrorMessages.push(new ErrorMessage());
  }

  canAddTextOptions(): boolean {
    var value = this.model.ErrorMessages.length < 3;
    return value;
  }

  deleteErrorMessage(element) {
    this.model.ErrorMessages.splice(element, 1);
  }

}
