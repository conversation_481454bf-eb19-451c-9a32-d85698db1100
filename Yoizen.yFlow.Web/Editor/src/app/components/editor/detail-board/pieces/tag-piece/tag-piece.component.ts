import { Component, OnInit } from '@angular/core';
import { BasePieceVM } from '../BasePieceVM';
import { EditorService } from '../../../../../services/editor.service';
import { ModalService } from '../../../../../services/Tools/ModalService';
import {TagPiece} from "../../../../../models/pieces/TagPiece";

@Component({
  selector: 'app-tag-piece',
  templateUrl: './tag-piece.component.html',
  styleUrls: ['./tag-piece.component.scss']
})
export class TagPieceComponent extends BasePieceVM implements OnInit {
  model : TagPiece;

  constructor( editorService : EditorService, public modalService : ModalService ) {
    super(editorService, modalService);
  }

  ngOnInit() {
    this.model = this.context as TagPiece;
  }

  onValueChange(value: boolean): void{
    this.model.importantTag = value;
  }
}
