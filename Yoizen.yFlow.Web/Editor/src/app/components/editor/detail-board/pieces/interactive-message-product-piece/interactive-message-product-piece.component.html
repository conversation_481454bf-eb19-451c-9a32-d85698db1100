<div class="card interactive-list {{ editorService.getChannelTypeName(model.Channel) }}" [ngClass]="{'invalid-piece': !model.isValid(editorService) }">
  <div class="trash" (click)="deleteAction()" *ngIf="!readOnly"
       data-toggle="tooltip" ngbTooltip="{{ 'CARD_DELETE_TEXT' | translate }}" placement="top" container="body" tooltipClass="tooltip-trash">
    <span class="fa fa-trash-alt"></span>
  </div>
  <div class="clone" (click)="cloneAction()" *ngIf="!readOnly"
       data-toggle="tooltip" ngbTooltip="{{ 'CARD_CLONE_TEXT' | translate }}" placement="top" container="body" tooltipClass="tooltip-trash">
    <span class="fa fa-clone"></span>
  </div>
  <div class="export" (click)="exportAction()" *ngIf="!readOnly"
       data-toggle="tooltip" ngbTooltip="{{ 'CARD_EXPORT_TEXT' | translate }}" placement="top" container="body" tooltipClass="tooltip-trash">
    <span class="fa fa-file-export"></span>
  </div>
  <div class="card-title">
    <span class="fa fa-list-ul"></span> {{ 'CARD_INTERACTIVE_MESSAGE_PRODUCT_TITLE' | translate }}
  </div>
  <div class="loading" *ngIf="!ready">
    <div>
      <i class="fas fa-spinner fa-spin"></i>
    </div>
  </div>
  <div *ngIf="ready">
    <div class="body">
      <div class="title">{{ 'INTERACTIVE_MESSAGE_BUTTONS_BODY' | translate }}</div>
      <div class="max-length" role="alert" *ngIf="!readOnly">
        <div class="alert alert-info">
          <span class="fa fa-lg fa-info-circle icon"></span>
          <span class="whatsapp">{{ 'CARD_MESSAGE_WHATSAPP_INTERACTIVE_MAX_LENGTH' | translate }}</span>
        </div>
      </div>
      <div class="messages">
        <app-text-list-message *ngFor="let text of model?.TextList let i = index"
                               [(Text)]="text.text"
                               [Index]="i"
                               [CanDelete]="!readOnly && model?.TextList.length > 1"
                               [readOnly]="readOnly"
                               [Validator]="isTextValid(i)"
                               (onDelete)="deleteElement($event)"></app-text-list-message>
        <div class="addText" [ngClass]="{'hide': !canAddTextOptions() }" *ngIf="!readOnly">
          <div (click)="addNewText()" data-toggle="tooltip" ngbTooltip="{{ 'CARD_MESSAGE_ADD_TEXT' | translate }}"
               placement="right" tooltipClass="tooltip-add">
            <span class="fa fa-plus"></span>
          </div>
        </div>
      </div>
    </div>
    <div class="select-catalog">
      <div class="option">
        <span class="title">{{'INTERACTIVE_MESSAGE_PRODUCTLIST_CATALOG' | translate}}:</span>
        <select class="select" name="" id="" [(ngModel)]="model.CatalogId"
                [disabled]="readOnly">
          <option *ngFor="let catalog of catalogsInfo" [ngValue]="catalog.Catalog.Id">{{ catalog.Catalog.Name }} ({{ catalog.Catalog.Id }})</option>
        </select>
      </div>
    </div>
    <div class="product" *ngIf="model.CatalogId !== null && model.CatalogId.length > 0">
      <div class="option">
        <span class="title">{{'INTERACTIVE_MESSAGE_PRODUCT_PRODUCT' | translate}}:</span>
        <select class="select" [(ngModel)]="model.ProductId"
                [disabled]="readOnly">
          <option *ngFor="let product of getCatalogProducts()" [ngValue]="product.RetailerId">{{ product.Name }} ({{ product.RetailerId }})</option>
        </select>
      </div>
      <app-product-preview class="preview"
                           *ngIf="model.ProductId !== null && model.ProductId.length > 0"
                           [product]="getCatalogProduct(model.ProductId)"></app-product-preview>
    </div>
  </div>
</div>
