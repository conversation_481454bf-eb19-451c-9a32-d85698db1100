import { Component, OnInit } from '@angular/core';
import { BasePieceVM } from '../BasePieceVM';
import { EditorService } from '../../../../../services/editor.service';
import { ModalService } from '../../../../../services/Tools/ModalService';
import { DeriveOperatorPiece } from '../../../../../models/pieces/DeriveOperatorPiece';
import { isStringValid } from '../../../../../urlutils.module'
import {MarkMessageAsPendingPiece} from "../../../../../models/pieces/MarkMessageAsPendingPiece";

@Component({
  selector: 'app-mark-message-as-pending-piece',
  templateUrl: './mark-message-as-pending-piece.component.html',
  styleUrls: ['./mark-message-as-pending-piece.component.scss']
})
export class MarkMessageAsPendingPieceComponent extends BasePieceVM implements OnInit {

  model : MarkMessageAsPendingPiece;

  constructor( editorService : EditorService, public modalService : ModalService ) {
    super(editorService, modalService);
   }

  ngOnInit() {
    this.model = this.context as MarkMessageAsPendingPiece;
  }
}
