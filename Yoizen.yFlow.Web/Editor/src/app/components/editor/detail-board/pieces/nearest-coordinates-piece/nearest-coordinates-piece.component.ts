import {Component, OnInit} from '@angular/core';
import {BaseDynamicComponent} from '../../../../utils/component-holder/BaseDynamicComponent';
import {BasePieceVM} from '../BasePieceVM';
import {EditorService} from '../../../../../services/editor.service';
import {ModalService} from '../../../../../services/Tools/ModalService';
import {OperandTypeDefinition, OperatorDefinitions} from '../../../../../models/OperatorType';
import {ConditionPiece} from '../../../../../models/pieces/ConditionPiece';
import {BlockDefinition} from '../../../../../models/BlockDefinition';
import {VariableConditionPiece} from "../../../../../models/pieces/VariableConditionPiece";
import {VariableDefinition} from "../../../../../models/VariableDefinition";
import {TypeDefinition} from "../../../../../models/TypeDefinition";
import {NearestCoordinatesPiece} from "../../../../../models/pieces/NearestCoordinatesPiece";
import {ChannelTypes} from "../../../../../models/ChannelType";

@Component({
  selector: 'app-nearest-coordinates-piece',
  templateUrl: './nearest-coordinates-piece.component.html',
  styleUrls: ['./nearest-coordinates-piece.component.scss']
})
export class NearestCoordinatesPieceComponent extends BasePieceVM implements OnInit {
  model: NearestCoordinatesPiece;
  variableFilter = [TypeDefinition.Array];
  storeVariableFilter = [TypeDefinition.Array];
  isChat: boolean = false;

  get VariableData(): VariableDefinition {
    return this.editorService.getVariableWithId(this.model.VariableId);
  }

  get StoreVariableData(): VariableDefinition {
    return this.editorService.getVariableWithId(this.model.StoreVariableId);
  }

  constructor(editorService: EditorService, public modalService: ModalService) {
    super(editorService, modalService);
  }

  ngOnInit() {
    this.model = this.context as NearestCoordinatesPiece;
    let flow = this.editorService.getCurrentFlow();
    this.isChat = flow.channel == ChannelTypes.Chat;

    if (this.model.VariableId !== -1) {
      this.setVariable(this.VariableData);
    }
    if (this.model.StoreVariableId !== -1) {
      this.setStoreVariable(this.StoreVariableData);
    }
  }

  setVariable(variable: VariableDefinition) {
    if (variable != null) {
      this.model.VariableId = variable.Id;
    }
    else {
      this.model.VariableId = -1;
    }
  }

  setStoreVariable(variable: VariableDefinition) {
    if (variable != null) {
      this.model.StoreVariableId = variable.Id;
    }
    else {
      this.model.StoreVariableId = -1;
    }
  }
}
