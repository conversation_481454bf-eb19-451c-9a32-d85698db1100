@import '_variables';
@import '_mixins';

.operator{
	width: 550px;
	background-color: #fff;

  .derivation-key, .vim, .alert-message {
    display: flex;
    flex-direction: row;
    margin-bottom: 10px;
    width: 100%;

    .title {
      font-family: $fontFamilyTitles;
      font-weight: bold;
      margin-right: 10px;
      justify-self: center;
      align-self: center;
      text-align: center;
      flex-grow: 0;
      flex-shrink: 0;
    }

    .input-variable-area {
      flex-grow: 1;
    }

    .input {
      flex-grow: 1;
      flex-shrink: 1;
    }
  }
}
.summary-section {
  margin-top: 15px;

  .summary-options {
    margin-top: 10px;
    padding: 10px;
    background-color: #f8f9fa;
    border-radius: 4px;

    .summary-type {
      display: flex;
      align-items: center;
      margin-bottom: 10px;

      .title {
        margin-right: 10px;
        font-weight: bold;
      }

      select {
        width: 200px;
      }
    }
  }
}

