<div class="jump card" [ngClass]="{'invalid-piece': !model.isValid(editorService)}">
  <div class="trash" (click)="deleteAction()" *ngIf="!readOnly"
       data-toggle="tooltip" ngbTooltip="{{ 'CARD_DELETE_TEXT' | translate }}" placement="top" container="body" tooltipClass="tooltip-trash">
    <span class="fa fa-trash-alt"></span>
  </div>
  <div class="clone" (click)="cloneAction()" *ngIf="!readOnly"
       data-toggle="tooltip" ngbTooltip="{{ 'CARD_CLONE_TEXT' | translate }}" placement="top" container="body" tooltipClass="tooltip-trash">
    <span class="fa fa-clone"></span>
  </div>
  <div class="export" (click)="exportAction()" *ngIf="!readOnly"
       data-toggle="tooltip" ngbTooltip="{{ 'CARD_EXPORT_TEXT' | translate }}" placement="top" container="body" tooltipClass="tooltip-trash">
    <span class="fa fa-file-export"></span>
  </div>
  <div class="card-title">
    <span class="fa fa-sitemap fa-rotate-270"></span> {{ 'CARD_SWITCHJUMPTOBLOCK_TITLE' | translate }}
  </div>
  <div class="variable-entity-switch" *ngIf="cognitivityEnabled">
    <span class="title">{{'VARIABLECONDITION_SOURCE' | translate}}:</span>
    <ui-switch [(ngModel)]="model.UsesCognitiveEntities" [disabled]="readOnly" (change)="clearCache()"
               color="#45c195" size="small" defaultBgColor="#e0e0e0" switchColor="#ffffff"></ui-switch>
  </div>
  <div class="source">
    <span class="title" *ngIf="!model.UsesCognitiveEntities">{{'VARIABLECONDITION_SOURCE_VARIABLE' | translate}}:</span>
    <span class="title" *ngIf="model.UsesCognitiveEntities">{{'VARIABLECONDITION_SOURCE_ENTITY' | translate}}:</span>
    <app-variable-selector-input *ngIf="!model.UsesCognitiveEntities"
      [VariableData]="VariableData"
      [includeImplicit]="true"
      (setVariable)="setVariable($event)"
      [typeFilters]="[ variableTypes.Number, variableTypes.Decimal, variableTypes.Timestamp, variableTypes.StringDate, variableTypes.Text ]"
      [readOnly]="readOnly">
    </app-variable-selector-input>
    <app-entity-selector-input *ngIf="model.UsesCognitiveEntities"
      [readOnly]="readOnly"
      [entity]="selectedEntity"
      (setEntity)="setEntity($event)">
    </app-entity-selector-input>
  </div>
  <div class="conditions" *ngIf="CanAddConditions">
    <div class="title">
      {{ 'CARD_SWITCHJUMPTOBLOCK_CONDITIONS' | translate }}
    </div>
    <div class="conditions-table" *ngIf="model.Conditions !== null && model.Conditions.length > 0">
      <div class="header">
        <div>{{ 'CARD_SWITCHJUMPTOBLOCK_CONDITION_VALUE' | translate }}</div>
        <div>{{ 'CARD_SWITCHJUMPTOBLOCK_CONDITION_BLOCK' | translate }}</div>
        <div></div>
      </div>
      <div class="condition-row" *ngFor="let condition of model.Conditions let i = index">
        <div class="condition-value">
          <input class="input" type="text" [(ngModel)]="condition.Value" [disabled]="readOnly" spellcheck="false"
            autocomplete="off" />
        </div>
        <div class="condition-variable">
          <app-block-picker class="input" [blockId]="condition.BlockId" (onSelectNewBlock)="onSelectBlock(i, $event)"
            [readOnly]="readOnly" (onDeleteBlock)="onDeleteBlock(i, $event)"
            [isInvalid]="!condition.isBlockValid(editorService)"></app-block-picker>
        </div>
        <div class="trash" *ngIf="!readOnly">
          <div (click)="deleteCondition(i)" tooltipClass="tooltip-trash-left" data-toggle="tooltip"
            ngbTooltip="{{ 'CARD_SWITCHJUMPTOBLOCK_CONDITION_REMOVE' | translate }}" placement="left">
            <span class="fa fa fa-trash-alt"></span>
          </div>
        </div>
      </div>
    </div>
    <div class="empty" *ngIf="model.Conditions === null || model.Conditions.length === 0" role="alert">
      <div class="alert alert-info">
        {{ 'CARD_SWITCHJUMPTOBLOCK_CONDITIONS_EMPTY' | translate }}
      </div>
    </div>
    <div class="add" (click)="addCondition()" *ngIf="!readOnly">
      <span class="fa fa-plus"></span> {{ 'CARD_SWITCHJUMPTOBLOCK_CONDITIONS_ADD' | translate }}
    </div>
  </div>
</div>