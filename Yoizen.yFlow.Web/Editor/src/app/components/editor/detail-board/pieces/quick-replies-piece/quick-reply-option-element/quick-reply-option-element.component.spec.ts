import { async, ComponentFixture, TestBed } from '@angular/core/testing';

import { QuickReplyOptionElementComponent } from './quick-reply-option-element.component';

describe('QuickReplyOptionElementComponent', () => {
  let component: QuickReplyOptionElementComponent;
  let fixture: ComponentFixture<QuickReplyOptionElementComponent>;

  beforeEach(async(() => {
    TestBed.configureTestingModule({
      declarations: [ QuickReplyOptionElementComponent ]
    })
    .compileComponents();
  }));

  beforeEach(() => {
    fixture = TestBed.createComponent(QuickReplyOptionElementComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });
});
