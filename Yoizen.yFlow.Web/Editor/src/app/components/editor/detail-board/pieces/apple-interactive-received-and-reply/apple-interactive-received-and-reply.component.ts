import {Component, Input, OnInit} from '@angular/core';
import { BaseDynamicComponent } from '../../../../utils/component-holder/BaseDynamicComponent';
import { BasePieceVM } from '../BasePieceVM';
import { EditorService } from '../../../../../services/editor.service';
import { ModalService } from '../../../../../services/Tools/ModalService';
import { JsonPiece } from '../../../../../models/pieces/JsonPiece';
import {RichLinkPiece, RichLinkTypes} from "../../../../../models/pieces/RichLinkPiece";
import {HttpClient} from "@angular/common/http";
import {containsExpressions, containsVariables, isUrlValid} from "../../../../../urlutils.module";
import {finalize} from "rxjs/operators";
import {StatusResponse} from "../../../../../models/StatusResponse";
import {ErrorPopupComponent} from "../../../../error-popup/error-popup.component";
import {ServerService} from "../../../../../services/server.service";
import {
  AppleInteractiveReceivedAndReplyMessage,
  AppleInteractiveStyles
} from "../../../../../models/pieces/AppleInteractiveReceivedAndReply";

@Component({
  selector: 'app-apple-interactive-received-and-reply',
  templateUrl: './apple-interactive-received-and-reply.component.html',
  styleUrls: ['./apple-interactive-received-and-reply.component.scss']
})
export class AppleInteractiveReceivedAndReplyComponent implements OnInit {

  @Input() receivedMessage : AppleInteractiveReceivedAndReplyMessage;
  @Input() replyMessage : AppleInteractiveReceivedAndReplyMessage;
  @Input() readOnly : boolean = false;

  appleInteractiveStyles = AppleInteractiveStyles;

  constructor( editorService : EditorService, public modalService : ModalService, public serverService: ServerService ) {
  }

  ngOnInit() {
  }
}
