@import "_variables";
@import "_mixins";

.option {
  display: flex;
  flex-direction: row;
  margin-bottom: 10px;
  align-items: center;
  width: 100%;

  .title {
    font-family: $fontFamilyTitles;
    font-weight: bold;
    margin-right: 10px;
    justify-self: center;
    align-self: center;
    text-align: center;
    flex-grow: 0;
    flex-shrink: 0;
  }

  app-input-with-variables {
    flex-grow: 1;
    flex-shrink: 1;
  }

  &.with-info {
    .info {
      margin-left: 3px;
      flex-grow: 0;
      flex-shrink: 0;
    }
  }
}

.options {
  //max-width: 100%;
  //overflow-x: auto;
  margin-bottom: 5px;
  border: 1px solid $cardSeparatorBorderColor;
  border-radius: 7px;
  padding: 5px;

  & > .title {
    font-family: $fontFamilyTitles;
    font-weight: bold;
    font-size: 110%;
    margin-bottom: 3px;
  }
}
