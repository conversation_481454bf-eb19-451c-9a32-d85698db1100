<div class="update-case card">
  <div class="trash" (click)="deleteAction()" *ngIf="!readOnly"
       data-toggle="tooltip" ngbTooltip="{{ 'CARD_DELETE_TEXT' | translate }}" placement="top" container="body" tooltipClass="tooltip-trash">
    <span class="fa fa-trash-alt"></span>
  </div>
  <div class="clone" (click)="cloneAction()" *ngIf="!readOnly"
       data-toggle="tooltip" ngbTooltip="{{ 'CARD_CLONE_TEXT' | translate }}" placement="top" container="body" tooltipClass="tooltip-trash">
    <span class="fa fa-clone"></span>
  </div>
  <div class="export" (click)="exportAction()" *ngIf="!readOnly"
       data-toggle="tooltip" ngbTooltip="{{ 'CARD_EXPORT_TEXT' | translate }}" placement="top" container="body" tooltipClass="tooltip-trash">
    <span class="fa fa-file-export"></span>
  </div>
  <div class="card-title">
    <span class="fa fa-edit"></span> {{ 'CARD_UPDATEPIECE_TITLE' | translate }}
  </div>
  <div class="card-info">
    {{ 'CARD_UPDATEPIECE_INFO' | translate }}
  </div>
  <div class="alert alert-warning" *ngIf="model.Channel === channelTypes.Generic">
    <span class="fa fa-lg fa-exclamation-triangle icon"></span>
    {{ 'UPDATECASE_CLOSECASE_WARNING_GENERIC_CHANEL' | translate }}
  </div>
  <div class="option">
    <span class="title">{{'UPDATECASE_UPDATECASEDATA_TITLE' | translate}}:</span>
    <select class="select" [(ngModel)]="model.updateBusinessData" [disabled]="readOnly">
      <option [ngValue]="updateCaseActionTypes.UpdateAndMerge">{{'UPDATEPROFILE_UPDATEBUSINESSDATA_UPDATEANDMERGE' |
        translate}}</option>
      <option [ngValue]="updateCaseActionTypes.UpdateAndReplace">{{'UPDATEPROFILE_UPDATEBUSINESSDATA_UPDATEANDREPLACE' |
        translate}}</option>
    </select>
  </div>

  <div class="bussiness">
    <div class="title">
      {{ 'UPDATECASE_UPDATEBUSINESSDATA_TITLE' | translate }}
    </div>
    <div class="bussiness-table">
      <div class="header">
        <div>{{ 'UPDATECASE_UPDATEBUSINESSDATA_BUSSINESS_KEY' | translate }}</div>
        <div>{{ 'UPDATECASE_UPDATEBUSINESSDATA_BUSSINESS_VALUE' | translate }}</div>
        <div></div>
      </div>
      <div class="bussiness-row" *ngFor="let businessData of model.businessData let i = index">
        <div class="bussiness-value">
          <app-input-with-variables [placeholder]="'UPDATECASE_UPDATEBUSINESSDATA_BUSSINESS_KEY' | translate"
            [(value)]="businessData.Key" [disabled]="readOnly"
            [ngClass]="{'validation-error': !businessData.isValidKey()}"></app-input-with-variables>
        </div>
        <div class="bussiness-value">
          <app-input-with-variables [placeholder]="'UPDATECASE_UPDATEBUSINESSDATA_BUSSINESS_VALUE' | translate"
            [(value)]="businessData.Value" [disabled]="readOnly"
            [ngClass]="{'validation-error': !businessData.isValidValue()}"></app-input-with-variables>
        </div>
        <div class="trash" *ngIf="!readOnly">
          <div (click)="deleteBussinessData(i)" tooltipClass="tooltip-trash-left" data-toggle="tooltip"
            ngbTooltip="{{ 'CARD_UPDATECASE_BUSSINESS_REMOVE' | translate }}" placement="left">
            <span class="fa fa fa-trash-alt"></span>
          </div>
        </div>
      </div>
    </div>
    <div class="empty" role="alert" *ngIf="model.businessData.length == 0">
      <div class="alert alert-info">
        {{ 'CARD_UPDATEPIECE_INFO_EMPTY' | translate }}
      </div>
    </div>
    <div class="add" (click)="addBussinessData()" *ngIf="!readOnly">
      <span class="fa fa-plus"></span> {{ 'CARD_UPDATECASE_BUSSINESS_ADD' | translate }}
    </div>
  </div>

</div>