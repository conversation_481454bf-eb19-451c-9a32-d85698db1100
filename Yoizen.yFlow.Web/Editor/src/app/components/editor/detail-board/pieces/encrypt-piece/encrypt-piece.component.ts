import { Component, OnInit } from '@angular/core';
import { BasePieceVM } from '../BasePieceVM';
import { EncryptPiece, CharacterReplacement } from 'src/app/models/pieces/EncryptPiece';
import { EditorService } from 'src/app/services/editor.service';
import { ModalService } from 'src/app/services/Tools/ModalService';
import { EncryptModeType, EncryptPaddingType, EncryptType } from 'src/app/models/pieces/AccountLinkingPiece';
import { TypeDefinition } from 'src/app/models/TypeDefinition';
import { VariableDefinition } from 'src/app/models/VariableDefinition';
import { BlockDefinition } from 'src/app/models/BlockDefinition';

@Component({
  selector: 'app-encrypt-piece',
  templateUrl: './encrypt-piece.component.html',
  styleUrls: ['./encrypt-piece.component.scss']
})
export class EncryptPieceComponent extends BasePieceVM implements OnInit {
  model: EncryptPiece;

  encryptTypes = [
    { value: EncryptType.AES, label: 'ENCRYPT_TYPE_AES' },
    { value: EncryptType.TripleDES, label: 'ENCRYPT_TYPE_TRIPLEDES' },
    { value: EncryptType.Rabbit, label: 'ENCRYPT_TYPE_RABBIT' },
  ];

  encryptModeTypes = [
    { value: EncryptModeType.CBC, label: 'ENCRYPT_MODE_CBC' },
    { value: EncryptModeType.CFB, label: 'ENCRYPT_MODE_CFB' },
    { value: EncryptModeType.CTR, label: 'ENCRYPT_MODE_CTR' },
    { value: EncryptModeType.OFB, label: 'ENCRYPT_MODE_OFB' },
    { value: EncryptModeType.ECB, label: 'ENCRYPT_MODE_ECB' },
  ];

  encryptPaddingTypes = [
    { value: EncryptPaddingType.Pkcs7, label: 'ENCRYPT_PADDING_PKCS7' },
    { value: EncryptPaddingType.Iso97971, label: 'ENCRYPT_PADDING_ISO97971' },
    { value: EncryptPaddingType.AnsiX923, label: 'ENCRYPT_PADDING_ANSIX923' },
    { value: EncryptPaddingType.Iso10126, label: 'ENCRYPT_PADDING_ISO10126' },
    { value: EncryptPaddingType.ZeroPadding, label: 'ENCRYPT_PADDING_ZEROPADDING' },
    { value: EncryptPaddingType.NoPadding, label: 'ENCRYPT_PADDING_NOPADDING' },
  ];

  ivLengthTypes = [
    { value: 16, label: '16 bytes (128 bits)' },
    { value: 24, label: '24 bytes (192 bits)' },
    { value: 32, label: '32 bytes (256 bits)' },
  ];

  delimiterTypes = [
    { value: '|', label: '| (Pipe)' },
    { value: null, label: 'ENCRYPT_DELIMITER_NO_USE' }
  ];

  outputVariableFilter = [TypeDefinition.Text, TypeDefinition.Object];
  variableTypes = TypeDefinition;
  EncryptType = EncryptType;

  constructor(public editorService: EditorService, public modalService: ModalService) {
    super(editorService, modalService);
  }

  ngOnInit() {
    this.model = this.context as EncryptPiece;
    if (!this.model.EncryptType) {
      this.model.EncryptType = EncryptType.AES;
    }

    // Initialize custom method properties if not present
    if (this.model.CustomMethod === undefined || this.model.CustomMethod === null) {
      this.model.CustomMethod = false;
    }

    if (!this.model.CharactersToReplace) {
      this.model.CharactersToReplace = [];
    }
  }

  onSelectVariableToEncrypt(variableData: VariableDefinition) {
    this.model.VariableToEncryptId = variableData ? variableData.Id : null;
  }

  onSelectVariableEncrypted(variableData: VariableDefinition) {
    this.model.VariableEncryptedId = variableData ? variableData.Id : null;
  }

  onSelectErrorBlock(blockData: BlockDefinition) {
    this.model.ErrorBlockId = blockData.Id;
  }

  onDeleteErrorBlock() {
    this.model.ErrorBlockId = "-1";
  }

  public addCharacterReplacement(): void {
    if (!this.model.CharactersToReplace) {
      this.model.CharactersToReplace = [];
    }
    this.model.CharactersToReplace.push(new CharacterReplacement());
  }

  public removeCharacterReplacement(index: number): void {
    if (this.model.CharactersToReplace && index >= 0 && index < this.model.CharactersToReplace.length) {
      this.model.CharactersToReplace.splice(index, 1);
    }
  }

  public requiresIV(): boolean {
    return this.model.EncryptType === EncryptType.AES || this.model.EncryptType === EncryptType.TripleDES;
  }

  isValid(): boolean {
    return this.isEncryptKeyValid() &&
      this.isVariableToEncryptValid() &&
      this.isVariableEncryptedValid() &&
      this.isErrorBlockValid() &&
      this.isCustomMethodConfigurationValid();
  }

  isEncryptKeyValid(): boolean {
    if (this.model.CustomMethod) {
      if (!this.model.EncryptKey || this.model.EncryptKey.trim() === '') {
        return false;
      }

      if (this.model.type === "rabbit") {
        // Rabbit encryption does not use a key, so we can skip this validation
        return true;
      }

      if (this.model.EncryptType === EncryptType.AES || this.model.EncryptType === EncryptType.TripleDES) {
        if (this.model.EncryptKey.length < 16) {
          // AES and TripleDES require a key of at least 16 characters
          return false;
        }
      }

      // Validate IV if present and EncryptType is AES or TripleDES
      if (this.model.EncryptType === EncryptType.AES || this.model.EncryptType === EncryptType.TripleDES) {
        if (this.model.EncryptKey && this.model.EncryptKey.trim() !== '') {
          if (!this.model.IvBytesLength) {
            return false; // IV Length must be specified if IV is provided
          }

          if (this.model.EncryptKey.length !== this.model.IvBytesLength) {
            return false; // IV length must match specified IVLength
          }

        } else if (this.model.IvBytesLength) {
          return false; // IV must be provided if IvBytesLength is specified
        }
      }
    }

    return !!this.model.EncryptKey && this.model.EncryptKey.trim().length > 0;
  }

  isVariableToEncryptValid(): boolean {
    const variable = this.editorService.findVariableWithId(this.model.VariableToEncryptId);
    return variable != null && this.outputVariableFilter.indexOf(variable.Type) !== -1;
  }

  isVariableEncryptedValid(): boolean {
    const variable = this.editorService.findVariableWithId(this.model.VariableEncryptedId);
    return variable != null && (
      variable.Type === this.variableTypes.Text ||
      variable.Type === this.variableTypes.Number ||
      variable.Type === this.variableTypes.Decimal ||
      variable.Type === this.variableTypes.Bool ||
      variable.Type === this.variableTypes.Array
    );
  }

  isErrorBlockValid(): boolean {
    const block = this.editorService.findBlockWithId(this.model.ErrorBlockId);
    return this.model.ErrorBlockId !== "-1" && block != null && block.validateCurrentModuleBlock(this.editorService);
  }

  isIvBytesLengthValid(): boolean {
    if (!this.model.CustomMethod || !this.requiresIV()) {
      return true;
    }

    return this.model.IvBytesLength != null &&
      this.model.IvBytesLength > 0 &&
      this.model.IvBytesLength % 4 === 0;
  }

  isCustomMethodConfigurationValid(): boolean {
    if (!this.model.CustomMethod) {
      return true;
    }

    // Validate IV bytes length for algorithms that require it
    if (this.requiresIV() && !this.isIvBytesLengthValid()) {
      return false;
    }

    // Validate character replacement configuration
    if (this.model.ReplaceCharacters) {
      if (!this.model.CharactersToReplace || this.model.CharactersToReplace.length === 0) {
        return false;
      }

      // Validate each character replacement entry
      for (let i = 0; i < this.model.CharactersToReplace.length; i++) {
        const replacement = this.model.CharactersToReplace[i];
        if (!replacement.value || !replacement.replaceWith) {
          return false;
        }
      }
    }

    return true;
  }
}