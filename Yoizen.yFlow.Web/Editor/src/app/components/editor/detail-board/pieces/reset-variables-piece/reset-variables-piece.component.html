<div class="reset card" [ngClass]="{'invalid-piece': !model.isValid(editorService)}">
  <div class="trash" (click)="deleteAction()" *ngIf="!readOnly"
       data-toggle="tooltip" ngbTooltip="{{ 'CARD_DELETE_TEXT' | translate }}" placement="top" container="body" tooltipClass="tooltip-trash">
    <span class="fa fa-trash-alt"></span>
  </div>
  <div class="clone" (click)="cloneAction()" *ngIf="!readOnly"
       data-toggle="tooltip" ngbTooltip="{{ 'CARD_CLONE_TEXT' | translate }}" placement="top" container="body" tooltipClass="tooltip-trash">
    <span class="fa fa-clone"></span>
  </div>
  <div class="export" (click)="exportAction()" *ngIf="!readOnly"
       data-toggle="tooltip" ngbTooltip="{{ 'CARD_EXPORT_TEXT' | translate }}" placement="top" container="body" tooltipClass="tooltip-trash">
    <span class="fa fa-file-export"></span>
  </div>
  <div class="card-title">
    <span class="fa fa-tools"></span> {{ 'CARD_RESETVARIABLES_TITLE' | translate }}
  </div>
  <div class="variables-selector-container">
    <div class="empty" *ngIf="Variables.length === 0" role="alert">
      <div class="alert alert-info">
        {{ 'VARIABLES_EMPTY' | translate }}
      </div>
    </div>
    <div class="variables" *ngIf="Variables.length > 0">
      <div class="variables-container">
        <div class="variables-table">
          <div class="variables-table-header">
            <div></div>
            <div>{{'NAME' | translate}}</div>
            <div>{{'VARIABLE_DEFAULT_VALUE' | translate}}</div>
          </div>
          <div class="variables-table-row" *ngFor="let variable of Variables">
            <div class="variables-table-cell">
              <input type="checkbox"
                     [checked]="isVariableSelected(variable)"
                     (change)="changeVariable(variable)" />
            </div>
            <div class="variables-table-cell"><span class="variable-name">{{ variable.Name }}</span><span class="variable-type">{{ getVariableType(variable) | translate }}</span></div>
            <div class="variables-table-cell"><span class="variable-defaultvalue">{{ variable.DefaultValue | translate }}</span></div>
          </div>
        </div>
      </div>
      <div class="button-area">
        <div class="action-button" (click)="selectAll()">{{ 'VARIABLES_SELECT_ALL' | translate}}</div>
        <div class="action-button" (click)="unselectAll()">{{ 'VARIABLES_UNSELECT_ALL' | translate}}</div>
      </div>
    </div>
  </div>
</div>
