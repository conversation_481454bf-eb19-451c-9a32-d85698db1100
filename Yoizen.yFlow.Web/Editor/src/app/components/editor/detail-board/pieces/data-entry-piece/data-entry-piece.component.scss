@import '_variables';
@import '_mixins';

.dataEntry {
  background-color: #fff;
  padding: 10px;
  min-width: 830px;

  .destvariable, .destvariableparseformat, .regex, .errormessage, .retries, .next, .command, .cognitivity, .jumping  {
    display: flex;
    flex-direction: row;
    margin-bottom: 10px;
    width: 100%;

    .title {
      font-family: $fontFamilyTitles;
      font-weight: bold;
      margin-right: 10px;
      justify-self: center;
      align-self: center;
      text-align: center;
      flex-grow: 0;
      flex-shrink: 0;
    }
  }

  .validation {
    .validation-table {
      display: table;
      width: 100%;

      .header {
        display: table-header-group;
        font-family: $fontFamilyTitles;
        font-weight: bold;

        &>div {
          display: table-cell;
          vertical-align: middle;
          border-bottom: 1px solid $sidebarBorderColor;
          padding-left: 5px;
          padding-right: 5px;

          &.center {
            text-align: center;
            width: 180px;
          }
        }
      }

      .row {
        display: table-row;
        height: 40px;

        .center {
          .circle-button {
            cursor: pointer;
          }
        }

        /*&:hover {
          background: lighten($version-highlight, 10%);
        }*/

        .readonly-button {
          cursor: not-allowed !important;
          filter: brightness(65%);
        }

        &>div {
          display: table-cell;
          vertical-align: middle;
          border-bottom: 1px solid $sidebarBorderColor;
          padding-left: 5px;
          padding-right: 5px;

          &.center {
            text-align: center;
            width: 130px;
          }
        }

        &:last-child {
          &>div {
            border-bottom: 1px none $sidebarBorderColor;
          }
        }
      }
    }
  }

  .validationerror {
    margin-bottom: 10px;

    .messages {
      display: flex;
      flex-direction: row;

      app-text-list-error-message {
        width: 218px;
      }

      .addText {
        @include addButton;
        width: 40px;
        flex-grow: 0;
        flex-shrink: 0;
      }
    }

    .errormessage {
      .inputerrormessage {
        flex-grow: 1;
        flex-shrink: 1;
      }
    }

    .retries {
      .tries {
        width: 80px;
      }
    }
  }

  .regex, .destvariableparseformat {
    .input {
      flex-grow: 1;
      flex-shrink: 1;
    }
  }

  .validationerror, .commands, .cognitivities, .validation {
    border-bottom: 1px solid $gray;
    margin-bottom: 10px;
  }

  .regex-container {
    display: flex;
    width: 100%;

    .input {
      padding-right: 40px;
    }

    .icon {
      position: relative;
      min-width: 30px;
      line-height: 30px;
      text-align: center;
      top: 0;
      left: -30px;
    }

    .fa-question-circle {
      color: $flowColor;
      cursor: pointer;
      &:hover {
        color: lighten($flowColor, 15%);
      }
    }
  }

  .template-container {
    display: flex;
    width: 100%;

    .input {
      padding-right: 70px;
    }

    .icon {
      position: relative;
      min-width: 30px;
      line-height: 30px;
      text-align: center;
      top: 0;
      left: -60px;
    }

    .fa-trash {
      color: red;
      cursor: pointer;
      &:hover {
        color: lighten(red, 15%);
      }
    }

    .fa-edit {
      color: green;
      cursor: pointer;
      &:hover {
        color: lighten(green, 15%);
      }
    }
  }

  .regex-help {
      text-align: center;
      text-transform: uppercase;
      padding: 10px 0;
      cursor: pointer;
      font-size: 12px;
      position: relative;
      font-weight: normal;
      color: $linkActionColor;
      border-top: 1px solid $gray;
      border-bottom: 1px solid $gray;
      margin-bottom: 10px;

      &:hover {
        color: lighten($linkActionColor, 10%);
      }

      span {
        display: inline-block;
        margin-right: 10px;
        position: absolute;
        left: -10px;
        top: 13px;
        margin-left: 30px;
      }
  }

  .add-condition {
    text-align: center;
    text-transform: uppercase;
    padding: 10px 0;
    cursor: pointer;
    font-size: 12px;
    position: relative;
    font-weight: normal;
    color: $linkActionColor;
    border-top: 1px solid $gray;
    border-bottom: 1px solid $gray;
    margin-bottom: 10px;

    &:hover {
      color: lighten($linkActionColor, 10%);
    }

    span {
      display: inline-block;
      margin-right: 10px;
      position: absolute;
      left: -10px;
      top: 13px;
      margin-left: 30px;
    }
  }

  .condition {
    position: relative;
    border-top: 1px solid $gray;
    border-bottom: 1px solid $gray;
    padding-right: 20px;

    .info {
      padding: 5px 0;
      justify-content: flex-start;
      align-content: center;
      display: flex;

      .value {
        flex-grow: 1;
        flex-shrink: 1;
        padding-left: 10px;
      }
    }

    .trash {
      @include trash;
      position: absolute;
      right: -13px;
      top: 50%;
      transform: translateY(-50%);

      &:hover {
        color: #555;
      }
    }

    &:hover {
      .trash {
        opacity: 1;
      }
    }
  }

  .mark-as-pending-reply {
    .alert-info {
      color: #0c5460;
      background-color: #d1ecf1;
      border-color: #bee5eb;
      margin-top: 5px;
      margin-bottom: 5px;
    }

    .option {
      display: flex;
      flex-direction: row;
      margin-bottom: 10px;
      width: 100%;

      .title {
        font-family: $fontFamilyTitles;
        font-weight: bold;
        margin-right: 10px;
        justify-self: center;
        align-self: center;
        text-align: center;
        flex-grow: 0;
        flex-shrink: 0;
      }
    }
  }

  .mark-as-pending-reply {
    border-top: 1px solid $gray;

    app-input-with-variables {
      flex-grow: 1;
      flex-shrink: 1;
    }
  }
}
