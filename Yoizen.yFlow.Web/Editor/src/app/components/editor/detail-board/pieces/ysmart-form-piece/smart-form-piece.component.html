<div class="rest card" [ngClass]="{'invalid-piece': !model.isValid(editorService)}">
  <div class="trash" (click)="deleteAction()" *ngIf="!readOnly"
       data-toggle="tooltip" ngbTooltip="{{ 'CARD_DELETE_TEXT' | translate }}" placement="top" container="body" tooltipClass="tooltip-trash">
    <span class="fa fa-trash-alt"></span>
  </div>
  <div class="clone" (click)="cloneAction()" *ngIf="!readOnly"
       data-toggle="tooltip" ngbTooltip="{{ 'CARD_CLONE_TEXT' | translate }}" placement="top" container="body" tooltipClass="tooltip-trash">
    <span class="fa fa-clone"></span>
  </div>
  <div class="export" (click)="exportAction()" *ngIf="!readOnly"
       data-toggle="tooltip" ngbTooltip="{{ 'CARD_EXPORT_TEXT' | translate }}" placement="top" container="body" tooltipClass="tooltip-trash">
    <span class="fa fa-file-export"></span>
  </div>
  <div class="card-title">
    <span class="fas fa-tasks"></span> {{ 'CARD_SMART_FORM_TITLE' | translate }}
  </div>
  <div class="smart-form">
    <span class="title">{{ 'SMART_FORM_SELECT' | translate }}:</span>
    <select #formDropdown class="select" name="" id="" [(ngModel)]="model.FormId"
            (ngModelChange)="onSelectForm($event)"
            [disabled]="readOnly"
            [ngClass]="{'invalid-piece': !model.isFormIdValid()}">
      <option *ngFor="let form of availableForms" [ngValue]="form.Id">{{form.Name}}
      </option>
    </select>
  </div>
  <div class="varOutput" *ngIf="model.isFormIdValid()">
    <label class="title">{{ 'OUTPUTS' | translate}}</label>
    <div class="fields" *ngIf="model.Outputs !== null && model.Outputs.length > 0">
      <div class="header">
        <div>{{ 'OUTPUTS_FIELD' | translate }}</div>
        <div>{{ 'OUTPUTS_WILLASSIGNTO' | translate }}</div>
      </div>
      <div class="field-row" *ngFor="let output of model.Outputs">
        <div class="field-cell">
          <span class="variable-name">{{ output.formEntityName }}</span><span class="variable-type">{{ getVariableType(output.type) | translate }}</span>
        </div>
        <div class="field-cell">
          <app-variable-selector-input [VariableData]="getVariableRef(output.variableRefId)"
                                       (setVariable)="setVariableOnOutput(output, $event)"
                                       [typeFilters]="[output.type]"
                                       [validator]="OutputValidator"
                                       [canSelectConstants]="false"
                                       [readOnly]="readOnly"
                                       class="input-variable-area"></app-variable-selector-input>
        </div>
      </div>
    </div>
  </div>

  <div class="next" *ngIf="model.isFormIdValid()">
    <div class="retries">
      <span class="title">{{ 'DATAENTRY_RETRIES' | translate }}:</span>
      <input class="input tries" type="number" placeholder="0" min="0"
             [disabled]="readOnly"
             [(ngModel)]="model.TryLimit" [ngClass]="{'invalid-input': !model.isTryLimitValid()}">
    </div>
    <span class="title">{{ 'ON_ERROR_GO_TO' | translate }}:</span>
    <app-block-picker class="input" [blockId]="model.ErrorBlockId"
                      (onSelectNewBlock)="onSelectBlock($event)"
                      (onDeleteBlock)="onDeleteBlock($event)"
                      [readOnly]="readOnly"
                      [isInvalid]="!model.isErrorBlockValid(editorService)"></app-block-picker>
  </div>

  <div class="next" *ngIf="model.isFormIdValid()">
    <span class="title">{{ 'ON_EXIT_GO_TO' | translate }}:</span>
    <app-block-picker class="input" [blockId]="model.ExitBlockId"
                      (onSelectNewBlock)="onSelectExitBlock($event)"
                      (onDeleteBlock)="onDeleteExitBlock($event)"
                      [readOnly]="readOnly"
                      [isInvalid]="!model.isExitBlockValid(editorService)"></app-block-picker>
  </div>
</div>
