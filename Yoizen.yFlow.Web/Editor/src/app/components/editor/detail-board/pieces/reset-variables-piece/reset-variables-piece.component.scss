@import "_variables";
@import "_mixins";

.reset {
  background-color: #fff;
  min-width: 500px;

  .variables-selector-container {
    .variables-container {
      max-height: 300px;
      overflow-y: auto;

      .variables-table {
        display: table;
        width: 100%;

        .variables-table-header, .variables-table-row {
          height: 30px;
        }

        .variables-table-header {
          display: table-row;
          font-family: $fontFamilyTitles;

          div {
            display: table-cell;
            vertical-align: middle;
            border-bottom: 1px solid $sidebarBorderColor;
          }
        }

        .variables-table-row {
          display: table-row;

          .variables-table-cell {
            display: table-cell;
            vertical-align: middle;
            border-bottom: 1px solid $sidebarBorderColor;

            .variable-name {
              @include variable-name;
            }

            .variable-type {
              @include variable-type;
            }
          }
        }
      }
    }

    .button-area {
      flex-grow: 0;
      flex-shrink: 0;
      display: flex;
      flex-direction: row;
      justify-content: flex-start;
      align-content: flex-end;
      margin-top: 5px;
      margin-bottom: 5px;

      .action-button {
        margin-left: 0;
      }
    }
  }
}
