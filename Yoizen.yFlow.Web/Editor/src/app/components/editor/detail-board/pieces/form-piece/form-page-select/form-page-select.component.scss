@import "_variables";
@import "_mixins";

.option {
  display: flex;
  flex-direction: row;
  margin-bottom: 10px;
  align-items: center;
  width: 100%;

  .title {
    font-family: $fontFamilyTitles;
    font-weight: bold;
    margin-right: 10px;
    justify-self: center;
    align-self: center;
    text-align: center;
    flex-grow: 0;
    flex-shrink: 0;
  }

  app-input-with-variables {
    flex-grow: 1;
    flex-shrink: 1;
  }

  &.with-info {
    .info {
      margin-left: 3px;
      flex-grow: 0;
      flex-shrink: 0;
    }
  }
}

.items {
  //max-width: 100%;
  //overflow-x: auto;
  margin-bottom: 5px;

  & > .title {
    font-family: $fontFamilyTitles;
    font-weight: bold;
    font-size: 110%;
    margin-bottom: 3px;
  }

  .item {
    border: solid 1px #ebebeb;
    padding: 10px;
    display: flex;
    flex-direction: row;
    position: relative;

    .trash {
      @include trash;
      position: absolute;
      right: -13px;
      top: -13px;
      cursor: pointer;

      &:hover {
        color: #555;
      }
    }

    &:hover {
      .trash {
        @include trashOver;
      }
    }

    &:first-child {
      border-bottom-style: none;
    }

    .icon {
      width: 30px;
      flex-grow: 0;
      flex-shrink: 0;
      display: flex;
      flex-direction: row;
      align-items: center;
      justify-content: flex-start;
    }

    .data {
      flex-grow: 1;
      flex-shrink: 1;
      display: flex;
      flex-direction: column;
    }
  }

  .items-add {
    @include addPieceButton;
    border: solid 1px #ebebeb;
    border-top-style: none;
  }
}
