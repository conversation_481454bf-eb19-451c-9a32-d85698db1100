import { Component, OnInit } from '@angular/core';
import { BaseDynamicComponent } from '../../../../utils/component-holder/BaseDynamicComponent';
import { BasePieceVM } from '../BasePieceVM';
import { EditorService } from '../../../../../services/editor.service';
import { ModalService } from '../../../../../services/Tools/ModalService';
import { JsonPiece } from '../../../../../models/pieces/JsonPiece';
import {RichLinkPiece, RichLinkTypes} from "../../../../../models/pieces/RichLinkPiece";
import {HttpClient} from "@angular/common/http";
import {containsExpressions, containsVariables, isUrlValid} from "../../../../../urlutils.module";
import {finalize} from "rxjs/operators";
import {StatusResponse} from "../../../../../models/StatusResponse";
import {ErrorPopupComponent} from "../../../../error-popup/error-popup.component";
import {ServerService} from "../../../../../services/server.service";
import {TimePickerPiece} from "../../../../../models/pieces/TimePickerPiece";
import {VariableDefinition} from "../../../../../models/VariableDefinition";
import {TypeDefinition} from "../../../../../models/TypeDefinition";
import {BlockDefinition} from "../../../../../models/BlockDefinition";

@Component({
  selector: 'app-time-picker-piece',
  templateUrl: './time-picker-piece.component.html',
  styleUrls: ['./time-picker-piece.component.scss']
})
export class TimePickerPieceComponent extends BasePieceVM implements OnInit {
  model : TimePickerPiece;
  richLinkTypes = RichLinkTypes;
  loading: boolean = false;
  assignToVariableFilter = [TypeDefinition.Text];

  constructor( public editorService : EditorService, public modalService : ModalService, public serverService: ServerService ) {
    super(editorService, modalService);
  }

  ngOnInit() {
    this.model = this.context as TimePickerPiece;
  }

  get assignToVariableData(): VariableDefinition {
    let variable = this.editorService.getVariableWithId(this.model.assignToVariableId);
    if (typeof(variable) === 'undefined') {
      variable = null;
    }
    return variable;
  }

  onSelectBlock(blockData : BlockDefinition) {
    this.model.blockId = blockData.Id;
  }

  onDeleteBlock() {
    this.model.blockId = null;
  }

  setAssignToVariable(variable: VariableDefinition) {
    if (typeof(variable) !== 'undefined' &&
      variable !== null) {
      this.model.assignToVariableId = variable.Id;
    }
    else {
      this.model.assignToVariableId = null;
    }
  }
}
