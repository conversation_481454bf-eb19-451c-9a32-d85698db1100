@import "_variables";

.storemessage {
	width: 500px;

	textarea {
		border-radius: 10px;
		width: 100%;
		margin-bottom: 10px;
		font-weight: normal;
	}

  .contents {
    margin-top: 5px;
  }

  .next {
	input{
		width: 100%;
	}
	.title {
		font-family: $fontFamilyTitles;
		font-weight: bold;
		margin-right: 10px;
		justify-self: center;
		align-self: center;
		text-align: center;
		flex-grow: 0;
		flex-shrink: 0;
	  }
	}

	
	.structured-data {
		margin-top: 10px;
	
		& > .title {
		  font-family: $fontFamilyTitles;
		  font-weight: bold;
		  font-size: 120%;
		  margin-bottom: 5px;
		}
	
		.structured-data-table {
		  display: table;
		  width: 100%;
	
		  .header {
			display: table-header-group;
			font-family: $fontFamilyTitles;
	
			& > div {
			  display: table-cell;
			  vertical-align: middle;
			  border-bottom: 1px solid $sidebarBorderColor;
			}
		  }
	
		  .structured-data-row {
			display: table-row;
			width: 100%;
			margin-top: 5px;
			height: 40px;
			min-height: 40px;
	
			& > div {
			  display: table-cell;
			  vertical-align: middle;
			  border-bottom: 1px solid $sidebarBorderColor;
			  padding-left: 3px;
			  padding-right: 3px;
			}
	
			.structured-data-value {
			  width: 200px;
			}

			.structured-data-info {
				margin: 0 16px;
				font-size: 16px;
				text-align: center;
				z-index: 1400;
				cursor: pointer;
			
				& > span {
				  line-height: 24px;
				}
			
				&:hover {
				  color: rgb(6, 3, 167);
				}
			  }
		  }
		}
	  }
}
