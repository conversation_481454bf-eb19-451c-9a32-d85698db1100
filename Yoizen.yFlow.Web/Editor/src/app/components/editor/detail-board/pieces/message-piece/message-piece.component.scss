@import "_variables";
@import "_mixins";

.text {
  min-width: 300px;
  background: #fff;
  position: relative;

  .more-buttons-than-allowed {
    display: none;
  }

  &.buttons-warning {
    .more-buttons-than-allowed {
      display: block;
    }
  }

  .max-length {
    display: none;

    .messenger, .twitter, .whatsapp, .telegram {
      display: none;
    }
  }

  .messages {
    display: flex;
    flex-direction: row;

    app-text-list-message {
      width: 218px;
    }

    .addText {
      @include addButton;
      width: 40px;
      flex-grow: 0;
      flex-shrink: 0;
    }
  }

  &.messenger {
    .max-length {
      display: block;

      .messenger {
        display: block;
      }
    }
  }

  &.twitter {
    .max-length {
      display: block;

      .twitter {
        display: block;
      }
    }
  }

  &.whatsapp {
    .max-length {
      display: block;

      .whatsapp {
        display: block;
      }
    }

    .messages {
      border-bottom-style: none;
    }
  }

  .buttons {
    border-top: solid 1px #ebebeb;
  }

  .quick, .addButton {
    @include addPieceButton;
  }

  .addName, .addButton {
    border-bottom: solid 1px #ebebeb;
  }
  .addName {
    padding: 10px;
  }

  textarea {
    width: 100%;
    background: transparent;
    white-space: pre-wrap;
    height: 33px;
    font-weight: normal;
    border-radius: 10px;
    border: 1px solid #9a9a9a;
    padding-left: 10px;
  }
}
