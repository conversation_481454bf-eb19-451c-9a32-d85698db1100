<div class="signature-pad card" [ngClass]="{'invalid-piece': !model.isValid(editorService) }">
  <div class="trash" (click)="deleteAction()" *ngIf="!readOnly"
       data-toggle="tooltip" ngbTooltip="{{ 'CARD_DELETE_TEXT' | translate }}" placement="top" container="body" tooltipClass="tooltip-trash">
    <span class="fa fa-trash-alt"></span>
  </div>
  <div class="clone" (click)="cloneAction()" *ngIf="!readOnly"
       data-toggle="tooltip" ngbTooltip="{{ 'CARD_CLONE_TEXT' | translate }}" placement="top" container="body" tooltipClass="tooltip-trash">
    <span class="fa fa-clone"></span>
  </div>
  <div class="export" (click)="exportAction()" *ngIf="!readOnly"
       data-toggle="tooltip" ngbTooltip="{{ 'CARD_EXPORT_TEXT' | translate }}" placement="top" container="body" tooltipClass="tooltip-trash">
    <span class="fa fa-file-export"></span>
  </div>
  <div class="card-title">
    <span class="fa fa-file-signature icon"></span> {{ 'CARD_SIGNATURE_PAD_TITLE' | translate }}
  </div>
  <div class="card-info">
    <span [innerHTML]="'CARD_SIGNATURE_PAD_INFO' | translate"></span>
  </div>

  <div class="next block-picker">
    <div class="title">{{ 'PAYMENT_GATEWAY_BLOCK_SUCCESS' | translate }}</div>
    <div [ngClass]="{'hide': hasSuccessBlock()}" *ngIf="!readOnly">
      <input class="input" type="text" placeholder="{{'REDIRECT_TO_BLOCK_PLACEHOLDER' | translate}}"
             [(ngModel)]="searchSuccessBlockString" LimitLength (focusin)="onSuccessInputFocusIn()" (focusout)="onSuccessInputFocusOut()">
      <div #successBlockPicker class="hide block-selection-anchor">
        <app-block-selector class="select-block" [BlockName]=searchSuccessBlockString
                            (onSelectBlock)="onSuccessBlockSelect($event)"></app-block-selector>
      </div>
    </div>
    <span class="block-selected" [ngClass]="{'hide': !hasSuccessBlock()}">
        <span class="block-display">{{SuccessBlockData?.Name}}</span>
        <span class="fa fa-unlink trash" (click)="deleteSuccessBlock()" *ngIf="!readOnly"
              data-toggle="tooltip" ngbTooltip="{{ 'BLOCK_REMOVESELECTED' | translate }}"
              placement="top" container="body" tooltipClass="tooltip-trash"></span>
      </span>
  </div>

  <div class="name">
    <span class="title">{{ 'SIGNATURE_ICO_URL' | translate}}:</span>
    <app-input-with-variables
      class="input-variable-area"
      [placeholder]="'SIGNATURE_ICO_URL' | translate"
      [(value)]="model.IcoUrl"
      [validator]="model.isIcoUrlValid.bind(model)"
      [disabled]="readOnly"
      [wideInput]="true">
    </app-input-with-variables>
  </div>

  <div class="name">
    <span class="title">{{ 'SIGNATURE_SIGNATURE_TITLE' | translate}}:</span>
    <app-input-with-variables
      class="input-variable-area"
      [placeholder]="'SIGNATURE_SIGNATURE_TITLE' | translate"
      [(value)]="model.SignatureTitle"
      [disabled]="readOnly"
      [wideInput]="true">
    </app-input-with-variables>
  </div>

  <div class="name">
    <span class="title">{{ 'SIGNATURE_PAGE_TITLE' | translate}}:</span>
    <app-input-with-variables
      class="input-variable-area"
      [placeholder]="'SIGNATURE_PAGE_TITLE' | translate"
      [(value)]="model.PageTitle"
      [disabled]="readOnly"
      [wideInput]="true">
    </app-input-with-variables>
  </div>

  <div class="name">
    <span class="title">{{ 'SIGNATURE_SAVE_BUTTON_TITLE' | translate}}:</span>
    <app-input-with-variables
      class="input-variable-area"
      [placeholder]="'SIGNATURE_SAVE_BUTTON_TITLE' | translate"
      [(value)]="model.SaveButton"
      [disabled]="readOnly"
      [wideInput]="true">
    </app-input-with-variables>
  </div>

  <div class="name">
    <span class="title">{{ 'SIGNATURE_CLEAR_BUTTON_TITLE' | translate}}:</span>
    <app-input-with-variables
      class="input-variable-area"
      [placeholder]="'SIGNATURE_CLEAR_BUTTON_TITLE' | translate"
      [(value)]="model.ClearButton"
      [disabled]="readOnly"
      [wideInput]="true">
    </app-input-with-variables>
  </div>

  <div class="name">
    <span class="title">{{ 'SIGNATURE_COMPANY_TITLE' | translate}}:</span>
    <app-input-with-variables
      class="input-variable-area"
      [placeholder]="'SIGNATURE_COMPANY_TITLE' | translate"
      [(value)]="model.Company"
      [disabled]="readOnly"
      [wideInput]="true">
    </app-input-with-variables>
  </div>

</div>
