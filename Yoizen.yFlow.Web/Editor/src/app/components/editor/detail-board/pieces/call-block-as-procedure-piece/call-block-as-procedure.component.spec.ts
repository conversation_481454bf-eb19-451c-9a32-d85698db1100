import { async, ComponentFixture, TestBed } from '@angular/core/testing';

import { CallBlockAsProcedureComponent } from './call-block-as-procedure.component';

describe('CallBlockAsProcedureComponent', () => {
  let component: CallBlockAsProcedureComponent;
  let fixture: ComponentFixture<CallBlockAsProcedureComponent>;

  beforeEach(async(() => {
    TestBed.configureTestingModule({
      declarations: [ CallBlockAsProcedureComponent ]
    })
    .compileComponents();
  }));

  beforeEach(() => {
    fixture = TestBed.createComponent(CallBlockAsProcedureComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });
});
