<div class="app-entity">
  <div class="next entity">
    <div [ngClass]="{'hide': entity != null && entity.name != ''}" *ngIf="!readOnly">
      <input class="input" [ngClass]="{'invalid-state': !isValid()}" type="text"
             autocomplete="nomecompletesnadaaca"
             placeholder="{{'ENTITY' | translate}}" [(ngModel)]="searchEntityString" LimitLength
             (focusin)="onInputFocusIn()" (focusout)="onInputFocusOut()">
      <div #entityPicker class="hide block-selection-anchor">
        <div class="selector-container">
          <div class="scroll-area">
            <div class="entity-container" *ngFor="let ent of Entities">
              <div class="entity-name" (click)="selectEntity(ent); $event.stopPropagation();">{{ent.name}}</div>
            </div>
            <div class="entity-container" *ngIf="EmptyEntitySet">
              <div class="empty-entity-set">{{'EMPTY_ENTITY_SET' | translate}}</div>
            </div>
          </div>
        </div>
      </div>
    </div>
    <div class="entity-selected" [ngClass]="{'hide': entity == null || entity.name == ''}">
      <div class="entity-display">{{entity?.name}}</div>
      <div class="fa fa-unlink trash" (click)="deleteEntity()" *ngIf="!readOnly"
           data-toggle="tooltip" ngbTooltip="{{ 'ENTITY_REMOVESELECTED' | translate }}"
           placement="top" container="body" tooltipClass="tooltip-trash"></div>
    </div>
  </div>
</div>
