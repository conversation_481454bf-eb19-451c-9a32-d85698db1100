<div class="validate-dni card" [ngClass]="{'invalid-piece': !model.isValid(editorService)}">
  <div class="trash" (click)="deleteAction()" *ngIf="!readOnly"
       data-toggle="tooltip" ngbTooltip="{{ 'CARD_DELETE_TEXT' | translate }}" placement="top" container="body" tooltipClass="tooltip-trash">
    <span class="fa fa-trash-alt"></span>
  </div>
  <div class="clone" (click)="cloneAction()" *ngIf="!readOnly"
       data-toggle="tooltip" ngbTooltip="{{ 'CARD_CLONE_TEXT' | translate }}" placement="top" container="body" tooltipClass="tooltip-trash">
    <span class="fa fa-clone"></span>
  </div>
  <div class="export" (click)="exportAction()" *ngIf="!readOnly"
       data-toggle="tooltip" ngbTooltip="{{ 'CARD_EXPORT_TEXT' | translate }}" placement="top" container="body" tooltipClass="tooltip-trash">
    <span class="fa fa-file-export"></span>
  </div>
  <div class="card-title">
    <span class="fa fa-id-card icon"></span> {{ 'CARD_VALIDATE_FRONTAL_DNI_TITLE' | translate }}
  </div>
  <div class="card-info">
    <span [innerHTML]="'CARD_VALIDATE_FRONTAL_DNI_INFO' | translate"></span>
  </div>
  <div class="validationerror">
    <div class="errormessage">
      <span class="title">{{ 'MULTIMEDIAENTRY_VALIDATIONERROR_MESSAGES' | translate }}:</span>
      <div class="messages">
        <app-text-list-error-message *ngFor="let text of model?.ErrorMessages let i = index" [(Text)]="text.text"
          [Index]="i" [CanDelete]="!readOnly && model?.ErrorMessages.length > 1" [readOnly]="readOnly"
          (onDelete)="deleteErrorMessage($event)"></app-text-list-error-message>
        <div class="addText" [ngClass]="{'hide': !canAddTextOptions() }" *ngIf="!readOnly">
          <div (click)="addNewText()" data-toggle="tooltip" ngbTooltip="{{ 'CARD_MESSAGE_ADD_TEXT' | translate }}"
            placement="right" tooltipClass="tooltip-add">
            <span class="fa fa-plus"></span>
          </div>
        </div>
      </div>
    </div>
    <div class="retries">
      <span class="title">{{ 'MULTIMEDIAENTRY_RETRIES' | translate }}:</span>
      <input class="input tries" type="number" placeholder="0" min="0" [disabled]="readOnly"
        [(ngModel)]="model.TryLimit" [ngClass]="{'invalid-input': !model.isTryLimitValid()}">
    </div>
  </div>
  <div class="next">
    <span class="title">{{'ON_ERROR_GO_TO' | translate}}:</span>
    <app-block-picker class="input" [blockId]="model.ErrorBlockId" (onSelectNewBlock)="onSelectBlock($event)"
      (onDeleteBlock)="onDeleteBlock($event)" [readOnly]="readOnly"
      [isInvalid]="!model.isErrorBlockValid(editorService)"></app-block-picker>
  </div>
</div>
