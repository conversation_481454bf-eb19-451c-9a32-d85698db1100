import { Component, OnInit } from '@angular/core';
import { BasePieceVM } from '../BasePieceVM';
import { EditorService } from 'src/app/services/editor.service';
import { ModalService } from 'src/app/services/Tools/ModalService';
import { GetMessageEntitiesPiece } from 'src/app/models/pieces/GetMessageEntitiesPiece';
import { Entity } from 'src/app/models/cognitivity/Entity';

@Component({
  selector: 'app-get-message-entities-piece',
  templateUrl: './get-message-entities-piece.component.html',
  styleUrls: ['./get-message-entities-piece.component.scss']
})
export class GetMessageEntitiesPieceComponent extends BasePieceVM implements OnInit {
  model: GetMessageEntitiesPiece;
  entities: Entity[] = [];

  constructor(editorService: EditorService, public modalService: ModalService) {
    super(editorService, modalService);
  }

  ngOnInit() {
    this.model = this.context as GetMessageEntitiesPiece;
  }

  ElementAdded(entity: Entity) {
    this.entities.push(entity)
    this.model.Entities.push(entity)
  }

  ElementDeleted(entity: Entity) {
    let index = this.model.Entities.indexOf(entity);
    if (index > -1) {
      this.model.Entities.splice(index, 1);
    }
  }

}
