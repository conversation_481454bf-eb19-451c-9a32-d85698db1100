import {Component, ElementRef, EventEmitter, Input, OnInit, Output, ViewChild} from '@angular/core';
import {ButtonPiece, ButtonType} from '../../../../../models/pieces/ButtonPiece';
import {BlockDefinition} from '../../../../../models/BlockDefinition';
import {EditorService} from '../../../../../services/editor.service';
import {isStringValid, isUrlValid} from '../../../../../urlutils.module'
import {PersistentMenuEntry} from '../../../../../models/PersistentMenuEntry';
import {WebViewSizesDefinitions} from "../../../../../models/WebviewSizes";
import {OperatorDefinitions} from 'src/app/models/OperatorType';
import {VariableDefinition} from 'src/app/models/VariableDefinition';
import {ChannelTypes} from "../../../../../models/ChannelType";

const regexEmoji = /(\u00a9|\u00ae|[\u2000-\u3300]|\ud83c[\ud000-\udfff]|\ud83d[\ud000-\udfff]|\ud83e[\ud000-\udfff])/gi;

@Component({
  selector: 'app-add-button-piece',
  templateUrl: './add-button-piece.component.html',
  styleUrls: ['./add-button-piece.component.scss'],
})
export class AddButtonPieceComponent implements OnInit {

  @Output() onClose: EventEmitter<string> = new EventEmitter();
  @Input() Model: ButtonPiece;
  @Input() AllowedButtonTypes : ButtonType[] = null;
  @Input() DisplayCreateMenuOption: boolean = false;
  @Input() IsPersistentMenuButton: boolean = false;
  @Input() IsIceBreaker: boolean = false;
  @Input() readOnly: boolean = false;
  @Input() customVariableList : Array<VariableDefinition> = null;
  @Input() EmojisAllowed : boolean = true;
  @Input() MaxLength : number = 524288;

  hideBlockSelector = true;
  ActiveIdString: string;
  SearchBlockString: string = "";
  hideWebViewSize: boolean = true;
  joinCustomVariablesList: boolean = false;
  isAuthAlternativeMethodAllowed: boolean = false;
  allowedToSetValueToVariable: boolean = true;

  BlockData: BlockDefinition;

  subMenu: PersistentMenuEntry;

  get currentVariable(): VariableDefinition {
    return this.editorService.getVariableWithId(this.Model.AssignToVariableId);
  }

  @ViewChild('tabset', { static: false }) tabSet;

  @ViewChild('nameInput', { static: false }) NameInput: ElementRef;

  constructor(public editorService: EditorService) {
  }

  ngOnInit() {
    this.ActiveIdString = this.getActiveTab();
    this.BlockData = this.editorService.findBlockWithId(this.Model.BlockID);

    if (typeof(this.AllowedButtonTypes) === 'undefined') {
      this.AllowedButtonTypes = null;
    }

    let flow = this.editorService.getCurrentFlow();
    if (this.AllowedButtonTypes === null) {
      switch (flow.channel) {
        case ChannelTypes.FacebookMessenger:
        case ChannelTypes.Instagram:
          this.AllowedButtonTypes = [ ButtonType.Redirect, ButtonType.Auth, ButtonType.Url, ButtonType.Unlink, ButtonType.Location ];
          this.hideWebViewSize = false;
          this.isAuthAlternativeMethodAllowed = true;
          break;
        case ChannelTypes.Chat:
          this.AllowedButtonTypes = [ ButtonType.Redirect, ButtonType.Url, ButtonType.Location, ButtonType.LocationWithMap ];
          break;
        case ChannelTypes.Twitter:
          this.AllowedButtonTypes = [ ButtonType.Url ];
          this.Model.Type = ButtonType.Url;
          break;
        case ChannelTypes.Skype:
          this.AllowedButtonTypes = [ ButtonType.Redirect, ButtonType.Url ];
          break;
        case ChannelTypes.Telegram:
          this.AllowedButtonTypes = [ ButtonType.Redirect, ButtonType.Url ];
          break;
        case ChannelTypes.WhatsApp:
        case ChannelTypes.AppleMessaging:
          this.AllowedButtonTypes = [ ButtonType.Redirect ];
          break;
        case ChannelTypes.GoogleRBM:
          this.AllowedButtonTypes = [ ButtonType.Redirect, ButtonType.Location, ButtonType.Url, ButtonType.SendLocation, ButtonType.Dial ];
          break;
        case ChannelTypes.Generic:
          this.AllowedButtonTypes = [ ButtonType.Redirect ];
          break;
      }
    }
    this.joinCustomVariablesList = this.customVariableList !== null && this.customVariableList.length > 0;

    if (flow.channel === ChannelTypes.Telegram) {
      this.allowedToSetValueToVariable = false;
    }
  }

  ngAfterViewInit() {
    if (this.NameInput) {
      this.NameInput.nativeElement.select();
    }
  }

  getActiveTab(): string {
    switch (this.Model.Type) {
      case ButtonType.Auth:
        return "tab-auth-select";
      case ButtonType.Redirect:
        return "tab-redirect-to-block";
      case ButtonType.Url:
        return "tab-url-input";
      case ButtonType.Unlink:
        return "tab-unlink";
      case ButtonType.PersistentMenu:
        return "tab-create-submenu";
      case ButtonType.Location:
        return "tab-location";
      case ButtonType.LocationWithMap:
        return "tab-locationwithmap";
      case ButtonType.Dial:
        return "tab-dial";
      case ButtonType.SendLocation:
        return "tab-send-location";
      case ButtonType.Calendar:
        return "tab-calendar";
    }

    return "tab-redirect-to-block";
  }

  isNameValid(): boolean {
    return isStringValid(this.Model.Name);
  }

  isButtonTypeAllowed(type: string) : boolean {
    if (this.AllowedButtonTypes === null) {
      return true;
    }

    switch (type) {
      case 'redirect':
        return this.AllowedButtonTypes.indexOf(ButtonType.Redirect) >= 0;
      case 'url':
        return this.AllowedButtonTypes.indexOf(ButtonType.Url) >= 0;
      case 'auth':
        return this.AllowedButtonTypes.indexOf(ButtonType.Auth) >= 0;
      case 'unlink':
        return this.AllowedButtonTypes.indexOf(ButtonType.Unlink) >= 0;
      case 'location':
        return this.AllowedButtonTypes.indexOf(ButtonType.Location) >= 0;
      case 'location_with_map':
        return this.AllowedButtonTypes.indexOf(ButtonType.LocationWithMap) >= 0;
      case 'dial':
        return this.AllowedButtonTypes.indexOf(ButtonType.Dial) >= 0;
      case 'send-location':
        return this.AllowedButtonTypes.indexOf(ButtonType.SendLocation) >= 0;
      case 'calendar':
        return this.AllowedButtonTypes.indexOf(ButtonType.Calendar) >= 0;
    }

    return false;
  }

  isStringValid(str: string): boolean {
    if (!isStringValid(str)) {
      return false;
    }

    if (typeof(this.MaxLength) === 'number') {
      if (str.length > this.MaxLength) {
        return false;
      }
    }

    if (typeof(this.EmojisAllowed) === 'boolean' &&
      !this.EmojisAllowed) {
      if (regexEmoji.test(str)) {
        return false;
      }
    }

    return true;
  }

  isValidURL(str: string) {
    return isUrlValid(str);
  }

  onTabChange(eventInfo) {
    this.hideBlockSelector = true;
    switch (eventInfo.nextId) {
      case "tab-auth-select":
        this.Model.Type = ButtonType.Auth;
        return;
      case "tab-unlink":
        this.Model.Type = ButtonType.Unlink;
        break;
      case "tab-redirect-to-block":
        this.Model.Type = ButtonType.Redirect;
        return;
      case "tab-url-input":
        this.Model.Type = ButtonType.Url;
        return;
      case "tab-location":
        this.Model.Type = ButtonType.Location;
        return;
      case "tab-locationwithmap":
        this.Model.Type = ButtonType.LocationWithMap;
        return;
      case "tab-create-submenu":
        this.Model.Type = ButtonType.PersistentMenu;
        if (this.Model.PersistentMenuId == null) {
          this.subMenu = this.editorService.createPersistentMenu();
          this.Model.PersistentMenuId = this.subMenu.id;
        }
        if (this.subMenu == null) {
          this.subMenu = this.editorService.getMenuWithId(this.Model.PersistentMenuId);
        }
        this.subMenu.name = this.Model.Name;
        return;
      case "tab-dial":
        this.Model.Type = ButtonType.Dial;
        return;
      case "tab-send-location":
        this.Model.Type = ButtonType.SendLocation;
        return;
      case "tab-calendar":
        this.Model.Type = ButtonType.Calendar;
        return;
    }
  }

  onBlockSelect(blockData) {
    this.Model.BlockID = blockData.Id;
    this.SearchBlockString = blockData.Name;
    this.BlockData = this.editorService.findBlockWithId(this.Model.BlockID);
    this.NameInput.nativeElement.select();
  }

  hasBlock() {
    if (!this.BlockData || !this.BlockData.validateCurrentModuleBlock(this.editorService)) {
      return false;
    }
    return true;
  }

  deleteBlock() {
    this.BlockData = null;
    this.Model.BlockID = "-1";
    this.SearchBlockString = "";
    this.NameInput.nativeElement.select();
  }

  getWebViewSizes() {
    return WebViewSizesDefinitions.Definitions;
  }

  updateName(newName: string) {
    if (this.Model.Type === ButtonType.PersistentMenu) {
      this.subMenu.name = newName;
    }
  }

  getOperators() {
    return OperatorDefinitions.Operators;
  }

  showOperand() : boolean {
    let op = OperatorDefinitions.Operators.find( op => op.value == this.Model.Operator);
    if(op == null) {
      return false;
    }
    return op.requiresOperand;
  }

  getFirstValidator() {
    return str => { return this.Model.isFirstOperandValid(this.editorService, this.customVariableList);};
  }
  getSecondValidator() {
    return str => { return this.Model.isSecondOperandValid();};
  }

  setVariableOnOutput(variable : VariableDefinition) {
    if( variable != null) {
      this.Model.AssignToVariableId = variable.Id;
    }
    else {
      this.Model.AssignToVariableId = null;
    }
  }

  getVariableValidator() {
    return str => { return this.Model.isAssignValueValid();};
  }

  getAssignVariableIdValid() {
    return str => { return this.Model.isAssignToVariableIdValid(this.editorService);};
  }

  searchForVariable(varName) {
    if (this.customVariableList !== null && this.customVariableList.length > 0) {
      let index = this.customVariableList.findIndex(varDef => varDef.Name == varName);
      if (index >= 0) {
        return this.customVariableList[index];
      }
    }
    return this.editorService.findVariablesAndImplicitsWithName(varName);
  }
}
