import { Component, OnInit } from '@angular/core';
import { BaseDynamicComponent } from '../../../../utils/component-holder/BaseDynamicComponent';
import { BasePieceVM } from '../BasePieceVM';
import { EditorService } from '../../../../../services/editor.service';
import { ModalService } from '../../../../../services/Tools/ModalService';
import { JsonPiece } from '../../../../../models/pieces/JsonPiece';
import {RichLinkPiece, RichLinkTypes} from "../../../../../models/pieces/RichLinkPiece";
import {HttpClient} from "@angular/common/http";
import {containsExpressions, containsVariables, isUrlValid} from "../../../../../urlutils.module";
import {finalize} from "rxjs/operators";
import {StatusResponse} from "../../../../../models/StatusResponse";
import {ErrorPopupComponent} from "../../../../error-popup/error-popup.component";
import {ServerService} from "../../../../../services/server.service";
import {
  AppleInteractiveMessageAuthenticationPiece, AppleInteractiveMessageAuthenticationResponseTypes
} from "../../../../../models/pieces/AppleInteractiveMessageAuthenticationPiece";

@Component({
  selector: 'app-apple-interactive-message-authentication-piece',
  templateUrl: './apple-interactive-message-authentication-piece.component.html',
  styleUrls: ['./apple-interactive-message-authentication-piece.component.scss']
})
export class AppleInteractiveMessageAuthenticationPieceComponent extends BasePieceVM implements OnInit {

  model : AppleInteractiveMessageAuthenticationPiece;
  appleInteractiveMessageAuthenticationResponseTypes = AppleInteractiveMessageAuthenticationResponseTypes;
  loading: boolean = false;

  constructor( editorService : EditorService, public modalService : ModalService, public serverService: ServerService ) {
    super(editorService, modalService);
  }

  ngOnInit() {
    this.model = this.context as AppleInteractiveMessageAuthenticationPiece;
  }

  selectSuccessBlock(blockData) {
    this.model.authenticationSuccessBlockId = blockData.Id;
  }

  selectErrorBlock(blockData) {
    this.model.authenticationErrorBlockId = blockData.Id;
  }

  deleteSuccessBlock() {
    this.model.authenticationSuccessBlockId = null;
  }

  deleteErrorBlock() {
    this.model.authenticationErrorBlockId = null;
  }
}
