@import "_variables";
@import "_mixins";

.gallery {
  margin-left: 0;
  flex-direction: column;

  .sourcearray {
    display: flex;
    flex-direction: row;
    margin-bottom: 10px;
    padding-bottom: 10px;
    border-bottom: 1px solid $gray;

    .title {
      font-family: $fontFamilyTitles;
      font-weight: bold;
      margin-right: 10px;
      justify-self: center;
      align-self: center;
      text-align: center;
    }
  }

  .item-container {
    .implicit-variables {
      padding: 0 10px;
      margin-bottom: 10px;

      .variables-info {
        font-family: $fontFamily;
        margin-bottom: 5px;
        color: #767676;
      }

      .variables-table {
        display: table;
        width: 100%;

        .variables-header, .variable-row {
          height: 30px;
        }

        .variables-header {
          display: table-row;
          font-family: $fontFamilyTitles;

          div {
            display: table-cell;
            vertical-align: middle;
            border-bottom: 1px solid $sidebarBorderColor;
            padding-left: 3px;
            padding-right: 3px;
          }
        }

        .variable-row {
          display: table-row;

          .variable-cell {
            display: table-cell;
            vertical-align: middle;
            border-bottom: 1px solid $sidebarBorderColor;
            padding-left: 3px;
            padding-right: 3px;

            .variable-name {
              @include variable-name;
            }

            .variable-type {
              @include variable-type;
            }
          }

          &:last-child {
            .variable-cell {
              border-bottom-style: none;
            }
          }
        }
      }
    }

    .add-condition, .addQuick {
      text-align: center;
      text-transform: uppercase;
      padding: 15px 0;
      cursor: pointer;
      font-size: 12px;
      position: relative;
      font-weight: normal;
      color: $linkActionColor;
      border-bottom: $cardSeparator;
      margin-bottom: 10px;
      border-top: 1px solid $cardSeparatorBorderColor;

      &:hover {
        color: lighten($linkActionColor, 10%);
      }

      span {
        display: inline-block;
        margin-right: 10px;
        position: absolute;
        left: -10px;
        top: 18px;
        margin-left: 30px;
      }
    }

    .condition {
      border-bottom: 1px solid $cardSeparatorBorderColor;
      border-top: 1px solid $cardSeparatorBorderColor;
      position: relative;
      padding: 10px 0;

      .trash {
        @include trash;
        position: absolute;
        right: -16px;
        top: 50%;
        transform: translateY(-50%);

        &:hover {
          color: #555;
        }
      }

      &:hover {
        .trash {
          @include trashOver;
        }
      }

      .data {
        display: flex;
        flex-direction: row;
        margin-bottom: 10px;
        width: 100%;

        .item {
          width: 30%;
        }

        .select {
          width: 30%;
          margin-left: 5px;
          margin-right: 5px;
        }
      }

      .title {
        font-family: $fontFamilyTitles;
        font-weight: bold;
        margin-right: 10px;
        justify-self: center;
        align-self: center;
        text-align: center;
        flex-grow: 0;
        flex-shrink: 0;
      }

      .input-variable-area {
        flex-grow: 1;
      }
    }

    .input {
      width: 100%;
    }

    .image {
      width: 100%;
      height: 150px;
      text-align: center;
      vertical-align: middle;
      color: #bbb;
      overflow: hidden;
      position: relative;
      span {
        font-size: 22px;
        display: block;
        margin-top: 60px;
      }
      img {
        display: none;
        position: absolute;
        left: 50%;
        top: 50%;
        height: auto;
        max-width: 100%;
        -webkit-transform: translate(-50%, -50%);
        -ms-transform: translate(-50%, -50%);
        transform: translate(-50%, -50%);
      }
    }

    .showImg {
      img {
        display: block;
      }
      span {
        display: none;
      }
    }

    ul {
      border-top: 1px solid $cardSeparatorBorderColor;
      border-bottom: 1px solid $cardSeparatorBorderColor;
      margin-bottom: 0;
      li {
        padding: 5px;
      }
    }

    .addButton {
      @include addPieceButton;
    }
  }
}
