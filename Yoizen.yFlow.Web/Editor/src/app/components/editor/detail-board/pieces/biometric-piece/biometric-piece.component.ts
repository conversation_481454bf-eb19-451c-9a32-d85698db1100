import { Component, OnInit, ElementRef, Renderer2, ViewChild } from '@angular/core';
import { Metamap } from 'src/app/models/biometric/Metamap';
import { BlockDefinition } from 'src/app/models/BlockDefinition';
import { BiometricPiece } from 'src/app/models/pieces/BiometricPiece';
import { SignaturePadPiece } from 'src/app/models/pieces/SignaturePadPiece';
import { EditorService } from 'src/app/services/editor.service';
import { ServerService } from 'src/app/services/server.service';
import { ModalService } from 'src/app/services/Tools/ModalService';
import { isUrlValid } from 'src/app/urlutils.module';
import { BasePieceVM } from '../BasePieceVM';

@Component({
  selector: 'app-biometric-piece',
  templateUrl: './biometric-piece.component.html',
  styleUrls: ['./biometric-piece.component.scss']
})
export class BiometricPieceComponent extends BasePieceVM implements OnInit {
  model: BiometricPiece;
  searchMetamapString: String = "";
  lastSearchMetamapString: String = "";
  Metamaps: Metamap[] = [];

  SuccessBlockData: BlockDefinition = null;
  searchSuccessBlockString : String;
  ReviewNeededBlockData: BlockDefinition = null;
  searchReviewNeededBlockString : String;
  RejectedBlockData: BlockDefinition = null;
  searchRejectedBlockString : String;
  StartedBlockData: BlockDefinition = null;
  searchStartedBlockString : String;
  OpenedProcessBlockData: BlockDefinition = null;
  searchOpenedProcessBlockString : String;

  @ViewChild('successBlockPicker', { static: false }) SuccessBlockPicker : ElementRef;
  @ViewChild('reviewNeededBlockPicker', { static: false }) ReviewNeededBlockPicker : ElementRef;
  @ViewChild('rejectedBlockPicker', { static: false }) RejectedBlockPicker : ElementRef;
  @ViewChild('startedBlockPicker', { static: false }) StartedBlockPicker : ElementRef;
  @ViewChild('openedProcessBlockPicker', { static: false }) OpenedProcessBlockPicker : ElementRef;

  @ViewChild('metamapPicker', { static: false }) MetamapPicker : ElementRef;

  constructor(editorService: EditorService,
              public modalService: ModalService,
              private renderer: Renderer2,
              private serverService: ServerService) {
    super(editorService, modalService);
  }

  ngOnInit() {
    this.model = this.context as BiometricPiece;
    this.Metamaps = this.editorService.getBiometricMetamaps();
    if (this.model.SuccessBlockId !== "-1")
      this.SuccessBlockData = this.editorService.findBlockWithId(this.model.SuccessBlockId);
    if (this.model.ReviewNeededBlockId !== "-1")
      this.ReviewNeededBlockData = this.editorService.findBlockWithId(this.model.ReviewNeededBlockId);
    if (this.model.RejectedBlockId !== "-1")
      this.RejectedBlockData = this.editorService.findBlockWithId(this.model.RejectedBlockId);
    if (this.model.StartedBlockId !== "-1")
      this.StartedBlockData = this.editorService.findBlockWithId(this.model.StartedBlockId);
      if (this.model.OpenedProcessBlockId !== "-1")
      this.OpenedProcessBlockData = this.editorService.findBlockWithId(this.model.OpenedProcessBlockId);
  }

  onSuccessBlockSelect(blockData) {
    this.model.SuccessBlockId = blockData.Id;
    this.searchSuccessBlockString = blockData.Name;
    this.SuccessBlockData = this.editorService.findBlockWithId( this.model.SuccessBlockId);
  }

  deleteSuccessBlock() {
    this.SuccessBlockData = null;
    this.model.SuccessBlockId = "-1";
    this.searchSuccessBlockString = "";
  }

  onSuccessInputFocusIn() {
    this.renderer.removeClass(this.SuccessBlockPicker.nativeElement, 'hide');
  }

  onSuccessInputFocusOut() {

    setTimeout(() => {
      this.renderer.addClass(this.SuccessBlockPicker.nativeElement, 'hide');
    }, 500);
  }

  onReviewNeededBlockSelect(blockData) {
    this.model.ReviewNeededBlockId = blockData.Id;
    this.searchReviewNeededBlockString = blockData.Name;
    this.ReviewNeededBlockData = this.editorService.findBlockWithId( this.model.ReviewNeededBlockId);
  }

  deleteReviewNeededBlock() {
    this.ReviewNeededBlockData = null;
    this.model.ReviewNeededBlockId = "-1";
    this.searchReviewNeededBlockString = "";
  }

  onReviewNeededInputFocusIn() {
    this.renderer.removeClass(this.ReviewNeededBlockPicker.nativeElement, 'hide');
  }

  onReviewNeededInputFocusOut() {

    setTimeout(() => {
      this.renderer.addClass(this.ReviewNeededBlockPicker.nativeElement, 'hide');
    }, 500);
  }

  onRejectedBlockSelect(blockData) {
    this.model.RejectedBlockId = blockData.Id;
    this.searchRejectedBlockString = blockData.Name;
    this.RejectedBlockData = this.editorService.findBlockWithId( this.model.RejectedBlockId);
  }

  deleteRejectedBlock() {
    this.RejectedBlockData = null;
    this.model.RejectedBlockId = "-1";
    this.searchRejectedBlockString = "";
  }

  onRejectedInputFocusIn() {
    this.renderer.removeClass(this.RejectedBlockPicker.nativeElement, 'hide');
  }

  onRejectedInputFocusOut() {

    setTimeout(() => {
      this.renderer.addClass(this.RejectedBlockPicker.nativeElement, 'hide');
    }, 500);
  }

  onOpenedProcessBlockSelect(blockData) {
    this.model.OpenedProcessBlockId = blockData.Id;
    this.searchOpenedProcessBlockString = blockData.Name;
    this.OpenedProcessBlockData = this.editorService.findBlockWithId( this.model.OpenedProcessBlockId);
  }

  deleteOpenedProcessBlock() {
    this.OpenedProcessBlockData = null;
    this.model.OpenedProcessBlockId = "-1";
    this.searchOpenedProcessBlockString = "";
  }

  onOpenedProcessInputFocusIn() {
    this.renderer.removeClass(this.OpenedProcessBlockPicker.nativeElement, 'hide');
  }

  onOpenedProcessInputFocusOut() {

    setTimeout(() => {
      this.renderer.addClass(this.OpenedProcessBlockPicker.nativeElement, 'hide');
    }, 500);
  }

  onStartedBlockSelect(blockData) {
    this.model.StartedBlockId = blockData.Id;
    this.searchStartedBlockString = blockData.Name;
    this.StartedBlockData = this.editorService.findBlockWithId( this.model.StartedBlockId);
  }

  deleteStartedBlock() {
    this.StartedBlockData = null;
    this.model.StartedBlockId = "-1";
    this.searchStartedBlockString = "";
  }

  onStartedInputFocusIn() {
    this.renderer.removeClass(this.StartedBlockPicker.nativeElement, 'hide');
  }

  onStartedInputFocusOut() {

    setTimeout(() => {
      this.renderer.addClass(this.StartedBlockPicker.nativeElement, 'hide');
    }, 500);
  }

  isUrlValid(str): Boolean {
    return isUrlValid(str);
  }

  onInputFocusIn() {
    this.renderer.removeClass(this.MetamapPicker.nativeElement, 'hide');
  }

  onInputFocusOut() {
    setTimeout(() => {
      this.renderer.addClass(this.MetamapPicker.nativeElement, 'hide');
    }, 500);
  }

  hasMetamap() {
    if (this.model.Metamap == null || this.model.Metamap.id == null || this.model.Metamap.name == null) {
      return false;
    }
    return true;
  }

  get EmptyMetamapSet() : boolean {
    return this.Metamaps.length === 0;
  }

  getMetamapInfo() {
    if (this.model.Metamap == null || this.model.Metamap.id == null || this.model.Metamap.name == null) {
      return null;
    }
    return this.model.Metamap;
  }

  selectMetamap(metamapData) {
    this.searchMetamapString = metamapData.name;
    this.model.Metamap = metamapData;
  }

  deleteMetamap() {
    this.model.Metamap = null;
    this.searchMetamapString = "";
  }

  isValid() {
    return this.model.Metamap != null;
  }

}
