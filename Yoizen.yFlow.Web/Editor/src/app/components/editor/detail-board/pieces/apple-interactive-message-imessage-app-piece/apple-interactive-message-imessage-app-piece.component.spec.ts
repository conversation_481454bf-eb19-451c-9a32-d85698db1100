import { async, ComponentFixture, TestBed } from '@angular/core/testing';

import { AppleInteractiveMessageImessageAppPieceComponent } from './apple-interactive-message-imessage-app-piece.component';

describe('AppleInteractiveMessageImessageAppPieceComponent', () => {
  let component: AppleInteractiveMessageImessageAppPieceComponent;
  let fixture: ComponentFixture<AppleInteractiveMessageImessageAppPieceComponent>;

  beforeEach(async(() => {
    TestBed.configureTestingModule({
      declarations: [ AppleInteractiveMessageImessageAppPieceComponent ]
    })
    .compileComponents();
  }));

  beforeEach(() => {
    fixture = TestBed.createComponent(AppleInteractiveMessageImessageAppPieceComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });
});
