@import "_variables";
@import "_mixins";

.gallery {
  margin-left: 0;
  flex-direction: column;

  .item-container {
    flex-direction: row;
    display: flex;
    flex-wrap: nowrap;
    justify-content: flex-start;

    .item {
      width: 300px;
      margin-bottom: 20px;
      background-color: #fff;
      display: inline-block;
      margin-right: 20px;
      position: relative;
      height: max-content;
      border: 1px solid $cardSeparatorBorderColor;

      .trash {
        @include trash;
        position: absolute;
        right: -16px;
        top: -16px;
        cursor: pointer;

        &:hover {
          color: #555;
        }
      }

      .reorder {
        position: absolute;
        top: -16px;
        color: #bbb;
        cursor: pointer;
        opacity: 0;

        &.left {
          right: calc(50% + 10px);
        }

        &.right {
          right: calc(50% - 10px);
        }

        &:hover {
          color: #555;
        }
      }

      &:hover {
        .trash, .reorder {
          @include trashOver;
        }
      }

      .input {
        width: 100%;
      }

      .image {
        width: 100%;
        height: 150px;
        text-align: center;
        vertical-align: middle;
        color: #bbb;
        overflow: hidden;
        position: relative;
        span {
          font-size: 22px;
          display: block;
          margin-top: 60px;
        }
        img {
          display: none;
          position: absolute;
          left: 50%;
          top: 50%;
          height: auto;
          max-width: 100%;
          -webkit-transform: translate(-50%, -50%);
          -ms-transform: translate(-50%, -50%);
          transform: translate(-50%, -50%);
        }
      }
      .showImg {
        img {
          display: block;
        }
        span {
          display: none;
        }
      }
      ul {
        border-top: 1px solid $cardSeparatorBorderColor;
        border-bottom: 1px solid $cardSeparatorBorderColor;
        margin-bottom: 0;
        li {
          padding: 5px;
        }
      }
    }

    .addButton {
      @include addPieceButton;
    }

    .addItem {
      @include addButton;
    }
  }
  .addQuick {
    text-align: center;
    text-transform: uppercase;
    font-size: 12px;
    padding: 10px;
    border-top: 1px solid $cardSeparatorBorderColor;
    &:hover {
      cursor: pointer;
      span {
        color: #555;
      }
    }
    span {
      color: #bbb;
    }
  }
}
