<div class="quick-reply card" [ngClass]="{'invalid-piece': !model.isValid(editorService)}">
  <div class="trash" (click)="deleteAction()" *ngIf="!readOnly"
       data-toggle="tooltip" ngbTooltip="{{ 'CARD_DELETE_TEXT' | translate }}" placement="top" container="body" tooltipClass="tooltip-trash">
    <span class="fa fa-trash-alt"></span>
  </div>
  <div class="clone" (click)="cloneAction()" *ngIf="!readOnly"
       data-toggle="tooltip" ngbTooltip="{{ 'CARD_CLONE_TEXT' | translate }}" placement="top" container="body" tooltipClass="tooltip-trash">
    <span class="fa fa-clone"></span>
  </div>
  <div class="export" (click)="exportAction()" *ngIf="!readOnly"
       data-toggle="tooltip" ngbTooltip="{{ 'CARD_EXPORT_TEXT' | translate }}" placement="top" container="body" tooltipClass="tooltip-trash">
    <span class="fa fa-file-export"></span>
  </div>
  <div class="card-title">
    <span class="fa fa-ellipsis-h"></span> {{ 'CARD_QUICKREPLY_TITLE' | translate }}
  </div>
  <div class="empty" *ngIf="isInInvalidPosition()" role="alert">
    <div class="alert alert-danger">
      <span class="fa fa-lg fa-exclamation-triangle icon"></span>
      {{ 'QUICKREPLY_ONLYALLOWEDAFTERMESSAGE_INFO' | translate }}
    </div>
  </div>
  <div class="definition">
    <div class="options">
      <app-quick-reply-option-element *ngFor="let option of model.Options; let i = index;"
                                      [Model]="option"
                                      [Index]="i"
                                      [IsFirst]="i === 0"
                                      [IsLast]="i === model.Options.length - 1"
                                      [stats]="getButtonStats(option)"
                                      (onDelete)="removeOption($event)"
                                      (onMoveLeft)="moveLeftOption($event)"
                                      (onMoveRight)="moveRightOption($event)"
                                      [expandedBtn]="expandButton"
                                      (onShowDetail)="onShowButtonDetail($event)"
                                      [readOnly]="readOnly">
      </app-quick-reply-option-element>
    </div>
    <div class="addQuick" (click)="addOption()" *ngIf="!readOnly">
      <span class="fa fa-plus"></span> {{ 'QUICKREPLY_ADD' | translate }}
    </div>
    <div class="attribute" *ngIf="(!readOnly || hasVariable()) && showStoreVariable">
      <label>{{ 'SAVE_INTO' | translate }}</label>
      <div class="block block-picker">
        <div [ngClass]="{'hide': hasVariable()}" *ngIf="!readOnly">
          <input class="input" type="text" placeholder="{{ 'VARIABLE_NAME' | translate }}"
                 [(ngModel)]="SearchVariableString"
                 (focusin)="onInputFocusInVar()"
                 (focusout)="onInputFocusOutVar()">
          <div #varPicker class="hide block-selection-anchor">
            <app-variable-selector class="select-block"
                                   [VariableName]="SearchVariableString"
                                   [typeFilters]="['Text']"
                                   [canSelectConstants]="false"
                                   (onSelectVariable)="onVarSelect($event)"></app-variable-selector>
          </div>
        </div>
        <div class="selected-block-container" [ngClass]="{'hide': !hasVariable()}">
          <span class="block-selected">
            <span class="block-display">
              <span class="variable-name">{{ VariableData?.Name }}</span>
              <span class="variable-type">{{ getVariableType(VariableData) | translate }}</span>
            </span>
            <span class="fa fa-unlink trash" (click)="deleteVariable()" *ngIf="!readOnly"
                  data-toggle="tooltip" ngbTooltip="{{ 'VARIABLE_REMOVESELECTED' | translate }}"
                  placement="top" container="body" tooltipClass="tooltip-trash"></span>
          </span>
        </div>
      </div>
    </div>
  </div>
</div>
