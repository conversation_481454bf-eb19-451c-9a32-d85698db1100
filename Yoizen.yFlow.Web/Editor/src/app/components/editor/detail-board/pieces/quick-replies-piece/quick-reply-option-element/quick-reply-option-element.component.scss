@import "_variables";
@import "_mixins";

.button-content {
  position: relative;
  cursor: pointer;
  margin-right: 15px;
  padding-top: 5px;

  .addName {
    position: relative;
    vertical-align: top;
    background: #fff;
    width: 250px;
    border: 1px solid $cardSeparatorBorderColor;
    text-align: center;
    padding: 4px;
    border-radius: 20px;
    margin-right: 10px;
    font-size: 12px;

    &.invalid {
      border: 1px solid red;
    }

    &:hover{
      cursor: pointer;
    }

    .name {
      .text {
        font-weight: bold;
      }

      .icon {
        color: $linkActionColor;
        margin-right: 3px;
      }

      &.invalid {
        .text {
          font-style: italic;
          color: red;
        }
      }
    }

    .block {
      color: $linkActionColor;

      .goto {
        display: inline-block;
        margin-left: 10px;
        transform: scale(0.3);
        opacity: 0;
        transition: opacity 200ms cubic-bezier(0.2, 0.7, 0.5, 1), transform 200ms cubic-bezier(0.2, 0.7, 0.5, 1), -webkit-transform 200ms cubic-bezier(0.2, 0.7, 0.5, 1);
        cursor: pointer;
      }

      &:hover {
        .goto {
          transform: scale(1);
          opacity: 1;
        }
      }
    }

    .trash {
      @include trashSmall;
      right: -8px;
      top: -8px;
      position: absolute;

      &:hover {
        color: #555;
      }
    }

    .reorder {
      position: absolute;
      top: -16px;
      color: #bbb;
      cursor: pointer;
      opacity: 0;

      &.left {
        right: calc(50% + 10px);
      }

      &.right {
        right: calc(50% - 10px);
      }

      &:hover {
        color: #555;
      }
    }

    &:hover {
      .trash, .reorder {
        opacity: 1;
      }
    }
  }
}
