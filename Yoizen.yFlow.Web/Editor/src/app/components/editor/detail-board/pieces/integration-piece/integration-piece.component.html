<div class="rest card" [ngClass]="{'invalid-piece': !model.isValid(editorService)}">
  <div class="trash" (click)="deleteAction()" *ngIf="!readOnly"
       data-toggle="tooltip" ngbTooltip="{{ 'CARD_DELETE_TEXT' | translate }}" placement="top" container="body" tooltipClass="tooltip-trash">
    <span class="fa fa-trash-alt"></span>
  </div>
  <div class="clone" (click)="cloneAction()" *ngIf="!readOnly"
       data-toggle="tooltip" ngbTooltip="{{ 'CARD_CLONE_TEXT' | translate }}" placement="top" container="body" tooltipClass="tooltip-trash">
    <span class="fa fa-clone"></span>
  </div>
  <div class="export" (click)="exportAction()" *ngIf="!readOnly"
       data-toggle="tooltip" ngbTooltip="{{ 'CARD_EXPORT_TEXT' | translate }}" placement="top" container="body" tooltipClass="tooltip-trash">
    <span class="fa fa-file-export"></span>
  </div>
  <div class="card-title">
    <span class="fa fa-server"></span> {{ 'CARD_INTEGRATION_TITLE' | translate }}
  </div>
  <div class="integration">
    <span class="title">{{ 'INTEGRATION_SELECT' | translate }}:</span>
    <select #integrationDropdown class="select" name="" id="" [(ngModel)]="model.integrationId"
            (ngModelChange)="onSelectIntegration($event)"
            [disabled]="readOnly"
            [ngClass]="{'invalid-piece': !model.isIntegrationIdValid(editorService)}">
      <option *ngFor="let integration of getAvailableIntegrations()" [ngValue]="integration.id">{{integration.name}}
      </option>
    </select>
  </div>

  <div class="alert alert-warning" *ngIf="model.integrationId !== -1 && !model.isEnabled(editorService)">
    <span class="fa fa-lg fa-exclamation-triangle icon"></span>
    {{ 'INTEGRATION_DISABLED_WARNING' | translate }}
  </div>
  <div class="add-condition" (click)="model.HasCondition = true" *ngIf="model.integrationId !== -1 && !model.HasCondition && !readOnly">
    <span class="fa fa-plus"></span> {{ 'CARD_ADD_CONDITION' | translate }}
  </div>
  <div class="condition" *ngIf="model.integrationId !== -1 && model.HasCondition">
    <label for="">{{ 'INTEGRATION_CONDITION' | translate }}</label>
    <div class="data">
      <app-input-with-variables [placeholder]="'VALUE' | translate"
                                [(value)]="model.FirstValue"
                                [disabled]="readOnly"
                                [ngClass]="{'validation-error': !model.isFirstValueValid(editorService)}"></app-input-with-variables>
      <select class="select"
              [(ngModel)]="model.Operator"
              [disabled]="readOnly"
              [ngClass]="{'validation-error': !model.isOperatorValid()}">
        <option *ngFor="let operator of getOperators()" [ngValue]="operator.value">{{ operator.localized | translate }}</option>
      </select>
      <app-input-with-variables *ngIf="showOperand()"
                                [placeholder]="'VALUE' | translate"
                                [(value)]="model.SecondValue"
                                [disabled]="readOnly"
                                [ngClass]="{'validation-error': !model.isSecondValueValid(editorService)}"></app-input-with-variables>
    </div>
    <div class="trash" (click)="model.HasCondition=false" *ngIf="!readOnly"
         data-toggle="tooltip" ngbTooltip="{{ 'CARD_REMOVE_CONDITION' | translate }}" placement="top" container="body"
         tooltipClass="tooltip-trash">
      <span class="fa fa-trash-alt"></span>
    </div>
  </div>
  <div class="varInput" *ngIf="model.integrationId !== -1">
    <label class="title">{{ 'INPUTS' | translate}}</label>
    <div class="fields" *ngIf="model.inputs !== null && model.inputs.length > 0">
      <div class="header">
        <div>{{ 'INPUTS_EXPRESSION' | translate }}</div>
        <div>{{ 'INPUTS_FIELD' | translate }}</div>
      </div>
      <div class="field-row" *ngFor="let input of model.inputs">
        <div class="field-cell">
          <app-input-with-variables [placeholder]="'VALUE' | translate"
                                    [(value)]="input.value"
                                    [wideInput]="true"
                                    [disabled]="readOnly"
                                    [validator]="isInputValid"></app-input-with-variables>
        </div>
        <div class="field-cell">
          <span class="variable-name">{{ input.integrationVariableName }}</span><span class="variable-type">{{ getVariableType(input.type) | translate }}</span>
        </div>
      </div>
    </div>
    <div class="empty" *ngIf="model.inputs === null || model.inputs.length === 0" role="alert">
      <div class="alert alert-info">
        {{ 'INPUTS_EMPTY' | translate }}
      </div>
    </div>
  </div>
  <div class="varOutput" *ngIf="model.integrationId !== -1">
    <label class="title">{{ 'OUTPUTS' | translate}}</label>
    <div class="fields" *ngIf="model.outputs !== null && model.outputs.length > 0">
      <div class="header">
        <div>{{ 'OUTPUTS_FIELD' | translate }}</div>
        <div>{{ 'OUTPUTS_WILLASSIGNTO' | translate }}</div>
      </div>
      <div class="field-row" *ngFor="let output of model.outputs">
        <div class="field-cell">
          <span class="variable-name">{{ output.integrationVariableName }}</span><span class="variable-type">{{ getVariableType(output.type) | translate }}</span>
        </div>
        <div class="field-cell">
          <app-variable-selector-input [VariableData]="getVariableRef(output.variableRefId)"
                                       (setVariable)="setVariableOnOutput(output, $event)"
                                       [typeFilters]="[output.type]"
                                       [validator]="OutputValidator"
                                       [canSelectConstants]="false"
                                       [readOnly]="readOnly"
                                       class="input-variable-area"></app-variable-selector-input>
        </div>
      </div>
    </div>
    <div class="empty" *ngIf="model.outputs === null || model.outputs.length === 0" role="alert">
      <div class="alert alert-info">
        {{ 'OUTPUTS_EMPTY' | translate }}
      </div>
    </div>
  </div>
  <div class="add-condition" (click)="model.IgnoreErrors = false" *ngIf="model.integrationId !== -1 && model.IgnoreErrors && !readOnly">
    <span class="fa fa-plus"></span> {{ 'INTEGRATION_ADD_ERROR' | translate }}
  </div>
  <div class="next" *ngIf="model.integrationId !== -1 && !model.IgnoreErrors">
    <span class="title">{{ 'ON_ERROR_GO_TO' | translate }}:</span>
    <app-block-picker class="input" [blockId]="model.errorBlockId"
                      (onSelectNewBlock)="onSelectBlock($event)"
                      (onDeleteBlock)="onDeleteBlock($event)"
                      [readOnly]="readOnly"
                      [isInvalid]="!model.isErrorBlockValid(editorService)"></app-block-picker>
    <div class="trash" (click)="model.IgnoreErrors = true" *ngIf="!readOnly"
         data-toggle="tooltip" ngbTooltip="{{ 'INTEGRATION_REMOVE_ERROR' | translate }}" placement="top" container="body"
         tooltipClass="tooltip-trash">
      <span class="fa fa-trash-alt"></span>
    </div>
  </div>
</div>
