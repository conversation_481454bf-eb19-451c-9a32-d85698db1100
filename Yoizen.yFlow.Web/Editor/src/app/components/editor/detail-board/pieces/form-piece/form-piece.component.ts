import {Component, EventEmitter, OnInit} from '@angular/core';
import {BasePieceVM} from '../BasePieceVM';
import {EditorService} from '../../../../../services/editor.service';
import {ModalService} from '../../../../../services/Tools/ModalService';
import {BasePiece} from "../../../../../models/pieces/BasePiece";
import {
  FormDatePickerPage,
  FormPage,
  FormPageTypes,
  FormPiece,
  FormPickerPage,
  FormSelectPage, FormInputPage
} from 'src/app/models/pieces/FormPiece';
import * as _ from "lodash";
import {DeleteCardQuestionComponent} from "../../../popups/delete-card-question/delete-card-question.component";
import {NewFormPageComponent} from "../../../popups/new-form-page/new-form-page.component";
import {BlockDefinition} from "../../../../../models/BlockDefinition";

@Component({
  selector: 'app-form-piece',
  templateUrl: './form-piece.component.html',
  styleUrls: ['./form-piece.component.scss']
})
export class FormPieceComponent extends BasePieceVM implements OnInit {

  model: FormPiece;
  formPageTypes = FormPageTypes;
  FormDatePickerPage = FormDatePickerPage;
  FormPickerPage = FormPickerPage;
  FormSelectPage = FormSelectPage;
  FormInputPage = FormInputPage;

  constructor(editorService: EditorService, public modalService: ModalService) {
    super(editorService, modalService);
  }

  ngOnInit() {
    this.model = this.context as FormPiece;
  }

  addPage() {
    //this.model.pages.push(new GalleryImage());
    let createAction = new EventEmitter<any>();
    createAction.subscribe((type: FormPageTypes) => {
      this.model.pages.push(FormPage.create(type, this.model));
    });

    this.modalService.init(NewFormPageComponent, {}, {CreateAction: createAction});
  }

  canAddPage() {
    if (this.readOnly) {
      return false;
    }
    return true;
  }

  removePage(page : FormPage) {
    let emmitAction = new EventEmitter();
    emmitAction.subscribe( () => {
      _.remove(this.model.pages, (im) => {return im == page; });
      if(this.model.pages.length == 0) {
        this.editorService.removePieceForBlockInEdition( this.context as BasePiece );
      }
    });
    this.modalService.init( DeleteCardQuestionComponent, { }, { DeleteAction : emmitAction} );
  }

  moveLeft(index: number) {
    let option = this.model.pages[index - 1];
    this.model.pages[index - 1] = this.model.pages[index];
    this.model.pages[index] = option;
  }

  moveRight(index: number) {
    let option = this.model.pages[index + 1];
    this.model.pages[index + 1] = this.model.pages[index];
    this.model.pages[index] = option;
  }

  onSelectBlock(blockData : BlockDefinition) {
    this.model.blockId = blockData.Id;
  }

  onDeleteBlock() {
    this.model.blockId = null;
  }
}
