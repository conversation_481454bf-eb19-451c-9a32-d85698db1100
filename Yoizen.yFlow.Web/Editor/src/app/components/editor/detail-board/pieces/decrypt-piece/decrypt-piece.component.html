<div class="decryptPiece card" [ngClass]="{'invalid-piece': !isValid()}">

  <div class="trash" (click)="deleteAction()" *ngIf="!readOnly" data-toggle="tooltip"
    ngbTooltip="{{ 'CARD_DELETE_TEXT' | translate }}" placement="top" container="body" tooltipClass="tooltip-trash">
    <span class="fa fa-trash-alt"></span>
  </div>
  <div class="clone" (click)="cloneAction()" *ngIf="!readOnly" data-toggle="tooltip"
    ngbTooltip="{{ 'CARD_CLONE_TEXT' | translate }}" placement="top" container="body" tooltipClass="tooltip-trash">
    <span class="fa fa-clone"></span>
  </div>
  <div class="export" (click)="exportAction()" *ngIf="!readOnly" data-toggle="tooltip"
    ngbTooltip="{{ 'CARD_EXPORT_TEXT' | translate }}" placement="top" container="body" tooltipClass="tooltip-trash">
    <span class="fa fa-file-export"></span>
  </div>

  <div class="card-title">
    <span class="fa fa-unlock-alt"></span> {{ 'CARD_DECRYPT_TITLE' | translate }}
  </div>

  <div class="definition">
    <div class="option">
      <span class="title">{{ 'ENCRYPT_TYPE' | translate }}:</span>
      <select class="select" [(ngModel)]="model.DecryptType" [disabled]="readOnly">
        <option *ngFor="let type of encryptTypes" [ngValue]="type.value">
          {{ type.label | translate }}
        </option>
      </select>
    </div>

    <div class="option">
      <span class="title">{{ 'DECRYPTION_KEY' | translate }}:</span>
      <app-input-with-variables class="input" [(value)]="model.DecryptKey" [disabled]="readOnly"
        [validator]="isDecryptKeyValid.bind(this)">
      </app-input-with-variables>
    </div>

    <div class="option" *ngIf="model.DecryptType === EncryptType.AES || model.DecryptType === EncryptType.TripleDES">
      <span class="title">{{ 'DECRYPT_MODE' | translate }}:</span>
      <select class="select" [(ngModel)]="model.DecryptModeType" [disabled]="readOnly">
        <option *ngFor="let mode of decryptModeTypes" [ngValue]="mode.value">
          {{ mode.label | translate }}
        </option>
      </select>
    </div>

    <div class="option" *ngIf="model.DecryptType === EncryptType.AES || model.DecryptType === EncryptType.TripleDES">
      <span class="title">{{ 'DECRYPT_PADDING' | translate }}:</span>
      <select class="select" [(ngModel)]="model.DecryptPaddingType" [disabled]="readOnly">
        <option *ngFor="let padding of decryptPaddingTypes" [ngValue]="padding.value">
          {{ padding.label | translate }}
        </option>
      </select>
    </div>

    <div class="option">
      <span class="title">{{'ENCRYPT_USE_CUSTOM' | translate}}:</span>
      <ui-switch [(ngModel)]="model.CustomMethod" color="#45c195" size="small" defaultBgColor="#e0e0e0"
        switchColor="#ffffff"></ui-switch>
    </div>

    <!-- Custom Method Configuration Section -->
    <div *ngIf="model.CustomMethod">
 <div class="option">
        <span class="title">{{'ENCRYPT_DELIMITER' | translate}}:</span>
        <select class="select" [(ngModel)]="model.Delimiter" [disabled]="readOnly">
          <option *ngFor="let delimiter of delimiterTypes" [ngValue]="delimiter.value">
            {{ delimiter.label | translate }}
          </option>
        </select>
      </div>

      <div class="option">
        <span class="title">{{'ENCRYPT_IV_BYTES_LENGTH' | translate}}:</span>
        <select class="select" [(ngModel)]="model.IvBytesLength" [disabled]="readOnly">
          <option *ngFor="let length of ivLengthTypes" [ngValue]="length.value">
            {{ length.label | translate }}
          </option>
        </select>
      </div>

      <div class="option">
        <span class="title">{{'ENCRYPT_REPLACE_CHARACTERS' | translate}}:</span>
        <ui-switch [(ngModel)]="model.ReplaceCharacters" color="#45c195" size="small" defaultBgColor="#e0e0e0"
          switchColor="#ffffff"></ui-switch>
      </div>

      <!-- Character Replacement Configuration -->
      <div *ngIf="model.ReplaceCharacters">
        <div class="bussiness-table">
          <div class="bussiness-row" *ngFor="let replacement of model.CharactersToReplace; let i = index">
            <div class="replacement-row">
              <div class="bussiness-value">
                <span class="title">{{'ENCRYPT_CHARACTER_VALUE' | translate}}:</span>
                <input type="text" class="input small-input" [(ngModel)]="replacement.value" [disabled]="readOnly"
                  placeholder="" maxlength="1">
              </div>
              <div class="bussiness-value">
                <span class="title">{{'ENCRYPT_CHARACTER_REPLACE_WITH' | translate}}:</span>
                <input type="text" class="input small-input" [(ngModel)]="replacement.replaceWith" [disabled]="readOnly"
                  placeholder="" maxlength="1">
              </div>
              <div class="trash">
                <div (click)="removeCharacterReplacement(i)" tooltipClass="tooltip-trash-left" data-toggle="tooltip"
                  ngbTooltip="{{ 'ENCRYPT_CHARACTER_REPLACE_REMOVE' | translate }}" placement="left">
                  <span class="fa fa fa-trash-alt"></span>
                </div>
              </div>
            </div>
          </div>
          <div *ngIf="model.CharactersToReplace.length < 3" class="add" (click)="addCharacterReplacement()">
            <span class="fa fa-plus"></span> {{ 'CARD_ADD_CONDITION' | translate }}
          </div>
        </div>
      </div>

    </div>

    <div class="option">
      <span class="title">{{ 'VARIABLE_TO_DECRYPT' | translate }}:</span>
      <app-variable-selector-input class="input"
        [VariableData]="editorService.findVariableWithId(model.VariableToDecryptId)"
        (setVariable)="onSelectVariableToDecrypt($event)" [readOnly]="readOnly" [typeFilters]="[variableTypes.Text]"
        [validator]="isVariableToDecryptValid.bind(this)">
      </app-variable-selector-input>
    </div>

    <div class="option">
      <span class="title">{{ 'VARIABLE_DECRYPTED' | translate }}:</span>
      <app-variable-selector-input class="input"
        [VariableData]="editorService.findVariableWithId(model.VariableDecryptedId)"
        (setVariable)="onSelectVariableDecrypted($event)" [readOnly]="readOnly"
        [typeFilters]="[variableTypes.Text, variableTypes.Number, variableTypes.Decimal, variableTypes.Bool, variableTypes.Array]"
        [validator]="isVariableDecryptedValid.bind(this)">
      </app-variable-selector-input>
    </div>
    <!-- NO TIENE USO, SE DEJA COMENTADO
 <div class="option" >
 <span class="title">{{ 'ENCRYPT_ERROR_MESSAGES' | translate }}:</span>
 <app-input-with-variables [(value)]="model.ErrorMessage" [isTextArea]="true" [wideInput]="true"
 [disabled]="readOnly" [placeholder]="'ENCRYPT_ERROR_MESSAGES' | translate">
 </app-input-with-variables>
 </div>
 -->

    <div class="option">
      <span class="title">{{ 'ON_ERROR_GO_TO' | translate }}:</span>
      <app-block-picker class="input" [blockId]="model.ErrorBlockId" (onSelectNewBlock)="onSelectErrorBlock($event)"
        (onDeleteBlock)="onDeleteErrorBlock()" [readOnly]="readOnly" [isInvalid]="!isErrorBlockValid()">
      </app-block-picker>
    </div>
  </div>
</div>