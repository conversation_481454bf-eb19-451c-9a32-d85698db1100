<div class="account-linking card"
     [ngClass]="{'invalid-piece': !model.isValid(editorService) }">
  <div class="trash"
       (click)="deleteAction()"
       data-toggle="tooltip"
       ngbTooltip="{{ 'CARD_DELETE_TEXT' | translate }}"
       placement="top"
       container="body"
       tooltipClass="tooltip-trash">
    <span class="fa fa-trash-alt"></span>
  </div>
  <div class="card-title">
    <span class="fas fa-user-check"></span> {{ 'CARD_ACCOUNT_LINKING_TITLE' | translate }}
  </div>
  <div class="card-info">
    <span [innerHTML]="'CARD_ACCOUNT_LINKING_INFO' | translate"></span>
  </div>

  <div class="name">
    <span class="title">{{ 'ACCOUNT_LINKING_URL' | translate}}:</span>
    <app-input-with-variables class="input-variable-area"
                              [placeholder]="'ACCOUNT_LINKING_URL' | translate"
                              [(value)]="model.Url"
                              [validator]="model.isUrlValid.bind(model)"
                              [disabled]="readOnly"
                              [wideInput]="true">
    </app-input-with-variables>
  </div>

  <div class="name">
    <span class="title">{{'ACCOUNT_LINKING_IS_ENCRIPTED' | translate}}:</span>
    <ui-switch [(ngModel)]="model.IsEncrypted"
               [disabled]="readOnly"
               color="#45c195"
               size="small"
               defaultBgColor="#e0e0e0"
               switchColor="#ffffff"></ui-switch>
  </div>

  <div class="name" *ngIf="model.IsEncrypted">
    <span class="title">{{'ACCOUNT_LINKING_ENCRIPT_MODE' | translate}}:</span>
    <select class="select"
            [(ngModel)]="model.EncryptModeType"
            [disabled]="readOnly">
      <option [ngValue]="encryptModeType.CBC">{{'CBC' |
        translate}}</option>
      <option [ngValue]="encryptModeType.CFB">{{'CFB' |
        translate}}</option>
      <option [ngValue]="encryptModeType.CTR">{{'CTR' |
        translate}}</option>
      <option [ngValue]="encryptModeType.OFB">{{'OFB' |
        translate}}</option>
      <option [ngValue]="encryptModeType.ECB">{{'ECB' |
        translate}}</option>
    </select>
  </div>

  <div class="name" *ngIf="model.IsEncrypted">
    <span class="title">{{'ACCOUNT_LINKING_ENCRIPT_PADDING' | translate}}:</span>
    <select class="select"
            [(ngModel)]="model.EncryptPaddingType"
            [disabled]="readOnly">
      <option [ngValue]="encryptPaddingType.Pkcs7">{{'Pkcs7' |
        translate}}</option>
      <option [ngValue]="encryptPaddingType.Iso97971">{{'Iso97971' |
        translate}}</option>
      <option [ngValue]="encryptPaddingType.AnsiX923">{{'AnsiX923' |
        translate}}</option>
      <option [ngValue]="encryptPaddingType.Iso10126">{{'Iso10126' |
        translate}}</option>
      <option [ngValue]="encryptPaddingType.ZeroPadding">{{'ZeroPadding' |
        translate}}</option>
      <option [ngValue]="encryptPaddingType.NoPadding">{{'NoPadding' |
        translate}}</option>
    </select>
  </div>

  <div class="name" *ngIf="model.IsEncrypted">
    <span class="title">{{ 'ACCOUNT_LINKING_HASH_KEY' | translate}}:</span>
    <app-input-with-variables class="input-variable-area"
                              [placeholder]="'ACCOUNT_LINKING_HASH_KEY' | translate"
                              [(value)]="model.EncryptKey"
                              [validator]="model.isEncryptKeyValid.bind(model)"
                              [disabled]="readOnly"
                              [wideInput]="true">
    </app-input-with-variables>
  </div>

  <div class="name">
    <span class="title">{{'ACCOUNT_LINKING_IS_HASHED' | translate}}:</span>
    <ui-switch [(ngModel)]="model.IsHashed"
               [disabled]="readOnly"
               color="#45c195"
               size="small"
               defaultBgColor="#e0e0e0"
               switchColor="#ffffff"></ui-switch>
  </div>

  <div class="name" *ngIf="model.IsHashed">
    <span class="title">{{'ACCOUNT_LINKING_HASH_TYPE' | translate}}:</span>
    <select class="select"
            [(ngModel)]="model.HashType"
            [disabled]="readOnly">
      <option [ngValue]="hashType.HmacSHA1">{{'HmacSHA1' |
        translate}}</option>
      <option [ngValue]="hashType.HmacSHA256">{{'HmacSHA256' |
        translate}}</option>
      <option [ngValue]="hashType.HmacSHA512">{{'HmacSHA512' |
        translate}}</option>
      <option [ngValue]="hashType.HmacMD5">{{'HmacMD5' |
        translate}}</option>
      <option [ngValue]="hashType.HmacSHA3">{{'HmacSHA3' |
        translate}}</option>
    </select>
  </div>

  <div class="name" *ngIf="model.IsHashed">
    <span class="title">{{ 'ACCOUNT_LINKING_HASH_KEY' | translate}}:</span>
    <app-input-with-variables class="input-variable-area"
                              [placeholder]="'ACCOUNT_LINKING_HASH_KEY' | translate"
                              [(value)]="model.HashKey"
                              [validator]="model.isHashKeyValid.bind(model)"
                              [disabled]="readOnly"
                              [wideInput]="true">
    </app-input-with-variables>
  </div>

  <div class="bussiness">
    <div class="title">
      {{ 'ACCOUNT_LINKING_UPDATEBUSINESSDATA_TITLE' | translate }}
    </div>
    <div class="bussiness-table">
      <div class="header">
        <div>{{ 'ACCOUNT_LINKING_UPDATEBUSINESSDATA_BUSSINESS_KEY' | translate }}</div>
        <div>{{ 'ACCOUNT_LINKING_UPDATEBUSINESSDATA_BUSSINESS_VALUE' | translate }}</div>
        <div></div>
      </div>
      <div class="bussiness-row"
           *ngFor="let businessData of model.BusinessData let i = index">
        <div class="bussiness-value">
          <app-input-with-variables [placeholder]="'ACCOUNT_LINKING_UPDATEBUSINESSDATA_BUSSINESS_KEY' | translate"
                                    [(value)]="businessData.Key"
                                    [disabled]="readOnly"
                                    [validator]="isValidValue"></app-input-with-variables>
        </div>
        <div class="bussiness-value">
          <app-input-with-variables [placeholder]="'ACCOUNT_LINKING_UPDATEBUSINESSDATA_BUSSINESS_VALUE' | translate"
                                    [(value)]="businessData.Value"
                                    [disabled]="readOnly"
                                    [validator]="isValidValue"></app-input-with-variables>
        </div>
        <div class="trash"
             *ngIf="!readOnly">
          <div (click)="deleteBussinessData(i)"
               tooltipClass="tooltip-trash-left"
               data-toggle="tooltip"
               ngbTooltip="{{ 'CARD_ACCOUNT_LINKING_BUSSINESS_REMOVE' | translate }}"
               placement="left">
            <span class="fa fa fa-trash-alt"></span>
          </div>
        </div>
      </div>
    </div>
    <div class="empty"
         role="alert"
         *ngIf="model.BusinessData.length == 0">
      <div class="alert alert-info">
        {{ 'CARD_UPDATEPIECE_INFO_EMPTY' | translate }}
      </div>
    </div>
    <div class="add"
         (click)="addBussinessData()"
         *ngIf="!readOnly">
      <span class="fa fa-plus"></span> {{ 'CARD_ACCOUNT_LINKING_BUSSINESS_ADD' | translate }}
    </div>
  </div>

</div>