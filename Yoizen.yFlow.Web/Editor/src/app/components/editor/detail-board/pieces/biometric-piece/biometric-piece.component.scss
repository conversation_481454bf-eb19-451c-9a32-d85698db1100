@import '_variables';
@import '_mixins';

.biometric-pad {
  background-color: #fff;
  padding: 10px;
  width: 500px;

  .biometric-list {
    width: 50%;
    vertical-align: top;
    position: relative;
    display: flex;

    .next {
      width: 100%;

      input {
        width: 100%;
      }
    }

    .invalid-state {
      border-color: $error-color;
    }

    .label {
      margin-right: 10px;
      margin-top: auto;
      margin-bottom: auto;
    }

    .biometric {
      padding: 0px;
      max-width: 100%;
    }

    .biometric-no-overflow {
      overflow: hidden;
    }

    .addName input,
    .biometric input {
      width: 100%;
    }

    .biometric-selected {
      width: fit-content;
      align-content: center;
      justify-content: center;
      white-space: nowrap;
      max-width: 100%;
      position: relative;
      display: inline-block;
      vertical-align: top;
      height: 28px;
      border-radius: 7px;
      background-color: #e0e0e0;
      border: solid 1px rgba(0, 0, 0, 0.21);
      padding: 0;
      margin: 0 8px 0 0;
      user-select: none !important;
      cursor: pointer;

      .biometric-display {
        padding: 2px 10px;
        flex-wrap: nowrap;
        display: inline-block;
        white-space: nowrap;
        text-overflow: ellipsis;
        max-width: 100%;
      }

      .trash {
        @include trashSmall;
        right: -10px;
        top: -10px;
        position: absolute;

        &:hover {
          color: #555;
        }
      }

      &:hover {
        .trash {
          @include trashOver;
        }
      }
    }

    .selector-container {
      overflow: hidden;
      border-radius: 5px;
      background: white;
      box-shadow: 0 8px 16px 0 rgba(0, 0, 0, 0.13);
      display: flex;
      flex-direction: column;
      width: 100%;
      height: fit-content;
      padding: 5px;

      .scroll-area {
        max-height: 200px;
        overflow-y: auto;
        text-align: left;

        .biometric-container {
          width: 100%;
          height: fit-content;

          .biometric-name,
          .empty-biometric-set {
            font-weight: 600;
            margin-bottom: 3px;
            padding: 3px;
            border: 1px solid transparent;

            &:hover {
              background: $block-selector-hover-color;
              border: 1px solid darken($block-selector-hover-color, 5%);
            }
          }

          .biometric-name {
            cursor: pointer;
          }
        }
      }
    }

  }

  .next,
  .name {
    display: flex;
    flex-direction: row;
    align-items: center;
    margin-bottom: 10px;
    width: 100%;

    .title {
      font-family: $fontFamilyTitles;
      font-weight: bold;
      margin-right: 10px;
      justify-self: center;
      align-self: center;
      text-align: center;
      flex-grow: 0;
      flex-shrink: 0;
    }

    .input,
    .input-variable-area {
      flex-grow: 1;
    }
  }

  .next {
    margin-top: 10px;
  }
  
  .slider {
    margin-bottom: 20px;
    margin-left: 20px;
    margin-right: 20px;
  }
}