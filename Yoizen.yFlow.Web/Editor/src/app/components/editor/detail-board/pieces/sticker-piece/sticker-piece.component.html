<div class="attach card" [ngClass]="{'invalid-piece': !model.isValid(editorService)}">
  <div class="trash" (click)="deleteAction()" *ngIf="!readOnly"
       data-toggle="tooltip" ngbTooltip="{{ 'CARD_DELETE_TEXT' | translate }}" placement="top" container="body" tooltipClass="tooltip-trash">
    <span class="fa fa-trash-alt"></span>
  </div>
  <div class="clone" (click)="cloneAction()" *ngIf="!readOnly"
       data-toggle="tooltip" ngbTooltip="{{ 'CARD_CLONE_TEXT' | translate }}" placement="top" container="body" tooltipClass="tooltip-trash">
    <span class="fa fa-clone"></span>
  </div>
  <div class="export" (click)="exportAction()" *ngIf="!readOnly"
       data-toggle="tooltip" ngbTooltip="{{ 'CARD_EXPORT_TEXT' | translate }}" placement="top" container="body" tooltipClass="tooltip-trash">
    <span class="fa fa-file-export"></span>
  </div>
  <div class="card-title">
    <span class="fa fa-paperclip"></span> {{ 'PIECE_STICKER' | translate }}
  </div>
  <div class="card-info">
    {{ 'PIECE_STICKER_INFO' | translate }}
  </div>
  <div class="definition">
    <ngb-tabset [activeId]="ActiveIdString" class="tabsbutton" (tabChange)="onTabChange($event)">
      <ngb-tab id="tab-url" [disabled]="readOnly">
        <ng-template ngbTabTitle><span class="fa fa-link"></span> URL</ng-template>
        <ng-template ngbTabContent>
          <div class="contents">
            <div class="url">
              <span class="title">{{ 'URL' | translate}}:</span>
              <app-input-with-variables
                [placeholder]="'URL'"
                [(value)]="model.Url"
                [validator]="isUrlValid"
                [wideInput]="true"
                [disabled]="readOnly"
                class="input-variable-area"
                (focusout)="onFocusOutEvent($event)">
              </app-input-with-variables>
            </div>
          </div>
        </ng-template>
      </ngb-tab>
      <ngb-tab id="tab-variable" [disabled]="readOnly">
        <ng-template ngbTabTitle><span class="fa fa-database"></span> {{'VARIABLE' | translate}}</ng-template>
        <ng-template ngbTabContent>
          <div class="variable">
            <span class="title">{{ 'ATTACHMENT_NAME' | translate}}:</span>
            <div class="contents">
              <app-variable-selector-input
                [VariableData]="variableDefinition"
                (setVariable)="setVariableOnOutput(null, $event)"
                [typeFilters]="variableFilter"
                [readOnly]="readOnly"
                [validator]="getFileVaraibleValidator()">
              </app-variable-selector-input>
            </div>
          </div>
        </ng-template>
      </ngb-tab>
    </ngb-tabset>

    <div class="name">
      <span class="title">{{ 'ATTACHMENT_NAME' | translate}}:</span>
      <app-input-with-variables
        class="input-variable-area"
        [placeholder]="'ATTACHMENT_NAME' | translate"
        [(value)]="model.Name"
        [validator]="isNameValid"
        [disabled]="readOnly"
        [wideInput]="true">
      </app-input-with-variables>
    </div>

    <div class="mimetype">
      <span class="title">{{ 'PIECE_STICKER_MIMETYPE' | translate}}:</span>
      <app-input-with-variables
        class="input-variable-area"
        [placeholder]="'PIECE_STICKER_MIMETYPE' | translate"
        [(value)]="model.MimeType"
        [wideInput]="'true'"
        [list]="'knowncontenttypes'"
        [disabled]="true"
        [spellCheck]="false">
      </app-input-with-variables>
    </div>

    <div class="preview" *ngIf="previewUrl != null && showPreview(model.Url)">
      <span class="title">{{ 'PIECE_STICKER_PREVIEW' | translate}}:</span>
      <div class="contents">
        <div class="preview-image">
          <img [src]="previewUrl" alt="">
        </div>
      </div>
    </div>

  </div>
</div>
