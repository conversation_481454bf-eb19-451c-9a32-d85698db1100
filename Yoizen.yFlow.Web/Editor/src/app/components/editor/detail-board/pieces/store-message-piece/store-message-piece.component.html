<div class="storemessage card" [ngClass]="{'invalid-piece': !model.isValid(editorService)}">
  <div class="trash" (click)="deleteAction()" *ngIf="!readOnly"
       data-toggle="tooltip" ngbTooltip="{{ 'CARD_DELETE_TEXT' | translate }}" placement="top" container="body" tooltipClass="tooltip-trash">
    <span class="fa fa-trash-alt"></span>
  </div>
  <div class="clone" (click)="cloneAction()" *ngIf="!readOnly"
       data-toggle="tooltip" ngbTooltip="{{ 'CARD_CLONE_TEXT' | translate }}" placement="top" container="body" tooltipClass="tooltip-trash">
    <span class="fa fa-clone"></span>
  </div>
  <div class="export" (click)="exportAction()" *ngIf="!readOnly"
       data-toggle="tooltip" ngbTooltip="{{ 'CARD_EXPORT_TEXT' | translate }}" placement="top" container="body" tooltipClass="tooltip-trash">
    <span class="fa fa-file-export"></span>
  </div>
  <div class="card-title">
    <span class="fa fa-bolt"></span> {{ 'CARD_STORE_MESSAGE_TITLE' | translate }}
  </div>
  <div class="card-info" *ngIf="!isFileEvent(editorService)">
    {{ 'STORE_MESSAGE_INFO' | translate }}
  </div>
  <div class="card-info" *ngIf="isFileEvent(editorService)">
    {{ 'STORE_MESSAGE_FILE_INFO' | translate }}
  </div>
  <div class="contents" *ngIf="!isFileEvent(editorService)">
    <app-input-with-variables
      [placeholder]="'MESSAGE' | translate"
      [(value)]="model.Message"
      [isTextArea]="true"
      [wideInput]="true"
      [disabled]="readOnly">
    </app-input-with-variables>
  </div>
</div>
