import {Component, OnInit} from '@angular/core';
import {BasePieceVM} from '../BasePieceVM';
import {EditorService} from '../../../../../services/editor.service';
import {ModalService} from '../../../../../services/Tools/ModalService';
import {VariableDefinition} from "../../../../../models/VariableDefinition";
import {ResetVariablesPiece} from "../../../../../models/pieces/ResetVariablesPiece";

@Component({
  selector: 'app-reset-variables-piece',
  templateUrl: './reset-variables-piece.component.html',
  styleUrls: ['./reset-variables-piece.component.scss']
})
export class ResetVariablesPieceComponent extends BasePieceVM implements OnInit {
  model: ResetVariablesPiece;
  Variables: VariableDefinition[] = null;

  constructor(editorService: EditorService, public modalService: ModalService) {
    super(editorService, modalService);
  }

  ngOnInit() {
    this.model = this.context as ResetVariablesPiece;
    this.Variables = this.editorService.getVariableList().filter(v => !v.Constant);
  }

  getVariableType(variable: VariableDefinition): string {
    for (let i = 0; i < VariableDefinition.variableType.length; i++) {
      if (variable.Type === VariableDefinition.variableType[i].value) {
        return VariableDefinition.variableType[i].localized;
      }
    }

    return '';
  }

  isVariableSelected(variable: VariableDefinition) : boolean {
    if (this.model.Variables === null) {
      return false;
    }

    return this.model.Variables.findIndex(v => v === variable.Id) >= 0;
  }

  changeVariable(variable: VariableDefinition) {
    let index = this.model.Variables.findIndex(v => v === variable.Id);
    if (index === -1) {
      this.model.Variables.push(variable.Id);
    }
    else {
      this.model.Variables.splice(index, 1);
    }
  }

  selectAll() {
    this.Variables.forEach(variable => {
      let index = this.model.Variables.findIndex(v => v === variable.Id);
      if (index === -1) {
        this.model.Variables.push(variable.Id);
      }
    });
  }

  unselectAll() {
    this.model.Variables = [];
  }
}
