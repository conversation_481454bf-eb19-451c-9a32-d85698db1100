import { Component, OnInit } from '@angular/core';
import { BaseDynamicComponent } from '../../../../utils/component-holder/BaseDynamicComponent';
import { BasePieceVM } from '../BasePieceVM';
import { EditorService } from '../../../../../services/editor.service';
import { ModalService } from '../../../../../services/Tools/ModalService';
import {OperandTypeDefinition, OperatorDefinitions, OperatorType} from '../../../../../models/OperatorType';
import { ConditionPiece } from '../../../../../models/pieces/ConditionPiece';
import { BlockDefinition } from '../../../../../models/BlockDefinition';
import {VariableConditionPiece} from "../../../../../models/pieces/VariableConditionPiece";
import {VariableDefinition} from "../../../../../models/VariableDefinition";
import {TypeDefinition} from "../../../../../models/TypeDefinition";
import {CommandDefinition} from "../../../../../models/commands/CommandDefinition";
import { Entity } from 'src/app/models/cognitivity/Entity';

@Component({
  selector: 'app-variable-condition-piece',
  templateUrl: './variable-condition-piece.component.html',
  styleUrls: ['./variable-condition-piece.component.scss']
})
export class VariableConditionPieceComponent extends BasePieceVM implements OnInit {
  model: VariableConditionPiece;
  operators = [];
  cognitivityEnabled: boolean = false;
  selectedEntity: Entity;

  get VariableData(): VariableDefinition {
    if (this.model.VariableId < 1000) {
      return this.editorService.findImplicitVariable(this.model.VariableName);
    }
    else {
      return this.editorService.getVariableWithId(this.model.VariableId);
    }
  }

  get Commands(): CommandDefinition[] {
    return this.editorService.getCommands();
  }

  get CanAddOperators(): boolean {
    if (!this.model.UsesCognitiveEntities) {
      return this.model.VariableId !== -1 && this.VariableData !== null;
    }
    return this.model.EntityId !== null;
  }

  constructor(editorService: EditorService, public modalService: ModalService) {
    super(editorService, modalService);
  }

  ngOnInit() {
    this.model = this.context as VariableConditionPiece;

    if (this.model.VariableId !== -1) {
      this.setVariable(this.VariableData);
    }

    this.cognitivityEnabled = this.editorService.isCognitivityEnabled();
    if (this.cognitivityEnabled) {
      let availableEntities = this.editorService.getEntities();
      let entity = availableEntities.find(e => e.cognitiveServiceId === this.model.EntityId);
      if (entity === undefined) {
        this.model.EntityId = null;
        this.model.EntityName = null;
      } else {
        this.setEntity(entity);
      }
    } else {
      this.model.UsesCognitiveEntities = false;
    }
  }

  setVariable(variable: VariableDefinition) {
    if (variable != null) {
      this.operators = OperatorDefinitions.Operators.filter(op => {
        if (
          op.types.findIndex(operand => {
            return operand === variable.Type || operand === TypeDefinition.Any
          }) === -1) {
          return false;
        }

        return true;
      });

      if (variable.Type === TypeDefinition.Text) {
        this.operators.push(OperatorDefinitions.MeetsAnyCommandConditionOperator);
      }

      if (this.operators.findIndex(o => o.value === this.model.Operator) === -1) {
        this.model.Operator = this.operators[0].value;
      }

      this.model.VariableId = variable.Id;
      this.model.VariableName = variable.Name;
    }
    else {
      this.model.VariableId = -1;
      this.model.VariableName = null;
      this.operators = null;
    }
  }

  setEntity(entity: Entity) {
    if (typeof(entity) !== 'undefined' && entity != null) {
      this.operators = OperatorDefinitions.EntityOperators;
      if (this.operators.findIndex(o => o.value === this.model.Operator) === -1) {
        this.model.Operator = this.operators[0].value;
      }

      this.model.EntityId = entity.cognitiveServiceId;
      this.model.EntityName = entity.name;
      this.selectedEntity = entity;
    } else {
      this.model.EntityId = null;
      this.model.EntityName = null;
      this.selectedEntity = null;
    } 
  }

  onSelectBlock(blockData: BlockDefinition) {
    this.model.ErrorBlockId = blockData.Id;
  }

  onDeleteBlock() {
    this.model.ErrorBlockId = null;
  }

  showOperand(): boolean {
    for (let i = 0; i < OperatorDefinitions.AllOperators.length; i++) {
      if (OperatorDefinitions.AllOperators[i].value === this.model.Operator) {
        return OperatorDefinitions.AllOperators[i].requiresOperand;
      }
    }

    return true;
  }

  showCommands(): boolean {
    if (this.model.Operator === OperatorType.MeetsAnyCommandCondition) {
      return true;
    }

    return false;
  }

  showStringOptions(): boolean {
    return (this.model.EntityId !== null && 
              (this.model.Operator === OperatorType.Equals ||
               this.model.Operator === OperatorType.NotEquals)
            ) || 
           (this.VariableData != null &&
              this.VariableData.Type === TypeDefinition.Text);
  }

  getSecondInputValidator() {
    if (!this.model.UsesCognitiveEntities) {
      return str => {
        return this.model.isSecondValueValid(this.editorService);
      };
    }
    return str => {
      return this.model.isEntityValueValid(this.editorService);
    }
  }

  isCommandSelected(command: CommandDefinition) : boolean {
    if (this.model.Commands === null) {
      return false;
    }

    return this.model.Commands.findIndex(v => v === command.id) >= 0;
  }

  changeCommand(command: CommandDefinition) {
    let index = this.model.Commands.findIndex(v => v === command.id);
    if (index === -1) {
      this.model.Commands.push(command.id);
    }
    else {
      this.model.Commands.splice(index, 1);
    }
  }

  clearCache() {
    this.model.VariableId = -1;
    this.model.VariableName = null;
    this.model.EntityName = null;
    this.model.EntityId = null;
    this.selectedEntity = null;
  }
}
