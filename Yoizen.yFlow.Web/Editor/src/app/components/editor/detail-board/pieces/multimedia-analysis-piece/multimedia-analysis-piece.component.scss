@import '_variables';
@import '_mixins';

.multimediaAnalysisPiece {
  background-color: #fff;
  padding: 10px;
  width: 830px;

  .form-group {
    margin-bottom: 10px;
  }

  .card-title {
    font-family: $fontFamilyTitles;
    font-weight: bold;
    margin-bottom: 10px;
  }

  .next {
    margin-top: 10px;

    .title {
      font-family: $fontFamilyTitles;
      font-weight: bold;
      margin-right: 10px;
    }
  }

  .next,
  .name {
    display: flex;
    flex-direction: row;
    align-items: center;
    margin-bottom: 10px;
    width: 100%;

    .title {
      font-family: $fontFamilyTitles;
      font-weight: bold;
      margin-right: 10px;
      justify-self: center;
      align-self: center;
      text-align: center;
      flex-grow: 0;
      flex-shrink: 0;
    }

    .input,
    .input-variable-area {
      flex-grow: 1;
    }
  }

  .invalid-piece {
    border: 1px solid red;
  }

  .data {
    display: flex;
    flex-direction: row;
    margin-bottom: 10px;
    width: 100%;

    &.condition {
      border-bottom: $cardSeparator;
      position: relative;
      padding: 10px 0;

      .select {
        margin-left: 5px;
        margin-right: 5px;
      }

      .trash {
        @include trash;
        position: absolute;
        right: -16px;
        top: 50%;
        transform: translateY(-50%);

        &:hover {
          color: #555;
        }
      }

      &:hover {
        .trash {
          @include trashOver;
        }
      }
    }
  }
}