import { async, ComponentFixture, TestBed } from '@angular/core/testing';

import { TimePickerEventComponent } from './time-picker-event.component';

describe('TimePickerEventComponent', () => {
  let component: TimePickerEventComponent;
  let fixture: ComponentFixture<TimePickerEventComponent>;

  beforeEach(async(() => {
    TestBed.configureTestingModule({
      declarations: [ TimePickerEventComponent ]
    })
    .compileComponents();
  }));

  beforeEach(() => {
    fixture = TestBed.createComponent(TimePickerEventComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });
});
