<div class="condition card" [ngClass]="{'invalid-piece': !model.isValid(editorService)}">
  <div class="trash" (click)="deleteAction()" *ngIf="!readOnly"
       data-toggle="tooltip" ngbTooltip="{{ 'CARD_DELETE_TEXT' | translate }}" placement="top" container="body" tooltipClass="tooltip-trash">
    <span class="fa fa-trash-alt"></span>
  </div>
  <div class="clone" (click)="cloneAction()" *ngIf="!readOnly"
       data-toggle="tooltip" ngbTooltip="{{ 'CARD_CLONE_TEXT' | translate }}" placement="top" container="body" tooltipClass="tooltip-trash">
    <span class="fa fa-clone"></span>
  </div>
  <div class="export" (click)="exportAction()" *ngIf="!readOnly"
       data-toggle="tooltip" ngbTooltip="{{ 'CARD_EXPORT_TEXT' | translate }}" placement="top" container="body" tooltipClass="tooltip-trash">
    <span class="fa fa-file-export"></span>
  </div>
  <div class="card-title">
    <span class="fa fa-check"></span> {{ 'CARD_CONDITION_TITLE' | translate }}
  </div>
  <div class="expression">
    <span class="title">{{'CONDITION_EXPRESSION' | translate}}:</span>
    <app-input-with-variables [placeholder]="'CONDITION_EXPRESSION' | translate"
                              [(value)]="model.FirstValue"
                              [disabled]="readOnly"
                              class="input"
                              [wideInput]="true"
                              [validator]="getFirstInputValidator()"></app-input-with-variables>
  </div>
  <div class="operator">
    <span class="title">{{'CONDITION_OPERATOR' | translate}}:</span>
    <select class="select" name="" id="" [(ngModel)]="model.Operator" [disabled]="readOnly">
      <option *ngFor="let operator of getOperators()" [ngValue]="operator.value">{{ operator.localized | translate }}
      </option>
    </select>
  </div>
  <div class="operand" *ngIf="showOperand()">
    <span class="title">{{'CONDITION_OPERAND' | translate}}:</span>
    <app-input-with-variables [placeholder]="'CONDITION_OPERAND' | translate"
                              [(value)]="model.SecondValue"
                              [disabled]="readOnly"
                              class="input"
                              [wideInput]="true"
                              [validator]="getSecondInputValidator()"></app-input-with-variables>
  </div>
  <div class="next">
    <span class="title">{{'CONDITION_CONTINUEONFALSE' | translate}}</span>
    <app-block-picker class="input"
                      [blockId]="model.ErrorBlockId"
                      (onSelectNewBlock)="onSelectBlock($event)"
                      (onDeleteBlock)="onDeleteBlock($event)"
                      [readOnly]="readOnly"
                      [isInvalid]="!model.isErrorBlockValid(editorService)"></app-block-picker>
  </div>
</div>
