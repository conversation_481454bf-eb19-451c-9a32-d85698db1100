<div class="rich-link card" [ngClass]="{'invalid-piece': !model.isValid(editorService)}">
  <div class="trash" (click)="deleteAction()" *ngIf="!readOnly"
       data-toggle="tooltip" ngbTooltip="{{ 'CARD_DELETE_TEXT' | translate }}" placement="top" container="body" tooltipClass="tooltip-trash">
    <span class="fa fa-trash-alt"></span>
  </div>
  <div class="clone" (click)="cloneAction()" *ngIf="!readOnly"
       data-toggle="tooltip" ngbTooltip="{{ 'CARD_CLONE_TEXT' | translate }}" placement="top" container="body" tooltipClass="tooltip-trash">
    <span class="fa fa-clone"></span>
  </div>
  <div class="export" (click)="exportAction()" *ngIf="!readOnly"
       data-toggle="tooltip" ngbTooltip="{{ 'CARD_EXPORT_TEXT' | translate }}" placement="top" container="body" tooltipClass="tooltip-trash">
    <span class="fa fa-file-export"></span>
  </div>
  <div class="card-title">
    <span class="fa fa-code"></span> {{ 'PIECE_RICH_LINK' | translate }}
  </div>
  <div class="option">
    <span class="title">{{'RICH_LINK_TYPE' | translate}}:</span>
    <select [(ngModel)]="model.richLinkType" [disabled]="readOnly" class="select">
      <option [ngValue]="richLinkTypes.Image">{{ 'RICH_LINK_TYPE_IMAGE' | translate }}</option>
      <option [ngValue]="richLinkTypes.Video">{{ 'RICH_LINK_TYPE_VIDEO' | translate }}</option>
    </select>
  </div>
  <div class="option with-action">
    <span class="title">{{'RICH_LINK_URL' | translate}}:</span>
    <app-input-with-variables
      [(value)]="model.url"
      [validator]="model.isUrlValid.bind(model)"
      [wideInput]="true"
      [isTextArea]="false"
      [disabled]="readOnly"
      class="input">
    </app-input-with-variables>
    <div class="action" (click)="tryToRetrieveOpenGraph()" *ngIf="model.richLinkType === richLinkTypes.Image"
         ngbTooltip="{{ 'RICH_LINK_URL_SCRAP' | translate }}"
         placement="right" container="body" tooltipClass="tooltip-persistent">
      <span class="fa fa-binoculars"></span>
    </div>
  </div>
  <div class="option">
    <span class="title">{{'RICH_LINK_TITLE' | translate}}:</span>
    <app-input-with-variables
      [(value)]="model.title"
      [validator]="model.isTitleValid.bind(model)"
      [wideInput]="true"
      [isTextArea]="false"
      [disabled]="readOnly"
      class="input">
    </app-input-with-variables>
  </div>
  <div class="option" *ngIf="model.richLinkType === richLinkTypes.Image">
    <span class="title">{{'RICH_LINK_IMAGE_URL' | translate}}:</span>
    <app-input-with-variables
      [(value)]="model.image.url"
      [validator]="model.image.isUrlValid.bind(model.image)"
      [wideInput]="true"
      [isTextArea]="false"
      [disabled]="readOnly"
      class="input">
    </app-input-with-variables>
  </div>
</div>
