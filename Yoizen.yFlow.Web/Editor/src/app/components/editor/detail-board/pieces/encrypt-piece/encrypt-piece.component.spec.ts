import { async, ComponentFixture, TestBed } from '@angular/core/testing';

import { EncryptPieceComponent } from './encrypt-piece.component';

describe('EncryptPieceComponent', () => {
  let component: EncryptPieceComponent;
  let fixture: ComponentFixture<EncryptPieceComponent>;

  beforeEach(async(() => {
    TestBed.configureTestingModule({
      declarations: [ EncryptPieceComponent ]
    })
    .compileComponents();
  }));

  beforeEach(() => {
    fixture = TestBed.createComponent(EncryptPieceComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });
});
