<div class="contents" [ngClass]="{'hide': !componentSelected(), 'withStats': isShowingStats }">
  <div class="title">
    <div class="status">
      <span class="fas fa-unlock-alt"
            *ngIf="mCurrentModule && (((mCurrentModule.isMaster() && editorState.SelectedBlock?.SystemProtected) || mCurrentModule.id == editorState.SelectedBlock?.ModuleId) && editorState.SelectedBlock?.IsPublic) && isMasterBot"
            (click)="changePublicStatus()"></span>
    </div>
    <div class="status">
      <span class="fas fa-lock"
            *ngIf="mCurrentModule && (((mCurrentModule.isMaster() && editorState.SelectedBlock?.SystemProtected) || mCurrentModule.id == editorState.SelectedBlock?.ModuleId) && !editorState.SelectedBlock?.IsPublic) && isMasterBot"
            (click)="changePublicStatus()"></span>
    </div>
    <input type="text" [ngModel]="editorState.SelectedBlock?.Name"
           (change)="onBlockNameChanged($event)"
           *ngIf="!editorState.SelectedBlock?.SystemProtected && !readOnly"
           onReturn />
    <span class="title" *ngIf="editorState.SelectedBlock?.SystemProtected || readOnly">{{ editorState.SelectedBlock?.Name }}</span>
    <span class="display-conections" (click)="displayConnections()"><span class="fas fa-directions"></span> {{ 'DISPLAY_CONECTIONS' | translate }}</span>
  </div>
  <div class="doesnt-return-message" *ngIf="editorState.SelectedBlock && !returnsMessage()" role="alert">
    <div class="alert alert-info">
      <span class="fa fa-exclamation-circle icon"></span>
      {{ 'BLOCK_DOESNT_RETURN_MESSAGE' | translate }}
    </div>
  </div>
  <div class="stats" *ngIf="isShowingStats">
    <div class="title">{{ 'BLOCK_STATS_TITLE' | translate }}</div>
    <div class="stats-empty" *ngIf="stats === null">
      <div class="alert alert-info">
        {{ 'BLOCK_STATS_DESC_EMPTY' | translate }}
      </div>
    </div>
    <div class="stats-info" *ngIf="stats !== null">
      <ul>
        <li><span class="title">{{ 'BLOCK_STATS_FIRSTEXECUTION' | translate }}:</span><span class="value">{{ stats.firstExecution | dateFormat: 'LLL' }}</span></li>
        <li><span class="title">{{ 'BLOCK_STATS_LASTEXECUTION' | translate }}:</span><span class="value">{{ stats.lastExecution | dateFormat: 'LLL' }}</span></li>
        <li><span class="title">{{ 'BLOCK_STATS_COUNT' | translate }}:</span><span class="value">{{ stats.count }}</span></li>
      </ul>
    </div>
  </div>
  <div class="empty" *ngIf="editorState.SelectedBlock?.Pieces.length === 0" role="alert">
    <div class="alert alert-info">
      {{ 'BLOCK_EMPTY' | translate }}
    </div>
  </div>
  <div class="piece" *ngFor="let piece of editorState.SelectedBlock?.Pieces; let i = index; let first = first; let last=last">
    <div class="insert" *ngIf="!readOnly">
      <span class="fa fa-plus-circle add"
            (click)="addPieceAtIndex(i)"
            data-toggle="tooltip" ngbTooltip="{{ 'INSERT_PIECE' | translate }}" placement="top" container="body" tooltipClass="tooltip-trash"></span>
    </div>
    <div class="cont">
      <div class="drag-holder drag-icon-area" *ngIf="!readOnly">
        <span class="fa fa-sort-up drag-icon" [ngClass]="{'hide': first }" (click)="movePieceUp(i)"
              data-toggle="tooltip" ngbTooltip="{{ 'MOVE_UP_PIECE' | translate }}" placement="top" container="body" tooltipClass="tooltip-trash"></span>
        <span class="drag-span"></span>
        <span class="fa fa-sort-down drag-icon" [ngClass]="{'hide': last }" (click)="movePieceDown(i)"
              data-toggle="tooltip" ngbTooltip="{{ 'MOVE_DOWN_PIECE' | translate }}" placement="top" container="body" tooltipClass="tooltip-trash"></span>
      </div>
      <div class="component">
        <app-component-holder
                              [context]="piece"
                              [stats]="getPieceStats(piece)"
                              [readOnly]="readOnly"
                              [index]="i"></app-component-holder>
      </div>
    </div>
  </div>
  <div class="addpiece" *ngIf="showMenuPiece">
    <app-menu-pieces (PieceSelected)="pieceSelected($event)"></app-menu-pieces>
  </div>
</div>
