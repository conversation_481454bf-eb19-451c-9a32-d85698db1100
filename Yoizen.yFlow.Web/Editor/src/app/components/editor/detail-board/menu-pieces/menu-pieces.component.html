<div class="addCard">
  <div class="title" *ngIf="ShowTitle">{{ 'PIECES_ADD' | translate }}</div>
  <div class="card">
    <div class="row">
      <mat-form-field appearance="outline" class="cognitivity-filter col-sm-11">
        <mat-label>{{'SEARCH'|translate}}</mat-label>
        <input matInput type="text" [(ngModel)]="searchTerm" (ngModelChange)="filterPieces()" placeholder="{{'SEARCH'|translate}}">
      </mat-form-field>
    </div>
  </div>
  <div class="option" *ngIf="!isLite">
    <span class="title">{{'PIECE_SHOWADVANCESPIECES' | translate}}:</span>
    <ui-switch color="#45c195" size="small" defaultBgColor="#e0e0e0"
               switchColor="#ffffff" (valueChange)="refreshPieces($event)"></ui-switch>
  </div>
  <div class="items">
    <app-button-menu-pieces *ngFor="let piece of filteredPieces" [Piece]="piece" (onSelect)="addNewPiece($event)">
    </app-button-menu-pieces>
  </div>

</div>
