import {Component, OnInit, Input, Output, EventEmitter} from '@angular/core';
import { EditorService } from 'src/app/services/editor.service';
import { ModalService } from 'src/app/services/Tools/ModalService';
import { BlockDefinition } from 'src/app/models/BlockDefinition';
import {FlowDefinition} from "../../../../models/FlowDefinition";
import {ErrorPopupComponent} from "../../../error-popup/error-popup.component";
import {environment} from "../../../../../environments/environment";
import {finalize} from "rxjs/operators";
import {ServerService} from "../../../../services/server.service";
import {TypedJSON, jsonArrayMember } from "typedjson";
import {ChatDefinition} from "../../../../models/ChatDefinition";
import {BlockGroupModel} from "../../../../models/BlockGroupModel";

@Component({
  selector: 'app-flow-blockgroup-selector-dialog',
  templateUrl: './flow-blockgroup-selector-dialog.component.html',
  styleUrls: ['./flow-blockgroup-selector-dialog.component.scss']
})
export class FlowBlockGroupSelectorDialogComponent implements OnInit {
  loading: boolean = true;
  flowTitleParam: any = null;
  showWarningAnotherChannel: boolean = false;
  publishedFlows : FlowDefinition[];
  stagingFlows : FlowDefinition[];

  FlowToIgnore : number = null;
  CurrentChannel: string = null;
  DefaultBlocks : BlockDefinition[] = null;
  SelectedBlock: BlockDefinition = null;
  SelectedFlow: FlowDefinition = null;
  Definition: ChatDefinition = null;
  BlockGroups: BlockGroupModel[] = null;
  SelectedGroup: BlockGroupModel = null;

  @Output() AcceptAction: EventEmitter<BlockGroupModel> = new EventEmitter<BlockGroupModel>();

  constructor(private modalService: ModalService, private editorService : EditorService, private serverService: ServerService) {
  }

  ngOnInit() {
    this.loading = true;
    this.serverService.getFlows()
      .pipe(finalize( ()=> {this.loading = false;}))
      .subscribe( (flows : FlowDefinition[]) => {
          this.publishedFlows = flows.filter(f => f.ActiveProductionVersion != null && (this.FlowToIgnore === null || this.FlowToIgnore !== f.id));
          this.stagingFlows = flows.filter(f => f.ActiveStagingVersion != null && (this.FlowToIgnore === null || this.FlowToIgnore !== f.id) && f.master_flow_id === null);
        },
        error => {
          this.modalService.init(ErrorPopupComponent, {}, {});
        });
  }

  closePopup() {
    this.modalService.removeLastElement();
  }

  onFlowSelected(flow: FlowDefinition, isStaging: boolean) {
    this.flowTitleParam = { flow: flow.name };
    this.SelectedFlow = flow;
    let blob = isStaging ? flow.ActiveStagingVersion.blob : flow.ActiveProductionVersion.blob;
    if (typeof(blob) === 'undefined') {
      this.loading = true;
      this.serverService.getFlow(flow.id)
        .pipe(finalize( ()=> {this.loading = false;}))
        .subscribe((downloadedFlow: FlowDefinition) => {
          flow.ActiveProductionVersion = downloadedFlow.ActiveProductionVersion;
          flow.ActiveStagingVersion = downloadedFlow.ActiveStagingVersion;
          blob = isStaging ? flow.ActiveStagingVersion.blob : flow.ActiveProductionVersion.blob;

          // Validar y corregir estructura antes de deserializar
          let jsonData = JSON.parse(blob);
          if (!jsonData.BlockGroups) {
            jsonData.BlockGroups = [];
          }
          if (!jsonData.BlockList) {
            jsonData.BlockList = [];
          }
          this.Definition = this.editorService.recoverComplexPieces(TypedJSON.parse(JSON.stringify(jsonData), ChatDefinition), downloadedFlow.channel);
          this.DefaultBlocks = this.Definition.DefaultBlocks;
          this.BlockGroups = this.Definition.BlockGroups;

          // Fix para corregir problemas de referencia de blockgroups
          for (let i = 0; i < this.BlockGroups.length; i++) {
            for (let j = 0; j < this.BlockGroups[i].Blocks.length; j++) {
              this.BlockGroups[i].Blocks[j] = this.Definition.BlockList.find(b => b.Id === this.BlockGroups[i].Blocks[j].Id);
            }
          }

          this.showWarningAnotherChannel = (this.CurrentChannel !== flow.channel);
        });
    }
    else {
      // Validar y corregir estructura antes de deserializar
      let jsonData = JSON.parse(blob);
      if (!jsonData.BlockGroups) {
        jsonData.BlockGroups = [];
      }
      if (!jsonData.BlockList) {
        jsonData.BlockList = [];
      }
      this.Definition = this.editorService.recoverComplexPieces(TypedJSON.parse(JSON.stringify(jsonData), ChatDefinition), flow.channel);
      this.DefaultBlocks = this.Definition.DefaultBlocks;
      this.BlockGroups = this.Definition.BlockGroups;

      // Fix para corregir problemas de referencia de blockgroups
      for (let i = 0; i < this.BlockGroups.length; i++) {
        for (let j = 0; j < this.BlockGroups[i].Blocks.length; j++) {
          this.BlockGroups[i].Blocks[j] = this.Definition.BlockList.find(b => b.Id === this.BlockGroups[i].Blocks[j].Id);
        }
      }

      this.showWarningAnotherChannel = (this.CurrentChannel !== flow.channel);
    }
  }

  acceptPopup() {
    if (this.SelectedGroup === null) {
      return;
    }

    this.AcceptAction.emit(this.SelectedGroup);
    this.modalService.destroy();
  }

  goBack() {
    this.SelectedFlow = null;
    this.SelectedGroup = null;
  }
}
