import { Component, OnInit, Input, Output, EventEmitter, ChangeDetectorRef } from '@angular/core';
import { EditorService } from 'src/app/services/editor.service';
import { ModalService } from 'src/app/services/Tools/ModalService';
import { BlockDefinition } from 'src/app/models/BlockDefinition';
import { FlowDefinition } from "../../../../models/FlowDefinition";
import { ErrorPopupComponent } from "../../../error-popup/error-popup.component";
import { finalize } from "rxjs/operators";
import { ServerService } from "../../../../services/server.service";
import { TypedJSON } from "typedjson";
import { ChatDefinition } from "../../../../models/ChatDefinition";
import { BlockGroupModel } from "../../../../models/BlockGroupModel";
import { FlowModuleResponse } from 'src/app/models/FlowModuleResponse';
import { ModuleDefinition } from 'src/app/models/ModuleDefinition';

@Component({
  selector: 'app-flow-block-selector-dialog',
  templateUrl: './flow-block-selector-dialog.component.html',
  styleUrls: ['./flow-block-selector-dialog.component.scss']
})
export class FlowBlockSelectorDialogComponent implements OnInit {  loading: boolean = true;
  flowTitleParam: any = null;
  showWarningAnotherChannel: boolean = false;
  publishedFlows: FlowDefinition[];
  stagingFlows: FlowDefinition[];
  @Input() FlowToIgnore: number = null;
  @Input() CurrentChannel: string = null;
  DefaultBlocks: BlockDefinition[] = null;
  SelectedBlock: BlockDefinition = null;  
  SelectedFlow: FlowDefinition = null;
  Definition: ChatDefinition = null;
  BlockGroups: BlockGroupModel[] = null;
  currentModule: ModuleDefinition = null;
  modules: ModuleDefinition[] = null;
  
  @Output() AcceptAction: EventEmitter<BlockDefinition> = new EventEmitter<BlockDefinition>();
  constructor(
    private readonly modalService: ModalService, 
    private readonly editorService: EditorService, 
    private readonly serverService: ServerService, 
    private readonly cdRef: ChangeDetectorRef
  ) {
  }
  ngOnInit() {
    this.loading = true;
    
    this.serverService.getFlows()
      .pipe(finalize(() => { this.loading = false; }))
      .subscribe((flows: FlowDefinition[]) => {
        this.publishedFlows = flows.filter(f => f.ActiveProductionVersion != null && (this.FlowToIgnore === null || this.FlowToIgnore !== f.id));
        this.stagingFlows = flows.filter(f => f.ActiveStagingVersion != null && (this.FlowToIgnore === null || this.FlowToIgnore !== f.id) && f.master_flow_id === null);
      },
        error => {
          this.modalService.init(ErrorPopupComponent, {}, {});
        });
  }

  closePopup() {
    this.modalService.removeLastElement();
  }
  onFlowSelected(flow: FlowDefinition, isStaging: boolean) {
    this.flowTitleParam = { flow: flow.name };

    let blob = isStaging ? flow.ActiveStagingVersion.blob : flow.ActiveProductionVersion.blob;

    if (typeof (blob) === 'undefined') {
      this.loading = true;

      this.serverService.getFlowModules(flow.id)        
      .pipe(finalize(() => {
        })).subscribe((response: FlowModuleResponse) => {
          if (response !== null) {
            let master = new ModuleDefinition();
            master.createMaster();
            master.id = Number(flow.id);            
            response.flows = response.flows.map(flow => {
              let module = new ModuleDefinition();
              module.init(flow);
              return module;
            });            
            response.flows.push(master);              
            this.currentModule = master;
            this.modules = response.flows;

            this.serverService.getFlow(flow.id)
              .pipe(finalize(() => { this.loading = false; }))
                   .subscribe((downloadedFlow: FlowDefinition) => {
                blob = isStaging ? downloadedFlow.ActiveStagingVersion.blob : downloadedFlow.ActiveProductionVersion.blob;
                this.Definition = this.editorService.recoverComplexPieces(TypedJSON.parse(blob, ChatDefinition), downloadedFlow.channel);                
                this.Definition = this.editorService.setModulesToFlow(response.flows, this.Definition);
                this.DefaultBlocks = this.Definition.DefaultBlocks;
                this.BlockGroups = this.Definition.BlockGroups;

                // Fix para corregir problemas de referencia de blockgroups
                if (this.BlockGroups) { 
                  for (let i = 0; i < this.BlockGroups.length; i++) {
                    if (this.BlockGroups[i] && this.BlockGroups[i].Blocks) { // Add null check for group and group.Blocks
                      for (let j = 0; j < this.BlockGroups[i].Blocks.length; j++) {
                        const originalBlockInGroup = this.BlockGroups[i].Blocks[j];
                        if (originalBlockInGroup && this.Definition && this.Definition.BlockList) { // Add null checks
                          this.BlockGroups[i].Blocks[j] = this.Definition.BlockList.find(b => b && b.Id === originalBlockInGroup.Id);
                          if (!this.BlockGroups[i].Blocks[j]) {
                              console.warn(`[FlowBlockSelector] Could not find block with Id ${originalBlockInGroup.Id} in Definition.BlockList for group ${this.BlockGroups[i].Name}. Keeping original partial block.`);
                              this.BlockGroups[i].Blocks[j] = originalBlockInGroup; // Restore original if not found
                          }
                        }
                      }
                    }
                  }                }
                  this.showWarningAnotherChannel = (this.CurrentChannel !== downloadedFlow.channel);
                this.SelectedFlow = downloadedFlow;
                this.cdRef.detectChanges();
              });          
            } else {
            this.loading = false;
          }
        },
          error => {
            this.modalService.init(ErrorPopupComponent, {}, {});
          });
    }    else {
      // Validar y corregir estructura antes de deserializar
      let jsonData = JSON.parse(blob);
      if (!jsonData.BlockGroups) {
        jsonData.BlockGroups = [];
      }
      if (!jsonData.BlockList) {
        jsonData.BlockList = [];
      }
      this.Definition = this.editorService.recoverComplexPieces(TypedJSON.parse(JSON.stringify(jsonData), ChatDefinition), flow.channel);
      this.DefaultBlocks = this.Definition.DefaultBlocks;
      this.BlockGroups = this.Definition.BlockGroups;

      // Fix para corregir problemas de referencia de blockgroups
      if (this.BlockGroups) {
        for (let i = 0; i < this.BlockGroups.length; i++) {
          if (this.BlockGroups[i] && this.BlockGroups[i].Blocks) {
            for (let j = 0; j < this.BlockGroups[i].Blocks.length; j++) {
              const originalBlockInGroup = this.BlockGroups[i].Blocks[j];
              if (originalBlockInGroup && this.Definition && this.Definition.BlockList) {
                this.BlockGroups[i].Blocks[j] = this.Definition.BlockList.find(b => b && b.Id === originalBlockInGroup.Id);
                if (!this.BlockGroups[i].Blocks[j]) {
                    console.warn(`[FlowBlockSelector] Could not find block with Id ${originalBlockInGroup.Id} in Definition.BlockList for group ${this.BlockGroups[i].Name}. Keeping original partial block.`);
                    this.BlockGroups[i].Blocks[j] = originalBlockInGroup;
                }
              }
            }
          }
        }
      }
      
      this.showWarningAnotherChannel = (this.CurrentChannel !== flow.channel);
      this.SelectedFlow = flow;
      this.cdRef.detectChanges();
    }
  }

  onBlockSelected(block: BlockDefinition) {
    this.SelectedBlock = block;
  }

  acceptPopup() {
    if (this.SelectedBlock === null) {
      return;
    }

    this.AcceptAction.emit(this.SelectedBlock);
    this.modalService.destroy();
  }    
  goBack() {
    this.SelectedBlock = null;
    this.SelectedFlow = null;
    this.cdRef.detectChanges();
  }
}
