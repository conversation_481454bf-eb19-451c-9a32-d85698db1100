@import '_variables';
@import '_mixins';

:host > div {
  display: table-cell;
  vertical-align: middle;
  border-bottom: 1px solid $sidebarBorderColor;
  padding-left: 5px;
  padding-right: 5px;
}

:host:last-child {
  & > div {
    border-bottom: 1px none $sidebarBorderColor;
  }
}

.name {
  width: 200px;
  input {
    width: 100%;
    font-family: $fontFamilyMono;
  }
}

.type {
  width: 200px;
  select {
  }
}

.ok {
  width: 30px;

  & > div {
    margin: 0;
    width: 26px;
    height: 26px;
    background: #ffffff none;
    box-shadow: 0 2px 4px 0 rgba(0, 0, 0, 0.07);
    border: solid 1px #aaa;
    border-radius: 13px;
    opacity: 0;
    font-size: 12px;
    text-align: center;
    color: #bbb;
    z-index: 1400;
    cursor: pointer;
    line-height: 24px;

    & > span {
      line-height: 24px;
    }

    &:hover {
      color: #003d0d;
    }
  }
}

.trash {
  width: 30px;

  & > div {
    @include trash;
    cursor: pointer;

    &:hover {
      color: #555;
    }
  }
}

:host:hover {
  .trash {
    & > div {
      @include trashOver;
    }
  }
  .ok {
    & > div {
      opacity: 1;
    }
  }
}
