import { Component, OnInit, Input, Output, EventEmitter } from '@angular/core';
import { VariableDefinition } from '../../../../models/VariableDefinition';
import { TypeDefinition } from "../../../../models/TypeDefinition";
import { EditorService } from "../../../../services/editor.service";
import { Integration } from "../../../../models/integration/Integration";
import { _MatTabHeaderMixinBase } from '@angular/material/tabs/typings/tab-header';

interface BooleanOptions {
  name: string;
  value: boolean
}

@Component({
  selector: 'app-inputs',
  templateUrl: './inputs.component.html',
  styleUrls: ['./inputs.component.scss']
})
export class InputsComponent implements OnInit {

  @Input() value: VariableDefinition;
  @Input() disabled: false;
  @Input() selectedIntegration: Integration;
  @Input() pending: boolean = false;
  @Input() isInUse: boolean = false;
  @Output() onFinish: EventEmitter<any> = new EventEmitter();

  @Output() onDelete = new EventEmitter();

  variableTypes: any[] = null;
  booleanOptions: BooleanOptions[] = [{ name: "INTEGRATION_INPUTFIELDS_YES", value: true }, { name: "INTEGRATION_INPUTFIELDS_NO", value: false }]

  constructor() {
  }

  finish() {
    if (!this.isInvalidValidName(this.value.Name, this.value.Id)) {
      this.pending = false;
      this.onFinish.emit();
    }
  }

  getVariablesTypes() {
    return this.variableTypes;
  }

  booleanOpts() {
    return this.booleanOptions;
  }

  empty(str: string): Boolean {
    return str == null || str.length == 0;
  }

  isInvalidValidName(str, id) {
    if (this.empty(str)) {
      return true;
    }

    if (str === 'index' || str === 'item') {
      return true;
    }

    if (this.selectedIntegration !== null &&
      this.selectedIntegration.inputs !== null) {
      var found: VariableDefinition;
      if ((found = this.selectedIntegration.inputs.find(i => i.Name === str)) != null &&
        found.Id != id) {
        return true;
      }
    }

    let pattern = /^[a-zA-Z][a-zA-Z0-9_]{2,}$/;
    if (!pattern.test(str)) {
      return true;
    }
    return false;
  }

  ngOnInit() {
    this.variableTypes = VariableDefinition.variableType.filter(v => {
      return v.value !== TypeDefinition.Any &&
        v.value !== TypeDefinition.Object &&
        v.value !== TypeDefinition.Array &&
        v.value !== TypeDefinition.ByteArray &&
        v.value !== TypeDefinition.Base64
    });
  }

  delete() {
    this.onDelete.emit();
  }
}
