import { Component, OnInit, Input, Output, EventEmitter, ElementRef } from '@angular/core';
import { IntegrationHeader } from '../../../../models/integration/IntegrationHeader';
import { VariableDefinition } from '../../../../models/VariableDefinition';

@Component({
  selector: 'app-headers',
  templateUrl: './headers.component.html',
  styleUrls: ['./headers.component.scss']
})
export class HeadersComponent implements OnInit {

  @Input() header : IntegrationHeader;  
  @Input() variableFinder : Function;
  @Input() variableCreator : Function;
  @Input() integrationInputs : Array<VariableDefinition>;
  @Input() disabled : boolean = false;
  @Output() removeHeader = new EventEmitter();  

  constructor() { }

  ngOnInit() {
  }

  onDelete() {
    this.removeHeader.emit();
  }

  empty(str : string) : boolean {
    return str == null || str.length == 0;
  }
}
