<div class="name">
  <input class="input" type="text" [disabled]="disabled" placeholder="{{ 'HEADER_NAME' | translate }}" [(ngModel)]="header.key" [ngClass]="{'invalid-input': empty(header.key)}" list="knownheaders">
</div>
<div class="value">
  <app-input-with-variables
    [placeholder]="'HEADER_VALUE' | translate"
    [extendedStyles]="{ 'width': '100%' }"
    [disabled]="disabled"
    [(value)]="header.value"
    [variableFinder]="variableFinder"
    [variableCreator]="variableCreator"
    [wideInput]="true"
    [customVariableList]="integrationInputs"></app-input-with-variables>
</div>
<div class="trash" *ngIf="!disabled">
  <div (click)="onDelete()" data-toggle="tooltip" ngbTooltip="{{ 'HEADER_DELETE' | translate }}" placement="top" tooltipClass="tooltip-trash">
    <span class="fa fa-trash-alt"></span>
  </div>
</div>
