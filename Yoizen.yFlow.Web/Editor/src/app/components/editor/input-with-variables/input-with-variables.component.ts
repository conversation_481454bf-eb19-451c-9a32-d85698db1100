import { ModalService } from './../../../services/Tools/ModalService';
import {Component, OnInit, Input, Output, EventEmitter, Renderer2, ViewChild, ElementRef} from '@angular/core';
import {VariableDefinition} from '../../../models/VariableDefinition';
import {EditorService} from '../../../services/editor.service';
import {VariableInputValidator} from '../../../Utils/variable-input-validator';
import {DomSanitizer} from "@angular/platform-browser";
import { FunctionModalComponent } from '../popups/function-modal/function-modal.component';

@Component({
  selector: 'app-input-with-variables',
  templateUrl: './input-with-variables.component.html',
  styleUrls: ['./input-with-variables.component.scss']
})
export class InputWithVariablesComponent implements OnInit {
  withFocus: boolean = false;
  inputValue: string = null;
  currentVarName: string = null;
  OpenVariableChar = '{';
  CloseVariableChar = '}';
  ScriptCharacter = '$';
  variableStartPos: number = -1;
  variableValidator: VariableInputValidator;
  lastCursorPos: number = -1;

  @Output() valueChange: EventEmitter<string> = new EventEmitter<string>();
  @Output() blur: EventEmitter<any> = new EventEmitter<any>();
  @Input() set value(value) {
    this.inputValue = value;
  }

  @Input() placeholder: string = '';
  @Input() variableFinder: Function;
  @Input() variableCreator: Function;
  @Input() validator: Function;
  @Input() customVariableList: Array<VariableDefinition>;
  @Input() wideInput: boolean;
  @Input() isTextArea: boolean;
  @Input() canCreate: boolean = false;
  @Input() JoinCustomVariable: boolean = false;
  @Input() extendedStyles: {
    [key: string]: string;
  } = null;
  @Input() minRows: number = 3;
  @Input() maxRows: number = null;
  @Input() disabled: boolean = false;
  @Input() spellCheck: boolean = false;
  @Input() isHtml: boolean = false;
  @Input() list: string = null;
  @Input() insideHelp: boolean = false;
  showVarPicker: boolean = false;

  @ViewChild('varPicker', { static: false }) VarPicker: ElementRef;
  @ViewChild('inputArea', { static: false }) InputArea: ElementRef;
  @ViewChild('maskArea', { static: false }) MaskArea: ElementRef;

  constructor(private editorService: EditorService, private modalService: ModalService, private renderer: Renderer2, private sanitizer: DomSanitizer) {

  }

  ngOnInit() {
    this.variableValidator = new VariableInputValidator(this.variableFinder ?
      this.variableFinder :
      this.editorService.findVariablesAndImplicitsWithName.bind(this.editorService));
  }

  getInputEditPos(): number {
    if (this.InputArea.nativeElement.selectionStart !== this.InputArea.nativeElement.selectionEnd) {
      return -1;
    }
    return this.InputArea.nativeElement.selectionStart;
  }

  testInputForVariable(text: string) {
    this.lastCursorPos = this.getInputEditPos();
    this.valueChange.emit(text);
    if (this.variableStartPos > -1) {
      this.currentVarName = text.substr(this.variableStartPos);
    }
    else
      this.currentVarName = null;

    if (this.lastCursorPos < 2) return;
    if (text[this.lastCursorPos - 1] === this.OpenVariableChar &&
      text[this.lastCursorPos - 2] === this.OpenVariableChar) {

      this.showVarPicker = true;
      //this.renderer.removeClass(this.VarPicker.nativeElement, 'hide');
      this.variableStartPos = this.lastCursorPos;
    }
    else if (text[this.lastCursorPos - 1] === this.CloseVariableChar &&
      text[this.lastCursorPos - 2] === this.CloseVariableChar &&
      this.variableStartPos > 0) {

      //this.renderer.addClass(this.VarPicker.nativeElement, 'hide');
      this.showVarPicker = false;
      this.currentVarName = null;

      var varName = text.substr(this.variableStartPos, this.lastCursorPos - this.variableStartPos - 2)
      var isNew = this.variableFinder ? this.variableFinder(varName) == null : this.editorService.findVariablesAndImplicitsWithName(varName) == null;
      if (isNew && this.canCreate) {
        if (this.variableCreator)
          this.variableCreator(varName);
        else
          this.editorService.createNewVariableWithName(varName);
      }
      this.variableStartPos = -1;
    }
  }

  onInputFocusIn() {

    if (this.inputValue == null)
      return;

    this.testInputForVariable(this.inputValue);
  }

  focusInputArea() {
    if (this.disabled || this.withFocus) {
      return;
    }
    this.withFocus = true;
    if (typeof(this.MaskArea) !== 'undefined' && this.MaskArea !== null) {
      setTimeout(() => {
        var maskArea = this.MaskArea.nativeElement;
        var inputArea = this.InputArea.nativeElement;
        inputArea.scrollTop = maskArea.scrollTop;
        inputArea.focus();
      }, 100);
    }
  }

  onInputFocusOut() {
    if (this.disabled) {
      return;
    }
    this.withFocus = false;
    this.blur.emit();
    setTimeout(() => {
      //this.renderer.addClass(this.VarPicker.nativeElement, 'hide');
      this.showVarPicker = false;
      if (typeof(this.MaskArea) !== 'undefined' && this.MaskArea !== null) {
        var maskArea = this.MaskArea.nativeElement;
        var inputArea = this.InputArea.nativeElement;
        maskArea.scrollTop = inputArea.scrollTop;
      }
    }, 100);
  }

  onSelectVariable(value: VariableDefinition) {
    const finalName = value.Name;
    var missingCharactes = finalName.substr(this.lastCursorPos - this.variableStartPos, finalName.length) + this.CloseVariableChar + this.CloseVariableChar;

    this.inputValue = [this.inputValue.slice(0, this.lastCursorPos), missingCharactes, this.inputValue.slice(this.lastCursorPos)].join('');
    this.valueChange.emit(this.inputValue);

    //this.renderer.addClass(this.VarPicker.nativeElement, 'hide');
    this.showVarPicker = false;

    let newPos = this.lastCursorPos + missingCharactes.length;

    setTimeout(() => {
      this.InputArea.nativeElement.focus();
      this.InputArea.nativeElement.setSelectionRange(newPos, newPos);
    }, 500);

  }

  validate(str: string): boolean {
    if (this.validator) {
      return this.validator(str) && this.variableValidator.isValidText(str);
    }
    else {
      return this.variableValidator.isValidText(str);
    }
  }

  parseInputText(): string {
    if (typeof(this.inputValue) === 'undefined' || this.inputValue === null) {
      return '';
    }

    let inputValue = this.inputValue;
    //if (this.isHtml) {
      var htmlEscapes = {
        '&': '&amp;',
        '<': '&lt;',
        '>': '&gt;',
        '"': '&quot;',
        "'": '&#x27;',
        '/': '&#x2F;'
      };

      // Regex containing the keys listed immediately above.
      var htmlEscaper = /[&<>"'\/]/g;

      // Escape a string for HTML interpolation.
      inputValue = inputValue.replace(htmlEscaper, function (match) {
        return htmlEscapes[match];
      });
    //}

    const regex = /\{\{[a-zA-Z][a-zA-Z0-9_]{2,}\}\}/gm;

    var scriptRegex = new RegExp(/\${.+?}\$/g);
    let that = this;

    let processedText = inputValue.replace(regex, function (match) {
      let variableName = match.substr(2, match.length - 4);
      let variable = that.variableFinder ? that.variableFinder(variableName) : that.editorService.findVariablesAndImplicitsWithName(variableName);
      if (typeof(variable) !== 'undefined' && variable !== null) {
        return '<span class="variable">' + match + '</span>';
      }
      else {
        return '<span class="variable inexistent">' + match + '</span>';
      }
    });

    processedText = processedText.replace(scriptRegex, function (match) {
      return '<span class="script">' + match + '</span>';
    });

    return processedText;
  }

  openFunctionModal() {
    let action = new EventEmitter();
    action.subscribe((txtFunction: string) => {
      this.inputValue += txtFunction;
    });
    this.modalService.init(FunctionModalComponent, {}, {ReturnFunctionAction: action});
  }
}
