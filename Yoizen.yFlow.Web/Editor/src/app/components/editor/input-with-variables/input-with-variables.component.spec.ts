import { async, ComponentFixture, TestBed } from '@angular/core/testing';

import { InputWithVariablesComponent } from './input-with-variables.component';

describe('InputWithVariablesComponent', () => {
  let component: InputWithVariablesComponent;
  let fixture: ComponentFixture<InputWithVariablesComponent>;

  beforeEach(async(() => {
    TestBed.configureTestingModule({
      declarations: [ InputWithVariablesComponent ]
    })
    .compileComponents();
  }));

  beforeEach(() => {
    fixture = TestBed.createComponent(InputWithVariablesComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });
});
