.app-input-with-variables {
  display: inline-block;
  position: relative;

  &:not(.multiline) {
    height: 30px;
  }

  .help {
    position: absolute;
    margin: 0;
    width: 26px;
    height: 26px;
    background: #ffffff none;
    box-shadow: 0 2px 4px 0 rgba(0, 0, 0, 0.07);
    border: solid 1px #aaa;
    border-radius: 13px;
    opacity: 0;
    font-size: 12px;
    text-align: center;
    color: #bbb;
    z-index: 1400;
    cursor: pointer;
    line-height: 24px;
    right: -13px;
    bottom: -13px;

    & > span {
      line-height: 24px;
    }

    &:hover {
      color: rgb(6, 3, 167);
      border-color: #666;
    }
    right: -15px;
    top: 15px;
  }

  .picker-container  {
    /*position: fixed;*/
    z-index: 10;
  }

  textarea, .textarea-mask {
    height: 200px;
    resize: none;
  }

  &:hover {
    .help {
      opacity: 1;
    }
  }

  &.disabled {
    .textarea-mask, .input-mask {
      background-color: rgb(235, 235, 228);
    }
  }

  &.wide-input {
    width: 100%;

    input, textarea, .input-mask, .textarea-mask {
      width: 100%;
    }

    .input-mask {
      max-width: 100%;
      overflow: hidden;
      display: flex;
      align-items: center;

      & > span {
        width: 100%;
        word-break: break-all;
        overflow: hidden;
        height: 100%;
        line-height: 27px;

        ::ng-deep & > .script, ::ng-deep & > .variable {
          line-height: 27px;
        }
      }
    }
  }
}
