import { Component, Input, OnInit } from '@angular/core';
import { EditorService } from '../../../../services/editor.service';
import { TranslateService } from '@ngx-translate/core';
import { Greetings } from "../../../../models/Greeting";
import { VariableDefinition } from "../../../../models/VariableDefinition";
import { TypeDefinition } from "../../../../models/TypeDefinition";
import { ChannelTypes } from "../../../../models/ChannelType";
import { FlowDefinition } from "../../../../models/FlowDefinition";

@Component({
  selector: 'app-greetings',
  templateUrl: './greetings.component.html',
  styleUrls: ['./greetings.component.scss']
})
export class GreetingsComponent implements OnInit {
  model: Greetings;
  implicitVariables: VariableDefinition[] = [];
  supportImplicitVariables: boolean = false;
  flow: FlowDefinition;
  @Input() readOnly: boolean = false;

  constructor(private editorService: EditorService, private translateService: TranslateService) {
  }

  ngOnInit() {
    this.model = this.editorService.getGreetings();
    this.flow = this.editorService.getCurrentFlow();

    switch (this.flow.channel) {
      case ChannelTypes.FacebookMessenger:
        this.supportImplicitVariables = true;
        this.implicitVariables = [];
        this.implicitVariables.push({ Id: 0, Name: 'user_first_name', Description: 'CONFIGURATION_GREETINGS_VARIABLE_USERFIRSTNAME', DefaultValue: null, Type: TypeDefinition.Text, Constant: true, Private: false, SendYSmart: false, Persist: true });
        this.implicitVariables.push({ Id: 1, Name: 'user_last_name', Description: 'CONFIGURATION_GREETINGS_VARIABLE_USERLASTNAME', DefaultValue: null, Type: TypeDefinition.Text, Constant: true, Private: false, SendYSmart: false, Persist: true });
        this.implicitVariables.push({ Id: 2, Name: 'user_full_name', Description: 'CONFIGURATION_GREETINGS_VARIABLE_USERFULLNAME', DefaultValue: null, Type: TypeDefinition.Text, Constant: true, Private: false, SendYSmart: false, Persist: true });
        break;
      case ChannelTypes.Chat:
        this.supportImplicitVariables = true;
        this.implicitVariables = [];
        this.implicitVariables.push({ Id: 0, Name: 'user_name', Description: 'CONFIGURATION_GREETINGS_VARIABLE_CHAT_USERNAME', DefaultValue: null, Type: TypeDefinition.Text, Constant: true, Private: false, SendYSmart: false, Persist: true });
        break;
    }
  }

  getVariableType(variable: VariableDefinition): string {
    for (let i = 0; i < VariableDefinition.variableType.length; i++) {
      if (variable.Type === VariableDefinition.variableType[i].value) {
        return VariableDefinition.variableType[i].localized;
      }
    }

    return '';
  }

  searchForVariable(varName) {
    if (this.implicitVariables.length == 0) {
      return null;
    }
    return (this.implicitVariables.some(varDef => varDef.Name == varName) ? true : null);
  }

  isValid(): boolean {
    if (this.flow.channel === ChannelTypes.Chat) {
      if (this.model.Locales === null ||
        this.model.Locales.length === 0 ||
        this.model.Locales[0].text.length === 0) {
        return false;
      }
    }

    return true;
  }
}
