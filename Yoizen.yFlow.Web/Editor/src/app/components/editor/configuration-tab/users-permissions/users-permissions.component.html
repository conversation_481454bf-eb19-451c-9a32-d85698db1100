<div class="configurable-item">
  <div class="title">{{ 'USERS' | translate }}</div>
  <div class="description">{{'USERS_PERMISSIONS_DESCRIPTION' | translate}}</div>
  <div class="users">
    <action-spinner class="spinner" *ngIf="loadingUsers"></action-spinner>
    <div *ngIf="!loadingUsers">
      <div class="empty" *ngIf="usersPermissions === null || usersPermissions.length === 0" role="alert">
        <div class="alert alert-info">
          <span class="fa fa-exclamation-circle icon"></span>
          {{ 'USERS_PERMISSIONS_EMPTY' | translate }}
        </div>
      </div>
      <div class="users-table" *ngIf="usersPermissions !== null && usersPermissions.length > 0">
        <div class="header">
          <div>{{ 'USER_NAME' | translate }}</div>
          <div class="center">{{ 'USER_CAN_EDIT' | translate }}</div>
          <div class="center">{{ 'USER_CAN_PUBLISH' | translate }}</div>
          <div class="center">{{ 'USER_SEE_STATISTICS' | translate }}</div>
        </div>
        <div class="row" *ngFor="let user of usersPermissions">
          <div>{{ user.username }}</div>
          <div class="center" (dblclick)="toggleEdit(user)">
            <span class="fa fa-check-circle icon-green circle-button" *ngIf="user.canEdit"></span>
            <span class="fa fa-times-circle icon-red circle-button" *ngIf="!user.canEdit"></span>
          </div>
          <div class="center" (dblclick)="togglePublish(user)">
            <span class="fa fa-check-circle icon-green circle-button" *ngIf="user.canPublish"></span>
            <span class="fa fa-times-circle icon-red circle-button" *ngIf="!user.canPublish"></span>
          </div>
          <div class="center" (dblclick)="toggleStatistics(user)">
            <span class="fa fa-check-circle icon-green circle-button" *ngIf="user.canSeeStatistics"></span>
            <span class="fa fa-times-circle icon-red circle-button" *ngIf="!user.canSeeStatistics"></span>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>
