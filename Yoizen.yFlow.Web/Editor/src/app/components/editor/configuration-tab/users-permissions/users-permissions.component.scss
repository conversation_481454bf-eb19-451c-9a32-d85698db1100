@import "_variables";
@import "_mixins";

.configurable-item {
  @include configurable-item;

  .users {
    .users-table {
      display: table;
      width: 100%;

      .header {
        display: table-header-group;
        font-family: $fontFamilyTitles;
        font-weight: bold;

        &>div {
          display: table-cell;
          vertical-align: middle;
          border-bottom: 1px solid $sidebarBorderColor;
          padding-left: 5px;
          padding-right: 5px;

          &.center {
            text-align: center;
            width: 130px;
          }
        }
      }

      .row {
        display: table-row;
        height: 40px;

        .center {
          .circle-button {
            cursor: pointer;
          }
        }

        &:hover {
          background: lighten($version-highlight, 10%);
        }

        &>div {
          display: table-cell;
          vertical-align: middle;
          border-bottom: 1px solid $sidebarBorderColor;
          padding-left: 5px;
          padding-right: 5px;

          &.center {
            text-align: center;
            width: 130px;
          }
        }

        &:last-child {
          &>div {
            border-bottom: 1px none $sidebarBorderColor;
          }
        }
      }
    }
  }
}
