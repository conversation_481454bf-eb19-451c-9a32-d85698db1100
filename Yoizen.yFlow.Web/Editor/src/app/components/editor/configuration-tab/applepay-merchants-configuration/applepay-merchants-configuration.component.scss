@import "_variables";
@import "_mixins";

.configurable-item {
  @include configurable-item;

  .merchants {
    .merchants-table {
      display: table;
      width: 100%;

      .header {
        display: table-header-group;
        font-family: $fontFamilyTitles;
        font-weight: bold;

        & > div {
          display: table-cell;
          vertical-align: middle;
          border-bottom: 1px solid $sidebarBorderColor;
        }
      }

      .row {
        display: table-row;
        width: 100%;
        margin-top: 5px;
        height: 40px;
        min-height: 40px;

        & > div {
          display: table-cell;
          vertical-align: middle;
          border-bottom: 1px solid $sidebarBorderColor;
          padding-left: 3px;
          padding-right: 3px;

          &:last-child {
            width: 40px;
          }
        }

        .icons {
          width: 60px;
          display: flex;
          flex-direction: row;
          align-items: center;

          & > div {
            @include trash;
            cursor: pointer;
            margin-left: 3px;

            &:hover {
              color: #555;
            }

            &:first-child {
              margin-left: 0;
            }
          }
        }

        &:hover {
          .icons {
            & > div {
              @include trashOver;
            }
          }
        }
      }
    }

    .add {
      color: $linkActionColor;
      text-transform: uppercase;
      font-size: 12px;
      margin: 20px 10px;
      width: max-content;
      cursor: pointer;

      &:hover {
        color: lighten($linkActionColor, 10%);
      }
    }
  }
}
