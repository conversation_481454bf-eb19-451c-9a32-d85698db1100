import { async, ComponentFixture, TestBed } from '@angular/core/testing';

import { ApplepayMerchantsConfigurationComponent } from './applepay-merchants-configuration.component';

describe('ApplepayMerchantsConfigurationComponent', () => {
  let component: ApplepayMerchantsConfigurationComponent;
  let fixture: ComponentFixture<ApplepayMerchantsConfigurationComponent>;

  beforeEach(async(() => {
    TestBed.configureTestingModule({
      declarations: [ ApplepayMerchantsConfigurationComponent ]
    })
    .compileComponents();
  }));

  beforeEach(() => {
    fixture = TestBed.createComponent(ApplepayMerchantsConfigurationComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });
});
