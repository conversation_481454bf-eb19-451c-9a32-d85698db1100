<div class="configurable-item">
  <div class="title">
    {{ 'CONFIGURATION_STRING_FORMAT_TITLE' | translate }}
  </div>
  <div class="description">
    {{ 'CONFIGURATION_STRING_FORMAT_DESCRIPTION' | translate }}
  </div>
  <div class="defaults">
    <div class="data">
      <span class="title">{{ 'CONFIGURATION_STRING_FORMAT_DECIMAL_SEPARATOR' | translate }}:</span>
      <input type="text"
             class="input"
             maxlength="1"
             [(ngModel)]="defaultSettings.DefaultDecimalSeparator"
             spellcheck="false"
             [placeholder]="'CONFIGURATION_STRING_FORMAT_DECIMAL_SEPARATOR' | translate"
             [disabled]="readOnly" />
    </div>
    <div class="data">
      <span class="title">{{ 'CONFIGURATION_STRING_FORMAT_THOUSANDS_SEPARATOR' | translate }}:</span>
      <input type="text"
             class="input"
             maxlength="1"
             [(ngModel)]="defaultSettings.DefaultThousandsSeparator"
             spellcheck="false"
             [placeholder]="'CONFIGURATION_STRING_FORMAT_THOUSANDS_SEPARATOR' | translate"
             [disabled]="readOnly" />
    </div>
    <div class="data">
      <span class="title">{{ 'CONFIGURATION_STRING_FORMAT_CURRENCY_SYMBOL' | translate }}:</span>
      <input type="text"
             class="input"
             [(ngModel)]="defaultSettings.DefaultCurrencySymbol"
             spellcheck="false"
             [placeholder]="'CONFIGURATION_STRING_FORMAT_CURRENCY_SYMBOL' | translate"
             [disabled]="readOnly" />
    </div>
  </div>
  <div class="default-formats">
    <div class="title">
      {{ 'CONFIGURATION_STRING_FORMAT_DEFAULT_TITLE' | translate }}
    </div>
    <div class="formats-table">
      <div class="header">
        <div>{{ 'CONFIGURATION_STRING_FORMAT_HEADER_NAME' | translate }}</div>
        <div>{{ 'CONFIGURATION_STRING_FORMAT_HEADER_TYPES' | translate }}</div>
        <div>{{ 'CONFIGURATION_STRING_FORMAT_HEADER_DEFINITION' | translate }}</div>
        <div>{{ 'CONFIGURATION_STRING_FORMAT_HEADER_EXAMPLEVALUE' | translate }}</div>
        <div>{{ 'CONFIGURATION_STRING_FORMAT_HEADER_RESULTVALUE' | translate }}</div>
      </div>
      <div class="format-row" *ngFor="let format of formatList">
        <div class="format-name">{{ format.name | translate }} (<span class="key">{{ format.key }}</span>)</div>
        <div class="format-type" [innerHTML]="getSupportedTypes(format)"></div>
        <div class="format-definition">
          <div>
            <input class="input format-definition" type="text" [(ngModel)]="format.format"
                   [disabled]="readOnly"
                   spellcheck="false" autocomplete="off"/>
            <div class="info" ngbTooltip="{{ 'CONFIGURATION_STRING_FORMAT_INFO_NUMBER' | translate }}"
                 placement="right" container="body" tooltipClass="tooltip-persistent" *ngIf="format.formatType === formatTypes.Number">
              <span class="fa fa-question-circle"></span>
            </div>
          </div>
        </div>
        <div class="demo-value">{{ getFormatExampleFor(format) }}</div>
        <div class="demo-result">{{ getFormatedDate(format, getFormatExampleFor(format))}}</div>
      </div>
    </div>
  </div>
  <div class="user-formats">
    <div class="title">
      {{ 'CONFIGURATION_STRING_FORMAT_USER_TITLE' | translate }}
    </div>
    <div class="formats-table" *ngIf="userFormatList !== null && userFormatList.length > 0">
      <div class="header">
        <div>{{ 'CONFIGURATION_STRING_FORMAT_HEADER_NAME' | translate }}</div>
        <div>{{ 'CONFIGURATION_STRING_FORMAT_HEADER_TYPES' | translate }}</div>
        <div>{{ 'CONFIGURATION_STRING_FORMAT_HEADER_DEFINITION' | translate }}</div>
        <div>{{ 'CONFIGURATION_STRING_FORMAT_HEADER_EXAMPLEVALUE' | translate }}</div>
        <div>{{ 'CONFIGURATION_STRING_FORMAT_HEADER_RESULTVALUE' | translate }}</div>
        <div></div>
      </div>
      <div class="format-row" *ngFor="let format of userFormatList">
        <div class="format-name">
          <input class="input format-key" type="text" [(ngModel)]="format.key"
                 [disabled]="readOnly"
                 spellcheck="false" autocomplete="off"/>
        </div>
        <div class="format-type">
          <select class="select" name="" id="" [(ngModel)]="format.inputType" [disabled]="readOnly">
            <option *ngFor="let type of getVariablesTypes()" [value]="type.value">{{ type.localized | translate }}</option>
          </select>
        </div>
        <div class="format-definition">
          <input class="input format-definition" type="text" [(ngModel)]="format.format" [disabled]="readOnly"
                 spellcheck="false" autocomplete="off"/>
        </div>
        <div class="demo-value"><span *ngIf="format.inputType !== null">{{ getUserFormatExampleFor(format) }}</span></div>
        <div class="demo-result"><span *ngIf="format.inputType !== null">{{ getFormatedDate(format, getUserFormatExampleFor(format))}}</span></div>
        <div class="trash">
          <div (click)="deleteUserFormat(format)" *ngIf="!readOnly"
               tooltipClass="tooltip-trash-left"
               data-toggle="tooltip" ngbTooltip="{{ 'CONFIGURATION_STRING_FORMAT_USER_REMOVE' | translate }}" placement="left">
            <span class="fa fa fa-trash-alt"></span>
          </div>
        </div>
      </div>
    </div>
    <div class="empty" *ngIf="userFormatList === null || userFormatList.length === 0" role="alert">
      <div class="alert alert-info">
        {{ 'CONFIGURATION_STRING_FORMAT_USER_EMPTY' | translate }}
      </div>
    </div>
    <div class="add" (click)="addUserFormat()" *ngIf="!readOnly">
      <span class="fa fa-plus"></span> {{ 'CONFIGURATION_STRING_FORMAT_USER_ADD' | translate }}
    </div>
  </div>
</div>
