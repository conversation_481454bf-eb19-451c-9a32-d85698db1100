@import "_variables";
@import "_mixins";

.configurable-item {
  @include configurable-item;

  .defaults {
    .data {
      display: flex;
      flex-direction: row;
      align-items: center;
      margin-bottom: 10px;
      width: 100%;

      .title {
        font-family: $fontFamilyTitles;
        font-weight: bold;
        margin-right: 10px;
        justify-self: center;
        align-self: center;
        text-align: center;
        flex-grow: 0;
        flex-shrink: 0;
      }

      input {
        flex-grow: 1;
        flex-shrink: 1;
      }

      ui-switch {
        height: 20px;
      }

      &.separator {
        border-top: 1px solid $cardSeparatorBorderColor;
        padding-top: 5px;
        margin-bottom: 5px;
      }

      &:last-child {
        margin-bottom: 0;
      }
    }
  }

  .default-formats, .user-formats {
    margin-top: 10px;

   & > .title {
     font-family: $fontFamilyTitles;
     font-weight: bold;
     font-size: 120%;
     margin-bottom: 5px;
   }

    .formats-table {
      display: table;
      width: 100%;

      .header {
        display: table-header-group;
        font-family: $fontFamilyTitles;

        & > div {
          display: table-cell;
          vertical-align: middle;
          border-bottom: 1px solid $sidebarBorderColor;
        }
      }

      .format-row {
        display: table-row;
        width: 100%;
        margin-top: 5px;
        height: 40px;
        min-height: 40px;

        & > div {
          display: table-cell;
          vertical-align: middle;
          border-bottom: 1px solid $sidebarBorderColor;
          padding-left: 3px;
          padding-right: 3px;
        }

        .format-name {
          width: 150px;
          .key {
            font-family: $fontFamilyMono;
          }
        }

        .format-type {
          width: 200px;
        }

        .format-definition {
          width: 200px;

          & > div {
            display: flex;
            flex-direction: row;
            align-items: center;
            width: 100%;

            input {
              flex-grow: 1;
              flex-shrink: 1;
            }

            .info {
              margin-left: 5px;
              flex-grow: 0;
              flex-shrink: 0;
            }
          }
        }

        .demo-value {
          width: 150px;
        }

        .demo-result {
          width: 200px;
          color: $linkActionColor;
        }
      }
    }
  }

  .user-formats {
    .format-row {
      .trash {
        width: 30px;

        & > div {
          @include trash;
          cursor: pointer;

          &:hover {
            color: #555;
          }
        }
      }

      &:hover {
        .trash {
          & > div {
            @include trashOver;
          }
        }
      }
    }

    .format-name {
      width: 100px;
      padding-right: 10px;
      .format-key {
        font-family: $fontFamilyMono;
        width: 100%;
      }
    }

    .add {
      color: $linkActionColor;
      text-transform: uppercase;
      font-size: 12px;
      margin: 20px 10px;
      width: max-content;
      cursor: pointer;

      &:hover {
        color: lighten($linkActionColor, 10%);
      }
    }
  }
}
