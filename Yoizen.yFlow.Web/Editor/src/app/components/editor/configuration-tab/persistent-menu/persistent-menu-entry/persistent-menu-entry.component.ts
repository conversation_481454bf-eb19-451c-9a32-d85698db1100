import { Component, OnInit, ViewChild, Input, Output, EventEmitter, ElementRef } from '@angular/core';
import { ButtonPiece, ButtonType } from '../../../../../models/pieces/ButtonPiece';
import { BlockDefinition } from '../../../../../models/BlockDefinition';
import { EditorService } from '../../../../../services/editor.service';
import { PersistentMenuEntry } from '../../../../../models/PersistentMenuEntry';
import {TranslateService} from "@ngx-translate/core";

@Component({
  selector: 'app-persistent-menu-entry',
  templateUrl: './persistent-menu-entry.component.html',
  styleUrls: ['./persistent-menu-entry.component.scss']
})
export class PersistentMenuEntryComponent implements OnInit {

  @ViewChild('buttonDetail', {read: ElementRef, static: false}) ButtonDetail : ElementRef;
  @Input() readOnly : boolean = false;
  @Input() Model : ButtonPiece;
  @Input() Index : number;
  @Input() expandedBtn : ButtonPiece;
  @Input() DisplayCreateMenuOption: boolean = false;
  @Input() AllowedButtonTypes: ButtonType[] = null;
  @Output() onDelete : EventEmitter<number> = new EventEmitter<number>();
  @Output() onShowDetail : EventEmitter<ButtonPiece> = new EventEmitter<ButtonPiece>();
  @Output() onExpandMenu: EventEmitter<number> = new EventEmitter<number>();

  public get HideDetail(): boolean {
    return this.expandedBtn != this.Model;
  }

  public get ReferencedBlock(): BlockDefinition {
    return this.editorService.findBlockWithId( this.Model.BlockID);
  }

  constructor(private editorService : EditorService, private translateService: TranslateService) { }

  ngOnInit() {
  }

  onClick() {
    this.onShowDetail.emit(this.Model);
  }

  deleteElement() {
  	this.onDelete.emit(this.Index);
  }

  closePopup() {
    this.onShowDetail.emit(null);
  }

  hasSubMenu(): boolean {
    return (this.Model.Type === ButtonType.PersistentMenu) && this.Model.PersistentMenuId != null;
  }

  getName() {
    return this.Model.Name;
  }


  getButtonText() : string {
    if (typeof(this.Model.Name) !== 'undefined' &&
      this.Model.Name !== null) {
      return this.Model.Name;
    }

    return this.translateService.instant('BUTTON_MISSING_NAME');
  }

  withName() : boolean {
    if (typeof(this.Model.Name) !== 'undefined' &&
      this.Model.Name !== null &&
      this.Model.Name.length > 0) {
      return true;
    }

    return false;
  }

  withRedirect() : boolean {
    if (this.Model.Type === ButtonType.Redirect &&
      this.Model.BlockID !== null &&
      this.Model.BlockID !== "-1") {
      return true;
    }

    return false;
  }

  getBlockName() : string {
    let block = this.editorService.findBlockWithId(this.Model.BlockID);
    if (typeof(block) !== 'undefined' &&
      block !== null) {
      return block.Name;
    }
    return '';
  }

  withUrl() : boolean {
    if (this.Model.Type === ButtonType.Url &&
      typeof(this.Model.Url) !== 'undefined' &&
      this.Model.Url !== null &&
      this.Model.Url.length > 0) {
      return true;
    }

    return false;
  }

  withAuthLoginUrl() : boolean {
    if (this.Model.Type === ButtonType.Auth &&
      typeof(this.Model.AuthLoginUrl) !== 'undefined' &&
      this.Model.AuthLoginUrl !== null &&
      this.Model.AuthLoginUrl.length > 0) {
      return true;
    }

    return false;
  }

  withInvalidSubmenu() : boolean {
    if (this.Model.PersistentMenuId === -1) {
      return false;
    }
    let submenu = this.editorService.getMenuWithId(this.Model.PersistentMenuId);
    if (typeof(submenu) === 'undefined' || submenu === null) {
      return false;
    }

    return !submenu.isValid(this.editorService);
  }
}
