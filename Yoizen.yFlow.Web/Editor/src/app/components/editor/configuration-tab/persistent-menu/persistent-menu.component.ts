import {Component, Input, OnInit} from '@angular/core';
import { PersistentMenu } from '../../../../models/PersistentMenu';
import { PersistentMenuEntry } from '../../../../models/PersistentMenuEntry';
import { EditorService } from '../../../../services/editor.service';
import { TranslateService } from '@ngx-translate/core';
import {ChannelTypes} from "../../../../models/ChannelType";
import {FlowDefinition} from "../../../../models/FlowDefinition";
import {ButtonType} from "../../../../models/pieces/ButtonPiece";

@Component({
  selector: 'app-persistent-menu',
  templateUrl: './persistent-menu.component.html',
  styleUrls: ['./persistent-menu.component.scss']
})
export class PersistentMenuComponent implements OnInit {
  model : PersistentMenu;
  flow : FlowDefinition;
  displayMenuList : PersistentMenuEntry[] = [];
  showAllowUserInput : boolean = false;
  tooltipAllowUserInput : string = null;
  showDescriptionMessenger : boolean = false;
  showDescriptionChat : boolean = false;
  @Input() readOnly: boolean = false;

  constructor(private editorService : EditorService, private translateService: TranslateService) {
  }

  ngOnInit() {
    this.model = this.editorService.getPersistentMenu();
    this.displayMenuList = [];
    this.displayMenuList.push( this.editorService.getMenuWithId(this.model.root) );

    this.flow = this.editorService.getCurrentFlow();
    switch (this.flow.channel) {
      case ChannelTypes.FacebookMessenger:
        this.showAllowUserInput = true;
        this.showDescriptionMessenger = true;
        this.tooltipAllowUserInput = 'CONFIGURATION_PERSISTENTMENU_ALLOWUSERINPUT_TOOLTIP_MESSENGER';
        break;
      case ChannelTypes.Chat:
        this.showAllowUserInput = true;
        this.showDescriptionChat = true;
        this.tooltipAllowUserInput = 'CONFIGURATION_PERSISTENTMENU_ALLOWUSERINPUT_TOOLTIP_CHAT';
        break;
    }
  }

  displaySubMenu(menuToexpand:number, index: number) {
    this.displayMenuList.splice( index +1);
    this.displayMenuList.push( this.editorService.getMenuWithId(menuToexpand));
  }

  removeSubMenu(index: number, menu:number) {
    let arrayMenu = this.displayMenuList[index+1]
    if( arrayMenu.id === menu) {
      this.displayMenuList.splice( index +1);
    }
    this.editorService.deletePersistentMenu(menu);
  }

  getButtonLimitForIndex(index: number) : number {
    switch (this.flow.channel) {
      case ChannelTypes.FacebookMessenger:
        return 5;
      case ChannelTypes.Twitter:
        return 20;
      case ChannelTypes.Chat:
        return 5;
      case ChannelTypes.Instagram:
        return 4;
    }
  }

  canCreateSubMenu(index: number) : boolean {
    return false;
  }

  getAllowedButtonTypes() : ButtonType[] {
    switch (this.flow.channel) {
      case ChannelTypes.FacebookMessenger:
        return [ ButtonType.Redirect, ButtonType.Url ];
      case ChannelTypes.Twitter:
        return [ ButtonType.Redirect ];
      case ChannelTypes.Chat:
        return [ ButtonType.Redirect, ButtonType.Url ];
      case ChannelTypes.Instagram:
        return [ ButtonType.Redirect ];
    }

    return [];
  }

  getNameForIndex(index: number) : string{
    return this.translateService.instant('SIDEBAR_PERSISTENT_MENU');
  }
}
