<div class="configurable-item">
  <div class="title">
    {{ 'CONFIGURATION_PERSISTENTMENU_TITLE' | translate }}
  </div>
  <div class="description" *ngIf="showDescriptionMessenger">
    {{ 'CONFIGURATION_PERSISTENTMENU_DESCRIPTION_MESSENGER' | translate }}
  </div>
  <div class="description" *ngIf="showDescriptionChat">
    {{ 'CONFIGURATION_PERSISTENTMENU_DESCRIPTION_CHAT' | translate }}
  </div>
  <div class="controls" *ngIf="showAllowUserInput">
    <div class="input-group">
      <label class="label">{{ 'CONFIGURATION_PERSISTENTMENU_ALLOWUSERINPUT' | translate }}:</label>
      <ui-switch [(ngModel)]="model.allowUserInput" [disabled]="readOnly"
                 color="#45c195" size="small" defaultBgColor="#e0e0e0" switchColor="#ffffff"></ui-switch>
      <div class="info" ngbTooltip="{{ tooltipAllowUserInput | translate }}"
           placement="right" container="body" tooltipClass="tooltip-persistent">
        <span class="fa fa-question-circle"></span>
      </div>
    </div>
  </div>
  <div class="menu-listcontainer">
    <app-persistent-menu-option-list
      *ngFor="let menu of displayMenuList; let index=index"
      class="menu-entry"
      [model]="menu"
      [readOnly]="readOnly"
      (onExpandMenu)="displaySubMenu($event, index)"
      (onRemoveSubMenu)="removeSubMenu(index, $event)"
      [buttonLimit]="getButtonLimitForIndex(index)"
      [AllowedButtonTypes]="getAllowedButtonTypes()"
      [DisplayCreateMenuOption]="canCreateSubMenu(index)"
      [title]="getNameForIndex(index)">
    </app-persistent-menu-option-list>
  </div>
</div>
