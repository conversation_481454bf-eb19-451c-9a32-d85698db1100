<div class="menu-container">
  <app-persistent-menu-entry *ngFor="let button of model?.buttons let i = index"
                             [Model]="button"
                             [Index]="i"
                             [readOnly]="readOnly"
                             (onDelete)="deleteButtonElement($event)"
                             [expandedBtn]="expandButton"
                             (onShowDetail)="onShowButtonDetail($event)"
                             (onExpandMenu)="displaySubMenu($event)"
                             [DisplayCreateMenuOption]="DisplayCreateMenuOption"
                             [AllowedButtonTypes]="AllowedButtonTypes">
  </app-persistent-menu-entry>
  <div class="addButton" (click)="addNewButton(); $event.stopPropagation();" *ngIf="canCreateElement()">
    <span class="fa fa-plus"> </span> {{ 'CARD_MESSAGE_ADD_BUTTON' | translate }}
  </div>
</div>
