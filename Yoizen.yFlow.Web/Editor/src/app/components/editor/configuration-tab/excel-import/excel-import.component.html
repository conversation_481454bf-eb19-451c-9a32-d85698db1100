<div class="configurable-item" [ngStyle]="{'min-height': '200px'}">
  <div class="title">{{ 'EXCELCSV_IMPORT_CONFIGURATION' | translate }}</div>
  <div class="description">{{'EXCELCSV_IMPORT_CONFIGURATION_DESCRIPTION' | translate}}</div>
  <input type="file" accept=".csv" (change)="onChange($event, null, 'Upload')"  #inputFile>
  <!--<div *ngIf="isExcelFile === false && isCsvFile === false">{{'EXCELCSV_IMPORT_ALERT' | translate}}</div>-->
  <div class="alert alert-info">
    <span class="fa fa-lg fa-exclamation-triangle icon"></span>
    {{ 'EXCELCSV_IMPORT_CONTAINSPACE_ALERT' | translate }}
  </div>
  <div class="variables" *ngIf="headers.length > 0">
    <div class="header">
      <div>{{'VARIABLE_NAME' | translate}}</div>
      <div>{{'VARIABLE_TYPE' | translate}}</div>
      <div>{{'VARIABLE_INDEX' | translate}}</div>
    </div>

    <div class="variable" *ngFor="let item of headers; let i = index;">
      <div class="name">
        <input class="input variable-name" type="text" placeholder="{{'VARIABLE_NAME' | translate}}"
               [disabled]="true"
               spellcheck="false"
               autocomplete="off"
               [(ngModel)]="headers[i].variable.Name" />
      </div>
      <div class="type">
        <select class="select" name="" id="" [(ngModel)]="headers[i].variable.Type"
                [disabled]="readOnly">
          <option *ngFor="let type of getVariablesTypes()" [value]="type.value">{{ type.localized | translate }}</option>
        </select>
      </div>
      <div class="index">
        <ui-switch *ngIf="isValidType(headers[i].variable.Type)" [(ngModel)]="headers[i].index" color="#45c195" size="small" defaultBgColor="#e0e0e0" switchColor="#ffffff" (change)="onChangeIndex($event)"></ui-switch>
      </div>
    </div>
  </div>
  <div class="addButton" (click)="uploadExcel()" *ngIf="headers.length > 0 && operationType == 'Upload' && !uploading">{{ 'EXCELCSV_IMPORT_UPLOAD' | translate }}</div>
  <div *ngIf="uploading" class="progress">
    <div class="progress-bar" [ngStyle]="{'width': progressBar()}"></div>
    <p>{{timeLeft}}</p>
  </div>
  <div class="alert alert-info" *ngIf="(flowtablesStatus == null || flowtablesStatus.length == 0)">
    <span class="fa fa-lg fa-exclamation-triangle icon"></span>
    {{ 'EXCELCSV_IMPORT_TABLES_EMPTY' | translate }}
  </div>
  <div class="tables">
    <div class="tables-table" *ngIf="flowtablesStatus !== null && flowtablesStatus.length > 0">
      <div class="header">
        <div>
          {{ 'TABLE_NAME' | translate }}
          <span class="fa fas fa-undo circle-button" style="cursor: pointer;" (click)="refreshTables()"></span>
        </div>
        <div class="center">
          {{ 'TABLE_DELETE' | translate }}{{" "}}
          <input type="checkbox"
            [checked]="isDeleting"
            (change)="toggleIsDeleting()" />
        </div>
        <div class="center">{{ 'TABLE_STATUS' | translate }}</div>
        <div class="center">{{ 'TABLE_UPDATE' | translate }}</div>
        <div class="center">{{ 'VARIABLE_HEADERS' | translate }}</div>
      </div>
      <div class="row" *ngFor="let table of flowtablesStatus; let i = index">
        <div>{{ table.table_name }}</div>
        <div class="center">
          <span (click)="deleteTable(table)" *ngIf="isDeleting && table.status === 'FINISHED'" class="fa fa-trash icon-red circle-button"></span>
        </div>
        <div class="center" >
          <span class="fa fa-check-circle icon-green" *ngIf="table.status == 'FINISHED'"></span>
          <span class="fa fa-times-circle icon-red" *ngIf="table.status == 'ERROR'"></span>
          <span class="fa fa-cloud-upload" *ngIf="table.status == 'PROCESSING'"></span>
        </div>
        <div class="center" >
          <label for={{table.id}}><span class="far fa-arrow-alt-from-bottom circle-button"
            *ngIf="table.status == 'FINISHED' || table.status == 'ERROR'"></span></label>
          <input id={{table.id}} name="i" #fileInput type="file"
            accept=".csv"
            (change)="onChange($event, table, 'Update')">
        </div>
        <div class="center" >
          <div (click)="seeHeaders(table.table_name)"><span class="fa fa-plus circle-button"></span></div>
        </div>
      </div>
    </div>
  </div>
</div>