import { Component, OnInit, Input } from '@angular/core';
import { CommandDefinition } from 'src/app/models/commands/CommandDefinition';
import { BlockDefinition } from 'src/app/models/BlockDefinition';
import { EditorService } from 'src/app/services/editor.service';
import {BaseCondition, ConditionTypes} from "../../../../models/commands/BaseCondition";
import {ConditionGroup} from "../../../../models/commands/ConditionGroup";

@Component({
  selector: 'app-command-definition',
  templateUrl: './command-definition.component.html',
  styleUrls: ['./command-definition.component.scss']
})
export class CommandDefinitionComponent implements OnInit {
  @Input() selectedCommand: CommandDefinition;
  @Input() readOnly : boolean = false;

  constructor(public editorService: EditorService) { }

  ngOnInit() {
  }

  onSelectBlock(blockData) {
    this.selectedCommand.destinationBlockId = blockData.Id;
  }

  onDeleteBlock() {
    this.selectedCommand.destinationBlockId = null;
  }

  calculateDepth(condition: ConditionGroup) : number {
    let depth: number = 1;
    for (let i = 0; i < condition.conditionList.length; i++) {
      let c = condition.conditionList[i];
      if (c.type === ConditionTypes.Group) {
        let cg = <ConditionGroup> c;
        depth += this.calculateDepth(cg);
      }
    }

    return depth;
  }

  onCommandNameChanged(eventData) {
    this.selectedCommand.name = this.editorService.getUniqueCommandName(eventData.target.value, this.selectedCommand);
  }
}
