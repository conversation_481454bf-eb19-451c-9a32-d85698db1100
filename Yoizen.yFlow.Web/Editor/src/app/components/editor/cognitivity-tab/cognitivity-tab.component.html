<div class="cognitivity">
  <div class="card">
    <div class="row">
      <mat-form-field appearance="outline" class="cognitivity-filter col-sm-11">
        <mat-label>{{'SEARCH'|translate}}</mat-label>
        <input matInput type="text" (keyup)="applyFilter($event.target.value)" placeholder="Filter">
      </mat-form-field>
      <button class="btn col-sm-1" type="button" (click)="refresh()"><i class="fa fa-redo"></i></button>
    </div>
  </div>  
  
  <div class="cognitivity-tab">
    <div class="mat-elevation-z2">
      <table mat-table [dataSource]="dataSource" multiTemplateDataRows>
        <ng-container matColumnDef="state">
          <th mat-header-cell *matHeaderCellDef> {{ 'COGNITIVITY_TAB_STATE' | translate }}</th>
          <td mat-cell *matCellDef="let element">
            <div class="invalid" *ngIf="!element.isValid()" style="color: red;">
              <span class="fas fa-circle"></span>
            </div>
          </td>
        </ng-container>

        <ng-container matColumnDef="intent">
          <th mat-header-cell *matHeaderCellDef> {{ 'COGNITIVITY_TAB_INTENT' | translate }}</th>
          <td mat-cell *matCellDef="let element"> {{element.intent.name|formatIntentName}} </td>
        </ng-container>

        <ng-container matColumnDef="defaultBlockId">
          <th mat-header-cell *matHeaderCellDef> {{ 'COGNITIVITY_TAB_DEFAULT_BLOCK' | translate }}</th>
          <td mat-cell *matCellDef="let element">
            <div class="select-block" *ngIf="element.destinationBlockId != 0">
              <app-block-picker class="input" [blockId]="element.destinationBlockId"
                (onSelectNewBlock)="onSelectDestinationBlock($event, element)"
                (onDeleteBlock)="onDeleteDestinationBlock(element)" [readOnly]="readOnly" tooltipPlacement="left"
                tooltipClass="tooltip-gotoblock-left" [isInvalid]="!element.isTargetBlockValid(editorService)">
              </app-block-picker>
            </div>
            <div class="select-block" *ngIf="element.destinationBlockId == 0">
              <app-block-picker class="input" [blockId]="element.destinationBlockId"
                (onSelectNewBlock)="onSelectDestinationBlock($event, element)"
                (onDeleteBlock)="onDeleteDestinationBlock(element)" [readOnly]="readOnly" tooltipPlacement="left"
                tooltipClass="tooltip-gotoblock-left" [isInvalid]="!element.isTargetBlockValid(editorService)">
              </app-block-picker>
            </div>
          </td>
        </ng-container>

        <ng-container matColumnDef="expandedDetail">
          <td mat-cell *matCellDef="let element" [attr.colspan]="displayedColumns.length">
            <div class="element-detail"
              [@detailExpand]="element == expandedElement ? 'expanded' : 'collapsed'">
              <div class="extended-cognitivity" *ngFor="let extendedData of element.extendedData let i = index" [ngClass]="{ 'invalid': !extendedData.isValid()}">
                <!-- destinationBlockId-->
                <div class="destination-block" [ngClass]="{ 'invalid': !destinationBlockIsValid(extendedData) }">
                  <p>{{ 'COGNITIVITY_TAB_DESTINATION_BLOCK' | translate }}</p>
                  <div class="select-block" *ngIf="extendedData.destinationBlockId != 0">
                    <app-block-picker class="input" [blockId]="extendedData.destinationBlockId"
                      (onSelectNewBlock)="onSelectDestinationBlock($event, extendedData)"
                      (onDeleteBlock)="onDeleteDestinationBlock(extendedData)" [readOnly]="readOnly" tooltipPlacement="left"
                      tooltipClass="tooltip-gotoblock-left" [isInvalid]="!extendedData.isTargetBlockValid(editorService)">
                    </app-block-picker>
                  </div>
                  <div class="select-block" *ngIf="extendedData.destinationBlockId == 0">
                    <app-block-picker class="input" [blockId]="extendedData.destinationBlockId"
                      (onSelectNewBlock)="onSelectDestinationBlock($event, extendedData)"
                      (onDeleteBlock)="onDeleteDestinationBlock(extendedData)" [readOnly]="readOnly" tooltipPlacement="left"
                      tooltipClass="tooltip-gotoblock-left" [isInvalid]="!extendedData.isTargetBlockValid(editorService)" style="position: absolute; margin-top: -15px;">
                    </app-block-picker>
                  </div>
                </div>

                <!-- entities -->
                <div class="extended-cognitivity-table" *ngIf="expandedElement === element">
                  <div class="header">
                    <div>{{ 'COGNITIVITY_TAB_ENTITY' | translate }}</div>
                    <div>{{ 'COGNITIVITY_TAB_ACTIVATE_VALUE' | translate }}</div>
                    <div >{{ 'COGNITIVITY_TAB_VALUE' | translate }}</div>
                  </div>
                  <div class="extended-cognitivity-row" *ngFor="let mapper of extendedData.entities let i = index">
                    <div class="extended-cognitivity-value">
                      <app-entity-selector-input [readOnly]="readOnly" [entity]="mapper.entity"
                        (setEntity)="setEntity($event, extendedData, i)" style="position: absolute;width: 35%;margin-top: -15px;">
                      </app-entity-selector-input>
                    </div>
                    <div class="extended-cognitivity-value">
                      <ui-switch [(ngModel)]="mapper.needValue" *ngIf="mapper.entity !== null"
                          [disabled]="readOnly"
                          color="#45c195" size="small" defaultBgColor="#e0e0e0" switchColor="#ffffff"></ui-switch>
                    </div>
                    <div class="extended-cognitivity-value" *ngIf="mapper.entity !== null">
                      <app-input-with-variables [placeholder]="'COGNITIVITY_TAB_VALUE' | translate" *ngIf="mapper.entity !== null"
                        [(value)]="mapper.entity.value" [disabled]="readOnly || !mapper.needValue"></app-input-with-variables>
                    </div>
                    <div class="trash" *ngIf="!readOnly">
                      <div (click)="deleteEntity(extendedData, i)" tooltipClass="tooltip-trash-left" data-toggle="tooltip"
                        ngbTooltip="{{ 'COGNITIVITY_TAB_REMOVE' | translate }}" placement="left">
                        <span class="fa fa fa-trash-alt"></span>
                      </div>
                    </div>
                  </div>
                </div>
                <div class="empty" role="alert" *ngIf="extendedData.entities.length == 0">
                  <div class="alert alert-info">
                    {{ 'COGNITIVITY_TAB_EMPTY' | translate }}
                  </div>
                </div>

                <div class="actions">
                  <div class="add" (click)="addEntity(extendedData)" *ngIf="!readOnly">
                    <span class="fa fa-plus"></span> {{ 'COGNITIVITY_TAB_ADD' | translate }}
                  </div>

                  <div class="remove" (click)="deleteExtendedData(element, i)" *ngIf="!readOnly">
                    <span class="fa fa-minus"></span> {{ 'COGNITIVITY_TAB_REMOVE_EXTENDED_DATA' | translate }}
                  </div>
                </div>

              </div>

              <div class="add" (click)="addExtendedData(element)" *ngIf="!readOnly">
                <span class="fa fa-plus"></span> {{ 'COGNITIVITY_TAB_ADD_EXTENDED_DATA' | translate }}
              </div>

            </div>
          </td>
        </ng-container>

        <tr mat-header-row *matHeaderRowDef="displayedColumns"></tr>
        <tr mat-row *matRowDef="let element; columns: displayedColumns;" class="element-row"
          [class.expanded-row]="expandedElement === element" (click)="changeStatus(element)"></tr>
        <tr mat-row *matRowDef="let row; columns: ['expandedDetail']" class="detail-row"></tr>

      </table>
      <mat-paginator [pageSizeOptions]="[10, 20, 50]" showFirstLastButtons></mat-paginator>

    </div>
  </div>
</div>
