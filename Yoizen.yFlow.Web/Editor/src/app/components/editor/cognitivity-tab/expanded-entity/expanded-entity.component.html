<div>
  <ng-container matColumnDef="entity">
    <th mat-header-cell *matHeaderCellDef> {{ 'COGNITIVITY_TAB_ENTITIES' | translate }}</th>
    <td mat-cell *matCellDef="let element">
      <div class="extended-profile">
        <div class="extended-profile-table">
          <div class="header">
            <div>{{ 'COGNITIVITY_TAB_ENTITY' | translate }}</div>
            <div>{{ 'COGNITIVITY_TAB_VALUE' | translate }}</div>
            <div></div>
          </div>
          <div class="extended-profile-row" *ngFor="let entity of element.entities let i = index">
            <div class="extended-profile-value">
              <app-entity-selector-input [readOnly]="readOnly" [entity]="entity"
                (setEntity)="setEntity($event, element, i)">
              </app-entity-selector-input>
            </div>
            <div class="extended-profile-value">
              <app-input-with-variables [placeholder]="'COGNITIVITY_TAB_VALUE' | translate"
                [(value)]="entity.value" [disabled]="readOnly"></app-input-with-variables>
            </div>
            <div class="trash" *ngIf="!readOnly">
              <div (click)="deleteEntity(element, i)" tooltipClass="tooltip-trash-left" data-toggle="tooltip"
                ngbTooltip="{{ 'COGNITIVITY_TAB_REMOVE' | translate }}" placement="left">
                <span class="fa fa fa-trash-alt"></span>
              </div>
            </div>
          </div>
        </div>
        <div class="empty" role="alert" *ngIf="element.entities.length == 0">
          <div class="alert alert-info">
            {{ 'COGNITIVITY_TAB_EMPTY' | translate }}
          </div>
        </div>
        <div class="add" (click)="addEntity(element)" *ngIf="!readOnly">
          <span class="fa fa-plus"></span> {{ 'COGNITIVITY_TAB_ADD' | translate }}
        </div>
      </div>
    </td>
  </ng-container>

  <ng-container matColumnDef="destinationBlockId">
    <th mat-header-cell *matHeaderCellDef> {{ 'COGNITIVITY_TAB_DESTINATION_BLOCK' | translate }}</th>
    <td mat-cell *matCellDef="let element">
      <div class="select-block" *ngIf="element.destinationBlockId != 0">
        <app-block-picker class="input" [blockId]="element.destinationBlockId"
          (onSelectNewBlock)="onSelectDestinationBlock($event, element)"
          (onDeleteBlock)="onDeleteDestinationBlock(element)" [readOnly]="readOnly" tooltipPlacement="left"
          tooltipClass="tooltip-gotoblock-left" [isInvalid]="!element.isTargetBlockValid(editorService)">
        </app-block-picker>
      </div>
      <div class="select-block" *ngIf="element.destinationBlockId == 0">
        <app-block-picker class="input" [blockId]="element.destinationBlockId"
          (onSelectNewBlock)="onSelectDestinationBlock($event, element)"
          (onDeleteBlock)="onDeleteDestinationBlock(element)" [readOnly]="readOnly" tooltipPlacement="left"
          tooltipClass="tooltip-gotoblock-left" [isInvalid]="!element.isTargetBlockValid(editorService)">
        </app-block-picker>
      </div>
    </td>
  </ng-container>

</div>
