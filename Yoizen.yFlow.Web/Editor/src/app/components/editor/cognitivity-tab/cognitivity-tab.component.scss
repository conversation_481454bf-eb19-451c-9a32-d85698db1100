@import "_variables";
@import "_mixins";

.cognitivity {
  height: 100%;
  width: 100%;
  overflow-y: auto;
  overflow-x: hidden;
  padding: 10px;
  background-color: $detail-board-bg;

  @include scrollbar;

  .btn {
    background-color: DodgerBlue;
    border: none;
    color: white;
    font-size: 16px;
    cursor: pointer;
    max-width: 48px;
    max-height: 48px;
    margin-top: 14px;
  }

  .card {
    padding-bottom: 0px;
    margin: 10px;
    border-radius: 0px;
  }

  .cognitivity-filter {
    width: 100%;
    padding: 10px;
    margin-left: 12px;
  }

  .cognitivity-tab {
    overflow-y: auto;
    overflow-x: auto;
    padding: 10px;
    height: 100%;

    table {
      width: 100%;
      border-collapse: unset;
    }
    tr.mat-row{
      height: 48px;
      position: relative;

      &.detail-row{
        height: 0;
      }
    }

    tr.element-row:not(.-expanded-row):hover {
      background: whitesmoke;
    }

    tr.element-row:not(.-expanded-row):active {
      background: #efefef;
    }

    .element-row td {
      border-bottom-width: 0;
    }

    .element-detail {
      overflow: hidden;
      //display: flex;
    }

    .mat-form-field {
      font-size: 12px;
      width: 100%;
    }

    .extended-cognitivity {
      margin-top: 10px;
      padding: 16px;
      border: rgb(226, 226, 226) solid 1px;

      &.invalid {
        border-color: $error-color;
      }

      .destination-block {
        display: flex;
        flex-direction: row;
        align-items: center;

        p {
          margin: 0 6px 0 0;
        }
      }

      .extended-cognitivity-table {
        display: table;
        width: 100%;
        .header {
          display: table-header-group;
          font-family: $fontFamilyTitles;

          & > div {
            display: table-cell;
            vertical-align: middle;
            border-bottom: 1px solid $sidebarBorderColor;
          }
        }

        .extended-cognitivity-row {
          display: table-row;
          width: 100%;
          margin-top: 5px;
          height: 40px;
          min-height: 40px;

          & > div {
            display: table-cell;
            vertical-align: middle;
            border-bottom: 1px solid $sidebarBorderColor;
            padding-left: 3px;
            padding-right: 3px;
          }

          .extended-cognitivity-value {
            width: 100px;
          }

          .extended-cognitivity-variable {
            width: 100px;
          }

          .trash {
            width: 30px;

            & > div {
              @include trash;
              cursor: pointer;

              &:hover {
                color: #555;
              }
            }
          }

          &:hover {
            .trash {
              & > div {
                @include trashOver;
              }
            }
          }
        }
      }
      .actions {
        display: flex;
        .add {
          color: $linkActionColor;
          text-transform: uppercase;
          font-size: 12px;
          margin: 10px;
          width: max-content;
          cursor: pointer;

          &:hover {
            color: lighten($linkActionColor, 10%);
          }
        }
        .remove{
          color: $error-color;
          text-transform: uppercase;
          font-size: 12px;
          margin: 10px;
          width: max-content;
          cursor: pointer;

          &:hover {
            color: lighten($error-color, 10%);
          }
        }
      }
    }
  }
  
  .add {
    color: $linkActionColor;
    text-transform: uppercase;
    font-size: 12px;
    margin: 10px;
    width: max-content;
    cursor: pointer;

    &:hover {
      color: lighten($linkActionColor, 10%);
    }
  }
}

/*
.cognitivity-tab {
  height: 100%;
  width: 100%;
  overflow-y: hidden;
  overflow-x: hidden;
  display: flex;
  flex-direction: row;

  .cognitivity-list {
    background-color: $sidebarBackgroundColor;
    flex-grow: 0;
    flex-shrink: 0;
    width: 300px;
    padding: 20px 10px;
    max-height: 100%;
    overflow-y: auto;

    .cognitivity {

    }
  }

  .cognitivity-detail {
    flex-grow: 0;
    flex-shrink: 0;
    width: calc(100% - 300px);
    padding: 20px 10px;
    height: 100%;
    max-height: 100%;
    overflow-y: auto;
  }
}

.add{
	color: #337ab7;
	text-transform: uppercase;
	font-size: 12px;
	margin: 10px 0;
	width: max-content;
	cursor: pointer;
}*/
