import { EditorService } from 'src/app/services/editor.service';
import { CognitivityDefinition, ExtendedCognitivityData, MappedEntity } from './../../../models/cognitivity/CognitivityDefinition';
import { Component, Input, OnInit, OnDestroy, ViewChild } from '@angular/core';
import { MatTableDataSource, MatPaginator } from '@angular/material';
import { SectionType } from "../../../models/MenuType";
import { Subscription } from 'rxjs';
import { FormatIntentNamePipe } from 'src/app/pipes/FormatIntentNamePipe';
import { ServerService } from 'src/app/services/server.service';
import { TranslateService } from '@ngx-translate/core';
import { ToasterService } from 'angular2-toaster';
import { Intent } from 'src/app/models/cognitivity/Intent';
import { Entity } from 'src/app/models/cognitivity/Entity';
import { animate, state, style, transition, trigger } from '@angular/animations';
import { FormDefinition } from 'src/app/models/cognitivity/FormDefinition';
import { Category } from 'src/app/models/cognitivity/Category';
import { ExtractionFormat } from 'src/app/models/cognitivity/ExtractionFormat';

@Component({
  selector: 'app-cognitivity-tab',
  templateUrl: './cognitivity-tab.component.html',
  styleUrls: ['./cognitivity-tab.component.scss'],
  providers: [FormatIntentNamePipe],
  animations: [
    trigger('detailExpand', [
      state('collapsed', style({ height: '0px', minHeight: '0' })),
      state('expanded', style({ height: '*' })),
      transition('expanded <=> collapsed', animate('225ms cubic-bezier(0.4, 0.0, 0.2, 1)')),
    ]),
  ],
})

export class CognitivityTabComponent implements OnInit, OnDestroy {
  @Input() readOnly: boolean = false;
  subs = new Subscription();
  cognitivityDefinitionList: CognitivityDefinition[];
  displayedColumns: string[] = ['intent', 'state', "defaultBlockId"];
  dataSource: MatTableDataSource<CognitivityDefinition>;
  expandedElement: CognitivityDefinition | null;
  @ViewChild(MatPaginator, { static: false }) paginator: MatPaginator;

  get selectedCognitivity(): CognitivityDefinition {
    let state = this.editorService.getEditorState();
    if (state.SelectedTab !== SectionType.Cognitivity) {
      return null;
    }
    return state.SelectedCognitivity;
  }

  constructor(private editorService: EditorService,
    private formatIntentName: FormatIntentNamePipe,
    private serverService: ServerService,
    private translateService: TranslateService,
    private toasterService: ToasterService) {

  }

  applyFilter(filterValue) {
    this.dataSource.filter = filterValue.trim().toLowerCase();
  }

  ngOnInit() {
    this.cognitivityDefinitionList = this.editorService.getCognitivity();
    let intents = this.editorService.getIntents();

    intents.forEach((intent) => {
      var aux = this.cognitivityDefinitionList.find(cognitivity => cognitivity.intent.cognitiveServiceId === intent.cognitiveServiceId);
      if (aux == null) {
        this.cognitivityDefinitionList.push(new CognitivityDefinition(intent));
      }
    });

    this.dataSource = new MatTableDataSource(this.cognitivityDefinitionList);
    this.dataSource.paginator = this.paginator;

    this.dataSource.filterPredicate = (data: CognitivityDefinition, filter: string) => {
      return this.formatIntentName.transform(data.intent.name).trim().toLowerCase().indexOf(filter) != -1;
    }

    if (!this.readOnly){
      var currentModule = this.editorService.getCurrentModule();
      if (currentModule !== undefined){
        this.readOnly = !currentModule.isMaster();
      }
    }

  }

  changeStatus(element) {
    console.log(element);
    this.expandedElement = this.expandedElement === element ? null : element
  }

  setEntity(entity: Entity, element, index) {
    if (entity !== null) {
      var e = new Entity();
      e.cognitiveServiceId = entity.cognitiveServiceId;
      e.name = entity.name;
      e.value = entity.value;
      element.entities[index].entity = e;
    } else {
      element.entities[index].entity = null;
    }
  }

  deleteExtendedData(element, index) {
    element.extendedData.splice(index, 1);
  }

  addExtendedData(element) {
    element.extendedData.push(new ExtendedCognitivityData());
  }

  addEntity(element) {
    element.entities.push(new MappedEntity());
    console.log(element);
  }

  deleteEntity(element, index) {
    element.entities.splice(index, 1);
  }

  onSelectDestinationBlock(blockData, element) {
    element.destinationBlockId = blockData.Id;
  }

  onDeleteDestinationBlock(element) {
    element.destinationBlockId = 0;
  }

  ngOnDestroy() {
    this.subs.unsubscribe();
  }

  destinationBlockIsValid(extendedData: any): boolean {
    return extendedData !== null;
  }

  entityIsValid(entity: any): boolean {
    return entity.needValue && (entity.value !== null && entity.value !== '');
  }

  refresh() {
    let token = this.editorService.getCognitivityProjectToken();
    if (token !== null) {
      this.serverService.getIntents(token).subscribe(({ success, data }) => {
        if (success) {
          this.editorService.setAvailableIntents(Intent.ParseIntents(data));
          let intents = this.editorService.getIntents();
          intents.forEach((intent) => {
            if (!this.cognitivityDefinitionList.some((cognitivityDefinition) => cognitivityDefinition.intent.cognitiveServiceId === intent.cognitiveServiceId)) {
              let newCognitivityDefinition: CognitivityDefinition = new CognitivityDefinition();
              newCognitivityDefinition.intent = intent;
              newCognitivityDefinition.destinationBlockId = "0";
              this.editorService.addCognitivityDefinition(newCognitivityDefinition);
            }
          });
          this.cognitivityDefinitionList = this.editorService.getCognitivity();
          this.dataSource.data = this.cognitivityDefinitionList;
        } else {
          this.toasterService.pop('error', this.translateService.instant('COULD_NOT_FETCH_INTENTS'));
        }
      },
        error => {
          this.toasterService.pop('error', this.translateService.instant('COULD_NOT_FETCH_INTENTS'));
        });

      this.serverService.getEntities(token).subscribe(({ success, data }) => {
        if (success) {
          this.editorService.setAvailableEntities(Entity.ParseEntity(data));
        } else {
          this.toasterService.pop('error', this.translateService.instant('COULD_NOT_FETCH_ENTITIES'));
        }
      },
        error => {
          this.toasterService.pop('error', this.translateService.instant('COULD_NOT_FETCH_ENTITIES'));
        });

      this.serverService.getForms(token).subscribe(({ success, data }) => {
          if (success) {
            this.editorService.setAvailableForms(FormDefinition.ParseForm(data));
          } else {
            this.toasterService.pop('error', this.translateService.instant('COULD_NOT_FETCH_FORMS'));
          }
        },
          error => {
            this.toasterService.pop('error', this.translateService.instant('COULD_NOT_FETCH_FORMS'));
          });
      this.serverService.GetCategories(token).subscribe(({ success, data }) => {
          if (success) {
            this.editorService.setAvailableCategories(Category.ParseCategory(data));
          } else {
            this.toasterService.pop('error', this.translateService.instant('COULD_NOT_FETCH_CATEGORIES'));
          }
        },
          error => {
            this.toasterService.pop('error', this.translateService.instant('COULD_NOT_FETCH_CATEGORIES'));
          });

      this.serverService.GetExtractionFormats(token).subscribe(({ success, data }) => {
        if (success) {
          this.editorService.setAvailableExtractionFormats(ExtractionFormat.ParseExtractionFormats(data));
        } else {
          this.toasterService.pop('error', this.translateService.instant('COULD_NOT_FETCH_EXTRACTION_FORMATS'));
        }
      },
      error => {
        this.toasterService.pop('error', this.translateService.instant('COULD_NOT_FETCH_EXTRACTION_FORMATS'));
      });

    }
  }
}
