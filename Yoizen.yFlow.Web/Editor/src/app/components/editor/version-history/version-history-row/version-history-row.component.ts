import {Component, OnInit, Input, EventEmitter} from '@angular/core';
import {FlowVersionDefinition} from "../../../../models/FlowVersionDefinition";
import {EditorService} from "../../../../services/editor.service";
import {ServerService} from "../../../../services/server.service";
import {finalize} from "rxjs/operators";
import {ErrorPopupComponent} from "../../../error-popup/error-popup.component";
import {ModalService} from "../../../../services/Tools/ModalService";
import {DeleteGroupQuestionComponent} from '../../popups/delete-group-question/delete-group-question.component';
import {DeleteInput} from 'src/app/models/UI/DeleteInput';
import {StatusResponse} from 'src/app/models/StatusResponse';
import {environment} from "../../../../../environments/environment";
import {TranslateService} from "@ngx-translate/core";
import * as moment from 'moment';

@Component({
  selector: 'app-version-history-row',
  templateUrl: './version-history-row.component.html',
  styleUrls: ['./version-history-row.component.scss']
})
export class VersionHistoryRowComponent implements OnInit {
  loading: boolean = false;

  @Input() Version: FlowVersionDefinition;
  @Input() readOnly: boolean = false;
  @Input() Hightlight: boolean = false;

  constructor(private editorService: EditorService,
              private serverService: ServerService,
              private modalService: ModalService,
              private translateService: TranslateService) {
  }

  ngOnInit() {
  }

  download() {
    this.loading = true;
    this.serverService.downloadFlow(this.Version.flow_id, this.Version.id)
      .pipe(finalize(() => {
        this.loading = false;
      }))
      .subscribe((blob: Blob) => {
          // Doing it this way allows you to name the file
          var link = document.createElement('a');
          link.href = window.URL.createObjectURL(blob);
          link.download = `Flow ${this.editorService.getChatName()} (v${this.Version.number}).json`;
          document.body.appendChild(link);
          link.click();
          setTimeout(function(){
            document.body.removeChild(link);
            window.URL.revokeObjectURL(link.href);
          }, 100);
        },
        error => {
          var errorDesc: any = {};
          this.modalService.init(ErrorPopupComponent, errorDesc, {});
        })
  }


  revertToThisVersion() {
    let flow = this.editorService.getCurrentFlow();

    let deleteInfo = new DeleteInput();
    let emmitAction = new EventEmitter();
    deleteInfo.ElementName = this.Version.number.toString();
    emmitAction.subscribe(() => {
      this.loading = true;
      this.serverService.restoreFlow(this.Version.flow_id, this.Version.id)
        .pipe(finalize(() => {
          this.loading = false;
        }))
        .subscribe((status: StatusResponse) => {
            try {
              console.log('Restaurando versión:', this.Version.id);

              // Actualizar el editor con los datos de la versión restaurada
              console.log(`[DEBUG VersionHistory] Restaurando versión. Tipo de data: ${typeof status.data}`);
              let dataStr = typeof status.data === 'string' ? status.data : JSON.stringify(status.data);
              this.editorService.restoreFromSerializedString(dataStr, flow.channel);

              // Actualizar el flujo actual con la nueva versión
              this.editorService.prefreshCurrentFlowHash();

              // Marcar como guardado para evitar problemas de detección de cambios
              this.editorService.markAsSaved();

              // Emitir evento de flujo guardado para actualizar la interfaz
              this.editorService.onFlowSaved.emit();

              // Mostrar mensaje de éxito
              this.modalService.init(ErrorPopupComponent, {
                title: this.translateService.instant('SUCCESS'),
                message: this.translateService.instant('VERSION_RESTORED_SUCCESSFULLY')
              }, {});

              console.log('Versión restaurada correctamente');
            } catch (err) {
              console.error('Error al procesar la restauración:', err);
              this.modalService.init(ErrorPopupComponent, {
                title: this.translateService.instant('ERROR'),
                message: this.translateService.instant('ERROR_RESTORING_VERSION')
              }, {});
            }
          },
          error => {
            console.error('Error al restaurar versión:', error);
            this.modalService.init(ErrorPopupComponent, {
              title: this.translateService.instant('ERROR'),
              message: this.translateService.instant('ERROR_RESTORING_VERSION')
            }, {});
          });
    });
    this.modalService.init(
      DeleteGroupQuestionComponent,
      {
        DeleteDetail: deleteInfo, Title: 'ARE_YOU_SURE_YOU_WANT_TO_REVERT',
        HideAffected: true, HideConfirmation: true, deleteText: 'ACCEPT'
      },
      {DeleteAction: emmitAction}
    );
  }

  isStandalone(): boolean {
    if (typeof(environment.standAlone) === 'undefined' ||
      environment.standAlone === true) {
      return true;
    }
    return false;
  }

  generatePersonAvatarUrl() : string {
    return null;
  }

  getPublishedTooltip() : string {
    return this.translateService.instant('VERSIONHISTORY_PUBLISHED_TOOLTIP', {
      date: moment(this.Version.published_at).format('L LTS')
    });
  }
}
