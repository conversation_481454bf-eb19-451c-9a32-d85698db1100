@import '_variables';
@import '_mixins';

@keyframes blink {
  0% {
    opacity: 1;
  }
  50% {
    opacity: 0;
  }
  100% {
    opacity: 1;
  }
}
.item {
  @include box;
  width: 100%;
  margin-right: 0;
  .status-indicator {
    display: none;
    position: absolute;
    right: 4px;
    top: 4px;
    font-size: 4px;
    line-height: 4px;
    height: 4px;
  }

  &.status-red .status-indicator,
  &.status-yellow .status-indicator {
    display: block;
  }

  &.status-red .status-indicator {
    color: red;
  }

  &.status-yellow .status-indicator {
    &::before {
      color: black;
    }
  }


  &.invalid .status-indicator {
    display: block;
    color: red;
  }
  .trash {
    @include trashSmall;
    position: absolute;
    top: -9px;
    right: -9px;
    line-height: 14px;

    &:hover {
      color: #555;
    }
  }

  .clone {
    @include trashSmall;
    position: absolute;
    bottom: -9px;
    right: -9px;
    line-height: 14px;

    &:hover {
      color: #555;
    }
  }

  .download {
    @include trashSmall;
    position: absolute;
    bottom: -9px;
    left: -9px;
    line-height: 14px;

    &:hover {
      color: #555;
    }
  }

  .invalid {
    display: none;
    position: absolute;
    right: 4px;
    top: 4px;
    color: red;
    font-size: 4px;
    line-height: 4px;
    height: 4px;
  }

  &.invalid {
    .invalid {
      display: block;
    }
  }

  .name {
    display: inline-block;
  }

  &:hover {
    .trash {
      @include trashOver;
    }
    .clone {
      @include trashOver;
    }
    .download {
      @include trashOver;
    }
    .invalid {
      right: 12px;
    }
  }

}
