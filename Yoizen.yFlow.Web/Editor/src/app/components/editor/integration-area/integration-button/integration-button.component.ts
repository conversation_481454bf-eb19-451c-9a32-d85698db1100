import { Component, OnInit, Input, Output, EventEmitter } from '@angular/core';
import { EditorService } from '../../../../services/editor.service';

@Component({
  selector: 'app-integration-button',
  templateUrl: './integration-button.component.html',
  styleUrls: ['./integration-button.component.scss']
})
export class IntegrationButtonComponent implements OnInit {
  @Input() name: string;
  @Input() extendedStyles: string = null;
  @Input() isSelected: boolean = false;
  @Input() isValid: boolean = true;
  @Input() showActions: boolean = true;
  @Input() showCloneAction: boolean = true;
  @Input() isEnabled: boolean = null;

  @Input() deleteTooltip: string;
  @Input() cloneTooltip: string;
  @Input() downloadTooltip: string;
  @Input() showDownloadButton: boolean = false;
  @Output() onDeleteElement = new EventEmitter();
  @Output() onCloneElement = new EventEmitter();
  @Output() onDownloadElement = new EventEmitter();

  textWithHighlight: string;
  highlightSearch: boolean = false;

  constructor(private editorService: EditorService) { }

  ngOnInit() {
    this.textWithHighlight = this.name;
    this.editorService.onSearch.subscribe((text) => {
      let pureName = this.name.normalize("NFD").replace(/[\u0300-\u036f]/g, "").toLowerCase();
      let pureText = text.normalize("NFD").replace(/[\u0300-\u036f]/g, "").toLowerCase();
      let index = pureName.indexOf(pureText);
      if (index >= 0){
        this.highlightSearch = true;
        this.textWithHighlight = this.name.substring(0,index) + '<span class="search-match">' + this.name.substring(index, index+pureText.length) + '</span>' + this.name.substr(index+pureText.length);
      } else {
        this.highlightSearch = false;
        this.textWithHighlight = this.name;
      }
      this.editorService.onSearchHighlighted.emit(this.highlightSearch);
    });

    this.editorService.onSearchClosed.subscribe(() => {
      this.highlightSearch = false;
      this.textWithHighlight = this.name;
    });
  }

  onDelete() {
    this.onDeleteElement.emit();
  }

  onClone() {
    this.onCloneElement.emit();
  }

  onDownload() {
    this.onDownloadElement.emit();
  }

  getStatusClass(): string {
    if (this.isEnabled !== null) {
      let classes = [];

      if (!this.isValid) {
        classes.push('status-red');
      }
      if (!this.isEnabled) {
        classes.push('status-yellow');
      }
      return classes.join(' ');
    }

    return this.isValid ? '' : 'invalid';
  }

  getClassObject(): { [key: string]: boolean } {
    const classes = {
      'selected': this.isSelected,
      'highlight': this.highlightSearch,
    };

    const statusClass = this.getStatusClass();
    if (statusClass) {
      classes[statusClass] = true;
    }

    return classes;
  }

  isStatusYellow(): boolean {
    return this.getStatusClass() === 'status-yellow';
  }

}
