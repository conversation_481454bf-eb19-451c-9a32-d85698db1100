import { TranslateService } from '@ngx-translate/core';
import { ToasterService } from 'angular2-toaster';
import { TypedJSON } from 'typedjson';
import { CognitivityProject, CognitivityProjectType } from './../../models/cognitivity/CognitivityProject';
import { Intent } from './../../models/cognitivity/Intent';
import { Entity } from './../../models/cognitivity/Entity';
import { Component, OnInit, OnDestroy, EventEmitter } from '@angular/core';
import { Router, ActivatedRoute } from '@angular/router'
import { EditorService } from '../../services/editor.service';
import { SectionType } from '../../models/MenuType';
import { FlowDefinition } from "../../models/FlowDefinition";
import { ServerService } from '../../services/server.service';
import { finalize } from 'rxjs/operators'
import { ModalService } from '../../services/Tools/ModalService';
import { ErrorPopupComponent } from '../error-popup/error-popup.component';
import { SavemessageComponent } from '../savemessage/savemessage.component';
import { PublishmessageComponent } from '../publishmessage/publishmessage.component';
import { environment } from '../../../environments/environment';
import { FlowSaveInfo } from "../../models/FlowSaveInfo";
import { StatusResponse } from "../../models/StatusResponse";
import { getTokenPayload, yBiometricEnabled, ySmartEnabled } from "../../Utils/window";
import { SocketService } from 'src/app/services/socket.service';
import { YSocialSettings } from 'src/app/models/YSocialSettings';
import { Metamap } from 'src/app/models/biometric/Metamap';
import { ModuleDefinition } from 'src/app/models/ModuleDefinition';
import { FlowModuleResponse } from 'src/app/models/FlowModuleResponse';
import { FormDefinition } from 'src/app/models/cognitivity/FormDefinition';
import { Category } from 'src/app/models/cognitivity/Category';
import { ExtractionFormat } from 'src/app/models/cognitivity/ExtractionFormat';



@Component({
  selector: 'app-editor',
  templateUrl: './editor.component.html',
  styleUrls: ['./editor.component.scss'],
  host: { 'class': 'container-fluid-no-padding' }
})
export class EditorComponent implements OnInit, OnDestroy {
  currentFlow: FlowDefinition = null;
  value: number = 1;
  keepSessionInterval: number;
  paramsSub: any;
  isProductive: boolean = false;
  canSeeStatistics: boolean = false;
  canEdit: boolean = true;
  canPublish: boolean = true;
  homeUrl: string;
  isSearchActive: boolean = false;
  currentSearch: string = null;
  pendingChanges: boolean = false;
  isLoading: boolean = true;
  settings: YSocialSettings;
  usersEditing: any;

  constructor(
    public editorService: EditorService,
    private readonly serverService: ServerService,
    private readonly modalService: ModalService,
    private readonly router: Router,
    private readonly route: ActivatedRoute,
    private readonly toasterService: ToasterService,
    private readonly translateService: TranslateService,
    private readonly socketService: SocketService) {
  }
  getCurrentUrl(companyId, currentFlow, isProd, hasModules = false) {
    if (this.isMasterModule(currentFlow)) {
      return this.getMasterModuleUrl(companyId, currentFlow, isProd, hasModules);
    } else {
      return this.getRegularModuleUrl(companyId, currentFlow, isProd);
    }
  }

  isMasterModule(currentFlow) {
    return currentFlow.isMasterModule !== undefined && currentFlow.isMasterModule !== null;
  }

  getMasterModuleUrl(companyId, currentFlow, isProd, hasModules) {
    if (hasModules || this.hasMasterFlowId(currentFlow)) {
      return this.getModuleUrl(companyId, currentFlow, isProd);
    } else {
      return this.getHomeUrl(companyId);
    }
  }

  getRegularModuleUrl(companyId, currentFlow, isProd) {
    if (this.hasModulesOrMasterFlowId(currentFlow)) {
      return this.getModuleUrl(companyId, currentFlow, isProd);
    } else {
      return this.getHomeUrl(companyId);
    }
  }

  hasMasterFlowId(currentFlow) {
    return currentFlow.master_flow_id !== undefined && currentFlow.master_flow_id !== null;
  }

  hasModulesOrMasterFlowId(currentFlow) {
    return (currentFlow.modules !== null && currentFlow.modules !== undefined) ||
           (currentFlow.master_flow_id !== null && currentFlow.master_flow_id !== undefined);
  }

  getModuleUrl(companyId, currentFlow, isProd) {
    if (companyId) {
      return `/${companyId}/flow/${currentFlow.id}/modules/${isProd}`;
    } else {
      return `/flow/${currentFlow.id}/modules/${isProd}`;
    }
  }

  getHomeUrl(companyId) {
    if (companyId) {
      return `/${companyId}/home`;
    } else {
      return '/home';
    }
  }

  ngOnInit() {
    let self = this;

    let tokenPayload = getTokenPayload();

    this.keepSessionInterval = window.setInterval(function () {
      self.serverService.keepSession().subscribe(() => {
      });
    }, environment.keepAliveDelay);

    let currentFlow = this.editorService.getCurrentFlow();

    if (typeof (currentFlow) !== 'undefined' && currentFlow !== null) {
      this.canEdit = currentFlow.users_permissions_flows && currentFlow.users_permissions_flows[0] ? currentFlow.users_permissions_flows[0].canEdit : tokenPayload.edit;
      this.canPublish = currentFlow.users_permissions_flows && currentFlow.users_permissions_flows[0] ? currentFlow.users_permissions_flows[0].canPublish : tokenPayload.publish;
      this.canSeeStatistics = currentFlow.users_permissions_flows && currentFlow.users_permissions_flows[0] ? currentFlow.users_permissions_flows[0].canSeeStatistics : tokenPayload.seeStatistics;
      this.isProductive = this.editorService.isEditingProductiveVersion();
      this.currentFlow = currentFlow;
      this.isLoading = false;
      this.loadSmtpConfiguration();
      this.loadCognitiveServices();
      this.loadMetamaps();
      this.getTables();
      if (this.settings === null || this.settings === undefined) {
        this.settings = this.editorService.getYSocialSettings();
      }
      this.getWhatsappHSM();

      this.configSockets(tokenPayload.uid, currentFlow.id);

      this.paramsSub = this.route.params.subscribe(params => {
        const companyId = params['companyName']
        const isProd = params['isProd']
        self.homeUrl = this.getCurrentUrl(companyId, currentFlow, isProd)
      });

      this.setModule(currentFlow);
      return;
    }

    this.paramsSub = this.route.params.subscribe(params => {
      if (params['id']) {
        let flowId = params['id'];
        this.serverService.getFlowModules(flowId)
      .pipe(finalize(() => {
      }))
      .subscribe((response: FlowModuleResponse) => {
          if (response !== null) {
            let master = new ModuleDefinition();
            master.createMaster();
            master.id = Number(flowId);

            response.flows = response.flows.map(flow => {
              let module = new ModuleDefinition();
              module.init(flow);
              return module
            });
            response.flows.push(master)
            this.editorService.setModules(response.flows);

            this.serverService.getFlow(flowId)
              .subscribe((flow: FlowDefinition) => {
                self.isProductive = (params.isProd === 'true');
                // Verificar que las versiones existan antes de acceder a sus propiedades
                console.log(`[DEBUG Editor] Flow completo:`, flow);
                console.log(`[DEBUG Editor] isProductive: ${self.isProductive}`);
                console.log(`[DEBUG Editor] ActiveProductionVersion:`, flow.ActiveProductionVersion);
                console.log(`[DEBUG Editor] ActiveStagingVersion:`, flow.ActiveStagingVersion);

                if (self.isProductive && flow.ActiveProductionVersion) {
                  console.log(`[DEBUG Editor] Restaurando versión productiva. Tipo de blob: ${typeof flow.ActiveProductionVersion.blob}`);
                  console.log(`[DEBUG Editor] Blob productivo:`, flow.ActiveProductionVersion.blob);

                  // Manejar el caso donde blob es undefined debido a carga en fragmentos
                  if (flow.ActiveProductionVersion.blob === undefined || flow.ActiveProductionVersion.blob === null) {
                    console.log(`[DEBUG Editor] Blob productivo es undefined/null, obteniendo flujo completo usando downloadFlowInChunks`);
                    self.serverService.downloadFlowInChunks(flow.id).subscribe(
                      (fullFlow: FlowDefinition) => {
                        let fullBlob = fullFlow.ActiveProductionVersion.blob;
                        let fullBlobStr;

                        if (typeof fullBlob === 'string') {
                          fullBlobStr = fullBlob;
                        } else if (fullBlob && typeof fullBlob === 'object') {
                          // Si es un objeto, intentamos extraer el contenido sin referencias circulares
                          try {
                            fullBlobStr = JSON.stringify(fullBlob);
                          } catch (circularError) {
                            console.warn(`[DEBUG Editor] Error de referencia circular en productivo, usando estructura básica:`, circularError);
                            fullBlobStr = '{"BlockGroups":[],"BlockList":[]}';
                          }
                        } else {
                          fullBlobStr = '{"BlockGroups":[],"BlockList":[]}';
                        }

                        console.log(`[DEBUG Editor] Flujo productivo completo obtenido. Tamaño: ${Math.round(fullBlobStr.length / 1024)} KB`);
                        self.editorService.restoreFromSerializedString(fullBlobStr, flow.channel);
                      },
                      error => {
                        console.error(`[ERROR Editor] Error al obtener flujo productivo completo:`, error);
                        self.editorService.restoreFromSerializedString('{"BlockGroups":[],"BlockList":[]}', flow.channel);
                      }
                    );
                  } else {
                    let blobStr = typeof flow.ActiveProductionVersion.blob === 'string'
                      ? flow.ActiveProductionVersion.blob
                      : (flow.ActiveProductionVersion.blob ? JSON.stringify(flow.ActiveProductionVersion.blob) : '{"BlockGroups":[],"BlockList":[]}');
                    self.editorService.restoreFromSerializedString(blobStr, flow.channel);
                  }
                } else if (!self.isProductive && flow.ActiveStagingVersion) {
                  console.log(`[DEBUG Editor] Restaurando versión staging. Tipo de blob: ${typeof flow.ActiveStagingVersion.blob}`);
                  console.log(`[DEBUG Editor] Blob staging:`, flow.ActiveStagingVersion.blob);

                  // Manejar el caso donde blob es undefined debido a carga en fragmentos
                  if (flow.ActiveStagingVersion.blob === undefined || flow.ActiveStagingVersion.blob === null) {
                    console.log(`[DEBUG Editor] Blob staging es undefined/null, obteniendo flujo completo usando downloadFlowInChunks`);
                    self.serverService.downloadFlowInChunks(flow.id).subscribe(
                      (fullFlow: FlowDefinition) => {
                        let fullBlob = fullFlow.ActiveStagingVersion.blob;
                        let fullBlobStr;

                        if (typeof fullBlob === 'string') {
                          fullBlobStr = fullBlob;
                        } else if (fullBlob && typeof fullBlob === 'object') {
                          // Si es un objeto, intentamos extraer el contenido sin referencias circulares
                          try {
                            fullBlobStr = JSON.stringify(fullBlob);
                          } catch (circularError) {
                            console.warn(`[DEBUG Editor] Error de referencia circular en staging, usando estructura básica:`, circularError);
                            fullBlobStr = '{"BlockGroups":[],"BlockList":[]}';
                          }
                        } else {
                          fullBlobStr = '{"BlockGroups":[],"BlockList":[]}';
                        }

                        console.log(`[DEBUG Editor] Flujo staging completo obtenido. Tamaño: ${Math.round(fullBlobStr.length / 1024)} KB`);
                        self.editorService.restoreFromSerializedString(fullBlobStr, flow.channel);
                      },
                      error => {
                        console.error(`[ERROR Editor] Error al obtener flujo staging completo:`, error);
                        self.editorService.restoreFromSerializedString('{"BlockGroups":[],"BlockList":[]}', flow.channel);
                      }
                    );
                  } else {
                    let blobStr = typeof flow.ActiveStagingVersion.blob === 'string'
                      ? flow.ActiveStagingVersion.blob
                      : (flow.ActiveStagingVersion.blob ? JSON.stringify(flow.ActiveStagingVersion.blob) : '{"BlockGroups":[],"BlockList":[]}');
                    self.editorService.restoreFromSerializedString(blobStr, flow.channel);
                  }
                } else {
                  console.warn('No se encontró una versión válida para restaurar');
                  console.log(`[DEBUG Editor] Condiciones: isProductive=${self.isProductive}, ActiveProductionVersion=${!!flow.ActiveProductionVersion}, ActiveStagingVersion=${!!flow.ActiveStagingVersion}`);
                  // Usar un flujo vacío o una versión por defecto si no hay versión disponible
                  self.editorService.restoreFromSerializedString('{"BlockGroups":[],"BlockList":[]}', flow.channel);
                }
                self.editorService.setCurrentFlow(flow, self.isProductive);
                self.currentFlow = flow;
                self.canEdit = flow.users_permissions_flows ? flow.users_permissions_flows[0].canEdit : tokenPayload.edit;
                self.canPublish = flow.users_permissions_flows ? flow.users_permissions_flows[0].canPublish : tokenPayload.publish;
                self.canSeeStatistics = flow.users_permissions_flows ? flow.users_permissions_flows[0].canSeeStatistics : tokenPayload.seeStatistics;
                self.isLoading = false;
                self.loadSmtpConfiguration();
                self.loadCognitiveServices();
                self.loadMetamaps();
                self.getTables();
                self.configSockets(tokenPayload.uid, flow.id);
                if (this.settings === null || this.settings === undefined) {
                  this.settings = this.editorService.getYSocialSettings();
                }
                this.getWhatsappHSM();
                this.setModule(flow);

                let modules = this.editorService.getModules().length;
                let hasModules = modules !== undefined && modules !== null && modules > 1 ? true : false;

                self.homeUrl = this.getCurrentUrl(params['companyId'], flow, params['isProd'], hasModules)
              },
                error => {
                  if (params['companyName']) {
                    self.router.navigateByUrl(`/${params['companyName']}/home`);
                  }
                  else {
                    self.router.navigateByUrl('/home');
                  }
                });
          }
        },
        error => {
          this.modalService.init(ErrorPopupComponent, {}, {});
        });
      }
      else if (params['companyName']) {
          self.router.navigateByUrl(`/${params['companyName']}/home`);
        }
        else {
          self.router.navigateByUrl('/home');
        }
    });
  }

  setModule(flow: FlowDefinition) {
    let module = new ModuleDefinition();
    module.createMaster();
    module.id = flow.id;
    module.activeProductionVersionId = flow.ActiveProductionVersion !== null && flow.ActiveProductionVersion !== undefined ? flow.ActiveProductionVersion.id : null;
    module.activeStagingVersionId = flow.ActiveStagingVersion !== null && flow.ActiveStagingVersion !== undefined ? flow.ActiveStagingVersion.id : null;
    if (flow.master_flow_id !== null) {
      module.name = flow.name;
    }

    this.editorService.setCurrentModule(module);
  }
  configSockets(uid, flow) {
    console.log(`uid: ${uid} - flow: ${flow}`);

    this.usersEditing = this.socketService.ConnectedUsers;

    this.socketService.onMultipleEditorsChange.subscribe(({ multipleEditors, users}) => {
        this.usersEditing = users;
    });

    this.socketService.connectUserToFlow(uid, flow);
  }

  getTables() {
    const mCurrentFlow = this.editorService.getCurrentFlow();
    const flowId = mCurrentFlow.master_flow_id ? mCurrentFlow.master_flow_id : mCurrentFlow.id;

    this.serverService.getTablesByFlow(flowId)
      .subscribe((status: StatusResponse) => {
        this.editorService.setFlowTables(status.data);
      },
        error => {
          this.toasterService.pop('error', this.translateService.instant('COULD_NOT_FETCH_TABLES'));
          console.log(`Error al traer las tablas: ${error}`);
        });

    this.serverService.getExcelTablesStatus(flowId)
      .subscribe((status: StatusResponse) => {
        this.editorService.setFlowTablesStatus(status.data)
      },
        error => {
          this.toasterService.pop('error', this.translateService.instant('COULD_NOT_FETCH_TABLES_STATUS'));
          console.log(`Error al traer los estados de las tablas: ${error}`);
        });
  }

  getWhatsappHSM() {
    if (!this.settings) return;
    this.serverService.retrieveWhatsappHSMTemplates(this.settings.Url)
      .subscribe((status: StatusResponse) => {
        if (status.data !== null && Array.isArray(status.data)) {
          console.log(status.data)
          this.editorService.loadySocialSettings(status.data);
          //this.editorService.setYSocialSettings(this.settings)
        }
      },
        error => {
          console.log(error)
          this.toasterService.pop({
            type: 'error',
            body: this.translateService.instant('CANNOT_FIND_HSM'),
            timeout: 2000
          });

        });
  }

  loadSmtpConfiguration() {
    if (!this.editorService.isSmtpAvailable()) {
      this.serverService.getSmtpConfig().subscribe(({ success, data }) => {
        if (success && data !== null) {
          this.editorService.setSmtpConfig(data.content);
        }
      }, (error) => {
        this.toasterService.pop('error', this.translateService.instant('COULD_NOT_FETCH_PROJECTS'));
        console.log(`Error al traer los proyectos de cognitividad: ${error}`);
      });
    }
  }

  private async loadCognitiveServices(): Promise<void> {
    if (ySmartEnabled().toLocaleLowerCase() !== "true") {
      return;
    }

    await this.loadProjects();
    const token = this.editorService.getCognitivityProjectToken();

    if (token !== null) {
      await Promise.all([
        this.loadIntents(token),
        this.loadEntities(token),
        this.loadForms(token),
        this.loadCategories(token),
        this.loadExtractionFormats(token)
      ]);
    }
  }

  private async loadProjects(): Promise<void> {
    try {
      const { success, data } = await this.serverService.getProjects().toPromise();
      if (!success || !data) {
        throw new Error('Failed to fetch projects');
      }

      const projects = data.map(element => {
        const project = TypedJSON.parse<CognitivityProject>(JSON.stringify(element), CognitivityProject);
        project.type = element.projectService && element.projectService.type || CognitivityProjectType.RASA;
        return project;
      });

      this.editorService.setAvailableProjects(projects);
    } catch (error) {
      this.handleError('COULD_NOT_FETCH_PROJECTS', error);
    }
  }

  private async loadIntents(token: string): Promise<void> {
    try {
      const { success, data } = await this.serverService.getIntents(token).toPromise();
      if (!success || !data) {
        throw new Error('Failed to fetch intents');
      }
      this.editorService.setAvailableIntents(Intent.ParseIntents(data));
    } catch (error) {
      this.handleError('COULD_NOT_FETCH_INTENTS', error);
    }
  }

  private async loadEntities(token: string): Promise<void> {
    try {
      const { success, data } = await this.serverService.getEntities(token).toPromise();
      if (!success || !data) {
        throw new Error('Failed to fetch entities');
      }
      this.editorService.setAvailableEntities(Entity.ParseEntity(data));
    } catch (error) {
      this.handleError('COULD_NOT_FETCH_ENTITIES', error);
    }
  }

  private async loadForms(token: string): Promise<void> {
    try {
      const { success, data } = await this.serverService.getForms(token).toPromise();
      if (!success || !data) {
        throw new Error('Failed to fetch forms');
      }
      this.editorService.setAvailableForms(FormDefinition.ParseForm(data));
    } catch (error) {
      this.handleError('COULD_NOT_FETCH_FORMS', error);
    }
  }

  private async loadCategories(token: string): Promise<void> {
    try {
      const { success, data } = await this.serverService.GetCategories(token).toPromise();
      if (!success || !data) {
        throw new Error('Failed to fetch categories');
      }
      this.editorService.setAvailableCategories(Category.ParseCategory(data));
    } catch (error) {
      this.handleError('COULD_NOT_FETCH_CATEGORIES', error);
    }
  }

  private async loadExtractionFormats(token: string): Promise<void> {
    try {
      const { success, data } = await this.serverService.GetExtractionFormats(token).toPromise();
      if (!success || !data) {
        throw new Error('Failed to fetch extraction formats');
      }
      this.editorService.setAvailableExtractionFormats(ExtractionFormat.ParseExtractionFormats(data));
    } catch (error) {
      this.handleError('COULD_NOT_FETCH_EXTRACTION_FORMATS', error);
    }
  }

  private handleError(messageKey: string, error: any): void {
    this.toasterService.pop('error', this.translateService.instant(messageKey));

    const errorMessage = error instanceof Error
      ? error.message
      : error && error.message
        ? error.message
        : JSON.stringify(error);

    console.log(`Error in cognitive services - ${messageKey}: ${errorMessage}`);
  }

  loadMetamaps() {
    if (yBiometricEnabled().toLocaleLowerCase() === "true") {
      this.serverService.getMetamaps().subscribe(
        ({ success, data }) => {
          if (success) {
            let metamaps = [];
            for (const element of data) {
              let metamap = element;
              metamaps.push(TypedJSON.parse<Metamap>(JSON.stringify(metamap), Metamap));
            }
            this.editorService.setAvailableMetamaps(metamaps);
          }
        },
        (error) => {
          this.toasterService.pop('error', this.translateService.instant('COULD_NOT_FETCH_METAMAPS'));

          const errorMessage = error instanceof Error
            ? error.message
            : error && error.message
              ? error.message
              : JSON.stringify(error);

          console.log(`Error al traer los metamaps de biometría: ${errorMessage}`);
        }
      );
    }
  }

  ngOnDestroy() {
    if (this.currentFlow !== null && this.currentFlow !== undefined) {
      this.socketService.disconnectUserFromFlow(getTokenPayload().uid, this.currentFlow.id);
    }

    if (this.paramsSub != null) {
      this.paramsSub.unsubscribe();
    }
    this.editorService.clearCurrentFlow();
    clearInterval(this.keepSessionInterval);
  }

  displayBlockArea(): boolean {
    if (this.currentFlow === null) {
      return false;
    }

    let state = this.editorService.getEditorState();
    return this.editorService.ready() &&
      !state.OnDashboard &&
      state.SelectedTab === SectionType.Blocks;
  }

  displayMigrationTab(): boolean {
    if (this.currentFlow === null) {
      return false;
    }

    let state = this.editorService.getEditorState();
    return this.editorService.ready() &&
      !state.OnDashboard &&
      state.SelectedTab === SectionType.Integration;
  }

  displayConfigurationTab(): boolean {
    if (this.currentFlow === null) {
      return false;
    }

    let state = this.editorService.getEditorState();
    return this.editorService.ready() &&
      !state.OnDashboard &&
      state.SelectedTab === SectionType.Configuration;
  }

  displayVariablesTab(): boolean {
    if (this.currentFlow === null) {
      return false;
    }

    let state = this.editorService.getEditorState();
    return this.editorService.ready() &&
      !state.OnDashboard &&
      state.SelectedTab === SectionType.Variables;
  }

  displayVersionTab(): boolean {
    if (this.currentFlow === null) {
      return false;
    }

    let state = this.editorService.getEditorState();
    return this.editorService.ready() &&
      !state.OnDashboard &&
      state.SelectedTab === SectionType.Version;
  }

  displayCommandsTab(): boolean {
    if (this.currentFlow === null) {
      return false;
    }

    let state = this.editorService.getEditorState();
    return this.editorService.ready() &&
      !state.OnDashboard &&
      state.SelectedTab === SectionType.Commands;
  }

  displayCognitivityTab(): boolean {
    if (this.currentFlow === null) {
      return false;
    }

    let state = this.editorService.getEditorState();
    return this.editorService.ready() &&
      !state.OnDashboard &&
      state.SelectedTab === SectionType.Cognitivity &&
      this.editorService.isCognitivityEnabled();
  }

  displayReportsTab(): boolean {
    if (this.currentFlow === null) {
      return false;
    }

    if (!this.canSeeStatistics) {
      return false;
    }

    let state = this.editorService.getEditorState();
    return this.editorService.ready() &&
      !state.OnDashboard &&
      state.SelectedTab === SectionType.Reports;
  }

  displayModuleTab(): boolean {
    if (this.currentFlow === null) {
      return false;
    }
    let state = this.editorService.getEditorState();
    return this.editorService.ready() &&
      !state.OnDashboard &&
      state.SelectedTab === SectionType.Module;
  }

  displayStatisticsTab(): boolean {
    if (this.currentFlow === null) {
      return false;
    }

    if (!this.canSeeStatistics) {
      return false;
    }

    let state = this.editorService.getEditorState();
    return this.editorService.ready() &&
      !state.OnDashboard &&
      state.SelectedTab === SectionType.Statistics;
  }

  onPublishClick() {
    let flowId = this.editorService.getCurrentFlowId();
    let emitAction = new EventEmitter<FlowSaveInfo>();
    emitAction.subscribe((info: FlowSaveInfo) => {
      this.serverService.publishFlow(flowId)
        .subscribe((status: StatusResponse) => {
          this.editorService.onFlowPublished.emit(this.editorService.getChatName());
          console.log('Se publicó el flow');
        },
          error => {
            console.error(`Ocurrió un error publicando el flow ${flowId}: ${JSON.stringify(error)}`);
            this.modalService.init(ErrorPopupComponent, {}, {});
          });
    })

    this.modalService.init(PublishmessageComponent, {}, { AcceptAction: emitAction });

  }

  onSaveClick(isIntegrationsOnly: boolean = false) {
    if (this.editorService.pendingChanges()) {
      let emitAction = new EventEmitter<FlowSaveInfo>();
      emitAction.subscribe((info: FlowSaveInfo) => {
        this.editorService.onFlowSaving.emit();
        info.OnlyIntegrations = isIntegrationsOnly;

        let flowId = this.editorService.getCurrentFlowId();
        let flowName = this.editorService.getChatName();

        if (info.OnlyIntegrations) {
          this.serverService.publishFlow(
            flowId,
            true, // onlyIntegrations
            JSON.stringify(this.editorService.getIntegrationsState())
          )
            .subscribe((status) => {
              this.editorService.incrementCurrentVersion();
              this.editorService.prefreshCurrentFlowHash();
              this.editorService.markAsSaved();
              this.editorService.onFlowSaved.emit();
              this.editorService.onFlowPublished.emit(flowName);
            });
        } else {
          this.serverService.saveFlow(flowId, flowName, this.editorService.getProcessedSerializedState(true), info.Message)
            .subscribe((status) => {
              this.editorService.incrementCurrentVersion();
              this.editorService.prefreshCurrentFlowHash();
              this.editorService.markAsSaved();
              this.editorService.onFlowSaved.emit();

              this.socketService.saveFlow(getTokenPayload().uid, flowId);

              if (info.Publish) {
                if (this.editorService.isChatValid({})) {
                  this.serverService.publishFlow(flowId)
                    .subscribe((status: StatusResponse) => {
                      this.editorService.onFlowPublished.emit(flowName);
                    },
                    error => {
                      console.error(`Ocurrió un error publicando el flow ${flowId}: ${JSON.stringify(error)}`);
                      this.modalService.init(ErrorPopupComponent, {}, {});
                    });
                }
              }
            },
            error => {
              let inputs: any = {};
              if (error.error != null) {
                inputs.Title = 'ERROR';
                inputs.Desc = error.error.message;
              }
              console.error(`Ocurrió un error grabando el flow ${flowId}: ${JSON.stringify(error)}`);
              this.modalService.init(ErrorPopupComponent, inputs, {});
            });
        }
      });

      this.modalService.init(SavemessageComponent, {}, { AcceptAction: emitAction });
    } else {
      this.onPublishClick();
    }
  }



  onGenerateStagingClick() {
    let flow = this.editorService.getCurrentFlow();
    this.serverService.restoreFlow(flow.id, flow.ActiveProductionVersion.id)
      .subscribe((status) => {
        this.editorService.prefreshCurrentFlowHash();
        this.editorService.onFlowSaved.emit();
      },
        error => {
          console.error(`Ocurrió un error restaurando el flow ${flow.id}: ${JSON.stringify(error)}`);
          this.modalService.init(ErrorPopupComponent, {}, {});
        });
  }

  onTestBotClick() {
    let serializedStr = this.editorService.getSerializedState();
    console.log(serializedStr);
  }

  missingStatingVersion(): boolean {
    if (!this.isProductive) {
      return false;
    }

    let flow = this.editorService.getCurrentFlow();
    if (flow.ActiveStagingVersion === null) {
      return true;
    }

    return false;
  }

  onSearchClick() {
    this.isSearchActive = !this.isSearchActive;
    if (!this.isSearchActive) {
      this.editorService.onSearchClosed.emit();
    }
    else if (this.currentSearch !== null &&
        this.currentSearch.length > 3) {
        this.editorService.onSearch.emit(this.currentSearch);
      }
  }

  onSearch(text: string) {
    this.currentSearch = text;
    if (this.currentSearch !== null &&
      this.currentSearch.length > 3) {
      this.editorService.onSearch.emit(this.currentSearch);
    }
  }

  onCloseSearch() {
    this.isSearchActive = false;
    this.editorService.onSearchClosed.emit();
  }

  onCleanSearch() {
    this.currentSearch = null;
    this.editorService.onSearchClosed.emit();
  }

  onDashboardClick() {
    this.router.navigateByUrl(this.homeUrl);
  }

  onSaveIntegrationsClick() {
    this.onSaveClick(true);
  }
}
