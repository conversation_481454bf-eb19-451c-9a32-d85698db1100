import { ChannelTypes } from './../../models/ChannelType';
import { ChannelTypesToString } from 'src/app/models/ChannelType';
import { ChangeGroupChannelQuestionComponent } from './../editor/popups/change-group-channel-question/change-group-channel-question.component';
import { Component, OnInit, EventEmitter, Inject, ViewChild, ElementRef, OnDestroy } from '@angular/core';
import { Location, DOCUMENT } from '@angular/common';
import { ActivatedRoute, Router } from '@angular/router';
import { ModalService } from '../../services/Tools/ModalService';
import { ServerService } from '../../services/server.service';
import { FlowDefinition } from '../../models/FlowDefinition';
import { StatusResponse } from '../../models/StatusResponse';
import { NewFlowComponent } from "../editor/popups/new-flow/new-flow.component";
import { ErrorPopupComponent } from "../error-popup/error-popup.component";
import { finalize } from 'rxjs/operators'
import { EditorService } from '../../services/editor.service';
import { DeleteInput } from '../../models/UI/DeleteInput';
import { DeleteGroupQuestionComponent } from '../editor/popups/delete-group-question/delete-group-question.component';
import { getBaseUrl, getTokenPayload, ySmartEnabled, ySocialUrl } from '../../Utils/window';
import { environment } from '../../../environments/environment';
import { TranslateService } from '@ngx-translate/core';
import { HttpResponse } from "@angular/common/http";
import { TypedJSON, jsonArrayMember } from "typedjson";
import { ChatDefinition } from "../../models/ChatDefinition";
import { FilterComponent } from '../filter/filter.component';
import { FlowTypes } from 'src/app/models/FlowType';
import { ModuleDefinition } from 'src/app/models/ModuleDefinition';
import { BotType, BotTypeToString } from 'src/app/models/BotType';
import { DISABLED_CONTINGENCY_PIECES } from '@contingency/DisabledContingencyPieces';


@Component({
  selector: 'app-dashboard',
  templateUrl: './dashboard.component.html',
  styleUrls: ['./dashboard.component.scss'],
  host: { 'class': 'container-fluid-no-padding' }
})
export class DashboardComponent implements OnInit, OnDestroy {
  @ViewChild('fileField', { static: false }) fileField: ElementRef;

  constructor(
    public modalService: ModalService,
    private serverService: ServerService,
    private editorService: EditorService,
    private routerService: Router,
    private location: Location,
    private translate: TranslateService,
    private router: Router,
    private route: ActivatedRoute,
    @Inject(DOCUMENT) private doc: any) {
  }

  isAdmin: boolean = false;
  canEdit: boolean = true;
  canPublish: boolean = true;
  canSeeLogs: boolean = true;
  canSeeStatistics: boolean = true;
  canAccessYSmart: boolean = true;
  loading: boolean = true;
  flowsLoaded: boolean = false;
  flows: FlowDefinition[];
  publishedFlows: FlowDefinition[];
  stagingFlows: FlowDefinition[];
  keepSessionInterval: number;
  paramsSub: any;
  homeUrl: string;
  settingsUrl: string;
  logsErrorUrl: string;
  globalStatisticUrl: string;
  companyName: string = '';
  cachedKw: string = undefined;
  cachedChannel: string = undefined;
  cachedBotType: string = undefined;
  keywordfilterTag: string;
  channelFilterTag: string;
  botTypeFilterTag: string;

  newFlow() {
    let createAction = new EventEmitter<any>();
    createAction.subscribe((info: any) => {
      this.onCreate(info.name, info.channel, info.flowType);
    });

    this.modalService.init(NewFlowComponent, {}, { CreateAction: createAction });
  }

  openFilter() {
    let createAction = new EventEmitter<any>();
    createAction.subscribe((info: any) => {
      this.onFilter(info.name, info.channel, info.botType);
    });
    this.modalService.init(FilterComponent, { flowName: this.cachedKw, selectedChannel: this.cachedChannel, selectedBotType: this.cachedBotType }, { FilterAction: createAction });
  }

  openLogsError() {
    this.router.navigateByUrl(this.logsErrorUrl);
  }


  onFilter(kw, channel, botType) {
    this.loading = true;
    this.cachedKw = kw;
    this.cachedChannel = channel;
    this.cachedBotType = botType;
    console.log(`Filtrando bajo keyword=${kw} del canal=${channel} botType=${botType}`);
    this.keywordfilterTag = kw;
    this.channelFilterTag = channel;
    this.botTypeFilterTag = botType;
    this.filterFlows();
    setTimeout(_ => { this.loading = false }, 300);
  }

  private filterFlows() {
    this.publishedFlows = this.flows.filter(f => {
      let validkw, validchannel, validBotType;
      validkw = this.isKwValid(this.keywordfilterTag, f.name);
      validchannel = this.isChannelValid(this.channelFilterTag, f.channel);
      validBotType = this.isBotTypeValid(this.botTypeFilterTag, f);
      return validchannel && validkw && validBotType && f.ActiveProductionVersion != null;
    });

    this.stagingFlows = this.flows.filter((f) => {
      let validkw, validchannel, validBotType;
      validkw = this.isKwValid(this.keywordfilterTag, f.name);
      validchannel = this.isChannelValid(this.channelFilterTag, f.channel);
      validBotType = this.isBotTypeValid(this.botTypeFilterTag, f);
      return validkw && validchannel && validBotType && f.ActiveStagingVersion != null;
    });
  }

  removeFilterBotType() {
    this.botTypeFilterTag = undefined;
    this.onFilter(this.keywordfilterTag, this.channelFilterTag, undefined);
  }
  removeFilterKeyword() {
    this.keywordfilterTag = undefined;
    this.onFilter(undefined, this.channelFilterTag, this.botTypeFilterTag);
  }

  removeFilterChannel() {
    this.channelFilterTag = undefined;
    this.onFilter(this.keywordfilterTag, undefined, this.botTypeFilterTag);
  }

  private isKwValid(kw: string, name: string): boolean {
    if (kw == undefined) {
      return true;
    } else {
      return name.normalize("NFD").replace(/[\u0300-\u036f]/g, "").toLowerCase().includes(
        kw.normalize("NFD").replace(/[\u0300-\u036f]/g, "").toLowerCase()
      );
    }
  }

  private isChannelValid(ch: string, channel: string) {
    if (ch == undefined) {
      return true;
    } else {
      return (channel == ch);
    }
  }

  private isBotTypeValid(botType: any, flow: FlowDefinition) {
    if (botType == undefined) {
      return true;
    }
    if (flow.modules !== undefined && flow.modules.length > 0) {
      return botType === BotType.MasterBot;
    } else {
      return botType === BotType.Simple;
    }
  }

  onCreate(flowName, channel, flowType) {
    console.log(`Creando un nuevo flow=${flowName} para el canal=${channel}`);
    this.loading = true;
    var blob = this.editorService.createNewFromTemplate(flowName, channel);

    this.serverService.createFlow(flowName, channel, blob, flowType, null)
      .pipe(finalize(() => {
        this.loading = false;
      }))
      .subscribe((status: StatusResponse) => {
        this.modalService.destroy();

        let permissions = [];
        let { uid } = getTokenPayload();
        if (typeof (status.data.permissions) !== 'undefined' && status.data.permissions.length > 0) {
          permissions.push(status.data.permissions.find(p => p.userId === Number(uid)));
        }

        //this.editorService.setCurrentFlowId(status.data.id as number, false, channel, flowName, flowType, permissions);

        if (this.companyName.length > 0) {
          this.routerService.navigateByUrl(`${this.companyName}/edit-flow/${status.data.id}/false`);
        }
        else {
          this.routerService.navigateByUrl(`edit-flow/${status.data.id}/false`);
        }
      },
        error => {
          var inputs: any = {};
          if (error.error != null) {
            inputs.Title = 'ERROR';
            inputs.Desc = error.error.message;
          }
          this.modalService.init(ErrorPopupComponent, inputs, {});
        });
  }

  onClone(flow: FlowDefinition) {
    this.loading = true;
    this.serverService.duplicateFlow(flow.id, flow.name + " copia")
      .pipe(finalize(() => {
        this.loading = false;
      }))
      .subscribe((status: StatusResponse) => {
        this.ngOnInit();
      },
        error => {
          this.modalService.init(ErrorPopupComponent, {}, {});
        });
  }

  onUpload(flow: FlowDefinition, isPublished: boolean) {
    var vid = isPublished ? flow.ActiveProductionVersion.id : flow.ActiveStagingVersion.id;
    var vnumber = isPublished ? flow.ActiveProductionVersion.number : flow.ActiveStagingVersion.number;
    let deleteInfo = new DeleteInput();
    deleteInfo.ElementName = vnumber;

    let emmitAction = new EventEmitter();
    emmitAction.subscribe(() => {
      this.fileField.nativeElement.click();

      var self = this;
      this.fileField.nativeElement.onchange = async function (event) {
        var fileList = self.fileField.nativeElement.files;
        if (fileList.length > 0) {
          var reader = new FileReader();
          reader.onload = function () {
            var text = reader.result as string;
            var contents = null;
            var error = false;
            var errorTitle = null;
            var errorDesc = null;
            var definition = null;
            var emmitActionChannel = new EventEmitter();

            try {
              contents = JSON.parse(text);
              if (typeof (contents.channel) === 'undefined' || contents.channel === null) {
                error = true;
                errorTitle = 'UPLOAD_ERROR';
                errorDesc = 'UPLOAD_ERROR_INVALIDCONTENTS';
              }
              else if ((typeof (contents.type) === 'undefined' || contents.type === null) &&
                (typeof (contents.type) !== 'undefined' && contents.type !== null)) {
                error = true;
                errorTitle = 'UPLOAD_ERROR';
                errorDesc = 'UPLOAD_ERROR_INVALIDCONTENTS';
              }
              else if (typeof (flow.type) !== 'undefined' &&
                typeof (contents.type) !== 'undefined' &&
                contents.type !== flow.type) {
                error = true;
                errorTitle = 'UPLOAD_ERROR';
                errorDesc = 'UPLOAD_ERROR_TYPEMISMATCH';
              }
              else if (contents.channel !== flow.channel) {
                self.changeFlowChannel(emmitActionChannel, contents);
              }
              else {
                // Validar si estamos en modo contingencia
                if (self.editorService.getIsContingencyBot()) {
                  contents.def = self.convertUnsupportedPiecesToComments(contents.def);
                }
                definition = JSON.stringify(contents.def);
              }
            }
            catch (e) {
              console.log(e);
              error = true;
              errorTitle = 'UPLOAD_ERROR';
              errorDesc = 'UPLOAD_ERROR_INVALIDJSON';
            }

            emmitActionChannel.subscribe((def?) => {
              if (def) {
                error = false;
                definition = def;
              }
              if (!error) {
                let state: ChatDefinition = null;
                try {
                  state = TypedJSON.parse(definition, ChatDefinition);

                  state.YSocialSettings.Url = ySocialUrl();

                  state.DefaultBlocks = state.DefaultBlocks.filter(b => self.editorService.isDefaultBlockValidForChannel(b.Id, flow.channel))

                  state.BlockGroups.forEach(g => {
                    g.Blocks.forEach(b => {
                      b.Pieces = b.Pieces.filter(p => self.editorService.isPieceSupportedByChannel(p, flow.channel));
                      b.Pieces = b.Pieces.filter(p => self.editorService.isPieceSupportedByType(p, flow.type));
                    });
                  });

                  state.DefaultBlocks.forEach(b => {
                    b.Pieces = b.Pieces.filter(p => self.editorService.isPieceSupportedByChannel(p, flow.channel));
                    b.Pieces = b.Pieces.filter(p => self.editorService.isPieceSupportedByType(p, flow.type));
                  });

                  state.BlockList.forEach(b => {
                    b.Pieces = b.Pieces.filter(p => self.editorService.isPieceSupportedByChannel(p, flow.channel));
                    b.Pieces = b.Pieces.filter(p => self.editorService.isPieceSupportedByType(p, flow.type));
                  });

                  state.DefaultBlocks.forEach(block => {
                    block.ModuleId = flow.id;
                  });

                  state.BlockList.forEach(block => {
                    block.ModuleId = flow.id;
                  });

                  state.BlockGroups.forEach(g => {
                    g.ModuleId = flow.id;
                  });

                  state.BlockDeletedList = [];
                  state.GroupDeletedList = [];

                  let oldDefinition = definition;
                  definition = TypedJSON.stringify(state, ChatDefinition, { preserveNull: true });

                  if (self.editorService.exceedMaxBlocks(flow, state)) {
                    error = true;
                    errorTitle = 'UPLOAD_ERROR';
                    errorDesc = 'UPLOAD_ERROR_INVALIDBLOCKS_LITE';
                  }

                  if (self.editorService.exceedMaxCommands(flow, state)) {
                    error = true;
                    errorTitle = 'UPLOAD_ERROR';
                    errorDesc = 'UPLOAD_ERROR_INVALIDCOMMANDS_LITE';
                  }

                  if (definition !== oldDefinition) {
                    console.log(`Se filtraron piezas por no ser compatibles`);
                  }

                } catch (e) {
                  console.log(`No se pudo deserializar el JSON tipado ${contents.def}: ${e}`);
                  error = true;
                  errorTitle = 'UPLOAD_ERROR';
                  errorDesc = 'UPLOAD_ERROR_INVALIDJSON';
                }
              }

              if (!error) {
                self.loading = true;
                console.log(`[IMPORTACIÓN] Iniciando importación de flujo "${flow.name}". Tamaño: ${Math.round(definition.length / 1024)} KB`);
                self.serverService.overrideFlow(flow.id, vid, definition as string)
                  .subscribe((status: StatusResponse) => {
                    console.log(`[IMPORTACIÓN] Flujo "${flow.name}" importado correctamente`);
                    self.editorService.onFlowUploaded.emit(flow.name);
                    self.ngOnInit();
                  },
                    error => {
                      console.error(`[IMPORTACIÓN] Error al importar flujo "${flow.name}":`, error);
                      var err = null, desc = null;
                      if (error && error.status == 403) {
                        err = 'UPLOAD_ERROR';
                        desc = 'UPLOAD_ERROR_INVALIDCONTENTS'
                      }
                      self.modalService.init(
                        ErrorPopupComponent,
                        { Title: err, Desc: desc },
                        {});
                    });
              } else {
                self.modalService.init(
                  ErrorPopupComponent,
                  { Title: errorTitle, Desc: errorDesc },
                  {});
              }
            });

            if (!error) {
              emmitActionChannel.emit();
            } else {
              self.modalService.init(
                ErrorPopupComponent,
                { Title: errorTitle, Desc: errorDesc },
                {});
            }
          };
          reader.readAsText(fileList[0]);
        }
      }
    });

    this.modalService.init(
      DeleteGroupQuestionComponent,
      {
        DeleteDetail: deleteInfo, Title: 'ARE_YOU_SURE_OVERRIDE_QUESTION',
        HideAffected: true, HideConfirmation: true, deleteText: 'ACCEPT'
      }, { DeleteAction: emmitAction });
  }

  changeFlowChannel(action, contents) {
    let emmitAction = new EventEmitter();
    let definition;
    emmitAction.subscribe(() => {
      definition = JSON.stringify(contents.def);
      action.emit(definition);
    });
    this.modalService.init(
      ChangeGroupChannelQuestionComponent,
      {
        Title: 'ARE_YOU_SURE_CHANGE_CHANNEL',
        deleteText: 'ACCEPT'
      }, { ChangeChannelAction: emmitAction });

  }

  onDownload(flow: FlowDefinition, isPublished: boolean) {
    this.loading = true;
    this.serverService.downloadFlow(flow.id, isPublished ? flow.ActiveProductionVersion.id : flow.ActiveStagingVersion.id)
      .pipe(finalize(() => {
        this.loading = false;
      }))
      .subscribe(async (blobResponse: any) => {
        try {
          // Si la respuesta es una promesa (debido a la descompresión), esperar a que se resuelva
          const blob = blobResponse instanceof Promise ? await blobResponse : blobResponse;

          // Doing it this way allows you to name the file
          var link = document.createElement('a');
          //link.style = "display: none";
          link.href = window.URL.createObjectURL(blob);
          link.download = `Flow ${flow.name}.json`;
          document.body.appendChild(link);
          link.click();
          setTimeout(function () {
            document.body.removeChild(link);
            window.URL.revokeObjectURL(link.href);
          }, 100);
        } catch (error) {
          console.error('Error al procesar la descarga:', error);
          var errorDesc: any = { Title: 'ERROR', Desc: 'Error al procesar la descarga del flujo' };
          this.modalService.init(ErrorPopupComponent, errorDesc, {});
        }
      },
        error => {
          console.error('Error al descargar flujo:', error);
          var errorDesc: any = { Title: 'ERROR', Desc: 'Error al descargar el flujo' };
          this.modalService.init(ErrorPopupComponent, errorDesc, {});
        })
  }

  onPublish(flow: FlowDefinition) {
    let errorInfo: any = {
      title: <string>'INVALID_CHAT_TITLE',
      desc: <string>'INVALID_CHAT_DESCRIPTION'
    };

    if (typeof (flow.ActiveStagingVersion.blob) !== 'undefined' && flow.ActiveStagingVersion.blob !== null) {
      if (!this.isFlowValid(flow, errorInfo)) {
        this.modalService.init(ErrorPopupComponent, { Title: errorInfo.title, Desc: errorInfo.desc }, {});
        return;
      }

      this.publish(flow);
    }
    else {
      this.serverService.getFlow(flow.id)
        .subscribe((downloadedFlow: FlowDefinition) => {
          flow.ActiveProductionVersion = downloadedFlow.ActiveProductionVersion;
          flow.ActiveStagingVersion = downloadedFlow.ActiveStagingVersion;

          if (!this.isFlowValid(flow, errorInfo)) {
            this.modalService.init(ErrorPopupComponent, { Title: errorInfo.title, Desc: errorInfo.desc }, {});
            return;
          }

          this.publish(flow);
        });
    }
  }

  publish(flow: FlowDefinition) {
    let deleteInfo = new DeleteInput();
    let emitAction = new EventEmitter();
    emitAction.subscribe(() => {
      this.loading = true;
      this.serverService.publishFlow(flow.id)
        .subscribe((status: StatusResponse) => {
          this.editorService.onFlowPublished.emit(flow.name);
          this.ngOnInit();
        },
          error => {
            this.loading = false;
            this.modalService.init(ErrorPopupComponent, {}, {});
          });
    });

    deleteInfo.ElementName = flow.name;
    this.modalService.init(
      DeleteGroupQuestionComponent,
      {
        DeleteDetail: deleteInfo,
        Title: 'ARE_YOU_SURE_PUBLISH_QUESTION_FLOW',
        HideAffected: true,
        HideConfirmation: true,
        deleteText: 'ACCEPT'
      },
      { DeleteAction: emitAction }
    );
  }

  isFlowValid(flow: FlowDefinition, errorInfo: any): boolean {
    console.log(`[DEBUG Dashboard] Validando flujo. Tipo de blob: ${typeof flow.ActiveStagingVersion.blob}`);
    let blobStr = typeof flow.ActiveStagingVersion.blob === 'string'
      ? flow.ActiveStagingVersion.blob
      : JSON.stringify(flow.ActiveStagingVersion.blob);
    this.editorService.restoreFromSerializedString(blobStr, flow.channel);
    this.editorService.setCurrentFlow(flow, false);
    var master = new ModuleDefinition();
    master.createMaster();
    master.id = flow.id;
    this.editorService.setCurrentModule(master);
    return this.editorService.isChatValid(errorInfo);
  }

  onDelete(flow: FlowDefinition, isProductive: boolean) {
    let deleteInfo = new DeleteInput();
    let emitAction = new EventEmitter();
    emitAction.subscribe(() => {

      this.deleteFlow(flow, isProductive);
    });
    deleteInfo.ElementName = flow.name;
    this.modalService.init(
      DeleteGroupQuestionComponent,
      { DeleteDetail: deleteInfo, Title: 'ARE_YOU_SURE_QUESTION_FLOW', HideAffected: true }, { DeleteAction: emitAction });
  }

  deleteFlow(flow: FlowDefinition, isProductive: boolean) {
    this.loading = true;
    this.serverService.deleteFlow(flow.id, isProductive)
      .pipe(finalize(() => {
        this.loading = false;
      }))
      .subscribe((status: StatusResponse) => {

        this.editorService.onFlowDeleted.emit(flow.name);
        this.ngOnInit();

      },
        error => {
          var errorDesc: any = {};
          if (error.status == 403) {
            errorDesc.Title = 'CANNOT_DELETE_FLOW_TITLE';
            errorDesc.Desc = error.error.code == 1002 ? 'CANNOT_DELETE_FLOW_DESC' : 'CANNOT_DELETE_FLOW_USED_DESC';
          }
          this.modalService.init(ErrorPopupComponent, errorDesc, {});
        });
  }
  private convertUnsupportedPiecesToComments(def: any): any {
    if (def.BlockList) {
      def.BlockList = this.convertBlockPieces(def.BlockList);
    }

    if (def.DefaultBlocks) {
      def.DefaultBlocks = this.convertBlockPieces(def.DefaultBlocks);
    }

    return def;
  }

  private convertBlockPieces(blocks: any[]): any[] {
    return blocks.map(block => {
      if (block.Pieces) {
        block.Pieces = block.Pieces.map(piece => {
          if (!this.isPieceAllowedInContingency(piece)) {
            const translationKey = DISABLED_CONTINGENCY_PIECES[piece.type] || piece.type;
            const translatedPieceName = this.translate.instant(translationKey);
            return {
              type: "comment-piece",
              Id: piece.Id,
              Uid: piece.Uid,
              Text: `${this.translate.instant('ORIGINAL_PIECE')}: ${translatedPieceName}`,
              __type: "CommentPiece"
            };
          }
          return piece;
        });
      }
      return block;
    });
  }

  private isPieceAllowedInContingency(piece: any): boolean {
    return !Object.keys(DISABLED_CONTINGENCY_PIECES).includes(piece.type);
  }

  getChannelType(channel: string): string {
    let name = ChannelTypesToString(channel);
    return name;
  }

  getBotType(botType): string {
    return BotTypeToString(botType);
  }

  getChannelClass(channel: string): string {
    return FlowDefinition.getFlowClass(channel);
  }

  ngOnInit() {
    this.loading = true;
    this.flowsLoaded = false;
    this.serverService.getFlows()
      .pipe(finalize(() => {
        this.loading = false;
        this.flowsLoaded = true;
      }))
      .subscribe((flows: FlowDefinition[]) => {
        this.flows = flows.filter(flow => flow.master_flow_id === null);
        let flowModules = flows.filter(flow => flow.master_flow_id !== null);
        flowModules.forEach(element => {
          if (element.master_flow_id !== null) {
            let currentFlow = this.flows.find(item => item.id === element.master_flow_id);
            if (currentFlow !== undefined && currentFlow !== null && currentFlow.id === element.master_flow_id) {
              if (currentFlow.modules === undefined) {
                currentFlow.modules = [];
              }
              currentFlow.modules.push(element);
            }
          }
        });


        this.publishedFlows = this.flows.filter(f => f.ActiveProductionVersion != null);
        this.stagingFlows = this.flows.filter(f => f.ActiveStagingVersion != null);
      },
        error => {
          this.modalService.init(ErrorPopupComponent, {}, {});
        });

    this.keywordfilterTag = undefined;
    this.channelFilterTag = undefined;

    let tokenPayload = getTokenPayload();
    //console.log(tokenPayload);
    this.isAdmin = tokenPayload.admin;
    this.canEdit = tokenPayload.edit;
    this.canPublish = tokenPayload.publish;
    this.canSeeLogs = this.isAdmin && !this.editorService.getIsContingencyBot();
    this.canSeeStatistics = tokenPayload.seeStatistics && !this.editorService.getIsContingencyBot();
    this.canAccessYSmart = tokenPayload.canAccessYSmart && ySmartEnabled().toLocaleLowerCase() === "true" && !this.editorService.getIsContingencyBot();
    //console.log(`El usuario tiene los permisos: canEdit=${this.canEdit} y canPublish=${this.canPublish} y canSeeStatistics=${this.canSeeStatistics} y lang=${tokenPayload.lang} y canAccessYSmart=${this.canAccessYSmart}` );

    this.paramsSub = this.route.params.subscribe(params => {
      if (params['companyName']) {
        this.companyName = params['companyName'];
        this.homeUrl = `/${this.companyName}/home`;
        this.settingsUrl = `/${this.companyName}/settings`;
        this.globalStatisticUrl = `/${this.companyName}/gobalStatistics`;
        this.logsErrorUrl = `/${this.companyName}/logsError`;
      }
      else {
        this.companyName = '';
        this.homeUrl = `/home`;
        this.settingsUrl = `/settings`;
        this.globalStatisticUrl = `/gobalStatistics`;
        this.logsErrorUrl = `/logsError`;
      }
    });

    var self = this;
    this.serverService.keepSession();
    this.keepSessionInterval = window.setInterval(function () {
      self.serverService.keepSession().subscribe(() => {
      });
    }, environment.keepAliveDelay);
  }

  ngOnDestroy() {
    if (this.paramsSub != null) {
      this.paramsSub.unsubscribe();
    }
    clearInterval(this.keepSessionInterval);
  }

  logout() {
    this.doc.location.pathname = Location.joinWithSlash(getBaseUrl(), '/log_out_from_site');
  }

  showSettings() {
    this.router.navigateByUrl(this.settingsUrl);
  }

  showGlobalStatistic() {
    this.router.navigateByUrl(this.globalStatisticUrl);
  }

  showStatistics() {
    return this.canSeeStatistics;
  }

  accessYSmart() {
    this.serverService.getAccessySmart().subscribe(({ success, data }) => {
      if (success) {
        let tokenPayload = getTokenPayload();
        window.open(`${data}&lang=${tokenPayload.lang}`, "_blank");
      }
    }, (error) => {
      let errorMessage = 'Error desconocido';
      if (error.error && error.error.message) {
        errorMessage = error.error.message;
      } else if (error.message) {
        errorMessage = error.message;
      }

      const statusCode = error.status ? `[${error.status}]` : '';
      console.error(`Error al traer los proyectos de cognitividad${statusCode}: ${errorMessage}`);

      if (error.error && error.error.stack) {
        console.error('Stack:', error.error.stack);
      }
    });
  }

  onselectFlow() {
    this.loading = true;
  }
}
