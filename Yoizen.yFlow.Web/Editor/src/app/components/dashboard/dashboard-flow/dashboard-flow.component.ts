import { Component, OnInit, Input, Output, EventEmitter, OnDestroy } from '@angular/core';
import { ActivatedRoute, Router } from '@angular/router'
import { TranslateService } from '@ngx-translate/core';
import { EditorService } from '../../../services/editor.service';
import { FlowDefinition } from '../../../models/FlowDefinition';
import { ChannelTypes } from '../../../models/ChannelType'
import { ServerService } from "../../../services/server.service";
import { geflowsTypes, getTokenPayload } from 'src/app/Utils/window';
import { FlowTypesToString } from 'src/app/models/FlowType';
import { FlowModuleResponse } from 'src/app/models/FlowModuleResponse';
import { ModuleDefinition } from 'src/app/models/ModuleDefinition';
import { finalize } from 'rxjs/operators';
import { ModalService } from 'src/app/services/Tools/ModalService';
import { DashboardModuleModalComponent } from '../dashboard-module-modal/dashboard-module-modal.component';
import { ToasterService } from 'angular2-toaster';

@Component({
  selector: 'app-dashboard-flow',
  templateUrl: './dashboard-flow.component.html',
  styleUrls: ['./dashboard-flow.component.scss']
})
export class DashboardFlowComponent implements OnInit, OnDestroy {
  @Input() model: FlowDefinition;
  @Input() showActions: boolean = true;
  canPublish: boolean = true;
  canEdit: boolean = true;
  canSeeStatistics: boolean = false;
  @Input() showCloneButton: boolean = true;
  @Input() onSelect: Function = null;

  @Output() onDelete: EventEmitter<FlowDefinition> = new EventEmitter<FlowDefinition>();
  @Output() onClone: EventEmitter<FlowDefinition> = new EventEmitter<FlowDefinition>();
  @Output() onUpload: EventEmitter<FlowDefinition> = new EventEmitter<FlowDefinition>();
  @Output() onDownload: EventEmitter<FlowDefinition> = new EventEmitter<FlowDefinition>();
  @Output() onDownloadProd: EventEmitter<FlowDefinition> = new EventEmitter<FlowDefinition>();
  @Output() onPublish: EventEmitter<FlowDefinition> = new EventEmitter<FlowDefinition>();
  @Output() onselectFlow: EventEmitter<FlowDefinition> = new EventEmitter<FlowDefinition>();
  flowsTypes: string[] = [];
  icon: string;
  paramsSub: any;
  showFlowType: boolean = false;
  isFolder: boolean = false;

  constructor(protected editorService: EditorService,
    private serverService: ServerService,
    private routerService: Router,
    private translate: TranslateService,
    private route: ActivatedRoute,
    private modalService: ModalService,
    private toasterService: ToasterService,
    private translateService: TranslateService) {
  }

  ngOnInit() {
    let tokenPayload = getTokenPayload();
    if (typeof (geflowsTypes()) !== 'undefined') {
      geflowsTypes().split(',').forEach(item => {
        this.flowsTypes.push(item);
      });
    }
    this.showFlowType = this.flowsTypes.length === 2;
    this.canPublish = this.model.users_permissions_flows ? this.model.users_permissions_flows[0].canPublish : tokenPayload.publish;
    this.canEdit = this.model.users_permissions_flows ? this.model.users_permissions_flows[0].canEdit : tokenPayload.edit;
    this.isFolder = this.model.modules !== undefined && this.model.modules.length > 0;
    this.canSeeStatistics = this.model.users_permissions_flows ? this.model.users_permissions_flows[0].canSeeStatistics : tokenPayload.seeStatistics;
  }

  selectFlow($event: Event, isStaging) {
    if (typeof (this.onSelect) !== 'undefined' && this.onSelect !== null) {
      this.onSelect(this.model, isStaging);
    }
    else {
      let target = <Element>$event.target;
      if (target.classList.contains('dropdown-toggle') || target.parentElement.classList.contains('dropdown-toggle')) {
        return;
      }
      this.onselectFlow.emit();
      if (!this.isFolder && !this.canEdit && !this.canPublish) {
        if (!this.canSeeStatistics) {
          this.toasterService.pop('error', this.translateService.instant('DONT_HAVE_PERMISSION'));
          return;
        }

        if (this.model.ActiveProductionVersion === undefined || this.model.ActiveProductionVersion === null) {
          this.toasterService.pop('error', this.translateService.instant('DONT_HAVE_PRODUCTION_VERSION'));
          return;
        }

        if (this.model.master_flow_id !== null) {
          this.toasterService.pop('error', this.translateService.instant('DONT_HAVE_PERMISSION'));
          return;
        }
      }

      // Verificar si hay errores conocidos en el flujo
      if (this.model.ActiveStagingVersion && this.model.ActiveStagingVersion.blob &&
          typeof this.model.ActiveStagingVersion.blob === 'object' &&
          this.model.ActiveStagingVersion.blob.error) {
        // Mostrar mensaje de error
        this.toasterService.pop('error', this.model.ActiveStagingVersion.blob.error);
        console.error(`[ERROR] Error al cargar el flujo: ${this.model.ActiveStagingVersion.blob.error}`);
        return;
      }

      if (this.model.modules !== undefined && this.model.modules.length > 0) {
        this.editorService.setCurrentFlow(this.model, !isStaging);
        this.paramsSub = this.route.params.subscribe(params => {
          if (params['companyName']) {
            this.routerService.navigateByUrl(`${params['companyName']}/flow/${this.model.id}/modules/${!isStaging}`);
          }
          else {
            this.routerService.navigateByUrl(`/flow/${this.model.id}/modules/${!isStaging}`);
          }
        });
        return;
      }

      this.serverService.getFlowModules(this.model.id)
        .pipe(finalize(() => {
        }))
        .subscribe((response: FlowModuleResponse) => {
          if (response !== null) {
            let master = new ModuleDefinition();
            master.createMaster();
            master.id = Number(this.model.id);

            response.flows = response.flows.map(flow => {
              let module = new ModuleDefinition();
              module.init(flow);
              return module
            });
            response.flows.push(master);
            this.editorService.setModules(response.flows);

            this.serverService.getFlow(this.model.id)
              .subscribe((flow: FlowDefinition) => {
                this.model.ActiveProductionVersion = flow.ActiveProductionVersion;
                this.model.ActiveStagingVersion = flow.ActiveStagingVersion;
                this.model.isMasterBot = flow.isMasterBot;

                if ((isStaging && !flow.ActiveStagingVersion) || (!isStaging && !flow.ActiveProductionVersion)) {
                  console.error(`[ERROR] La versión ${isStaging ? 'staging' : 'production'} no está disponible para el flujo ${this.model.id}`);
                  this.toasterService.pop('error', this.translateService.instant('ERROR_VERSION_NOT_AVAILABLE'));
                  return;
                }

                // Verificar si hay errores conocidos en el flujo
                if ((isStaging && flow.ActiveStagingVersion && flow.ActiveStagingVersion.blob &&
                    typeof flow.ActiveStagingVersion.blob === 'object' &&
                    flow.ActiveStagingVersion.blob.error) ||
                    (!isStaging && flow.ActiveProductionVersion && flow.ActiveProductionVersion.blob &&
                    typeof flow.ActiveProductionVersion.blob === 'object' &&
                    flow.ActiveProductionVersion.blob.error)) {
                  // Obtener el mensaje de error
                  const errorMsg = isStaging ?
                    flow.ActiveStagingVersion.blob.error :
                    flow.ActiveProductionVersion.blob.error;

                  // Mostrar mensaje de error
                  this.toasterService.pop('error', errorMsg);
                  console.error(`[ERROR] Error al cargar el flujo: ${errorMsg}`);
                  return;
                }

                let blob = isStaging ? flow.ActiveStagingVersion.blob : flow.ActiveProductionVersion.blob;
                console.log(`[DEBUG DashboardFlow] Tipo de blob: ${typeof blob}`);

                // Manejar el caso donde blob es undefined debido a carga en fragmentos
                let blobStr;
                if (blob === undefined || blob === null) {
                  console.log(`[DEBUG DashboardFlow] blob es undefined/null, obteniendo flujo completo usando downloadFlowInChunks`);
                  // Para flujos grandes, necesitamos usar downloadFlowInChunks para obtener el flujo completo
                  this.serverService.downloadFlowInChunks(this.model.id).subscribe(
                    (fullFlow: FlowDefinition) => {
                      let fullBlob = isStaging ? fullFlow.ActiveStagingVersion.blob : fullFlow.ActiveProductionVersion.blob;
                      let fullBlobStr;

                      if (typeof fullBlob === 'string') {
                        fullBlobStr = fullBlob;
                      } else if (fullBlob && typeof fullBlob === 'object') {
                        // Si es un objeto, intentamos extraer el contenido sin referencias circulares
                        try {
                          fullBlobStr = JSON.stringify(fullBlob);
                        } catch (circularError) {
                          console.warn(`[DEBUG DashboardFlow] Error de referencia circular, usando estructura básica:`, circularError);
                          fullBlobStr = '{"BlockGroups":[],"BlockList":[]}';
                        }
                      } else {
                        fullBlobStr = '{"BlockGroups":[],"BlockList":[]}';
                      }

                      console.log(`[DEBUG DashboardFlow] Flujo completo obtenido. Tamaño: ${Math.round(fullBlobStr.length / 1024)} KB`);
                      this.editorService.restoreFromSerializedString(fullBlobStr, this.model.channel);

                      // Continuar con la navegación
                      this.paramsSub = this.route.params.subscribe(params => {
                        if (params['companyName']) {
                          this.routerService.navigateByUrl(`${params['companyName']}/edit-flow/${this.model.id}/${!isStaging}`);
                        }
                        else {
                          this.routerService.navigateByUrl(`edit-flow/${this.model.id}/${!isStaging}`);
                        }
                      });

                      this.editorService.setCurrentFlow(this.model, !isStaging);
                    },
                    error => {
                      console.error(`[ERROR DashboardFlow] Error al obtener flujo completo con downloadFlowInChunks:`, error);
                      // Usar flujo vacío como fallback
                      this.editorService.restoreFromSerializedString('{"BlockGroups":[],"BlockList":[]}', this.model.channel);
                    }
                  );
                  return; // Salir temprano para evitar ejecutar el código de abajo
                } else {
                  blobStr = typeof blob === 'string' ? blob : JSON.stringify(blob);
                  this.editorService.restoreFromSerializedString(blobStr, this.model.channel);

                  // Continuar con la navegación para el caso normal
                  this.paramsSub = this.route.params.subscribe(params => {
                    if (params['companyName']) {
                      this.routerService.navigateByUrl(`${params['companyName']}/edit-flow/${this.model.id}/${!isStaging}`);
                    }
                    else {
                      this.routerService.navigateByUrl(`edit-flow/${this.model.id}/${!isStaging}`);
                    }
                  });

                  this.editorService.setCurrentFlow(this.model, !isStaging);
                }
              });
          }
        });
    }
  }

  ngOnDestroy() {
    if (this.paramsSub != null) {
      this.paramsSub.unsubscribe();
    }
  }

  getFlowType(): string {
    return FlowTypesToString(this.model.type);
  }

  getClass(): string {
    return FlowDefinition.getFlowClass(this.model.channel);
  }

  getIcon(): string {
    return FlowDefinition.getFlowIcon(this.model.channel);
  }

  getTooltip(): string {
    if (!this.model || !this.model.ActiveStagingVersion) {
      return null;
    }

    var comments: string = '';
    comments = this.model.ActiveStagingVersion.comments && this.model.ActiveStagingVersion.comments.length > 0 ? this.model.ActiveStagingVersion.comments : null;
    if (comments != null) {
      return this.translate.instant('COMMENTS', { value: comments });
    }

    return comments;
  }
}
