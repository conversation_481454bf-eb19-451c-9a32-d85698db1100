import { Permission } from './../models/Permission';
import { FtpConfigurations } from './../models/FtpConfigurations';
import { FTPData} from './../models/FTPData';
import { DailyByGroups } from './../models/statistics/DailyByGroups';
import {HttpClient, HttpHeaders, HttpParams} from '@angular/common/http';
import {Injectable} from '@angular/core';
import {Location} from '@angular/common';
import {FlowDefinition} from '../models/FlowDefinition';
import {StatusResponse, StatusResponseUser, StatusResponseUsers} from '../models/StatusResponse';
import {Observable, of, from, throwError, Subscriber} from 'rxjs'
import {getBaseUrl, getToken} from '../Utils/window';
import {environment} from '../../environments/environment';
import {FlowVersionResponse} from '../models/FlowVersionResponse';
import {UserPublicInfo} from "../models/UserPublicInfo";
import { Daily } from '../models/statistics/Daily';
import { DailyByYFlow } from '../models/statistics/DailyByYFlow';
import { DailyByBlocks } from '../models/statistics/DailyByBlocks';
import { DailyByCommands } from '../models/statistics/DailyByCommands';
import { DailyByIntegrations } from '../models/statistics/DailyByIntegrations';
import { ReportType } from '../models/statistics/ReportType';
import * as moment from 'moment';
import { DailyByDerivationKey } from '../models/statistics/DailyByDerivationKey';
import { DailyByDefaultAnswers } from '../models/statistics/DailyByDefaultAnswers';
import { DailyByStatisticEvent } from '../models/statistics/DailyByStatisticEvent';
import { DailyByBlocksSequence } from '../models/statistics/DailyByBlocksSequence';
import { DailyByGroupsSequence } from '../models/statistics/DailyByGroupsSequence';
import { ReportsResponse } from '../models/Reports';
import { SFTPData } from '../models/SFTPData';
import { LogServiceErrorResponse } from '../models/LogServiceErrorResponse';
import {SmtpConfiguration} from "../models/SmtpConfiguration";
import {finalize, map, switchMap} from "rxjs/operators";
import {ErrorPopupComponent} from "../components/error-popup/error-popup.component";
import { FlowModuleResponse } from '../models/FlowModuleResponse';
import { ModuleDefinition } from '../models/ModuleDefinition';
import { SAMLConfiguration } from '../models/SAMLConfiguration';
import { SAMLResponse } from '../models/SAMLResponse';
import * as JSZip from 'jszip';

@Injectable()
export class ServerService {
  // todo create helper
  private BaseUrl = Location.joinWithSlash(Location.joinWithSlash(environment.serverUrl, getBaseUrl()), "api/");
  private static KeepSession = 'keep_session';
  private static GetFlows = 'flows';
  private static GetFlowVersions = 'flows/versions/';
  private static GetFlowModules = 'flows/modules/'
  private static GetFlowInfo = 'flows/get_flow_info';
  private static GetFlowChunk = 'flows/get_flow_chunk';
  private static CreateFlow = 'flows/';
  private static SaveFlow = 'flows/';
  private static DeleteFlow = 'flows/';
  private static PublishFlow = 'flows/publish';
  private static DownloadFlow = 'flows/download';
  private static DuplicateFlow = 'flows/duplicate';
  private static ImportFlow = 'flows/import';
  private static ImportFlowChunk = 'flows/import_flow_chunk';
  private static ImportFlowMerge = 'flows/import_flow_merge';
  private static OverrideFlow = 'flows/override_version';
  private static OverrideFlowChunk = 'flows/override_version_chunk';
  private static OverrideFlowMerge = 'flows/override_version_merge';
  private static RestoreFlow = 'flows/restore_version';
  private static RestoreFlowChunk = 'flows/restore_flow_chunk';
  private static RestoreFlowMerge = 'flows/restore_flow_merge';
  private static GetFlowPermissions = 'flows/permissions';
  private static SetFlowPermissions = 'flows/permissions';
  private static GetOpenGraphResolveUrl = 'flows/opengraph/resolveurl';
  private static GetUser = "users/public/";
  private static ChangePassword = "users/password";
  private static ChangeForgottenPassword = "users/forgotten_password";
  private static ListUsers = "users/list/";
  private static UpdateLang = "users/lang/";
  private static CreateUser = "users";
  private static UpdateUser = "users";
  private static DeleteUser = "users";
  private static GlobalStatisticsByMonth = "statistics/dailybymonth/normal";
  private static GlobalStatisticsByDay = "statistics/dailybyday/normal";
  private static GlobalStatisticsByYear = "statistics/dailybyyear/normal";
  private static GlobalStatisticsByInterval = "statistics/dailybyinterval/normal";
  private static GlobalStatisticsByFlowBy = "statistics/daily/byyflow";
  private static GlobalStatisticsByFlowByMonth = "statistics/dailybymonth/byyflow";
  private static GlobalStatisticsByFlowByDay = "statistics/dailybyday/byyflow";
  private static GlobalStatisticsByFlowByYear = "statistics/dailybyyear/byyflow";
  private static GlobalStatisticsByFlowByInterval = "statistics/dailybyinterval/byyflow";
  private static GlobalStatisticsDownloadExcel = "statistics/daily/normal/downloadexcel";
  private static GlobalStatisticsDownloadExcelByFlow = "statistics/daily/byyflow/downloadexcel";
  private static GlobalStatisticsDownloadExcelByIntegration = "statistics/daily/byintegration/downloadexcel";
  private static GlobalStatisticsDownloadExcelByCommands = "statistics/daily/bycommands/downloadexcel";
  private static GlobalStatisticsDownloadExcelByBlocks = "statistics/daily/byblocks/downloadexcel";
  private static GlobalStatisticsDownloadExcelByBlocksSequence = "statistics/daily/byblockssequence/downloadexcel";
  private static GlobalStatisticsDownloadExcelByGroupsSequence = "statistics/daily/bygroupssequence/downloadexcel";
  private static GlobalStatisticsDownloadExcelByAbandonedCases = "statistics/daily/byabandonedcases/downloadexcel";
  private static GlobalStatisticsDownloadExcelByGroups = "statistics/daily/bygroups/downloadexcel";
  private static GlobalStatisticsDownloadExcelByDefaultAnswers = "statistics/daily/bydefaultanswers/downloadexcel";
  private static GlobalStatisticsDownloadExcelByDerivationKey = "statistics/daily/byderivationkey/downloadexcel";
  private static GlobalStatisticsDownloadExcelByStatisticEvent = "statistics/daily/bystatisticevent/downloadexcel";
  private static GlobalStatisticsByAbandonedCases = "statistics/daily/byabandonedcases";
  private static GlobalStatisticsByAbandonedCasesByDay = "statistics/dailybyday/byabandonedcases";
  private static GlobalStatisticsByBlocks = "statistics/daily/byblocks";
  private static GlobalStatisticsByblocksByDay = "statistics/dailybyday/byblocks";
  private static GlobalStatisticsByGroups = "statistics/daily/bygroups";
  private static GlobalStatisticsByGroupsByDay = "statistics/dailybyday/bygroups";
  private static IntegrationsStatisticsByday = "statistics/dailybyday/byintegrations";
  private static EventsStatisticsByday = "statistics/dailybyday/bystatisticevent";
  private static CommandsStatisticsByday = "statistics/dailybyday/bycommands";
  private static GlobalStatisticsByDefaultAnswers = "statistics/daily/bydefaultanswers";
  private static GlobalStatisticsByCommands = "statistics/daily/bycommands";
  private static GlobalStatisticsByIntergations = "statistics/daily/byintegrations";
  private static GlobalStatisticsByDerivationKey = "statistics/daily/byderivationkey";
  private static GlobalStatisticsByStatisticEvent = "statistics/daily/bystatisticevent";
  private static GlobalStatisticsByBlocksSequence = "statistics/daily/byblockssequence";
  private static GlobalStatisticsByGroupsSequence = "statistics/daily/bygroupssequence";
  private static StatisticsDetailDownloadExcelByStatisticEvent = "statistics/detail/bystatisticevent/downloadexcel";
  private static GetAutomaticReportConfig = "configurations/auto_reports";
  private static SetAutomaticReportConfig = "configurations/auto_reports";
  private static GetSmtpConfig = "configurations/smtp";
  private static SetSmtpConfig = "configurations/smtp";
  private static TestSmtpConfig = "configurations/smtp/test";
  private static TestFTPConfig = "configurations/testftp";
  private static TestSFTPConfig = "configurations/testsftp";
  private static GetIntents = 'cognitive_services/intents';
  private static GetIntentInfo = 'cognitive_services/intentinfo';
  private static GetAccessySmart = 'cognitive_services/accessysmart';
  private static GetProjects = 'cognitive_services/projects';
  private static GetEntities = 'cognitive_services/entities';
  private static GetForms = 'cognitive_services/forms';
  private static GetCategories = 'cognitive_services/categories';
  private static GetExtractionFormats = 'cognitive_services/extractionFormats';
  private static GetMetamaps = 'biometric_services/metamaps';
  private static CreateExcelTable = 'excel_import';
  private static DeleteExcelTable = 'excel_import';
  private static UpdateExcelData = 'excel_import/update_data';
  private static UpdateExcelHeaders = 'excel_import/headers';
  private static GetExcelTablesStatus = 'excel_import/get/status';
  private static getTablesByFlow = 'excel_import/get_tables';
  private static UploadPersonalizedTable = 'excel_import/upload';
  private static MergePersonalizedTable = 'excel_import/merge';
  private static GenerateStatisticsReport = 'statistics/reports';
  private static GenerateStatisticsReportByFlow = 'statistics/reports/byflow';
  private static DownloadReport = 'statistics/reports/download';
  private static CancelReport = 'statistics/reports';
  private static GetLogsError = 'logs/error';
  private static DownloadLogsError = 'logs/error/download';
  private static GetSamlConfig = "configurations/saml";
  private static SetSamlConfig = "configurations/saml";
  private static GlobalStatisticsByBlocksUsage = 'statistics/blocks/usage';

  private options: object;
  private optionsDownload: object;
  private optionsUpload: object;
  private static GetWhatsappHSMTemplates = 'flows/whatsapphsmtemplates';
  private static GetWhatsappCatalogs = 'flows/whatsappcatalogs';

  constructor(public http: HttpClient) {
    const offset = moment().utcOffset().toString();
    var headers = new HttpHeaders({
      'Accept': 'application/json',
      'Content-Type': 'application/json',
      'Authorization': 'Bearer ' + getToken(),
      'offset': offset,
    });

    this.options = {headers: headers};

    var headersDownload = new HttpHeaders({
      'Authorization': 'Bearer ' + getToken(),
      'offset': offset,
    });

    let headersUpload = new HttpHeaders({
      'Authorization': 'Bearer ' + getToken(),
      'offset': offset,
    });

    this.optionsDownload = {
      headers: headersDownload,
      'responseType': 'blob' as 'json',
    };

    this.optionsUpload = {
      headers: headersUpload,
      'Content-Type': 'multipart/form-data',
    };
  }

  /**
   * Comprime datos usando JSZip
   * @param data Datos a comprimir (objeto o string)
   * @returns Promise con los datos comprimidos en base64
   */
  private async compressData(data: any): Promise<string> {
    // Convertir a string si es un objeto
    const dataStr = typeof data === 'string' ? data : JSON.stringify(data);

    // Calcular tamaño original en KB
    const originalSizeKB = Math.round(dataStr.length / 1024);
    console.log(`[COMPRESIÓN] Iniciando compresión de flujo. Tamaño original: ${originalSizeKB} KB`);

    // Crear un nuevo archivo ZIP
    const zip = new JSZip();
    // Usar un nombre de archivo consistente para facilitar la depuración
    zip.file('flow_data.json', dataStr);

    // Generar el ZIP como base64
    const zipData = await zip.generateAsync({
      type: 'base64',
      compression: 'DEFLATE',
      compressionOptions: {
        level: 7
      }
    });

    // Calcular tamaño comprimido en KB y ratio de compresión
    const compressedSizeKB = Math.round((zipData.length * 0.75) / 1024); // Aproximación base64 a binario
    const compressionRatio = Math.round((1 - (compressedSizeKB / originalSizeKB)) * 100);
    console.log(`[COMPRESIÓN] Compresión completada. Tamaño comprimido: ${compressedSizeKB} KB. Ratio: ${compressionRatio}%`);

    return zipData;
  }

  /**
   * Descomprime datos en formato ZIP
   * @param zipData Datos comprimidos en formato base64
   * @returns Promise con los datos descomprimidos
   */
  private async decompressData(zipData: string): Promise<any> {
    try {
      // Calcular tamaño comprimido en KB
      const compressedSizeKB = Math.round((zipData.length * 0.75) / 1024); // Aproximación base64 a binario
      console.log(`[DESCOMPRESIÓN] Iniciando descompresión de flujo. Tamaño comprimido: ${compressedSizeKB} KB`);

      // Cargar el ZIP
      const zip = await JSZip.loadAsync(zipData, { base64: true });

      // Obtener el primer archivo
      const files = Object.keys(zip.files);
      if (files.length === 0) {
        throw new Error('Archivo ZIP vacío');
      }

      // Leer el contenido del archivo
      const content = await zip.files[files[0]].async('string');

      // Calcular tamaño descomprimido en KB
      const decompressedSizeKB = Math.round(content.length / 1024);
      console.log(`[DESCOMPRESIÓN] Descompresión completada. Tamaño descomprimido: ${decompressedSizeKB} KB`);

      // Intentar parsear como JSON
      try {
        return JSON.parse(content);
      } catch (e) {
        // Si no es JSON, devolver el contenido como string
        return content;
      }
    } catch (error) {
      console.error('Error al descomprimir datos:', error);
      throw error;
    }
  }

  public getFlows(): Observable<FlowDefinition[]> {
    const request = `${this.BaseUrl}${ServerService.GetFlows}?includeModules=true`;
    return this.http.get<FlowDefinition[]>(request, this.options);
  }

  /**
   * Obtiene información básica de un flujo sin incluir el blob completo
   * @param id ID del flujo
   * @returns Observable con la información básica del flujo
   */
  public getFlowInfo(id: number): Observable<any> {
    const request = `${this.BaseUrl}${ServerService.GetFlowInfo}/${id}`;
    return this.http.get<any>(request, this.options);
  }

  /**
   * Obtiene un fragmento específico de un flujo
   * @param id ID del flujo
   * @param chunkIndex Índice del fragmento (0-based)
   * @returns Observable con el fragmento del flujo
   */
  // Propiedad para almacenar información del último flujo
  private lastFlowInfo: any = null;

  public getFlowChunk(id: number, chunkIndex: number): Observable<string> {
    const request = `${this.BaseUrl}${ServerService.GetFlowChunk}/${id}/${chunkIndex}`;

    // Crear headers con encabezado personalizado para compresión
    const headers = new HttpHeaders({
      'Authorization': 'Bearer ' + getToken(),
      'X-Accept-Zip': 'true'  // Encabezado personalizado en lugar de Accept-Encoding
    });

    // Opciones para la solicitud - usar responseType: 'blob' para datos binarios
    const options = {
      headers: headers,
      responseType: 'blob' as 'json',  // Importante: recibir como blob para manejar datos binarios
      observe: 'response' as 'body'  // Para acceder a los headers de la respuesta
    };

    // Realizar la solicitud y procesar la respuesta
    return this.http.get(request, options).pipe(
      switchMap((response: any) => {
        // Obtener encabezados para determinar el tipo de fragmento
        const contentType = response.body.type || '';
        // No podemos usar el operador de encadenamiento opcional (?.) en TS 3.5.3
        const isCompressedFragment = response.headers && response.headers.get &&
                                    response.headers.get('X-Compressed-Fragment') === 'true';

        // Obtener información de las versiones activas de los encabezados
        if (chunkIndex === 0) {
          // Solo necesitamos esta información en el primer fragmento
          const stagingVersionId = response.headers.get('X-Active-Staging-Version-Id');
          const stagingVersionNumber = response.headers.get('X-Active-Staging-Version-Number');
          const productionVersionId = response.headers.get('X-Active-Production-Version-Id');
          const productionVersionNumber = response.headers.get('X-Active-Production-Version-Number');

          // Almacenar esta información para usarla al reconstruir el flujo
          if (stagingVersionId) {
            this.lastFlowInfo = this.lastFlowInfo || {};
            this.lastFlowInfo.ActiveStagingVersion = {
              id: stagingVersionId,
              number: stagingVersionNumber
            };
            this.lastFlowInfo.ActiveStagingVersionId = stagingVersionId;
          }

          if (productionVersionId) {
            this.lastFlowInfo = this.lastFlowInfo || {};
            this.lastFlowInfo.ActiveProductionVersion = {
              id: productionVersionId,
              number: productionVersionNumber
            };
            this.lastFlowInfo.ActiveProductionVersionId = productionVersionId;
          }

          console.log(`[CHUNKING] Información de versiones obtenida: Staging=${stagingVersionId}#${stagingVersionNumber}, Production=${productionVersionId}#${productionVersionNumber}`);
        }

        // Si es un fragmento comprimido (nuevo formato)
        if (isCompressedFragment || contentType.includes('application/octet-stream')) {
          const responseSize = response.body ? response.body.size : 0;
          console.log(`[DESCOMPRESIÓN] Detectado fragmento comprimido de ${Math.round(responseSize / 1024)} KB (formato nuevo)`);

          // Simplemente devolver el fragmento como está, ya que será combinado y descomprimido después
          return new Observable<string>(observer => {
            // Convertir el blob a base64 para poder combinarlo como string
            const reader = new FileReader();
            reader.onload = () => {
              try {
                // El resultado es una URL de datos, extraer solo la parte base64
                const base64 = (reader.result as string).split(',')[1] || reader.result as string;
                observer.next(base64);
                observer.complete();
              } catch (error) {
                console.error('Error al convertir fragmento a base64:', error);
                observer.error(error);
              }
            };
            reader.onerror = (error) => {
              console.error('Error al leer el blob:', error);
              observer.error(error);
            };
            reader.readAsDataURL(response.body);
          });
        }
        // Formato antiguo: JSON o ZIP
        else if (contentType.includes('application/json') || contentType.includes('text/plain')) {
          // Si es JSON o texto plano, convertir el blob a texto
          return new Observable<string>(observer => {
            const reader = new FileReader();
            reader.onload = () => {
              try {
                const chunkText = reader.result as string;
                observer.next(chunkText);
                observer.complete();
              } catch (error) {
                console.error('Error al leer el fragmento:', error);
                observer.error(error);
              }
            };
            reader.onerror = (error) => {
              console.error('Error al leer el blob como texto:', error);
              observer.error(error);
            };
            reader.readAsText(response.body);
          });
        } else if (contentType.includes('application/zip') ||
                  (response.body && response.body.size > 0 && this.isZipBlob(response.body))) {
          // Si es ZIP (formato antiguo), descomprimir
          const responseSize = response.body ? response.body.size : 0;
          console.log(`[DESCOMPRESIÓN] Detectado fragmento comprimido de ${Math.round(responseSize / 1024)} KB (formato antiguo)`);

          return new Observable<string>(observer => {
            const reader = new FileReader();
            reader.onload = async (e) => {
              try {
                const arrayBuffer = (e.target as FileReader).result as ArrayBuffer;

                // Descomprimir usando JSZip
                const zip = await JSZip.loadAsync(arrayBuffer);

                // Obtener el primer archivo
                const files = Object.keys(zip.files);
                if (files.length === 0) {
                  throw new Error('Archivo ZIP vacío');
                }

                // Leer el contenido del archivo como texto
                const content = await zip.files[files[0]].async('text');
                console.log(`[DESCOMPRESIÓN] Fragmento descomprimido: ${Math.round(content.length / 1024)} KB`);

                observer.next(content);
                observer.complete();
              } catch (error) {
                console.error('Error al descomprimir ZIP:', error);
                observer.error(error);
              }
            };
            reader.onerror = (error) => {
              console.error('Error al leer el blob como ArrayBuffer:', error);
              observer.error(error);
            };
            reader.readAsArrayBuffer(response.body);
          });
        } else {
          // Si no es JSON ni ZIP, propagar un error
          console.error('Tipo de respuesta no reconocido:', contentType);
          return throwError('Tipo de respuesta no reconocido');
        }
      })
    );
  }

  /**
   * Descarga un flujo en fragmentos y los combina
   * @param id ID del flujo
   * @returns Observable con el flujo completo
   */
  public downloadFlowInChunks(id: number): Observable<FlowDefinition> {
    return new Observable<FlowDefinition>(observer => {
      // Primero obtener información básica del flujo
      this.getFlowInfo(id).subscribe(
        flowInfo => {
          // Verificar si tenemos información de tamaño comprimido
          const compressedSize = flowInfo.compressedBlobSize || flowInfo.blobSize;
          const isCompressedFormat = 'compressedBlobSize' in flowInfo;

          console.log(`[CHUNKING] Flujo "${flowInfo.name}" tiene ${flowInfo.totalChunks} fragmentos.`);
          console.log(`[CHUNKING] Tamaño original: ${Math.round(flowInfo.blobSize / 1024)} KB, Tamaño comprimido: ${Math.round(compressedSize / 1024)} KB`);
          console.log(`[CHUNKING] Usando formato ${isCompressedFormat ? 'nuevo (comprimido)' : 'antiguo (sin comprimir)'}`);

          // Siempre usar el método original de fragmentos para flujos grandes (>800KB)
          // Esto asegura que se use el método correcto para flujos grandes
          this.downloadFlowInChunksOriginal(id, flowInfo).subscribe(
            flowData => observer.next(flowData),
            error => observer.error(error),
            () => observer.complete()
          );
        },
        error => {
          console.error('Error al obtener información del flujo:', error);
          observer.error(error);
        }
      );
    });
  }

  /**
   * Método original de descarga en fragmentos (como fallback)
   * @param id ID del flujo
   * @param flowInfo Información del flujo
   * @returns Observable con el flujo completo
   */
  private downloadFlowInChunksOriginal(id: number, flowInfo: any): Observable<FlowDefinition> {
    return new Observable<FlowDefinition>(observer => {
      // Preparar para descargar todos los fragmentos
      const chunks: string[] = new Array(flowInfo.totalChunks).fill('');
      let downloadedChunks = 0;

      // Verificar si estamos usando el nuevo formato de fragmentos comprimidos
      // En TypeScript 3.5.3 no podemos usar el operador de encadenamiento opcional (?.)
      const isCompressedFormat = flowInfo && 'compressedBlobSize' in flowInfo;

      // Función para verificar si todos los fragmentos se han descargado
      const checkAllChunksDownloaded = () => {
        downloadedChunks++;
        const progress = Math.round((downloadedChunks / flowInfo.totalChunks) * 100);
        console.log(`[CHUNKING] Fragmento ${downloadedChunks}/${flowInfo.totalChunks} descargado (${progress}%)`);

        if (downloadedChunks === flowInfo.totalChunks) {
          // Todos los fragmentos descargados, combinarlos
          console.log(`[CHUNKING] Todos los fragmentos descargados. Combinando...`);

          if (isCompressedFormat) {
            // Para el nuevo formato, los fragmentos son partes de un archivo ZIP
            // Primero, convertir todos los fragmentos base64 a un solo buffer
            try {
              // Combinar todos los fragmentos base64 en uno solo
              const combinedBase64 = chunks.join('');

              // Convertir base64 a ArrayBuffer
              const binaryString = window.atob(combinedBase64);
              const bytes = new Uint8Array(binaryString.length);
              for (let i = 0; i < binaryString.length; i++) {
                bytes[i] = binaryString.charCodeAt(i);
              }

              // Descomprimir el ZIP completo
              console.log(`[DESCOMPRESIÓN] Iniciando descompresión del flujo completo...`);
              JSZip.loadAsync(bytes.buffer).then(zip => {
                // Obtener el archivo dentro del ZIP
                const files = Object.keys(zip.files);
                if (files.length === 0) {
                  throw new Error('Archivo ZIP vacío');
                }

                // Leer el contenido como texto
                return zip.files[files[0]].async('string');
              }).then(content => {
                // Parsear el JSON
                console.log(`[DESCOMPRESIÓN] Flujo descomprimido: ${Math.round(content.length / 1024)} KB`);
                const flowData = JSON.parse(content);

                // Asegurarnos de que el flujo tenga la estructura correcta
                // Verificar si el flujo tiene las propiedades necesarias
                // Primero intentar usar la información de lastFlowInfo (obtenida de los headers)
                const versionInfo = this.lastFlowInfo || flowInfo;

                if (!flowData.ActiveStagingVersion && versionInfo.ActiveStagingVersion) {
                  console.log(`[CHUNKING] Reconstruyendo estructura del flujo con versión staging`);
                  // Reconstruir la estructura del flujo
                  flowData.ActiveStagingVersion = {
                    id: versionInfo.ActiveStagingVersion.id,
                    number: versionInfo.ActiveStagingVersion.number,
                    blob: content  // Usar el contenido descomprimido como string
                  };
                }

                if (!flowData.ActiveProductionVersion && versionInfo.ActiveProductionVersion) {
                  console.log(`[CHUNKING] Reconstruyendo estructura del flujo con versión production`);
                  // Reconstruir la estructura del flujo
                  flowData.ActiveProductionVersion = {
                    id: versionInfo.ActiveProductionVersion.id,
                    number: versionInfo.ActiveProductionVersion.number,
                    blob: content  // Usar el contenido descomprimido como string
                  };
                }

                // Asegurar que se incluyan los IDs de versión
                if (!flowData.ActiveStagingVersionId && versionInfo.ActiveStagingVersionId) {
                  flowData.ActiveStagingVersionId = versionInfo.ActiveStagingVersionId;
                }

                if (!flowData.ActiveProductionVersionId && versionInfo.ActiveProductionVersionId) {
                  flowData.ActiveProductionVersionId = versionInfo.ActiveProductionVersionId;
                }

                // Limpiar la información temporal
                this.lastFlowInfo = null;

                console.log(`[CHUNKING] Flujo combinado correctamente. Tamaño: ${Math.round(content.length / 1024)} KB`);
                observer.next(flowData);
                observer.complete();
              }).catch(error => {
                console.error('Error al descomprimir o parsear el flujo:', error);
                observer.error(error);
              });
            } catch (error) {
              console.error('Error al procesar fragmentos comprimidos:', error);
              observer.error(error);
            }
          } else {
            // Para el formato antiguo, los fragmentos son JSON
            const combinedData = chunks.join('');

            try {
              // Intentar parsear como JSON
              const flowData = JSON.parse(combinedData);

              // Asegurarnos de que el flujo tenga la estructura correcta
              // Verificar si el flujo tiene las propiedades necesarias
              // Primero intentar usar la información de lastFlowInfo (obtenida de los headers)
              const versionInfo = this.lastFlowInfo || flowInfo;

              if (!flowData.ActiveStagingVersion && versionInfo.ActiveStagingVersion) {
                console.log(`[CHUNKING] Reconstruyendo estructura del flujo con versión staging`);
                // Reconstruir la estructura del flujo
                flowData.ActiveStagingVersion = {
                  id: versionInfo.ActiveStagingVersion.id,
                  number: versionInfo.ActiveStagingVersion.number,
                  blob: combinedData  // Usar los datos combinados como string
                };
              }

              if (!flowData.ActiveProductionVersion && versionInfo.ActiveProductionVersion) {
                console.log(`[CHUNKING] Reconstruyendo estructura del flujo con versión production`);
                // Reconstruir la estructura del flujo
                flowData.ActiveProductionVersion = {
                  id: versionInfo.ActiveProductionVersion.id,
                  number: versionInfo.ActiveProductionVersion.number,
                  blob: combinedData  // Usar los datos combinados como string
                };
              }

              // Asegurar que se incluyan los IDs de versión
              if (!flowData.ActiveStagingVersionId && versionInfo.ActiveStagingVersionId) {
                flowData.ActiveStagingVersionId = versionInfo.ActiveStagingVersionId;
              }

              if (!flowData.ActiveProductionVersionId && versionInfo.ActiveProductionVersionId) {
                flowData.ActiveProductionVersionId = versionInfo.ActiveProductionVersionId;
              }

              // Limpiar la información temporal
              this.lastFlowInfo = null;

              console.log(`[CHUNKING] Flujo combinado correctamente. Tamaño: ${Math.round(combinedData.length / 1024)} KB`);
              observer.next(flowData);
              observer.complete();
            } catch (error) {
              console.error('Error al parsear JSON combinado:', error);
              observer.error(error);
            }
          }
        }
      };

      // Descargar todos los fragmentos en paralelo (con límite de concurrencia)
      const MAX_CONCURRENT = 2; // Reducir el máximo de descargas simultáneas para evitar sobrecarga
      let activeDownloads = 0;
      let nextChunkIndex = 0;

      const downloadNextChunk = () => {
        if (nextChunkIndex >= flowInfo.totalChunks) {
          return; // No hay más fragmentos para descargar
        }

        if (activeDownloads >= MAX_CONCURRENT) {
          return; // Limitar descargas concurrentes
        }

        const chunkIndex = nextChunkIndex++;
        activeDownloads++;

        this.getFlowChunk(id, chunkIndex).subscribe(
          chunkData => {
            chunks[chunkIndex] = chunkData;
            activeDownloads--;
            checkAllChunksDownloaded();

            // Intentar descargar el siguiente fragmento
            downloadNextChunk();
          },
          error => {
            console.error(`[CHUNKING] Error al descargar fragmento ${chunkIndex}:`, error);
            activeDownloads--;

            // Reintentar este fragmento
            nextChunkIndex--;
            setTimeout(downloadNextChunk, 1000); // Esperar 1 segundo antes de reintentar
          }
        );

        // Intentar iniciar otra descarga si hay capacidad
        if (activeDownloads < MAX_CONCURRENT) {
          downloadNextChunk();
        }
      };

      // Iniciar la descarga de fragmentos
      for (let i = 0; i < Math.min(MAX_CONCURRENT, flowInfo.totalChunks); i++) {
        downloadNextChunk();
      }
    });
  }

  public getFlow(id: number): Observable<FlowDefinition> {
    // Primero obtener información básica del flujo para decidir si usar fragmentos
    return this.getFlowInfo(id).pipe(
      switchMap(flowInfo => {
        // Verificar si tenemos información de tamaño comprimido
        const compressedSize = flowInfo.compressedBlobSize || flowInfo.blobSize;

        // Siempre usar fragmentos para flujos grandes (>800KB)
        if (compressedSize > 800 * 1024) {
          console.log(`[CHUNKING] El flujo es grande (${Math.round(flowInfo.blobSize / 1024)} KB, comprimido: ${Math.round(compressedSize / 1024)} KB), usando carga en fragmentos`);
          return this.downloadFlowInChunks(id);
        } else {
          // Para flujos pequeños, usar el método normal
          const request = `${this.BaseUrl}${ServerService.GetFlows}/${id}`;

          // Crear headers con encabezado personalizado para compresión
          const headers = new HttpHeaders({
            'Authorization': 'Bearer ' + getToken(),
            'X-Accept-Zip': 'true'  // Encabezado personalizado en lugar de Accept-Encoding
          });

          // Opciones para la solicitud - usar responseType: 'blob' para datos binarios
          const options = {
            headers: headers,
            responseType: 'blob' as 'json'  // Importante: recibir como blob para manejar datos binarios
          };

          // Realizar la solicitud y procesar la respuesta
          return this.http.get(request, options).pipe(
            switchMap((response: any) => {
              // Verificar el tipo de contenido para determinar si es JSON o ZIP
              const contentType = response.type || '';

              if (contentType.includes('application/json')) {
                // Si es JSON, convertir el blob a texto y luego parsear
                return new Observable<FlowDefinition>(observer => {
                  const reader = new FileReader();
                  reader.onload = () => {
                    try {
                      const jsonText = reader.result as string;
                      const flowData = JSON.parse(jsonText);
                      observer.next(flowData);
                      observer.complete();
                    } catch (error) {
                      console.error('Error al parsear JSON:', error);
                      observer.error(error);
                    }
                  };
                  reader.onerror = (error) => {
                    console.error('Error al leer el blob como texto:', error);
                    observer.error(error);
                  };
                  reader.readAsText(response);
                });
              } else if (contentType.includes('application/zip') ||
                        (response.size > 0 && this.isZipBlob(response))) {
                // Si es ZIP, descomprimir
                console.log('Detectada respuesta comprimida, descomprimiendo...');

                return new Observable<FlowDefinition>(observer => {
                  const reader = new FileReader();
                  reader.onload = async (e) => {
                    try {
                      const arrayBuffer = (e.target as FileReader).result as ArrayBuffer;

                      // Descomprimir usando JSZip
                      const zip = await JSZip.loadAsync(arrayBuffer);

                      // Obtener el primer archivo
                      const files = Object.keys(zip.files);
                      if (files.length === 0) {
                        throw new Error('Archivo ZIP vacío');
                      }

                      // Leer el contenido del archivo como texto
                      const content = await zip.files[files[0]].async('text');

                      // Parsear el JSON
                      const flowData = JSON.parse(content);
                      observer.next(flowData);
                      observer.complete();
                    } catch (error) {
                      console.error('Error al descomprimir ZIP:', error);
                      observer.error(error);
                    }
                  };
                  reader.onerror = (error) => {
                    console.error('Error al leer el blob como ArrayBuffer:', error);
                    observer.error(error);
                  };
                  reader.readAsArrayBuffer(response);
                });
              } else {
                // Si no es JSON ni ZIP, propagar un error
                console.error('Tipo de respuesta no reconocido:', contentType);
                return throwError('Tipo de respuesta no reconocido');
              }
            })
          );
        }
      })
    );
  }

  /**
   * Verifica si un Blob parece ser un archivo ZIP basado en sus primeros bytes
   * @param blob El blob a verificar
   * @returns true si parece ser un ZIP, false en caso contrario
   */
  private isZipBlob(blob: Blob): boolean {
    // Esta es una verificación preliminar, la verificación real se hace al descomprimir
    return blob.type === 'application/zip' ||
           blob.type === 'application/x-zip-compressed' ||
           blob.type === 'application/octet-stream';
  }

  public getFlowVersions(id: number, page: number): Observable<FlowVersionResponse> {
    const request = `${this.BaseUrl}${ServerService.GetFlowVersions}${id}/${page}`;
    return this.http.get<FlowVersionResponse>(request, this.options);
  }

  public getFlowModules(id: number): Observable<FlowModuleResponse> {
    const request = `${this.BaseUrl}${ServerService.GetFlowModules}${id}`;
    return this.http.get<FlowModuleResponse>(request, this.options);
  }

  /**
   * Sube un flujo en fragmentos para creación
   * @param name Nombre del flujo
   * @param channel Canal del flujo
   * @param blob Datos del flujo
   * @param type Tipo de flujo
   * @param masterId ID del flujo maestro (si es un módulo)
   * @returns Observable con la respuesta del servidor
   */
  private uploadFlowChunksForCreate(name: string, channel: string, blob: string, type: string, masterId: number): Observable<StatusResponse> {
    return new Observable<StatusResponse>(observer => {
      // Verificar que el blob sea un JSON válido antes de procesarlo
      try {
        JSON.parse(blob);
        console.log(`[DEBUG] El blob es un JSON válido`);
      } catch (error) {
        console.error(`[ERROR] El blob no es un JSON válido:`, error);
        observer.error({ error: { success: false, message: 'El contenido del archivo no es un JSON válido' } });
        return;
      }

      // Comprimir el blob
      this.compressData(blob).then(compressedBlob => {
        const originalSizeKB = Math.round(blob.length / 1024);
        const compressedSizeKB = Math.round((compressedBlob.length * 0.75) / 1024);
        const compressionRatio = Math.round((1 - (compressedSizeKB / originalSizeKB)) * 100);
        console.log(`[COMPRESIÓN] Compresión completada para creación. Tamaño comprimido: ${compressedSizeKB} KB. Ratio: ${compressionRatio}%`);

        // Dividir en fragmentos (chunks) de 900KB
        const CHUNK_SIZE = 900 * 1024; // 900KB
        const chunks = this.sliceData(compressedBlob, CHUNK_SIZE);
        const totalChunks = chunks.length;
        console.log(`[CHUNKING] Dividiendo datos en ${totalChunks} fragmentos de ${CHUNK_SIZE / 1024}KB cada uno`);

        // Crear un identificador único para esta carga
        const uploadId = new Date().getTime().toString();
        let uploadedChunks = 0;

        // Primero, crear el flujo vacío para obtener su ID
        const createRequest = `${this.BaseUrl}${ServerService.CreateFlow}`;
        this.http.post<StatusResponse>(
          createRequest,
          {
            name: name,
            channel: channel,
            type: type,
            masterId: masterId,
            isChunked: true,
            uploadId: uploadId,
            totalChunks: totalChunks
          },
          this.options
        ).subscribe(
          response => {
            if (!response.success) {
              observer.error(response);
              return;
            }

            const flowId = response.data.id;
            console.log(`[CHUNKING] Flujo creado con ID ${flowId}, subiendo fragmentos...`);

            // Función recursiva para subir los fragmentos uno por uno
            const uploadNextChunk = () => {
              if (chunks.length === 0) {
                // Todos los fragmentos se han subido, ahora fusionarlos en el servidor
                console.log(`[CHUNKING] Todos los fragmentos subidos. Solicitando fusión...`);
                const mergeRequest = `${this.BaseUrl}flows/create_flow_merge/${flowId}`;

                this.http.post<StatusResponse>(
                  mergeRequest,
                  { uploadId: uploadId, totalChunks: totalChunks },
                  this.options
                ).subscribe(
                  mergeResponse => {
                    console.log(`[CHUNKING] Fusión completada:`, mergeResponse);
                    observer.next(mergeResponse);
                    observer.complete();
                  },
                  error => {
                    console.error(`[CHUNKING] Error al fusionar fragmentos:`, error);
                    observer.error(error);
                  }
                );
                return;
              }

              // Obtener el siguiente fragmento
              const item = chunks.shift();
              const chunkRequest = `${this.BaseUrl}flows/create_flow_chunk/${flowId}`;

              // Crear el cuerpo de la solicitud para este fragmento
              const chunkBody = {
                uploadId: uploadId,
                hash: item.hash,
                totalChunks: totalChunks,
                chunk: item.chunk,
                isCompressed: true
              };

              // Enviar el fragmento
              this.http.post<StatusResponse>(
                chunkRequest,
                chunkBody,
                this.options
              ).subscribe(
                chunkResponse => {
                  uploadedChunks++;
                  const progress = Math.round((uploadedChunks / totalChunks) * 100);
                  console.log(`[CHUNKING] Fragmento ${item.hash + 1}/${totalChunks} subido (${progress}%)`);

                  // Subir el siguiente fragmento
                  uploadNextChunk();
                },
                error => {
                  console.error(`[CHUNKING] Error al subir fragmento ${item.hash}:`, error);

                  // Reintentar este fragmento
                  chunks.unshift(item);
                  setTimeout(uploadNextChunk, 1000); // Esperar 1 segundo antes de reintentar
                }
              );
            };

            // Iniciar la carga de fragmentos
            uploadNextChunk();
          },
          error => {
            console.error(`[CHUNKING] Error al crear flujo:`, error);
            observer.error(error);
          }
        );
      }).catch(error => {
        console.error('Error al comprimir el blob:', error);
        observer.error(error);
      });
    });
  }

  public createFlow(name: string, channel: string, blob: string, type: string, masterId: number): Observable<StatusResponse> {
    const request = `${this.BaseUrl}${ServerService.CreateFlow}`;

    // Siempre comprimir primero para evaluar el tamaño
    return new Observable<StatusResponse>(observer => {
      // Verificar que el blob sea un JSON válido antes de procesarlo
      try {
        if (blob) {
          JSON.parse(blob);
          console.log(`[DEBUG] El blob es un JSON válido`);
        }
      } catch (error) {
        console.error(`[ERROR] El blob no es un JSON válido:`, error);
        observer.error({ error: { success: false, message: 'El contenido del archivo no es un JSON válido' } });
        return;
      }

      // Comprimir el blob
      this.compressData(blob).then(compressedBlob => {
        const originalSizeKB = Math.round(blob.length / 1024);
        const compressedSizeKB = Math.round((compressedBlob.length * 0.75) / 1024);
        const compressionRatio = Math.round((1 - (compressedSizeKB / originalSizeKB)) * 100);
        console.log(`[COMPRESIÓN] Compresión completada para creación. Tamaño comprimido: ${compressedSizeKB} KB. Ratio: ${compressionRatio}%`);

        // Si el blob comprimido es grande (más de 800KB), usar carga en fragmentos
        if (compressedSizeKB > 800) {
          console.log(`[CHUNKING] El blob comprimido es grande (${compressedSizeKB} KB), usando carga en fragmentos`);
          this.uploadFlowChunksForCreate(name, channel, blob, type, masterId).subscribe(
            response => observer.next(response),
            error => observer.error(error),
            () => observer.complete()
          );
          return;
        }

        // Crear headers con indicación de compresión
        const compressHeaders = new HttpHeaders({
          'Accept': 'application/json',
          'Content-Type': 'application/json',
          'Authorization': 'Bearer ' + getToken(),
          'offset': moment().utcOffset().toString(),
          'X-Content-Encoding': 'zip'
        });

        // Enviar los datos comprimidos
        this.http.post<StatusResponse>(
          request,
          {name: name, channel: channel, zipData: compressedBlob, type: type, masterId: masterId},
          {headers: compressHeaders}
        ).subscribe(
          response => observer.next(response),
          error => observer.error(error),
          () => observer.complete()
        );
      }).catch(error => {
        console.error('Error al comprimir el blob:', error);
        // Si falla la compresión, enviar sin comprimir
        this.http.post<StatusResponse>(
          request,
          {name: name, channel: channel, blob: blob, type: type, masterId: masterId},
          this.options
        ).subscribe(
          response => observer.next(response),
          error => observer.error(error),
          () => observer.complete()
        );
      });
    });
  }

  public saveFlow(id: number, name: string, blob: string, comment: string): Observable<StatusResponse> {
    const request = `${this.BaseUrl}${ServerService.SaveFlow}`;

    // Siempre comprimir primero para evaluar el tamaño
    return new Observable<StatusResponse>(observer => {
      // Verificar que el blob sea un JSON válido antes de procesarlo
      try {
        if (blob) {
          JSON.parse(blob);
          console.log(`[DEBUG] El blob es un JSON válido`);
        }
      } catch (error) {
        console.error(`[ERROR] El blob no es un JSON válido:`, error);
        observer.error({ error: { success: false, message: 'El contenido del archivo no es un JSON válido' } });
        return;
      }

      // Comprimir el blob
      this.compressData(blob).then(compressedBlob => {
        const originalSizeKB = Math.round(blob.length / 1024);
        const compressedSizeKB = Math.round((compressedBlob.length * 0.75) / 1024);
        const compressionRatio = Math.round((1 - (compressedSizeKB / originalSizeKB)) * 100);
        console.log(`[COMPRESIÓN] Compresión completada para guardado. Tamaño comprimido: ${compressedSizeKB} KB. Ratio: ${compressionRatio}%`);

        // Crear headers con indicación de compresión
        const compressHeaders = new HttpHeaders({
          'Accept': 'application/json',
          'Content-Type': 'application/json',
          'Authorization': 'Bearer ' + getToken(),
          'offset': moment().utcOffset().toString(),
          'X-Content-Encoding': 'zip'
        });

        // Enviar los datos comprimidos
        this.http.put<StatusResponse>(
          request,
          {id: id, name: name, zipData: compressedBlob, comments: comment},
          {headers: compressHeaders}
        ).subscribe(
          response => observer.next(response),
          error => observer.error(error),
          () => observer.complete()
        );
      }).catch(error => {
        console.error('Error al comprimir el blob:', error);
        // Si falla la compresión, enviar sin comprimir
        this.http.put<StatusResponse>(
          request,
          {id: id, name: name, blob: blob, comments: comment},
          this.options
        ).subscribe(
          response => observer.next(response),
          error => observer.error(error),
          () => observer.complete()
        );
      });
    });
  }

  public deleteFlow(id: Number, isProductive: boolean): Observable<StatusResponse> {
    const request = `${this.BaseUrl}${ServerService.DeleteFlow}${isProductive}`;
    var urlParams = new HttpParams().set('id', id.toString());
    var opts = {headers: this.options['headers'], params: urlParams};
    return this.http.delete<StatusResponse>(request, opts);
  }

  public downloadFlow(id: Number, versionId: Number): Observable<Blob> {
    const request = `${this.BaseUrl}${ServerService.DownloadFlow}/${id}/${versionId}`;

    // Agregar header personalizado para indicar que aceptamos respuestas comprimidas
    var options = {
      'headers': {
        'Authorization': 'Bearer ' + getToken(),
        'X-Accept-Zip': 'true'  // Usar encabezado personalizado en lugar de Accept-Encoding
      },
      'responseType': 'blob' as 'json'
    };

    // Usamos switchMap en lugar de map para manejar correctamente las operaciones asíncronas
    return this.http.get<Blob>(request, options).pipe(
      switchMap((response: Blob) => {
        // Verificar si la respuesta está comprimida
        if (response.type === 'application/zip') {
          // Convertir el blob a ArrayBuffer y luego procesarlo
          return new Observable<Blob>(observer => {
            // Crear un FileReader para leer el Blob como ArrayBuffer
            const reader = new FileReader();
            reader.onload = async (e) => {
              try {
                // Obtener el ArrayBuffer del resultado
                const arrayBuffer = (e.target as FileReader).result as ArrayBuffer;

                // Descomprimir usando JSZip
                const zip = await JSZip.loadAsync(arrayBuffer);

                // Obtener el primer archivo
                const files = Object.keys(zip.files);
                if (files.length === 0) {
                  throw new Error('Archivo ZIP vacío');
                }

                // Leer el contenido del archivo
                const content = await zip.files[files[0]].async('blob');

                // Devolver el contenido descomprimido como Blob
                observer.next(new Blob([content], { type: 'application/json' }));
                observer.complete();
              } catch (error) {
                console.error('Error al descomprimir respuesta:', error);
                // Si hay un error, devolver la respuesta original
                observer.next(response);
                observer.complete();
              }
            };

            reader.onerror = () => {
              console.error('Error al leer el blob como ArrayBuffer');
              observer.next(response);
              observer.complete();
            };

            // Iniciar la lectura del Blob como ArrayBuffer
            reader.readAsArrayBuffer(response);
          });
        }

        // Si no está comprimida, devolver la respuesta original como Observable
        return of(response);
      })
    );
  }

  public publishFlow(id: Number, onlyIntegrations: boolean = false, blob?: string): Observable<StatusResponse> {
    const request = `${this.BaseUrl}${ServerService.PublishFlow}/${id}`;
    const body = {
      onlyIntegrations,
      blob
    };
    return this.http.put<StatusResponse>(request, body, this.options);
  }

  public duplicateFlow(id: Number, newName: string): Observable<StatusResponse> {
    const request = `${this.BaseUrl}${ServerService.DuplicateFlow}/${id}`;
    return this.http.post<StatusResponse>(request, {newName: newName}, this.options);
  }

  /**
   * Sube un flujo en fragmentos para importación
   * @param flow_name Nombre del flujo
   * @param channel Canal del flujo
   * @param blob Datos del flujo
   * @param type Tipo de flujo
   * @returns Observable con la respuesta del servidor
   */
  private uploadFlowChunksForImport(flow_name: string, channel: string, blob: string, type: string): Observable<StatusResponse> {
    return new Observable<StatusResponse>(observer => {
      // Verificar que el blob sea un JSON válido antes de procesarlo
      try {
        JSON.parse(blob);
        console.log(`[DEBUG] El blob es un JSON válido`);
      } catch (error) {
        console.error(`[ERROR] El blob no es un JSON válido:`, error);
        observer.error({ error: { success: false, message: 'El contenido del archivo no es un JSON válido' } });
        return;
      }

      // Comprimir el blob
      this.compressData(blob).then(compressedBlob => {
        const originalSizeKB = Math.round(blob.length / 1024);
        const compressedSizeKB = Math.round((compressedBlob.length * 0.75) / 1024);
        const compressionRatio = Math.round((1 - (compressedSizeKB / originalSizeKB)) * 100);
        console.log(`[COMPRESIÓN] Compresión completada para importación. Tamaño comprimido: ${compressedSizeKB} KB. Ratio: ${compressionRatio}%`);

        // Dividir en fragmentos (chunks) de 900KB
        const CHUNK_SIZE = 900 * 1024; // 900KB
        const chunks = this.sliceData(compressedBlob, CHUNK_SIZE);
        const totalChunks = chunks.length;
        console.log(`[CHUNKING] Dividiendo datos en ${totalChunks} fragmentos de ${CHUNK_SIZE / 1024}KB cada uno`);

        // Crear un identificador único para esta carga
        const uploadId = new Date().getTime().toString();
        let uploadedChunks = 0;

        // Primero, iniciar el proceso de importación
        const importRequest = `${this.BaseUrl}${ServerService.ImportFlow}`;
        this.http.post<StatusResponse>(
          importRequest,
          {
            flow_name: flow_name,
            channel: channel,
            type: type,
            isChunked: true,
            uploadId: uploadId,
            totalChunks: totalChunks
          },
          this.options
        ).subscribe(
          response => {
            if (!response.success) {
              observer.error(response);
              return;
            }

            console.log(`[CHUNKING] Iniciando carga de fragmentos para importación...`);

            // Función recursiva para subir los fragmentos uno por uno
            const uploadNextChunk = () => {
              if (chunks.length === 0) {
                // Todos los fragmentos se han subido, ahora fusionarlos en el servidor
                console.log(`[CHUNKING] Todos los fragmentos subidos. Solicitando fusión...`);
                const mergeRequest = `${this.BaseUrl}${ServerService.ImportFlowMerge}`;

                this.http.post<StatusResponse>(
                  mergeRequest,
                  { uploadId: uploadId, totalChunks: totalChunks },
                  this.options
                ).subscribe(
                  mergeResponse => {
                    console.log(`[CHUNKING] Fusión completada:`, mergeResponse);
                    observer.next(mergeResponse);
                    observer.complete();
                  },
                  error => {
                    console.error(`[CHUNKING] Error al fusionar fragmentos:`, error);
                    observer.error(error);
                  }
                );
                return;
              }

              // Obtener el siguiente fragmento
              const item = chunks.shift();
              const chunkRequest = `${this.BaseUrl}${ServerService.ImportFlowChunk}`;

              // Crear el cuerpo de la solicitud para este fragmento
              const chunkBody = {
                uploadId: uploadId,
                hash: item.hash,
                totalChunks: totalChunks,
                chunk: item.chunk,
                isCompressed: true,
                flow_name: flow_name,
                channel: channel,
                type: type
              };

              // Enviar el fragmento
              this.http.post<StatusResponse>(
                chunkRequest,
                chunkBody,
                this.options
              ).subscribe(
                chunkResponse => {
                  uploadedChunks++;
                  const progress = Math.round((uploadedChunks / totalChunks) * 100);
                  console.log(`[CHUNKING] Fragmento ${item.hash + 1}/${totalChunks} subido (${progress}%)`);

                  // Subir el siguiente fragmento
                  uploadNextChunk();
                },
                error => {
                  console.error(`[CHUNKING] Error al subir fragmento ${item.hash}:`, error);

                  // Reintentar este fragmento
                  chunks.unshift(item);
                  setTimeout(uploadNextChunk, 1000); // Esperar 1 segundo antes de reintentar
                }
              );
            };

            // Iniciar la carga de fragmentos
            uploadNextChunk();
          },
          error => {
            console.error(`[CHUNKING] Error al iniciar importación:`, error);
            observer.error(error);
          }
        );
      }).catch(error => {
        console.error('Error al comprimir el blob:', error);
        observer.error(error);
      });
    });
  }

  /**
   * Importa un flujo
   * @param flow_name Nombre del flujo
   * @param channel Canal del flujo
   * @param blob Datos del flujo
   * @param type Tipo de flujo
   * @returns Observable con la respuesta del servidor
   */
  public importFlow(flow_name: string, channel: string, blob: string, type: string): Observable<StatusResponse> {
    const request = `${this.BaseUrl}${ServerService.ImportFlow}`;

    // Siempre comprimir primero para evaluar el tamaño
    return new Observable<StatusResponse>(observer => {
      // Verificar que el blob sea un JSON válido antes de procesarlo
      try {
        if (blob) {
          JSON.parse(blob);
          console.log(`[DEBUG] El blob es un JSON válido`);
        }
      } catch (error) {
        console.error(`[ERROR] El blob no es un JSON válido:`, error);
        observer.error({ error: { success: false, message: 'El contenido del archivo no es un JSON válido' } });
        return;
      }

      // Comprimir el blob
      this.compressData(blob).then(compressedBlob => {
        const originalSizeKB = Math.round(blob.length / 1024);
        const compressedSizeKB = Math.round((compressedBlob.length * 0.75) / 1024);
        const compressionRatio = Math.round((1 - (compressedSizeKB / originalSizeKB)) * 100);
        console.log(`[COMPRESIÓN] Compresión completada para importación. Tamaño comprimido: ${compressedSizeKB} KB. Ratio: ${compressionRatio}%`);

        // Si el blob comprimido es grande (más de 800KB), usar carga en fragmentos
        if (compressedSizeKB > 800) {
          console.log(`[CHUNKING] El blob comprimido es grande (${compressedSizeKB} KB), usando carga en fragmentos`);
          this.uploadFlowChunksForImport(flow_name, channel, blob, type).subscribe(
            response => observer.next(response),
            error => observer.error(error),
            () => observer.complete()
          );
          return;
        }

        // Crear headers con indicación de compresión
        const compressHeaders = new HttpHeaders({
          'Accept': 'application/json',
          'Content-Type': 'application/json',
          'Authorization': 'Bearer ' + getToken(),
          'offset': moment().utcOffset().toString(),
          'X-Content-Encoding': 'zip'
        });

        // Enviar los datos comprimidos
        this.http.post<StatusResponse>(
          request,
          {flow_name: flow_name, channel: channel, zipData: compressedBlob, type: type},
          {headers: compressHeaders}
        ).subscribe(
          response => observer.next(response),
          error => observer.error(error),
          () => observer.complete()
        );
      }).catch(error => {
        console.error('Error al comprimir el blob:', error);
        // Si falla la compresión, enviar sin comprimir
        this.http.post<StatusResponse>(
          request,
          {flow_name: flow_name, channel: channel, blob: blob, type: type},
          this.options
        ).subscribe(
          response => observer.next(response),
          error => observer.error(error),
          () => observer.complete()
        );
      });
    });
  }

  /**
   * Divide un string en fragmentos de tamaño específico
   * @param data String a dividir
   * @param chunkSize Tamaño de cada fragmento en bytes
   * @returns Array de fragmentos
   */
  private sliceData(data: string, chunkSize: number = 900 * 1024): { hash: number, chunk: string }[] {
    const chunks = [];
    let index = 0;

    for (let i = 0; i < data.length; i += chunkSize) {
      chunks.push({
        hash: index++,
        chunk: data.slice(i, i + chunkSize)
      });
    }

    return chunks;
  }

  /**
   * Sube un flujo en fragmentos
   * @param id ID del flujo
   * @param versionId ID de la versión
   * @param blob Datos del flujo
   * @returns Observable con la respuesta del servidor
   */
  private uploadFlowChunks(id: Number, versionId: Number, blob: string): Observable<StatusResponse> {
    return new Observable<StatusResponse>(observer => {
      // Verificar que el blob sea un JSON válido antes de procesarlo
      try {
        JSON.parse(blob);
        console.log(`[DEBUG] El blob es un JSON válido`);
      } catch (error) {
        console.error(`[ERROR] El blob no es un JSON válido:`, error);
        observer.error({ error: { success: false, message: 'El contenido del archivo no es un JSON válido' } });
        return;
      }

      // Comprimir el blob
      this.compressData(blob).then(compressedBlob => {
        const originalSizeKB = Math.round(blob.length / 1024);
        const compressedSizeKB = Math.round((compressedBlob.length * 0.75) / 1024);
        const compressionRatio = Math.round((1 - (compressedSizeKB / originalSizeKB)) * 100);
        console.log(`[COMPRESIÓN] Compresión completada para importación. Tamaño comprimido: ${compressedSizeKB} KB. Ratio: ${compressionRatio}%`);

        // Dividir en fragmentos (chunks) de 900KB
        const CHUNK_SIZE = 900 * 1024; // 900KB
        const chunks = this.sliceData(compressedBlob, CHUNK_SIZE);
        const totalChunks = chunks.length;
        console.log(`[CHUNKING] Dividiendo datos en ${totalChunks} fragmentos de ${CHUNK_SIZE / 1024}KB cada uno`);

        // Crear un identificador único para esta carga
        const uploadId = new Date().getTime().toString();
        let uploadedChunks = 0;

        // Función recursiva para subir los fragmentos uno por uno
        const uploadNextChunk = () => {
          if (chunks.length === 0) {
            // Todos los fragmentos se han subido, ahora fusionarlos en el servidor
            console.log(`[CHUNKING] Todos los fragmentos subidos. Solicitando fusión...`);
            const mergeRequest = `${this.BaseUrl}${ServerService.OverrideFlowMerge}/${id}/${versionId}`;

            this.http.post<StatusResponse>(
              mergeRequest,
              { uploadId: uploadId, totalChunks: totalChunks },
              this.options
            ).subscribe(
              response => {
                console.log(`[CHUNKING] Fusión completada:`, response);
                observer.next(response);
                observer.complete();
              },
              error => {
                console.error(`[CHUNKING] Error al fusionar fragmentos:`, error);
                observer.error(error);
              }
            );
            return;
          }

          // Obtener el siguiente fragmento
          const item = chunks.shift();
          const chunkRequest = `${this.BaseUrl}${ServerService.OverrideFlowChunk}/${id}/${versionId}`;

          // Crear el cuerpo de la solicitud para este fragmento
          const chunkBody = {
            uploadId: uploadId,
            hash: item.hash,
            totalChunks: totalChunks,
            chunk: item.chunk,
            isCompressed: true
          };

          // Enviar el fragmento
          this.http.post<StatusResponse>(
            chunkRequest,
            chunkBody,
            this.options
          ).subscribe(
            response => {
              uploadedChunks++;
              const progress = Math.round((uploadedChunks / totalChunks) * 100);
              console.log(`[CHUNKING] Fragmento ${item.hash + 1}/${totalChunks} subido (${progress}%)`);

              // Subir el siguiente fragmento
              uploadNextChunk();
            },
            error => {
              console.error(`[CHUNKING] Error al subir fragmento ${item.hash}:`, error);

              // Reintentar este fragmento
              chunks.unshift(item);
              setTimeout(uploadNextChunk, 1000); // Esperar 1 segundo antes de reintentar
            }
          );
        };

        // Iniciar la carga de fragmentos
        uploadNextChunk();
      }).catch(error => {
        console.error('Error al comprimir el blob:', error);
        observer.error(error);
      });
    });
  }

  public overrideFlow(id: Number, versionId: Number, blob: string): Observable<StatusResponse> {
    const request = `${this.BaseUrl}${ServerService.OverrideFlow}/${id}/${versionId}`;

    // Siempre comprimir primero para evaluar el tamaño
    return new Observable<StatusResponse>(observer => {
      // Verificar que el blob sea un JSON válido antes de procesarlo
      try {
        if (blob) {
          JSON.parse(blob);
          console.log(`[DEBUG] El blob es un JSON válido`);
        }
      } catch (error) {
        console.error(`[ERROR] El blob no es un JSON válido:`, error);
        observer.error({ error: { success: false, message: 'El contenido del archivo no es un JSON válido' } });
        return;
      }

      console.log(`[COMPRESIÓN] Iniciando compresión de flujo para override. Tamaño original: ${Math.round(blob.length / 1024)} KB`);

      // Comprimir el blob
      this.compressData(blob).then(compressedBlob => {
        const originalSizeKB = Math.round(blob.length / 1024);
        const compressedSizeKB = Math.round((compressedBlob.length * 0.75) / 1024);
        const compressionRatio = Math.round((1 - (compressedSizeKB / originalSizeKB)) * 100);
        console.log(`[COMPRESIÓN] Compresión completada para override. Tamaño comprimido: ${compressedSizeKB} KB. Ratio: ${compressionRatio}%`);

        // Si el blob comprimido es grande (más de 800KB), usar carga en fragmentos
        if (compressedSizeKB > 800) {
          console.log(`[CHUNKING] El blob comprimido es grande (${compressedSizeKB} KB), usando carga en fragmentos`);
          this.uploadFlowChunks(id, versionId, blob).subscribe(
            response => observer.next(response),
            error => observer.error(error),
            () => observer.complete()
          );
          return;
        }

        // Crear headers con indicación de compresión
        const compressHeaders = new HttpHeaders({
          'Accept': 'application/json',
          'Content-Type': 'application/json',
          'Authorization': 'Bearer ' + getToken(),
          'offset': moment().utcOffset().toString(),
          'X-Content-Encoding': 'zip'
        });

        // Enviar los datos comprimidos
        console.log(`[DEBUG] Enviando datos comprimidos al servidor. URL: ${request}`);
        console.log(`[DEBUG] Headers enviados:`, compressHeaders);
        console.log(`[DEBUG] Tamaño de zipData: ${compressedBlob.length} caracteres`);

        // Crear el cuerpo de la solicitud
        const requestBody = { zipData: compressedBlob };
        console.log(`[DEBUG] Propiedades en el cuerpo de la solicitud:`, Object.keys(requestBody));

        this.http.put<StatusResponse>(
          request,
          requestBody,
          {headers: compressHeaders}
        ).subscribe(
          response => {
            console.log(`[DEBUG] Respuesta exitosa del servidor:`, response);
            observer.next(response);
            observer.complete();
          },
          error => {
            console.error(`[DEBUG] Error del servidor:`, error);
            if (error.error) {
              console.error(`[DEBUG] Detalle del error:`, error.error);
            }

            // Intentar nuevamente sin compresión si hay un error
            console.log(`[DEBUG] Intentando nuevamente sin compresión...`);
            this.http.put<StatusResponse>(
              request,
              {blob: blob},
              this.options
            ).subscribe(
              response => {
                console.log(`[DEBUG] Respuesta exitosa del servidor (sin compresión):`, response);
                observer.next(response);
                observer.complete();
              },
              secondError => {
                console.error(`[DEBUG] Error del servidor (sin compresión):`, secondError);
                observer.error(error); // Devolver el error original
              }
            );
          }
        );
      }).catch(error => {
        console.error('Error al comprimir el blob:', error);
        // Si falla la compresión, enviar sin comprimir
        this.http.put<StatusResponse>(
          request,
          {blob: blob},
          this.options
        ).subscribe(
          response => observer.next(response),
          error => observer.error(error),
          () => observer.complete()
        );
      });
    });
  }

  public keepSession(): Observable<StatusResponse> {
    const request = `${this.BaseUrl}${ServerService.KeepSession}`;
    return this.http.get<StatusResponse>(request, this.options);
  }

  /**
   * Sube un flujo en fragmentos para restauración
   * @param id ID del flujo
   * @param versionId ID de la versión a restaurar
   * @param blob Datos del flujo (si se proporciona)
   * @returns Observable con la respuesta del servidor
   */
  private uploadFlowChunksForRestore(id: number, versionId: number, blob?: string): Observable<StatusResponse> {
    return new Observable<StatusResponse>(observer => {
      // Si se proporciona un blob, verificar que sea un JSON válido
      if (blob) {
        try {
          JSON.parse(blob);
          console.log(`[DEBUG] El blob es un JSON válido`);
        } catch (error) {
          console.error(`[ERROR] El blob no es un JSON válido:`, error);
          observer.error({ error: { success: false, message: 'El contenido del archivo no es un JSON válido' } });
          return;
        }
      }

      // Crear un identificador único para esta carga
      const uploadId = new Date().getTime().toString();

      // Primero, iniciar el proceso de restauración
      const restoreRequest = `${this.BaseUrl}${ServerService.RestoreFlow}`;
      var urlParams = new HttpParams().set('id', id.toString());
      urlParams = urlParams.set('flow_version_id', versionId.toString());

      this.http.put<StatusResponse>(
        restoreRequest,
        { isChunked: true, uploadId: uploadId },
        { headers: this.options['headers'], params: urlParams }
      ).subscribe(
        response => {
          if (!response.success) {
            observer.error(response);
            return;
          }

          // Si no se proporciona un blob, obtener la versión del servidor
          if (!blob) {
            // Obtener el flujo del servidor
            this.getFlow(id).subscribe(
              flowData => {
                // Comprimir y enviar en fragmentos
                this.processAndUploadChunks(
                  id,
                  versionId,
                  JSON.stringify(flowData),
                  uploadId,
                  observer
                );
              },
              error => {
                console.error(`[ERROR] Error al obtener flujo:`, error);
                observer.error(error);
              }
            );
          } else {
            // Usar el blob proporcionado
            this.processAndUploadChunks(
              id,
              versionId,
              blob,
              uploadId,
              observer
            );
          }
        },
        error => {
          console.error(`[CHUNKING] Error al iniciar restauración:`, error);
          observer.error(error);
        }
      );
    });
  }

  /**
   * Procesa y sube fragmentos para restauración
   * @param id ID del flujo
   * @param versionId ID de la versión
   * @param blob Datos del flujo
   * @param uploadId ID de la carga
   * @param observer Observable para notificar resultados
   */
  private processAndUploadChunks(
    id: number,
    versionId: number,
    blob: string,
    uploadId: string,
    observer: Subscriber<StatusResponse>
  ): void {
    // Comprimir el blob
    this.compressData(blob).then(compressedBlob => {
      const originalSizeKB = Math.round(blob.length / 1024);
      const compressedSizeKB = Math.round((compressedBlob.length * 0.75) / 1024);
      const compressionRatio = Math.round((1 - (compressedSizeKB / originalSizeKB)) * 100);
      console.log(`[COMPRESIÓN] Compresión completada para restauración. Tamaño comprimido: ${compressedSizeKB} KB. Ratio: ${compressionRatio}%`);

      // Dividir en fragmentos (chunks) de 900KB
      const CHUNK_SIZE = 900 * 1024; // 900KB
      const chunks = this.sliceData(compressedBlob, CHUNK_SIZE);
      const totalChunks = chunks.length;
      console.log(`[CHUNKING] Dividiendo datos en ${totalChunks} fragmentos de ${CHUNK_SIZE / 1024}KB cada uno`);

      let uploadedChunks = 0;

      // Función recursiva para subir los fragmentos uno por uno
      const uploadNextChunk = () => {
        if (chunks.length === 0) {
          // Todos los fragmentos se han subido, ahora fusionarlos en el servidor
          console.log(`[CHUNKING] Todos los fragmentos subidos. Solicitando fusión...`);
          const mergeRequest = `${this.BaseUrl}${ServerService.RestoreFlowMerge}`;

          this.http.post<StatusResponse>(
            mergeRequest,
            { uploadId: uploadId, totalChunks: totalChunks },
            this.options
          ).subscribe(
            mergeResponse => {
              console.log(`[CHUNKING] Fusión completada:`, mergeResponse);
              observer.next(mergeResponse);
              observer.complete();
            },
            error => {
              console.error(`[CHUNKING] Error al fusionar fragmentos:`, error);
              observer.error(error);
            }
          );
          return;
        }

        // Obtener el siguiente fragmento
        const item = chunks.shift();
        const chunkRequest = `${this.BaseUrl}${ServerService.RestoreFlowChunk}`;

        // Crear el cuerpo de la solicitud para este fragmento
        const chunkBody = {
          uploadId: uploadId,
          hash: item.hash,
          totalChunks: totalChunks,
          chunk: item.chunk,
          isCompressed: true,
          flowId: id,
          versionId: versionId
        };

        // Enviar el fragmento
        this.http.post<StatusResponse>(
          chunkRequest,
          chunkBody,
          this.options
        ).subscribe(
          _ => {
            uploadedChunks++;
            const progress = Math.round((uploadedChunks / totalChunks) * 100);
            console.log(`[CHUNKING] Fragmento ${item.hash + 1}/${totalChunks} subido (${progress}%)`);

            // Subir el siguiente fragmento
            uploadNextChunk();
          },
          error => {
            console.error(`[CHUNKING] Error al subir fragmento ${item.hash}:`, error);

            // Reintentar este fragmento
            chunks.unshift(item);
            setTimeout(uploadNextChunk, 1000); // Esperar 1 segundo antes de reintentar
          }
        );
      };

      // Iniciar la carga de fragmentos
      uploadNextChunk();
    }).catch(error => {
      console.error('Error al comprimir el blob:', error);
      observer.error(error);
    });
  }

  public restoreFlow(id: number, versionId: number): Observable<StatusResponse> {
    // Siempre comprimir primero para evaluar el tamaño
    if (id && versionId) {
      // Obtener el flujo primero para ver su tamaño
      return this.getFlow(id).pipe(
        switchMap(flowData => {
          const flowStr = JSON.stringify(flowData);

          // Comprimir el flujo y evaluar su tamaño
          return new Observable<StatusResponse>(observer => {
            this.compressData(flowStr).then(compressedBlob => {
              const originalSizeKB = Math.round(flowStr.length / 1024);
              const compressedSizeKB = Math.round((compressedBlob.length * 0.75) / 1024);
              const compressionRatio = Math.round((1 - (compressedSizeKB / originalSizeKB)) * 100);
              console.log(`[COMPRESIÓN] Compresión completada para restauración. Tamaño comprimido: ${compressedSizeKB} KB. Ratio: ${compressionRatio}%`);

              // Si el blob comprimido es grande (más de 800KB), usar carga en fragmentos
              if (compressedSizeKB > 800) {
                console.log(`[CHUNKING] El flujo comprimido es grande (${compressedSizeKB} KB), usando carga en fragmentos`);
                this.uploadFlowChunksForRestore(id, versionId, flowStr).subscribe(
                  response => observer.next(response),
                  error => observer.error(error),
                  () => observer.complete()
                );
                return;
              }

              // Para flujos pequeños, usar el método normal
              const request = `${this.BaseUrl}${ServerService.RestoreFlow}`;
              var urlParams = new HttpParams().set('id', id.toString());
              urlParams = urlParams.set('flow_version_id', versionId.toString());

              // Crear headers con encabezado personalizado para compresión
              const headers = new HttpHeaders({
                'Accept': 'application/json',
                'Content-Type': 'application/json',
                'Authorization': 'Bearer ' + getToken(),
                'offset': moment().utcOffset().toString(),
                'X-Accept-Zip': 'true'  // Indicar que aceptamos respuestas comprimidas
              });

              var opts = {
                headers: headers,
                params: urlParams,
                responseType: 'blob' as 'json'  // Importante: recibir como blob para manejar datos binarios
              };

              // Realizar la solicitud y procesar la respuesta
              return this.http.put(request, {}, opts).pipe(
              switchMap((response: any) => {
                // Verificar el tipo de contenido para determinar si es JSON o ZIP
                const contentType = response.type || '';

                if (contentType.includes('application/json')) {
                  // Si es JSON, convertir el blob a texto y luego parsear
                  return new Observable<StatusResponse>(observer => {
                    const reader = new FileReader();
                    reader.onload = () => {
                      try {
                        const jsonText = reader.result as string;
                        const responseData = JSON.parse(jsonText);
                        observer.next(responseData);
                        observer.complete();
                      } catch (error) {
                        console.error('Error al parsear JSON:', error);
                        observer.error(error);
                      }
                    };
                    reader.onerror = (error) => {
                      console.error('Error al leer el blob como texto:', error);
                      observer.error(error);
                    };
                    reader.readAsText(response);
                  });
                } else if (contentType.includes('application/zip') ||
                          (response.size > 0 && this.isZipBlob(response))) {
                  // Si es ZIP, descomprimir
                  console.log(`[DESCOMPRESIÓN] Detectada respuesta comprimida de ${Math.round(response.size / 1024)} KB`);

                  return new Observable<StatusResponse>(observer => {
                    const reader = new FileReader();
                    reader.onload = async (e) => {
                      try {
                        const arrayBuffer = (e.target as FileReader).result as ArrayBuffer;

                        // Descomprimir usando JSZip
                        const zip = await JSZip.loadAsync(arrayBuffer);

                        // Obtener el primer archivo
                        const files = Object.keys(zip.files);
                        if (files.length === 0) {
                          throw new Error('Archivo ZIP vacío');
                        }

                        // Leer el contenido del archivo como texto
                        const content = await zip.files[files[0]].async('text');
                        console.log(`[DESCOMPRESIÓN] Contenido descomprimido: ${Math.round(content.length / 1024)} KB`);

                        // Parsear el JSON
                        const responseData = JSON.parse(content);
                        observer.next(responseData);
                        observer.complete();
                      } catch (error) {
                        console.error('Error al descomprimir ZIP:', error);
                        observer.error(error);
                      }
                    };
                    reader.onerror = (error) => {
                      console.error('Error al leer el blob como ArrayBuffer:', error);
                      observer.error(error);
                    };
                    reader.readAsArrayBuffer(response);
                  });
                } else {
                  // Si no es JSON ni ZIP, propagar un error
                  console.error('Tipo de respuesta no reconocido:', contentType);
                  return throwError('Tipo de respuesta no reconocido');
                }
              })
            );
            }).catch(error => {
              console.error('Error al comprimir el blob:', error);
              observer.error(error);
            });
          });
        })
      );
    } else {
      return throwError('ID de flujo o versión no proporcionados');
    }
  }

  public getUser(id: number): Observable<StatusResponse> {
    const request = `${this.BaseUrl}${ServerService.GetUser}${id}`;
    return this.http.get<StatusResponse>(request, this.options);
  }

  public listUsers(): Observable<StatusResponseUsers> {
    const request = `${this.BaseUrl}${ServerService.ListUsers}`;
    return this.http.get<StatusResponseUsers>(request, this.options);
  }

  public updateLang(id: number, lang: string): Observable<StatusResponse> {
    const request = `${this.BaseUrl}${ServerService.UpdateLang}`;
    let body = {
      uid: id,
      lang: lang
    }
    return this.http.put<StatusResponse>(request, body, this.options);
  }

  public changePassword(currentPassword: string | null, newPassword: string, id?: number, confirmationOnly: boolean = false) : Observable<StatusResponse> {
    const request = (currentPassword === null)? `${this.BaseUrl}${ServerService.ChangeForgottenPassword}` : `${this.BaseUrl}${ServerService.ChangePassword}`;
    let body;
    if (currentPassword === null) {
      body = {
        uid: id,
        newPassword: newPassword,
        confirmationOnly: confirmationOnly // Agregar flag para indicar si es solo confirmación
      }
    } else {
      body = {
        currentPassword: currentPassword,
        newPassword: newPassword,
        confirmationOnly: confirmationOnly // Agregar flag para indicar si es solo confirmación
      };
    }
    return this.http.put<StatusResponse>(request, body, this.options);
  }

  public createUser(user: UserPublicInfo) : Observable<StatusResponseUser> {
    const request = `${this.BaseUrl}${ServerService.CreateUser}`;
    return this.http.post<StatusResponseUser>(request, user, this.options);
  }

  public updateUser(user: UserPublicInfo) : Observable<StatusResponseUser> {
    const request = `${this.BaseUrl}${ServerService.UpdateUser}`;
    return this.http.put<StatusResponseUser>(request, user, this.options);
  }

  public deleteUser(user: UserPublicInfo) : Observable<StatusResponseUser> {
    const request = `${this.BaseUrl}${ServerService.DeleteUser}/${user.id}`;
    return this.http.delete<StatusResponseUser>(request, this.options);
  }

  public getGlobalStatistics(start : moment.Moment, end: moment.Moment, type : ReportType): Observable<Daily[]>{
    var request;
    switch (type){
      case ReportType.interval:
        request = `${this.BaseUrl}${ServerService.GlobalStatisticsByInterval}/${start}/${end}`;
        break;
      case ReportType.monthly:
        request = `${this.BaseUrl}${ServerService.GlobalStatisticsByMonth}/${start}/${end}`;
        break;
      case ReportType.daily:
        request = `${this.BaseUrl}${ServerService.GlobalStatisticsByDay}/${start}/${end}`;
        break;
      case ReportType.annual:
        request = `${this.BaseUrl}${ServerService.GlobalStatisticsByYear}/${start}/${end}`;
        break;
    }

    return this.http.get<Daily[]>(request, this.options);
  }

  public getStatisticsByYFlow(flowId: Number, start : moment.Moment, end: moment.Moment, type : ReportType): Observable<DailyByYFlow[]>{
    var request;
    switch (type){
      case ReportType.interval:
        request = `${this.BaseUrl}${ServerService.GlobalStatisticsByFlowByInterval}/${flowId}/${start}/${end}`;
        break;
      case ReportType.monthly:
        request = `${this.BaseUrl}${ServerService.GlobalStatisticsByFlowByMonth}/${flowId}/${start}/${end}`;
        break;
      case ReportType.daily:
        request = `${this.BaseUrl}${ServerService.GlobalStatisticsByFlowByDay}/${flowId}/${start}/${end}`;
        break;
      case ReportType.annual:
        request = `${this.BaseUrl}${ServerService.GlobalStatisticsByFlowByYear}/${flowId}/${start}/${end}`;
        break;
    }

    return this.http.get<DailyByYFlow[]>(request, this.options);
  }

  public getGlobalStatisticsByYflow(start : moment.Moment, end: moment.Moment, type : ReportType): Observable<DailyByYFlow[]>{
    var request = `${this.BaseUrl}${ServerService.GlobalStatisticsByFlowBy}/${start}/${end}`;
    return this.http.get<DailyByYFlow[]>(request, this.options);
  }

  public getGlobalStatisticsDownloadExcel(start : moment.Moment, end: moment.Moment): Observable<StatusResponse>{
    const request = `${this.BaseUrl}${ServerService.GlobalStatisticsDownloadExcel}/${start}/${end}`;
    return this.http.get<StatusResponse>(request, this.optionsDownload);
  }

  public getGlobalStatisticsDownloadExcelByIntegration(flowId: Number, start : moment.Moment, end: moment.Moment): Observable<StatusResponse>{
    const request = `${this.BaseUrl}${ServerService.GlobalStatisticsDownloadExcelByIntegration}/${flowId}/${start}/${end}`;
    return this.http.get<StatusResponse>(request, this.optionsDownload);
  }

  public getGlobalStatisticsDownloadExcelByCommands(flowId: Number, start : moment.Moment, end: moment.Moment): Observable<StatusResponse>{
    const request = `${this.BaseUrl}${ServerService.GlobalStatisticsDownloadExcelByCommands}/${flowId}/${start}/${end}`;
    return this.http.get<StatusResponse>(request, this.optionsDownload);
  }

  public getGlobalStatisticsDownloadExcelByBlocksSequence(flowId: Number, start : moment.Moment, end: moment.Moment): Observable<StatusResponse>{
    const request = `${this.BaseUrl}${ServerService.GlobalStatisticsDownloadExcelByBlocksSequence}/${flowId}/${start}/${end}`;
    return this.http.get<StatusResponse>(request, this.optionsDownload);
  }

  public getGlobalStatisticsDownloadExcelByGroupsSequence(flowId: Number, start : moment.Moment, end: moment.Moment): Observable<StatusResponse>{
    const request = `${this.BaseUrl}${ServerService.GlobalStatisticsDownloadExcelByGroupsSequence}/${flowId}/${start}/${end}`;
    return this.http.get<StatusResponse>(request, this.optionsDownload);
  }

  public getGlobalStatisticsDownloadExcelByBlocks(flowId: Number, start : moment.Moment, end: moment.Moment): Observable<StatusResponse>{
    const request = `${this.BaseUrl}${ServerService.GlobalStatisticsDownloadExcelByBlocks}/${flowId}/${start}/${end}`;
    return this.http.get<StatusResponse>(request, this.optionsDownload);
  }

  public getGlobalStatisticsDownloadExcelByAbandonedCases(flowId: Number, start : moment.Moment, end: moment.Moment): Observable<StatusResponse>{
    const request = `${this.BaseUrl}${ServerService.GlobalStatisticsDownloadExcelByAbandonedCases}/${flowId}/${start}/${end}`;
    return this.http.get<StatusResponse>(request, this.optionsDownload);
  }

  public getGlobalStatisticsDownloadExcelByGroups(flowId: Number, start : moment.Moment, end: moment.Moment): Observable<StatusResponse>{
    const request = `${this.BaseUrl}${ServerService.GlobalStatisticsDownloadExcelByGroups}/${flowId}/${start}/${end}`;
    return this.http.get<StatusResponse>(request, this.optionsDownload);
  }

  public getGlobalStatisticsDownloadExcelByDefaultAnswers(flowId: Number, start : moment.Moment, end: moment.Moment): Observable<StatusResponse>{
    const request = `${this.BaseUrl}${ServerService.GlobalStatisticsDownloadExcelByDefaultAnswers}/${flowId}/${start}/${end}`;
    return this.http.get<StatusResponse>(request, this.optionsDownload);
  }

  public getGlobalStatisticsDownloadExcelByDerivationKey(flowId: Number, start : moment.Moment, end: moment.Moment): Observable<StatusResponse>{
    const request = `${this.BaseUrl}${ServerService.GlobalStatisticsDownloadExcelByDerivationKey}/${flowId}/${start}/${end}`;
    return this.http.get<StatusResponse>(request, this.optionsDownload);
  }

  public getGlobalStatisticsDownloadExcelByStatisticEvent(flowId: Number, start : moment.Moment, end: moment.Moment, events: string): Observable<any>{
    const request = `${this.BaseUrl}${ServerService.GlobalStatisticsDownloadExcelByStatisticEvent}/${flowId}/${start}/${end}/${events}`;
    return this.http.get<StatusResponse>(request, this.optionsDownload);
  }

  public getStatisticsDetailDownloadExcelByStatisticEvent(flowId: Number, start : moment.Moment, end: moment.Moment, events: string): Observable<any>{
    const request = `${this.BaseUrl}${ServerService.StatisticsDetailDownloadExcelByStatisticEvent}/${flowId}/${start}/${end}/${events}`;
    return this.http.get<StatusResponse>(request, this.optionsDownload);
  }

  public getGlobalStatisticsDownloadExcelByFlow(flowId: Number, start : moment.Moment, end: moment.Moment): Observable<StatusResponse>{
    const request = `${this.BaseUrl}${ServerService.GlobalStatisticsDownloadExcelByFlow}/${flowId}/${start}/${end}`;
    return this.http.get<StatusResponse>(request, this.optionsDownload);
  }

  public GlobalStatisticsByBlocks(flowId: Number, start : moment.Moment, end: moment.Moment): Observable<DailyByBlocks[]>{
    const request = `${this.BaseUrl}${ServerService.GlobalStatisticsByBlocks}/${flowId}/${start}/${end}`;
    return this.http.get<DailyByBlocks[]>(request, this.options);
  }

  public GlobalStatisticsByGroupsSequence(flowId: Number, blockId: string, start : moment.Moment, end: moment.Moment): Observable<DailyByGroupsSequence[]>{
    const request = `${this.BaseUrl}${ServerService.GlobalStatisticsByGroupsSequence}/${flowId}/${blockId}/${start}/${end}`;
    return this.http.get<DailyByGroupsSequence[]>(request, this.options);
  }

  public GlobalStatisticsByBlocksSequence(flowId: Number, blockId: string, start : moment.Moment, end: moment.Moment, leftLevel :boolean, rightLevel :boolean): Observable<DailyByBlocksSequence[]>{
    const request = `${this.BaseUrl}${ServerService.GlobalStatisticsByBlocksSequence}/${flowId}/${blockId}/${start}/${end}/${leftLevel}/${rightLevel}`;
    return this.http.get<DailyByBlocksSequence[]>(request, this.options);
  }

  public getGlobalStatisticsByBlocksByday(flowId: Number, blocksId: string[], start : moment.Moment, end: moment.Moment): Observable<DailyByBlocks[]>{
    const request = `${this.BaseUrl}${ServerService.GlobalStatisticsByblocksByDay}/${flowId}/${start}/${end}`;
    return this.http.post<DailyByBlocks[]>(request, {blocksId: blocksId}, this.options);
  }

  public GlobalStatisticsByAbandonedCases(flowId: Number, start : moment.Moment, end: moment.Moment): Observable<DailyByBlocks[]>{
    const request = `${this.BaseUrl}${ServerService.GlobalStatisticsByAbandonedCases}/${flowId}/${start}/${end}`;
    return this.http.get<DailyByBlocks[]>(request, this.options);
  }

  public getGlobalStatisticsByAbandonedCasesByday(flowId: Number, blocksId: string[], start : moment.Moment, end: moment.Moment): Observable<DailyByBlocks[]>{
    const request = `${this.BaseUrl}${ServerService.GlobalStatisticsByAbandonedCasesByDay}/${flowId}/${start}/${end}`;
    return this.http.post<DailyByBlocks[]>(request, {blocksId: blocksId}, this.options);
  }

  public GlobalStatisticsByGroups(flowId: Number, start: moment.Moment, end: moment.Moment): Observable<DailyByGroups[]>{
    const request = `${this.BaseUrl}${ServerService.GlobalStatisticsByGroups}/${flowId}/${start}/${end}`;
    return this.http.get<DailyByGroups[]>(request, this.options);
  }

  public getGlobalStatisticsByGroupsByday(flowId: Number, groupsId: String[], start : moment.Moment, end: moment.Moment): Observable<DailyByGroups[]>{
    const request = `${this.BaseUrl}${ServerService.GlobalStatisticsByGroupsByDay}/${flowId}/${start}/${end}`;
    return this.http.post<DailyByGroups[]>(request, {groupsId: groupsId}, this.options);
  }

  public getIntegrationsStatisticsByday(flowId: Number, integationsId: Number[], start : moment.Moment, end: moment.Moment): Observable<DailyByIntegrations[]>{
    const request = `${this.BaseUrl}${ServerService.IntegrationsStatisticsByday}/${flowId}/${start}/${end}`;
    return this.http.post<DailyByIntegrations[]>(request, {integationsId: integationsId}, this.options);
  }

  public getEventsStatisticsByday(flowId: Number, statisticEventsId: Number[], start : moment.Moment, end: moment.Moment): Observable<DailyByStatisticEvent[]>{
    const request = `${this.BaseUrl}${ServerService.EventsStatisticsByday}/${flowId}/${start}/${end}`;
    return this.http.post<DailyByStatisticEvent[]>(request, {statisticEventsId: statisticEventsId}, this.options);
  }

  public getCommandsStatisticsByday(flowId: Number, commandsId: Number[], start : moment.Moment, end: moment.Moment): Observable<DailyByCommands[]>{
    const request = `${this.BaseUrl}${ServerService.CommandsStatisticsByday}/${flowId}/${start}/${end}`;
    return this.http.post<DailyByCommands[]>(request, {commandsId: commandsId}, this.options);
  }

  public GlobalStatisticsByStatisticEvent(flowId: Number, start : moment.Moment, end: moment.Moment): Observable<DailyByStatisticEvent[]>{
    const request = `${this.BaseUrl}${ServerService.GlobalStatisticsByStatisticEvent}/${flowId}/${start}/${end}`;
    return this.http.get<DailyByStatisticEvent[]>(request, this.options);
  }

  public GlobalStatisticsByDefaultAnswers(flowId: Number, start : moment.Moment, end: moment.Moment): Observable<DailyByDefaultAnswers[]>{
    const request = `${this.BaseUrl}${ServerService.GlobalStatisticsByDefaultAnswers}/${flowId}/${start}/${end}`;
    return this.http.get<DailyByDefaultAnswers[]>(request, this.options);
  }

  public GlobalStatisticsByCommands(flowId: Number, start : moment.Moment, end: moment.Moment): Observable<DailyByCommands[]>{
    const request = `${this.BaseUrl}${ServerService.GlobalStatisticsByCommands}/${flowId}/${start}/${end}`;
    return this.http.get<DailyByCommands[]>(request, this.options);
  }

  public GlobalStatisticsByIntergations(flowId: Number, start : moment.Moment, end: moment.Moment): Observable<DailyByIntegrations[]>{
    const request = `${this.BaseUrl}${ServerService.GlobalStatisticsByIntergations}/${flowId}/${start}/${end}`;
    return this.http.get<DailyByIntegrations[]>(request, this.options);
  }

  public GlobalStatisticsByDerivationKey(flowId: Number, start : moment.Moment, end: moment.Moment, flowVersionId?: Number): Observable<DailyByDerivationKey[]>{
    const request = `${this.BaseUrl}${ServerService.GlobalStatisticsByDerivationKey}/${flowId}/${start}/${end}/${flowVersionId}`;
    return this.http.get<DailyByDerivationKey[]>(request, this.options);
  }

  public getAutomaticReportConfig(): Observable<StatusResponse> {
    const request = `${this.BaseUrl}${ServerService.GetAutomaticReportConfig}`;
    return this.http.get<StatusResponse>(request, this.options);
  }

  public getSmtpConfig(): Observable<StatusResponse> {
    const request = `${this.BaseUrl}${ServerService.GetSmtpConfig}`;
    return this.http.get<StatusResponse>(request, this.options);
  }

  public setSmtpConfig(smtpConfig: SmtpConfiguration): Observable<StatusResponse> {
    const request = `${this.BaseUrl}${ServerService.SetSmtpConfig}`;
    return this.http.put<StatusResponse>(request, smtpConfig, this.options);
  }

  public testSmtpConfig(smtpConfig: SmtpConfiguration): Observable<StatusResponse> {
    const request = `${this.BaseUrl}${ServerService.TestSmtpConfig}`;
    return this.http.post<StatusResponse>(request, smtpConfig, this.options);
  }

  public setAutomaticReportConfig(ftp: FtpConfigurations): Observable<StatusResponse> {
    const request = `${this.BaseUrl}${ServerService.SetAutomaticReportConfig}`;
    let body = ftp;
    return this.http.put<StatusResponse>(request, body, this.options);
  }

  public testFtpConfig(ftp: FTPData): Observable<StatusResponse> {
    const request = `${this.BaseUrl}${ServerService.TestFTPConfig}`;
    return this.http.post<StatusResponse>(request, {ftpData: ftp}, this.options);
  }

  public testSftpConfig(ftp: SFTPData): Observable<StatusResponse> {
    const request = `${this.BaseUrl}${ServerService.TestSFTPConfig}`;
    return this.http.post<StatusResponse>(request, {sftpData: ftp}, this.options);
  }

  public getFlowPermissions(flowId: number): Observable<StatusResponse> {
    const request = `${this.BaseUrl}${ServerService.GetFlowPermissions}/${flowId}`;
    return this.http.get<StatusResponse>(request, this.options);
  }

  public setFlowPermissions(flowId: number, permissions: Permission) : Observable<StatusResponse> {
    const request = `${this.BaseUrl}${ServerService.SetFlowPermissions}`;
    let body = {
      flow: flowId,
      user: permissions.uid,
      canEdit: permissions.canEdit,
      canPublish: permissions.canPublish,
      canSeeStatistics: permissions.canSeeStatistics
    }
    return this.http.put<StatusResponse>(request, body, this.options);
  }

  public getIntents(token: string) : Observable<StatusResponse> {
    const request = `${this.BaseUrl}${ServerService.GetIntents}`;
    var options = {
      'headers': {
        'Accept': 'application/json',
      'Content-Type': 'application/json',
      'Authorization': 'Bearer ' + getToken(),
      'cognitiveToken': token,
      }
    };
    return this.http.get<StatusResponse>(request, options);
  }

  public getProjects() : Observable<StatusResponse> {
    const request = `${this.BaseUrl}${ServerService.GetProjects}`;
    return this.http.get<StatusResponse>(request, this.options);
  }

  public getEntities(token: string) : Observable<StatusResponse> {
    const request = `${this.BaseUrl}${ServerService.GetEntities}`;
    var options = {
      'headers': {
      'Accept': 'application/json',
      'Content-Type': 'application/json',
      'Authorization': 'Bearer ' + getToken(),
      'cognitiveToken': token,
      }
    };
    return this.http.get<StatusResponse>(request, options);
  }

  public getForms(token) : Observable<StatusResponse> {
    const request = `${this.BaseUrl}${ServerService.GetForms}`;
    var options = {
      'headers': {
      'Accept': 'application/json',
      'Content-Type': 'application/json',
      'Authorization': 'Bearer ' + getToken(),
      'cognitiveToken': token,
      }
    };
    return this.http.get<StatusResponse>(request, options);
  }

  public GetCategories(token) : Observable<StatusResponse> {
    const request = `${this.BaseUrl}${ServerService.GetCategories}`;
    var options = {
      'headers': {
      'Accept': 'application/json',
      'Content-Type': 'application/json',
      'Authorization': 'Bearer ' + getToken(),
      'cognitiveToken': token,
      }
    };
    return this.http.get<StatusResponse>(request, options);
  }

  public GetExtractionFormats(token) : Observable<StatusResponse> {
    const request = `${this.BaseUrl}${ServerService.GetExtractionFormats}`;
    var options = {
      'headers': {
      'Accept': 'application/json',
      'Content-Type': 'application/json',
      'Authorization': 'Bearer ' + getToken(),
      'cognitiveToken': token,
      }
    };
    return this.http.get<StatusResponse>(request, options);
  }

  public getIntentInfo(token: string, intentName: string) {
    const request = `${this.BaseUrl}${ServerService.GetIntentInfo}/${encodeURIComponent(intentName)}/`;
    var options = {
      'headers': {
        'Accept': 'application/json',
        'Content-Type': 'application/json',
        'Authorization': 'Bearer ' + getToken(),
        'cognitiveToken': token,
      }
    };
    return this.http.get<StatusResponse>(request, options);
  }

  public getAccessySmart() {
    const request = `${this.BaseUrl}${ServerService.GetAccessySmart}`;
    return this.http.get<StatusResponse>(request, this.options);
  }

  public getMetamaps() {
    const request = `${this.BaseUrl}${ServerService.GetMetamaps}`;
    return this.http.get<StatusResponse>(request, this.options);
  }

  public createExcelTable(excelData: Object): Observable<StatusResponse> {
    const request = `${this.BaseUrl}${ServerService.CreateExcelTable}`;
    return this.http.post<StatusResponse>(request, excelData, this.options);
  }

  public deleteExcelTable(id: number): Observable<StatusResponse> {
    const request = `${this.BaseUrl}${ServerService.DeleteExcelTable}?id=${id}`;
    return this.http.delete<StatusResponse>(request, this.options);
  }

  public updateExcelData(excelData: Object): Observable<StatusResponse>{
    const request = `${this.BaseUrl}${ServerService.UpdateExcelData}`;
    return this.http.post<StatusResponse>(request, excelData, this.options);
  }

  public updateExcelHeaders(id: number, table_name: string, headers: string): Observable<StatusResponse> {
    const request = `${this.BaseUrl}${ServerService.UpdateExcelHeaders}?id=${id}&table_name=${table_name}`;
    return this.http.put<StatusResponse>(request, headers, this.options);
  }

  public getTablesByFlow(flowId: number): Observable<StatusResponse>{
    const request = `${this.BaseUrl}${ServerService.getTablesByFlow}/${flowId}`;
    return this.http.get<StatusResponse>(request, this.options);
  }

  public getExcelTablesStatus(flowId: Number): Observable<StatusResponse> {
    const request = `${this.BaseUrl}${ServerService.GetExcelTablesStatus}/${flowId}`;
    return this.http.get<StatusResponse>(request, this.options);
  }

  public uploadPersonalizedTable(file: any): Observable<StatusResponse> {
    const request = `${this.BaseUrl}${ServerService.UploadPersonalizedTable}`;
    return this.http.post<StatusResponse>(request, file, this.optionsUpload);
  }

  public mergePersonalizedTable(filename: string, extension: string): Observable<StatusResponse> {
    const request = `${this.BaseUrl}${ServerService.MergePersonalizedTable}?filename=${filename}&extension=${extension}`;
    return this.http.get<StatusResponse>(request, this.options);
  }

  public retrieveWhatsappHSMTemplates(ySocialUrl: string) {
    const request = `${this.BaseUrl}${ServerService.GetWhatsappHSMTemplates}?ysocial=${encodeURIComponent(ySocialUrl)}`;
    return this.http.get<StatusResponse>(request, this.options);
  }

  public retrieveWhatsappCatalogs(ySocialUrl: string) {
    const request = `${this.BaseUrl}${ServerService.GetWhatsappCatalogs}?ysocial=${encodeURIComponent(ySocialUrl)}`;
    return this.http.get<StatusResponse>(request, this.options);
  }

  public generateStatisticsReport(data: Object){
    const request = `${this.BaseUrl}${ServerService.GenerateStatisticsReport}`;
    return this.http.post<StatusResponse>(request, data, this.options);
  }

  public getAllStatisticsReportByFlow(flowId: Number, page: Number): Observable<ReportsResponse>{
    const request = `${this.BaseUrl}${ServerService.GenerateStatisticsReportByFlow}/${flowId}/${page}`;
    return this.http.get<ReportsResponse>(request, this.options);
  }

  public downloadReport(reportId: Number): Observable<StatusResponse>{
    const request = `${this.BaseUrl}${ServerService.DownloadReport}/${reportId}`;
    return this.http.get<StatusResponse>(request, this.optionsDownload);
  }

  public cancelReport(reportId: Number): Observable<StatusResponse>{
    const request = `${this.BaseUrl}${ServerService.CancelReport}/${reportId}/cancel`;
    return this.http.put<StatusResponse>(request, null, this.options);
  }

  public getLogsError(page: Number): Observable<LogServiceErrorResponse>{
    const request = `${this.BaseUrl}${ServerService.GetLogsError}/${page}`;
    return this.http.get<LogServiceErrorResponse>(request, this.options);
  }

  public downloadLogsError(filename: String): Observable<StatusResponse>{
    const request = `${this.BaseUrl}${ServerService.DownloadLogsError}/${filename}`;
    return this.http.get<StatusResponse>(request, this.optionsDownload);
  }

  public getOpenGraphInfoFromUrl(url: string) : Observable<StatusResponse> {
    const request = `${this.BaseUrl}${ServerService.GetOpenGraphResolveUrl}?url=${encodeURIComponent(url)}`;
    return this.http.get<StatusResponse>(request, this.options);
  }

  public getSamlConfig(): Observable<StatusResponse> {
    const request = `${this.BaseUrl}${ServerService.GetSamlConfig}`;
    return this.http.get<StatusResponse>(request, this.options);
  }

  public setSamlConfig(config: SAMLConfiguration): Observable<StatusResponse> {
    const request = `${this.BaseUrl}${ServerService.SetSamlConfig}`;
    return this.http.put<StatusResponse>(request, config, this.options);
  }

  public getBlocksUsageComparison(flowId: Number, start: moment.Moment, end: moment.Moment): Observable<any> {
    const request = `${this.BaseUrl}${ServerService.GlobalStatisticsByBlocksUsage}/${flowId}/${start}/${end}`;
    return this.http.get<any>(request, this.options);
  }


}

