import { Entity } from './../models/cognitivity/Entity';
import { EvaluateCognitivityPiece } from './../models/pieces/EvaluateCognitivityPiece';
import { CognitivityProject } from './../models/cognitivity/CognitivityProject';
import { CognitivityDefinition } from './../models/cognitivity/CognitivityDefinition';
import { InputVariableMap, OutputVariableMap } from './../models/pieces/IntegrationPiece';
import { IntegrationOutput } from './../models/integration/IntegrationOutput';
import { EventEmitter, Injectable, NgZone } from '@angular/core';
import { ChatDefinition } from '../models/ChatDefinition';
import { BlockDefinition } from '../models/BlockDefinition'
import { VariableDefinition } from '../models/VariableDefinition'
import { TypeDefinition } from '../models/TypeDefinition'
import { BlockGroupModel } from '../models/BlockGroupModel';
import { EditorState } from '../models/EditorState';
import { BasePiece } from '../models/pieces/BasePiece';
import { MessagePieceType, Text } from '../models/pieces/MessagePieceType';
import { AttachmentPiece } from '../models/pieces/AttachmentPiece';
import { MailPiece } from '../models/pieces/MailPiece';
import { ActionsPiece } from '../models/pieces/ActionsPiece';
import { DeriveOperatorPiece } from '../models/pieces/DeriveOperatorPiece';
import { JsonPiece } from '../models/pieces/JsonPiece';
import { QuickReplyPiece } from '../models/pieces/QuickReplyPiece';
import { GalleryImage, GalleryPiece } from '../models/pieces/GalleryPiece';
import arrayMove from 'array-move';
import * as _ from 'lodash';
import { PieceType } from '../models/PieceType';
import { Integration } from '../models/integration/Integration';
import { ArrayUtils } from '../Utils/ArrayUtils';
import { IntegrationPiece } from '../models/pieces/IntegrationPiece';
import { DataEntry } from '../models/pieces/DataEntry';
import { Md5 } from 'ts-md5/dist/md5';
import { TranslateService } from '@ngx-translate/core';
import { ConditionPiece } from '../models/pieces/ConditionPiece';
import { ButtonType } from '../models/pieces/ButtonPiece';
import { SetVariable } from '../models/pieces/SetVariable';
import { PersistentMenu } from '../models/PersistentMenu';
import { PersistentMenuEntry } from '../models/PersistentMenuEntry';
import { TypedJSON } from "typedjson";
import { JumpToBlockPiece } from "../models/pieces/JumpToBlockPiece";
import { Greeting, Greetings } from "../models/Greeting";
import { Concatenate } from '../models/pieces/Concatenate';
import { AccountLinking } from "../models/AccountLinking";
import { FormatDefinition } from '../models/FormatDefinition';
import * as numeral from 'numeral';
import { BusinessAvailability, WorkingDates, WorkingDay } from "../models/BusinessAvailability";
import * as moment from 'moment';
import { CommandDefinition } from '../models/commands/CommandDefinition';
import { ConditionGroup } from '../models/commands/ConditionGroup';
import { FlowDefinition } from "../models/FlowDefinition";
import { ChannelTypes } from "../models/ChannelType";
import { DynamicGalleryPiece } from "../models/pieces/DynamicGalleryPiece";
import { HSMFlowTemplateParameter, HSMTemplateButton, HSMTemplateParameter, WhatsappHSMTemplateByService, YSocialSettings } from "../models/YSocialSettings";
import { environment } from "../../environments/environment";
import { ReturnToLastBlockPiece } from "../models/pieces/ReturnToLastBlockPiece";
import { VariableConditionPiece } from "../models/pieces/VariableConditionPiece";
import { ResetVariablesPiece } from "../models/pieces/ResetVariablesPiece";
import { SwitchJumpToBlockPiece } from "../models/pieces/SwitchJumpToBlockPiece";
import { VideoEmbedPiece } from "../models/pieces/VideoEmbedPiece";
import { StoreMessagePiece } from "../models/pieces/StoreMessagePiece";
import { TagPiece } from "../models/pieces/TagPiece";
import { CoordinatesPiece } from "../models/pieces/CoordinatesPiece";
import { LogPiece } from "../models/pieces/LogPiece";
import { MultipleCoordinatesPiece } from "../models/pieces/MultipleCoordinatesPiece";
import { NearestCoordinatesPiece } from "../models/pieces/NearestCoordinatesPiece";
import { GetElementFromArrayPiece } from "../models/pieces/GetElementFromArrayPiece";
import { GetElementsFromArrayPiece } from "../models/pieces/GetElementsFromArrayPiece";
import { MultiMediaEntry } from '../models/pieces/MultiMediaEntry';
import { CloseCasePiece } from '../models/pieces/CloseCasePiece';
import { GeocoderGooglePiece } from '../models/pieces/GeocoderGooglePiece';
import { GoogleConfiguration } from '../models/GoogleConfiguration';
import { PostMessagePiece } from "../models/pieces/PostMessagePiece";
import { EvaluateCommandsPiece } from '../models/pieces/EvaluateCommandsPiece';
import { CallBlockAsProcedurePiece } from "../models/pieces/CallBlockAsProcedurePiece";
import { StatisticEventPiece } from '../models/pieces/StatisticEventPiece';
import { StatisticEventDefinition } from '../models/StatisticEventDefinition';
import { UpdateProfilePiece } from "../models/pieces/UpdateProfilePiece";
import { DbQueryPiece } from "../models/pieces/DbQueryPiece";
import { UpdateCasePiece } from "../models/pieces/UpdateCasePiece";
import { ShortenUrlPiece } from '../models/pieces/ShortenUrlPiece';
import { MultipleMessagePiece } from "../models/pieces/MultipleMessagePiece";
import { CalendarPiece } from './../models/pieces/CalendarPiece';
import { Permission } from '../models/Permission';
import { Intent } from '../models/cognitivity/Intent';
import { WAMenu } from '../models/pieces/WAMenu';
import { MultipleAttachmentPiece } from './../models/pieces/MultipleAttachmentPiece';
import { FlowTypes } from '../models/FlowType';
import { Tables } from '../models/Tables';
import { TablesStatus } from '../models/TablesStatus';
import { IceBreakers } from "../models/IceBreakers";
import { PaymentGatewayPiece } from '../models/pieces/PaymentGatewayPiece';
import { isContingencyBot, updateCasePieceEnabledInChat, ySocialUrl } from '../Utils/window';
import { InteractiveMessageListPiece } from "../models/pieces/InteractiveMessageListPiece";
import { ValidateFrontalDniPiece } from '../models/pieces/ValidateFrontalDniPiece';
import { ValidateBackDniPiece } from '../models/pieces/ValidateBackDniPiece';
import { EncodeBase64ImagePiece } from '../models/pieces/EncodeBase64ImagePiece';
import { SignaturePadPiece } from '../models/pieces/SignaturePadPiece';
import { InteractiveMessageButtonsPiece } from "../models/pieces/InteractiveMessageButtonsPiece";
import { MarkMessageAsPendingPiece } from "../models/pieces/MarkMessageAsPendingPiece";
import { InteractiveMessageProductListPiece } from "../models/pieces/InteractiveMessageProductListPiece";
import { InteractiveMessageProductPiece } from "../models/pieces/InteractiveMessageProductPiece";
import { WhatsappCatalog } from "../models/WhatsappCatalog";
import { SmtpConfiguration } from "../models/SmtpConfiguration";
import { StickerPiece } from '../models/pieces/StickerPiece';
import { DefaultSettings } from "../models/DefaultSettings";
import { SystemBlocks } from '../models/SystemBlock';
import { FormPiece } from "../models/pieces/FormPiece";
import { RichLinkPiece } from "../models/pieces/RichLinkPiece";
import { TimePickerPiece } from "../models/pieces/TimePickerPiece";
import { AppleInteractiveMessageAuthenticationPiece } from "../models/pieces/AppleInteractiveMessageAuthenticationPiece";
import { AppleInteractiveMessageIMessageAppPiece } from "../models/pieces/AppleInteractiveMessageIMessageAppPiece";
import { AppleInteractiveMessageApplePayPiece } from "../models/pieces/AppleInteractiveMessageApplePayPiece";
import { ApplePayMerchantConfig } from "../models/ApplePayMerchantConfig";
import { SendHsmPiece } from '../models/pieces/SendHsmPiece';
import { UserPublicInfo } from '../models/UserPublicInfo';
import { AccountLinkingPiece } from '../models/pieces/AccountLinkingPiece';
import { AccountUnlinkingPiece } from '../models/pieces/AccountUnlinkingPiece';
import { BiometricPiece } from '../models/pieces/BiometricPiece';
import { Metamap } from '../models/biometric/Metamap';
import { Subject } from 'rxjs';
import { ModuleDefinition } from '../models/ModuleDefinition';
import { v4 as uuidv4 } from 'uuid';
import { FormDefinition } from '../models/cognitivity/FormDefinition';
import { SmartFormPiece } from '../models/pieces/SmartFormPiece';
import { SetVariableFromEntity } from '../models/pieces/SetVariableFromEntity';
import { CommentPiece } from '../models/pieces/CommentPiece';
import { AuthenticateAnonymousProfilePiece } from '../models/pieces/AuthenticateAnonymousProfilePiece';
import { KnowledgeBasePiece } from '../models/pieces/KnowledgeBasePiece';
import { GetMessageEntitiesPiece } from '../models/pieces/GetMessageEntitiesPiece';
import { Category } from '../models/cognitivity/Category';
import { InteractiveMessageUrlButtonPiece } from '../models/pieces/InteractiveMessageUrlButtonPiece';
import { InvokeWhatsappFlowPiece } from '../models/pieces/InvokeWhatsappFlowPiece';
import { WhatsappFlowScreen, WhatsappFlowScreenData, WhatsappFlowsByService } from '../models/WhatsappFlows';
import { EncryptPiece } from '../models/pieces/EncryptPiece';
import { DecryptPiece } from '../models/pieces/DecryptPiece';
import { MultimediaAnalysisPiece } from '../models/pieces/MultimediaAnalysisPiece';
import { ExtractionFormat } from '../models/cognitivity/ExtractionFormat';
import { ProfileListPiece } from '../models/pieces/ProfileListPiece';


@Injectable({
  providedIn: 'root'
})
export class EditorService {

  public onNewVariable: EventEmitter<string> = new EventEmitter<string>();
  public onFlowDeleted: EventEmitter<string> = new EventEmitter<string>();
  public onFlowSaved: EventEmitter<string> = new EventEmitter<string>();
  public onFlowUploaded: EventEmitter<string> = new EventEmitter<string>();
  public onFlowPublished: EventEmitter<string> = new EventEmitter<string>();
  public onFlowSaving: EventEmitter<string> = new EventEmitter<string>();
  public onBlockSelected: EventEmitter<BlockDefinition> = new EventEmitter<BlockDefinition>();
  public onBlockSelectedSubject: Subject<BlockDefinition> = new Subject<BlockDefinition>();
  public onFlowSet: EventEmitter<string> = new EventEmitter<string>();
  public onSearch: EventEmitter<string> = new EventEmitter<string>();
  public onSearchClosed: EventEmitter<any> = new EventEmitter<any>();
  public onIntegrationInputAdded: EventEmitter<any> = new EventEmitter<any>();
  public onIntegrationInputDeleted: EventEmitter<any> = new EventEmitter<any>();
  public onIntegrationOutputAdded: EventEmitter<any> = new EventEmitter<any>();
  public onIntegrationOutputDeleted: EventEmitter<any> = new EventEmitter<any>();
  public onSearchHighlighted: EventEmitter<boolean> = new EventEmitter<boolean>();
  public onCognitivityStatusChanged: EventEmitter<boolean> = new EventEmitter<boolean>();
  public onModuleChanged: Subject<ModuleDefinition> = new Subject<ModuleDefinition>();
  public onTablesSet: EventEmitter<any> = new EventEmitter<any>();
  public onTablesStatusSet: EventEmitter<any> = new EventEmitter<any>();
  public onMaxBlocks: EventEmitter<boolean> = new EventEmitter<boolean>();
  public onMaxCommands: EventEmitter<boolean> = new EventEmitter<boolean>();
  public onCollapseAll: EventEmitter<boolean> = new EventEmitter<boolean>();
  public onWhatsappCatalogsLoaded: EventEmitter<any> = new EventEmitter<any>();
  public onSmtpConfigured: EventEmitter<SmtpConfiguration> = new EventEmitter<SmtpConfiguration>();
  public onYSocialSettingsChanged: EventEmitter<YSocialSettings> = new EventEmitter<YSocialSettings>();
  public onUserConnectToFlow: EventEmitter<UserPublicInfo> = new EventEmitter<UserPublicInfo>();
  public onRefreshBlockGroups: Subject<any> = new Subject<any>();
  public onVariablesChanged = new EventEmitter<void>();
  public onBlockGroupsOrderChanged = new EventEmitter<BlockGroupModel[]>();

  private mCurrentChatDefinition: ChatDefinition = null;
  private mEditorState: EditorState;
  private implicitVariableList: VariableDefinition[];
  private mCurrentFlow: FlowDefinition;
  private mCurrentModule: ModuleDefinition;
  private mCurrentModules: ModuleDefinition[];
  private mCurrentFlowId: number;
  private mCurrentFlowHash: string;
  private mOldFlowName: string = null;
  private isProductive: boolean;
  private mNextPieceTypeId: number = 0;
  private readonly mVariableMap: Map<number, VariableDefinition> = new Map<number, VariableDefinition>();
  private mSystemBlockGroup: BlockGroupModel = null;
  private readonly mPersistentMenuMap: Map<number, PersistentMenuEntry> = new Map<number, PersistentMenuEntry>();
  private availableCognitivityIntents: Intent[] = [];
  private availableCognitivityProjects: CognitivityProject[] = [];
  private availableBiometricMetamaps: Metamap[] = [];
  private availableCognitivityEntities: Entity[] = [];
  private availableCognitivityCategories: Category[] = []
  private availableCognitivityExtractionFormats: ExtractionFormat[] = []
  private availableCognitivityForms: FormDefinition[] = [];
  private smtpConfig: SmtpConfiguration = null;
  private flowTablesStatus: TablesStatus[] = [];
  private flowTables: Tables[] = [];
  private mRetrievedWhatsappCatalogs: boolean = false;
  private mRetrievedWhatsappCatalogsInfo: any = null;
  private mRetrievedWhatsappCatalogsInfoParsed: WhatsappCatalog[] = null;
  private lastStatusBlockIsValid: boolean = true;
  private readonly isContingencyBot: boolean = isContingencyBot();
  public onFlowHashUpdated: EventEmitter<void> = new EventEmitter<void>();
  private _justSaved: boolean = false;
  private _forcePendingChangesFalse: boolean = false;

  constructor(private readonly translateService: TranslateService, private ngZone: NgZone) {
    this.translateService = translateService;

    window['_editorService'] = this;

    numeral.register('locale', 'es', {
      delimiters: {
        thousands: '.',
        decimal: ','
      },
      abbreviations: {
        thousand: 'k',
        million: 'mm',
        billion: 'b',
        trillion: 't'
      },
      ordinal: function (number) {
        var b = number % 10;
        return (b === 1 || b === 3) ? 'er' :
          (b === 2) ? 'do' :
            (b === 7 || b === 0) ? 'mo' :
              (b === 8) ? 'vo' :
                (b === 9) ? 'no' : 'to';
      },
      currency: {
        symbol: '$'
      }
    });

    numeral.locale('es');
    moment.locale('es');
  }

  public setChatName(name: string) {
    if (this.mCurrentFlow) {
      this.mCurrentFlow.name = name;
    }
  }

  public getChatName(): string {
    return this.mCurrentFlow ? this.mCurrentFlow.name : null;
  }

  public getRetrievedWhatsappCatalogs(): boolean {
    return this.mRetrievedWhatsappCatalogs;
  }

  public getWhatsappCatalogs(): WhatsappCatalog[] {
    return this.mRetrievedWhatsappCatalogsInfoParsed;
  }

  public setWhatsappCatalogs(info: any) {
    this.mRetrievedWhatsappCatalogs = true;
    this.mRetrievedWhatsappCatalogsInfo = info;

    this.mRetrievedWhatsappCatalogsInfoParsed = WhatsappCatalog.Parse(info);

    console.log('Se establecieron los catálogos de Whatsapp');
  }

  public addPiece(piece: BasePiece) {
    this.mEditorState.SelectedBlock.Pieces.push(piece);
    piece.OwnerBlockId = this.mEditorState.SelectedBlock.Id;
    piece.Channel = this.mCurrentFlow.channel;
  }

  public getAllBlockGroupList(showVisibleBlocks: boolean = false): BlockGroupModel[] {
    var groups: BlockGroupModel[] = [];
    if (this.mSystemBlockGroup === null) {
      this.mSystemBlockGroup = new BlockGroupModel();
      this.mSystemBlockGroup.Name = this.translateService.instant('GROUPBLOCK_SYSTEM');
      this.mSystemBlockGroup.Blocks = this.getDefaultBlocks();
      if (!showVisibleBlocks) {
        this.mSystemBlockGroup.Blocks = this.mSystemBlockGroup.Blocks.filter(block => {
          return block.Visible;
        })
      }
    }

    groups.push(this.mSystemBlockGroup);

    for (const element of this.mCurrentChatDefinition.BlockGroups) {
      groups.push(element);
    }

    return groups;
  }

  public getAllStatisticEventList(): StatisticEventDefinition[] {
    let groups: StatisticEventDefinition[] = [];
    groups = this.mCurrentChatDefinition.StatisticEventList.map((e) => { return e; });
    return groups;
  }

  public statisticEventConfigurationIsValid(): boolean {
    const result = [];
    const map = new Map();
    const statisticsEvents = this.getAllStatisticEventList();
    for (const item of statisticsEvents) {
      if (!map.has(item.Name)) {
        map.set(item.Name, true);    // set any value to Map
        result.push({
          name: item.Name
        });
      }
    }

    return statisticsEvents.length === result.length;
  }

  public getStatisticEventList(): StatisticEventDefinition[] {
    let groups: StatisticEventDefinition[] = [];
    groups = this.mCurrentChatDefinition.StatisticEventList.filter((item) => {
      if (item.Enabled) {
        return item;
      }
    });
    return groups;
  }

  public isGoogleApiKeyRequired(): boolean {
    const allBlocks = this.mCurrentChatDefinition.BlockList.concat(this.mCurrentChatDefinition.DefaultBlocks);
    const block = allBlocks.find(
      block => {
        return block.Pieces.find(
          piece => {
            if (typeof (piece.type) !== "undefined") {
              switch (piece.type) {
                case "geocoder-google-piece":
                case "coordinates-piece":
                  return this.mCurrentFlow.channel === ChannelTypes.Chat;
                default:
              }
            }
          }) !== undefined;
      });

    return block !== undefined;
  }

  public getBlockGroupList(): BlockGroupModel[] {
    return this.mCurrentChatDefinition.BlockGroups;
  }

  public getBlockList(): BlockDefinition[] {
    return this.mCurrentChatDefinition.BlockList;
  }

  public getDefaultBlocks(): BlockDefinition[] {
    return this.mCurrentChatDefinition.DefaultBlocks;
  }

  public getImplicitVariablesList(): VariableDefinition[] {
    return this.implicitVariableList;
  }

  public getVariableList(): VariableDefinition[] {
    return this.mCurrentChatDefinition.VariableList;
  }

  public getIntegrations(): Integration[] {
    return this.mCurrentChatDefinition.IntegrationDefinitions;
  }

  public getIntegrationsDeleted(): Integration[] {
    return this.mCurrentChatDefinition.IntegrationDeletedDefinitions;
  }

  public getCommands(): CommandDefinition[] {
    return this.mCurrentChatDefinition.CommandDefinitions;
  }

  public getModules(): ModuleDefinition[] {
    return this.mCurrentModules;
  }

  public setModules(modules: ModuleDefinition[]): void {
    this.mCurrentModules = modules;
  }

  public getCommandsDeleted(): CommandDefinition[] {
    return this.mCurrentChatDefinition.CommandDeletedDefinitions;
  }

  public getCognitivity(): CognitivityDefinition[] {
    this.mCurrentChatDefinition.CognitivityDefinitions = this.mCurrentChatDefinition.CognitivityDefinitions.filter(item => item.intent.cognitiveServiceId != '');
    return this.mCurrentChatDefinition.CognitivityDefinitions;
  }

  public getFormatDefinitions(): FormatDefinition[] {
    return this.mCurrentChatDefinition.FormatDefinitions;
  }

  public getUserFormatDefinition(): FormatDefinition[] {
    if (this.mCurrentChatDefinition.UserFormatDefinitions === null) {
      this.mCurrentChatDefinition.UserFormatDefinitions = [];
    }
    return this.mCurrentChatDefinition.UserFormatDefinitions;
  }

  public getDefaultSettings(): DefaultSettings {
    if (this.mCurrentChatDefinition.DefaultSettings === null) {
      this.mCurrentChatDefinition.DefaultSettings = new DefaultSettings();
    }
    return this.mCurrentChatDefinition.DefaultSettings;
  }

  public ready(): boolean {
    return typeof (this.mEditorState) !== 'undefined' &&
      this.mEditorState !== null;
  }

  public getEditorState(): EditorState {
    return this.mEditorState;
  }

  public getPersistentMenu(): PersistentMenu {
    const shouldInitializeMenu =
      this.mCurrentChatDefinition.PersistentMenu === null ||
      !this.mPersistentMenuMap.has(this.mCurrentChatDefinition.PersistentMenu &&
        this.mCurrentChatDefinition.PersistentMenu.root);

    if (shouldInitializeMenu) {
      this.initializePersistentMenu();
    }

    return this.mCurrentChatDefinition.PersistentMenu;
  }

  private initializePersistentMenu(): void {
    this.mCurrentChatDefinition.PersistentMenuEntries = [];
    this.mPersistentMenuMap.clear();

    this.mCurrentChatDefinition.PersistentMenu = new PersistentMenu();
    const rootMenu = this.createPersistentMenu();
    this.mCurrentChatDefinition.PersistentMenu.root = rootMenu.id;
  }

  public getIceBreakers(): IceBreakers {
    if (this.mCurrentChatDefinition.IceBreakers === null) {
      this.mCurrentChatDefinition.IceBreakers = this.createIceBreakers();
    }

    return this.mCurrentChatDefinition.IceBreakers;
  }

  public getBusinessAvailability(): BusinessAvailability {
    // failsafe: remove me in a few months when old flows are gone
    if (this.mCurrentChatDefinition.BusinessAvailability === null) {
      this.mCurrentChatDefinition.BusinessAvailability = this.createBusinessAvailability();
    }
    return this.mCurrentChatDefinition.BusinessAvailability;
  }

  public getYSocialSettings(): YSocialSettings {
    if (this.mCurrentChatDefinition.YSocialSettings === null) {
      this.mCurrentChatDefinition.YSocialSettings = new YSocialSettings();
    }
    return this.mCurrentChatDefinition.YSocialSettings;
  }

  public setYSocialSettings(settings: YSocialSettings) {
    this.mCurrentChatDefinition.YSocialSettings = settings;
    this.onYSocialSettingsChanged.emit(settings)
  }

  public loadySocialSettings(data) {
    this.mCurrentChatDefinition.YSocialSettings.WhatsappHSMTemplatesServices = [];
    this.mCurrentChatDefinition.YSocialSettings.WhatsappFlowsByService = [];

    for (let i = 0; i < data.length; i++) {
      for (let j = 0; j < data[i].HSMTemplates.length; j++) {
        let service: WhatsappHSMTemplateByService = new WhatsappHSMTemplateByService();
        service.ID = data[i].ID;
        service.Name = data[i].Name;
        service.FullPhoneNumber = data[i].FullPhoneNumber;

        service.Description = data[i].HSMTemplates[j].Description;
        service.Namespace = data[i].HSMTemplates[j].Namespace;
        service.ElementName = data[i].HSMTemplates[j].ElementName;
        service.Language = data[i].HSMTemplates[j].Language;
        service.HeaderType = data[i].HSMTemplates[j].HeaderType;
        service.HeaderText = data[i].HSMTemplates[j].HeaderText;
        if (data[i].HSMTemplates[j].HeaderTextParameter !== null) {
          service.HeaderTextParameter = new HSMTemplateParameter();
          service.HeaderTextParameter.Description = data[i].HSMTemplates[j].HeaderTextParameter.Description;
          service.HeaderTextParameter.Name = data[i].HSMTemplates[j].HeaderTextParameter.Name;
        }
        service.HeaderMediaType = data[i].HSMTemplates[j].HeaderMediaType;
        service.HeaderMediaUrl = data[i].HSMTemplates[j].HeaderMediaUrl;
        service.Template = data[i].HSMTemplates[j].Template;
        service.FooterType = data[i].HSMTemplates[j].FooterType;
        service.FooterText = data[i].HSMTemplates[j].FooterText;
        service.ButtonsType = data[i].HSMTemplates[j].ButtonsType;
        if (data[i].HSMTemplates[j].Buttons !== null) {
          service.Buttons = [];
          for (let k = 0; k < data[i].HSMTemplates[j].Buttons.length; k++) {
            let button: HSMTemplateButton = new HSMTemplateButton();
            button.CallToActionButtonType = data[i].HSMTemplates[j].Buttons[k].CallToActionButtonType;
            button.UrlButtonType = data[i].HSMTemplates[j].Buttons[k].UrlButtonType;
            if (data[i].HSMTemplates[j].Buttons[k].QuickReplyParameter !== null) {
              button.QuickReplyParameter = new HSMTemplateParameter();
              button.QuickReplyParameter.Name = data[i].HSMTemplates[j].Buttons[k].QuickReplyParameter.Name;
              button.QuickReplyParameter.Description = data[i].HSMTemplates[j].Buttons[k].QuickReplyParameter.Description;
            }
            if (data[i].HSMTemplates[j].Buttons[k].UrlParameter !== null) {
              button.UrlParameter = new HSMTemplateParameter();
              button.UrlParameter.Name = data[i].HSMTemplates[j].Buttons[k].UrlParameter.Name;
              button.UrlParameter.Description = data[i].HSMTemplates[j].Buttons[k].UrlParameter.Description;
            }
            if (data[i].HSMTemplates[j].Buttons[k].OfferCodeParameter !== null) {
              button.OfferCodeParameter = new HSMTemplateParameter();
              button.OfferCodeParameter.Name = data[i].HSMTemplates[j].Buttons[k].OfferCodeParameter.Name;
              button.OfferCodeParameter.Description = data[i].HSMTemplates[j].Buttons[k].OfferCodeParameter.Description;
            }
            if (data[i].HSMTemplates[j].Buttons[k].FlowParameter !== null) {
              button.FlowParameter = new HSMFlowTemplateParameter();
              button.FlowParameter.Name = data[i].HSMTemplates[j].Buttons[k].FlowParameter.Name;
              button.FlowParameter.FlowAction = data[i].HSMTemplates[j].Buttons[k].FlowParameter.FlowAction;
              button.FlowParameter.FlowID = data[i].HSMTemplates[j].Buttons[k].FlowParameter.FlowID;
              button.FlowParameter.NavigateScreen = data[i].HSMTemplates[j].Buttons[k].FlowParameter.NavigateScreen;
              button.FlowParameter.ActionData = data[i].HSMTemplates[j].Buttons[k].FlowParameter.ActionData;
              button.FlowParameter.FlowDataUrl = data[i].HSMTemplates[j].Buttons[k].FlowParameter.FlowDataUrl;
            }
            button.Text = data[i].HSMTemplates[j].Buttons[k].Text;
            service.Buttons.push(button);
          }
        }

        if (data[i].HSMTemplates[j].TemplateParameters !== null) {
          service.TemplateParameters = [];
          for (let k = 0; k < data[i].HSMTemplates[j].TemplateParameters.length; k++) {
            let parameter: HSMTemplateParameter = new HSMTemplateParameter();
            parameter.Name = data[i].HSMTemplates[j].TemplateParameters[k].Name;
            parameter.Description = data[i].HSMTemplates[j].TemplateParameters[k].Description;
            service.TemplateParameters.push(parameter);
          }
        }

        this.mCurrentChatDefinition.YSocialSettings.WhatsappHSMTemplatesServices.push(service);
      }
      for (let k = 0; k < data[i].Flows.length; k++) {
        let service: WhatsappFlowsByService = new WhatsappFlowsByService();
        service.ID = data[i].ID;
        service.Name = data[i].Name;
        service.FullPhoneNumber = data[i].FullPhoneNumber;

        service.FlowId = data[i].Flows[k].ID;
        service.FlowName = data[i].Flows[k].Name;
        for (let l = 0; l < data[i].Flows[k].Screens.length; l++) {
          let screen: WhatsappFlowScreen = new WhatsappFlowScreen();
          screen.ID = data[i].Flows[k].Screens[l].ID;
          screen.Title = data[i].Flows[k].Screens[l].Title;
          for (var d in data[i].Flows[k].Screens[l].Data) {
            if (data[i].Flows[k].Screens[l].Data.hasOwnProperty(d)) {
              let screenData = new WhatsappFlowScreenData();
              screenData.Name = d;
              screenData.Type = data[i].Flows[k].Screens[l].Data[d].type;
              screen.Data.push(screenData);
            }
          }
          service.FlowScreens.push(screen);
        }
        this.mCurrentChatDefinition.YSocialSettings.WhatsappFlowsByService.push(service);
      }
    }
  }

  public getApplePayMerchantsConfig(): ApplePayMerchantConfig[] {
    if (this.mCurrentChatDefinition.ApplePayMerchantsConfig === null) {
      this.mCurrentChatDefinition.ApplePayMerchantsConfig = [];
    }

    return this.mCurrentChatDefinition.ApplePayMerchantsConfig;
  }

  public getGoogleConfiguration(): GoogleConfiguration {
    if (this.mCurrentChatDefinition.GoogleConfiguration === null) {
      this.mCurrentChatDefinition.GoogleConfiguration = new GoogleConfiguration();
    }
    return this.mCurrentChatDefinition.GoogleConfiguration;
  }

  public createIceBreakers(): IceBreakers {
    let iceBreakers = new IceBreakers();

    return iceBreakers;
  }

  public createBusinessAvailability(): BusinessAvailability {
    var ba = new BusinessAvailability();
    ba.daysDefinition = [];
    ba.daysDefinition.push(WorkingDay.createForDay(1));
    ba.daysDefinition.push(WorkingDay.createForDay(2));
    ba.daysDefinition.push(WorkingDay.createForDay(3));
    ba.daysDefinition.push(WorkingDay.createForDay(4));
    ba.daysDefinition.push(WorkingDay.createForDay(5));
    ba.daysDefinition.push(WorkingDay.createForDay(6));
    ba.daysDefinition.push(WorkingDay.createForDay(7));

    ba.nonWorkingDays = [];

    ba.workingDates = new WorkingDates();

    return ba;
  }

  public getGreetings(): Greetings {
    // failsafe: remove me in a few months when old flows are gone
    if (this.mCurrentChatDefinition.Greetings == null) {
      this.mCurrentChatDefinition.Greetings = this.createGreetings();
    }
    else if (this.mCurrentChatDefinition.Greetings.Locales.length === 0) {
      var defaultLocale = new Greeting();
      defaultLocale.locale = 'default';
      defaultLocale.text = '';
      this.mCurrentChatDefinition.Greetings.Locales.push(defaultLocale);
    }
    else {
      for (let i = 0; i < this.mCurrentChatDefinition.Greetings.Locales.length; i++) {
        let locale = this.mCurrentChatDefinition.Greetings.Locales[i];
        if (typeof (locale.text) === 'undefined' || locale.text === null) {
          locale.text = '';
        }
      }
    }

    return this.mCurrentChatDefinition.Greetings;
  }

  private createGreetings(): Greetings {
    var g = new Greetings();
    var defaultLocale = new Greeting();
    defaultLocale.locale = 'default';
    defaultLocale.text = '';
    g.Locales.push(defaultLocale);

    return g;
  }

  public getAccountLinking(): AccountLinking {
    // failsafe: remove me in a few months when old flows are gone
    if (this.mCurrentChatDefinition.AccountLinking === null) {
      this.mCurrentChatDefinition.AccountLinking = new AccountLinking();
    }
    return this.mCurrentChatDefinition.AccountLinking;
  }

  public selectedBlock(block: BlockDefinition) {
    this.mEditorState.SelectedBlock = block;
    this.onBlockSelected.emit(block);
    this.onBlockSelectedSubject.next(block);
  }

  public getSelectedBlock(): BlockDefinition {
    if (this.mEditorState !== null) {
      return this.mEditorState.SelectedBlock;
    }
    return null;
  }

  public cloneBlockGroup(group: BlockGroupModel) {
    let module = this.getCurrentModule();
    group.resetId();
    group.Name = ArrayUtils.getUniqueName(group.Name,
      this.mCurrentChatDefinition.BlockGroups,
      (str, instance) => { return str === instance.Name; });
    group.ModuleId = module.id;
    group.Module = module;
    for (let i = 0; i < group.Blocks.length; i++) {
      let block: BlockDefinition = group.Blocks[i];

      //let nextBlockId = this.mCurrentChatDefinition.NextBlockId;
      let nextBlockId = uuidv4();
      block.Name = this.getUniqueBlockName(block.Name);
      block.Pieces = block.Pieces.filter(p => {
        return this.isPieceSupportedByChannel(p, this.mCurrentFlow.channel);
      });
      block.Pieces = block.Pieces.filter(p => this.isPieceSupportedByType(p, this.mCurrentFlow.type));

      for (let j = 0; j < group.Blocks.length; j++) {
        let otherBlock: BlockDefinition = group.Blocks[j];
        otherBlock.updateBlockReferences(block.Id, nextBlockId);
      }

      block.Id = nextBlockId;
      //this.mCurrentChatDefinition.NextBlockId = uuidv4();
      block.ModuleId = module.id;
      this.mCurrentChatDefinition.BlockList.push(block);
    }

    this.mCurrentChatDefinition.BlockGroups.push(group);
  }

  public cloneBlockInGroup(blockToClone: BlockDefinition, group: BlockGroupModel): BlockDefinition {
    let module = this.getCurrentModule();
    const id = uuidv4();
    let name = blockToClone.Name;
    if (this.mCurrentChatDefinition.BlockList.findIndex(b => b.Name.toLowerCase() === name.toLowerCase()) >= 0 ||
      this.mCurrentChatDefinition.DefaultBlocks.findIndex(b => b.Name.toLowerCase() === name.toLowerCase()) >= 0) {
      name += ' ' + id;
    }

    let block: BlockDefinition = TypedJSON.parse(TypedJSON.stringify(blockToClone, BlockDefinition, { preserveNull: true }), BlockDefinition);
    block.Id = uuidv4();
    block.Name = name;
    block.ModuleId = module.id;

    if (block.Pieces !== null) {
      block.Pieces = block.Pieces.filter(p => {
        return this.isPieceSupportedByChannel(p, this.mCurrentFlow.channel);
      });
      block.Pieces = block.Pieces.filter(p => this.isPieceSupportedByType(p, this.mCurrentFlow.type));
      block.Pieces = block.Pieces.map((p, index) => {
        if (p.type === 'wa-menu-piece') {
          p = this.cloneOnePiece(blockToClone.Pieces[index]);
        }
        p.reset(this.mCurrentChatDefinition.NextPieceId);
        this.mCurrentChatDefinition.NextPieceId++;
        p.clearIncompatibleElements(this.mCurrentFlow.channel);
        p.OwnerBlockId = block.Id;
        p.Channel = this.mCurrentFlow.channel;
        return p;
      });
    }

    this.mCurrentChatDefinition.BlockList.push(block);
    group.Blocks.push(block);
    this.onMaxBlocks.emit(this.isMaxBlocks());
    return block;
  }

  public exceedMaxBlocks(flow: FlowDefinition, definition: ChatDefinition) {
    if (flow.type == FlowTypes.Lite) {
      let blockList = definition ? definition.BlockList : this.getBlockList();
      if (blockList.length > 20) {
        return true;
      } else {
        return false;
      }
    } else {
      return false;
    }
  }

  public exceedMaxCommands(flow: FlowDefinition, definition: ChatDefinition) {
    if (flow.type == FlowTypes.Lite) {
      let commands = definition ? definition.CommandDefinitions : this.getCommands();
      if (commands.length > 5) {
        return true;
      } else {
        return false;
      }
    } else {
      return false;
    }

  }

  public isMaxBlocks(flow = null) {
    if (!flow) {
      flow = this.mCurrentFlow
    }

    if (flow.type == FlowTypes.Lite) {
      if (this.getBlockList().length >= 20) {
        return true;
      } else {
        return false;
      }
    } else {
      return false;
    }
  }

  public isMaxCommands(flow = null) {
    if (!flow) {
      flow = this.mCurrentFlow
    }

    if (flow.type == FlowTypes.Lite) {
      if (this.getCommands().length >= 5) {
        return true;
      } else {
        return false;
      }
    } else {
      return false;
    }

  }

  public addNewBlockToGroup(group: BlockGroupModel): BlockDefinition {
    let id = this.mCurrentChatDefinition.BlockList.length + this.mCurrentChatDefinition.DefaultBlocks.length;
    let blockName = this.translateService.instant('BLOCK_NAME_NEW');
    const name = this.getUniqueBlockName(blockName + ' ' + id.toString());
    let block = this.createBlock(name);
    this.mCurrentChatDefinition.BlockList.push(block);
    group.Blocks.push(block);
    this.onMaxBlocks.emit(this.isMaxBlocks());
    return block;
  }

  public findBlockWithId(id: string): BlockDefinition {
    let block = this.mCurrentChatDefinition.BlockList.find(b => {
      return b.Id === id;
    });

    if (typeof (block) === 'undefined' || block === null) {
      block = this.mCurrentChatDefinition.DefaultBlocks.find(b => {
        return b.Id === id;
      });
    }

    return block;
  }

  public findStatisticEventWithId(id: number): StatisticEventDefinition {
    let event = this.mCurrentChatDefinition.StatisticEventList.find(b => {
      return b.Id === id;
    });

    return event;
  }

  public getUniqueBlockName(baseName: string): string {
    const id = this.mCurrentChatDefinition.BlockList.length + this.mCurrentChatDefinition.DefaultBlocks.length;

    var uname = ArrayUtils.getUniqueNameStartingAt(baseName, this.mCurrentChatDefinition.DefaultBlocks, id, (base, blockDef) => {
      return base.trim() === blockDef.Name.trim();
    });
    uname = ArrayUtils.getUniqueNameStartingAt(uname, this.mCurrentChatDefinition.BlockList, id, (base, blockDef) => {
      return base.trim() === blockDef.Name.trim();
    });
    return uname;
  }

  public getUniqueStatisticEventName(baseName: string): string {
    const id = this.mCurrentChatDefinition.StatisticEventList.length;

    var uname = ArrayUtils.getUniqueNameStartingAt(baseName, this.mCurrentChatDefinition.StatisticEventList, id, (base, blockDef) => {
      return base.trim() === blockDef.Name.trim();
    });
    return uname;
  }

  public getUniqueCommandName(baseName: string, ignoreCommand: CommandDefinition = null): string {
    return ArrayUtils.getUniqueName(baseName, this.mCurrentChatDefinition.CommandDefinitions, (base, commandDef) => {
      if (commandDef == ignoreCommand) return false;
      return base.trim() === commandDef.name.trim();
    });
  }

  /*public getUniqueCognitivityName(baseName: string, ignoreCognitivity: CognitivityDefinition = null) : string {
    return ArrayUtils.getUniqueName(baseName, this.mCurrentChatDefinition.CognitivityDefinition, (base, cognitivDef)=> {
      if( cognitivDef == ignoreCognitivity) return false;
      return base.trim() === cognitivDef.name.trim();
    });
  }*/

  public createNewBlockWithName(name: string): BlockDefinition {
    const uniqueName = this.getUniqueBlockName(name);
    let newBlock = this.createBlock(uniqueName);
    newBlock.ModuleId = this.mCurrentModule.id;
    this.mCurrentChatDefinition.BlockList.push(newBlock);
    let lastBlockGroup = this.getCurrentBlockGroup();
    if (lastBlockGroup == null) {
      lastBlockGroup = this.createBlockGroup();
      lastBlockGroup.Module = this.mCurrentModule;
      lastBlockGroup.ModuleId = this.mCurrentModule.id;
      this.mCurrentChatDefinition.BlockGroups.push(lastBlockGroup);
      this.onRefreshBlockGroups.next();
    }
    lastBlockGroup.Blocks.push(newBlock);
    this.onMaxBlocks.emit(this.isMaxBlocks());
    this.onRefreshBlockGroups.next();
    return newBlock;
  }

  public createNewStatisticEventWithName(name: string): StatisticEventDefinition {
    const uniqueName = this.getUniqueStatisticEventName(name);
    let newEvent = this.createStatisticEventDefinition(uniqueName);
    this.mCurrentChatDefinition.StatisticEventList.push(newEvent);
    return newEvent;
  }

  public getCurrentBlockGroup(): BlockGroupModel {
    let curBlock = this.mEditorState.SelectedBlock;
    var foundGroup: BlockGroupModel = null;
    this.mCurrentChatDefinition.BlockGroups.forEach(group => {
      let bg = group.Blocks.find((b) => { return b.Id === curBlock.Id });
      if (bg != null) {
        foundGroup = group;
      }
    });
    return foundGroup;
  }

  public createBlockGroup(): BlockGroupModel {
    let groupName = this.translateService.instant('BLOCKGROUP_NAME_NEW');
    const newGroupName = ArrayUtils.getUniqueName(groupName, this.mCurrentChatDefinition.BlockGroups, (str, instance) => { return str === instance.Name; });
    let newGroup = new BlockGroupModel();
    newGroup.Name = newGroupName;
    newGroup.Module = this.mCurrentModule;
    newGroup.ModuleId = this.mCurrentModule.id;
    console.log('Created new block group:', newGroup.Name);
    return newGroup;
  }

  public deleteBlockGroup(group: BlockGroupModel) {
    //console.log('Deleting block group from service:', group.Name, 'with ID:', group.Id);
    let index = this.mCurrentChatDefinition.BlockGroups.indexOf(group, 0);
    if (index > -1) {
      //console.log('Found block group at index:', index);
      this.mCurrentChatDefinition.BlockGroups.splice(index, 1);
      this.addGroupToDeletedGlobalList(group);
      group.Blocks.forEach(block => {
        this.deleteBlockFromGlobalList(block);
      });
      //console.log('Block group deleted successfully from service');
      this.onRefreshBlockGroups.next();
      return true;
    }
    console.warn('Block group not found in the service array');
    return false;
  }

  public addGroupToDeletedGlobalList(group: BlockGroupModel) {
    var deleted = new BlockGroupModel();
    deleted.Id = group.Id;
    deleted.Name = group.Name;
    this.mCurrentChatDefinition.GroupDeletedList.push(deleted);
  }

  private createBlock(name: string): BlockDefinition {
    let block = new BlockDefinition();
    //block.init(this.mCurrentChatDefinition.NextBlockId, name, false, null, true, this.mCurrentModule.id);
    //this.mCurrentChatDefinition.NextBlockId = uuidv4();
    block.init(uuidv4(), name, false, null, true, this.mCurrentModule.id, true);
    return block;
  }

  private createStatisticEventDefinition(name: string): StatisticEventDefinition {
    var id = this.mCurrentChatDefinition.StatisticEventList.length;
    let event = new StatisticEventDefinition();
    event.init(id, name);
    return event;
  }

  public createVariable(name: string, type: TypeDefinition = null, description: string = null, defaultValue: string = null, channel: ChannelTypes = null): VariableDefinition {
    if (typeof (defaultValue) === 'undefined') {
      defaultValue = null;
    }

    let variable = new VariableDefinition();
    variable.Name = name;
    variable.Id = this.mCurrentChatDefinition.NextVarId;
    variable.Description = description;
    variable.DefaultValue = defaultValue;
    variable.Channel = channel;
    this.mCurrentChatDefinition.NextVarId++;
    this.mVariableMap.set(variable.Id, variable);
    if (type != null) {
      variable.Type = type;
    }
    this.onVariablesChanged.emit();
    return variable;
  }

  public deleteVariable(id: number) {
    _.remove(this.mCurrentChatDefinition.VariableList, v => v.Id == id);
    this.mVariableMap.delete(id);
    this.onVariablesChanged.emit();
  }

  public deleteUserFormat(key: string) {
    _.remove(this.mCurrentChatDefinition.UserFormatDefinitions, f => f.key == key);
  }

  public getVariableWithId(id: number): VariableDefinition {
    return this.mVariableMap.get(id);
  }

  public createPersistentMenu(): PersistentMenuEntry {
    let menu = new PersistentMenuEntry();
    menu.id = this.mCurrentChatDefinition.NextPersistentMenuId;
    this.mCurrentChatDefinition.NextPersistentMenuId++;
    this.mPersistentMenuMap.set(menu.id, menu);
    this.mCurrentChatDefinition.PersistentMenuEntries.push(menu);
    return menu;
  }

  public getMenuWithId(id: number): PersistentMenuEntry {
    return this.mPersistentMenuMap.get(id);
  }

  public deletePersistentMenu(id: number) {
    this.mPersistentMenuMap.delete(id);
    this.mCurrentChatDefinition.PersistentMenuEntries = this.mCurrentChatDefinition.PersistentMenuEntries.filter(menu => menu.id === id);
  }

  public findVariablesAndImplicitsWithName(name: string): VariableDefinition {
    return this.findVariableWithName(name, true);
  }

  public findVariableWithName(name: string, includeImplicits: boolean = false): VariableDefinition {
    var v = _.find(this.mCurrentChatDefinition.VariableList, element => {
      return element.Name == name;
    });

    if (v != null && !includeImplicits) {
      return v;
    }

    // we want to return implicits first
    if (includeImplicits) {
      var i = _.find(this.implicitVariableList, element => element.Name == name);
      return i ? i : v;
    }
    else {
      return v;
    }
  }

  public findVariableWithId(id: number, includeImplicits: boolean = false): VariableDefinition {
    let variable = this.mCurrentChatDefinition.VariableList.find(v => {
      return v.Id === id;
    });

    if (typeof (variable) !== 'undefined' && variable !== null) {
      return variable;
    }

    if (includeImplicits) {
      variable = this.implicitVariableList.find(v => {
        return v.Id === id;
      });

      if (typeof (variable) !== 'undefined' && variable !== null) {
        return variable;
      }
    }

    return null;
  }

  public findImplicitVariable(name: string): VariableDefinition {
    let variable = this.implicitVariableList.find(v => {
      return v.Name === name;
    });

    if (typeof (variable) !== 'undefined' && variable !== null) {
      return variable;
    }

    return null;
  }

  private deleteBlockFromGlobalList(block: BlockDefinition) {
    var index = this.mCurrentChatDefinition.BlockList.indexOf(block, 0);
    if (index > -1) {
      this.mCurrentChatDefinition.BlockList.splice(index, 1);

      var deleted = new BlockDefinition();
      deleted.Id = block.Id;
      deleted.Name = block.Name;
      this.mCurrentChatDefinition.BlockDeletedList.push(deleted);
      this.onMaxBlocks.emit(this.isMaxBlocks());
    }
  }

  public createNewVariableWithName(name: string): VariableDefinition {
    let newVar = this.createVariable(name, TypeDefinition.Text);
    newVar.Persist = false;
    this.mCurrentChatDefinition.VariableList.push(newVar);
    this.onNewVariable.emit(name);
    return newVar;
  }

  public cloneVariable(varDef: VariableDefinition) {
    if (varDef === null) {
      return;
    }

    let newVar = this.createVariable(varDef.Name, varDef.Type, varDef.Description, varDef.DefaultValue);
    newVar.Name = ArrayUtils.getUniqueName(newVar.Name, this.mCurrentChatDefinition.VariableList, (str, instance) => { return str === instance.Name; }, "_");
    newVar.Constant = varDef.Constant;
    this.mCurrentChatDefinition.VariableList.push(newVar);
  }

  public findAndEraseBlock(block: BlockDefinition) {
    this.mCurrentChatDefinition.BlockGroups.forEach(group => {
      var index = group.Blocks.indexOf(block, 0);
      if (index > -1) {
        group.Blocks.splice(index, 1);
      }
    });
    this.deleteBlockFromGlobalList(block);
    if (block === this.mEditorState.SelectedBlock) {
      this.mEditorState.SelectedBlock = null;
    }

    let relatedBlocks = this.findBlocksReferencingBlock(block.Id);
    relatedBlocks.forEach(b => {
      let relatedBlock = this.findBlockWithId(b);
      if (typeof (relatedBlock) !== 'undefined' && relatedBlock !== null) {
        relatedBlock.Pieces.forEach(p => {
          p.clearBlockDefinition(block.Id);
        });
      }
    });

    let relatedCommands = this.findCommandsReferencingBlock(block.Id);
    relatedCommands.forEach(c => {
      c.destinationBlockId = "-1";
    });

    if (this.mCurrentChatDefinition.PersistentMenuEntries !== null) {
      for (let i = 0; i < this.mCurrentChatDefinition.PersistentMenuEntries.length; i++) {
        let m = this.mCurrentChatDefinition.PersistentMenuEntries[i];
        if (m.buttons !== null) {
          for (let j = 0; j < m.buttons.length; j++) {
            let button = m.buttons[j];
            button.clearBlockDefinition(block.Id);
          }
        }
      }
    }

    if (this.mCurrentChatDefinition.IceBreakers !== null) {
      for (let i = 0; i < this.mCurrentChatDefinition.IceBreakers.buttons.length; i++) {
        let button = this.mCurrentChatDefinition.IceBreakers.buttons[i];
        button.clearBlockDefinition(block.Id);
      }
    }
  }

  public updateBlockGroupArray(newGroups: BlockGroupModel[]) {
    if (this.mCurrentChatDefinition) {
      //console.log('Updating block groups array with:', newGroups.map(g => g.Name));

      this.mCurrentChatDefinition.BlockGroups = newGroups.slice();
      this.onBlockGroupsOrderChanged.emit(this.mCurrentChatDefinition.BlockGroups.slice());
      //console.log('Block groups updated in editor service:', this.mCurrentChatDefinition.BlockGroups.map(g => g.Name));
      return true;
    }

    console.warn('Failed to update block groups - mCurrentChatDefinition is null or undefined');
    return false;
  }

  public updateCommandDefnitions(newCommandDefinitions: CommandDefinition[]) {
    this.mCurrentChatDefinition.CommandDefinitions = newCommandDefinitions;
    this.onMaxCommands.emit(this.isMaxCommands());
  }

  public updateCognitivityDefinition(newCognitivityDefinition: CognitivityDefinition[]) {
    this.mCurrentChatDefinition.CognitivityDefinitions = newCognitivityDefinition;
  }

  public addCognitivityDefinition(newCognitivityDefinition: CognitivityDefinition) {
    this.mCurrentChatDefinition.CognitivityDefinitions.push(newCognitivityDefinition);
  }

  public incrementCurrentVersion() {
    if (typeof (this.mCurrentFlow.ActiveStagingVersion) === 'undefined' ||
      this.mCurrentFlow.ActiveStagingVersion === null) {
      this.mCurrentFlow.ActiveStagingVersion = {
        number: 0,
        blob: this.getSerializedState()
      };
    }
    this.mCurrentFlow.ActiveStagingVersion.number++;
  }

  prefreshCurrentFlowHash() {
    // Obtener el estado actual serializado
    const currentState = this.getProcessedSerializedState(true);

    // Actualiza el hash
    this.mCurrentFlowHash = this.calculateHash(currentState);

    console.log('Flow hash updated after saving');

    // Notificar a otros componentes que el hash ha sido actualizado
    this.onFlowHashUpdated.emit();
  }

  markAsSaved() {
    this._justSaved = true;
    this._forcePendingChangesFalse = true;
    this.prefreshCurrentFlowHash();

    setTimeout(() => {
      this._justSaved = false;
      this._forcePendingChangesFalse = false;
    }, 2000);
  }

  public pendingChanges(): boolean {
    if (this._forcePendingChangesFalse) {
      console.log('Force no pending changes');
      return false;
    }
    // Si acabamos de guardar, devuelve false directamente
    if (this._justSaved) {
      console.log('Just saved, no pending changes');
      return false;
    }

    // Obtener el estado actual
    const currentState = this.getProcessedSerializedState(true);

    // Usa un método simple para comparar estados
    const hasChanges = this.mCurrentFlowHash !== this.calculateHash(currentState);

    return hasChanges;
  }
  private calculateHash(state: string): string {
    // Implementación simple de hash
    let hash = 0;
    if (state.length === 0) return hash.toString();

    for (let i = 0; i < state.length; i++) {
      const char = state.charCodeAt(i);
      hash = ((hash << 5) - hash) + char;
      hash = hash & hash; // Convertir a entero de 32 bits
    }

    return hash.toString();
  }

  public getSerializedState(chatDefinition: ChatDefinition = null): string {
    if (chatDefinition === null) {
      chatDefinition = this.mCurrentChatDefinition;
    }
    return TypedJSON.stringify(chatDefinition, ChatDefinition, { preserveNull: true });
  }

  //TODO: Excluyo del calculo del md5 a los templates porque falla si estan, ver como resolver esto
  public getProcessedSerializedState(filterModuleElements: boolean, excludeHsmTemplates: boolean = false): string {
    if (filterModuleElements === undefined)
      filterModuleElements = false;

    let blobToSave = this.splitComplexPieces(this.mCurrentChatDefinition);

    if (excludeHsmTemplates) {
      blobToSave.YSocialSettings.WhatsappHSMTemplatesServices = null;
    }

    if (filterModuleElements) {
      let blobAux = this.createFlowFromTemplate();
      if (this.mCurrentModule.isMaster()) {
        blobAux = blobToSave;
      }
      blobAux.BlockList = blobToSave.BlockList.filter(block => block.ModuleId === this.mCurrentModule.id);
      blobAux.BlockDeletedList = blobToSave.BlockDeletedList.filter(block => block.ModuleId === this.mCurrentModule.id);
      blobAux.BlockGroups = blobToSave.BlockGroups.filter(blockGroup => blockGroup.ModuleId === this.mCurrentModule.id);
      blobAux.GroupDeletedList = blobToSave.GroupDeletedList.filter(blockGroup => blockGroup.ModuleId === this.mCurrentModule.id);

      //console.log('FINAL ORDER BEFORE SAVING:', blobToSave.BlockGroups.map(g => g.Name));
      return TypedJSON.stringify(blobAux, ChatDefinition, { preserveNull: true });
    }

    //console.log('Saving block groups in this order:', blobToSave.BlockGroups.map(g => g.Name));

    return TypedJSON.stringify(blobToSave, ChatDefinition, { preserveNull: true });
  }

  public splitComplexPieces(flow: ChatDefinition): ChatDefinition {
    const splitMethod = (block: BlockDefinition) => {
      let pieces = block.Pieces;
      let i = 0;
      while (i < pieces.length) {
        let pieceSplitted = false;
        let piece = pieces[i];
        switch (piece.type) { //Funcionalidad para futuras piezas de multiples modelos
          case 'wa-menu-piece':
            var msgPiece = <BasePiece>(<WAMenu>piece).messageModel;
            msgPiece.type = 'message-piece';
            msgPiece.Id = piece.Id;
            var dataEntryPiece = <BasePiece>(<WAMenu>piece).dataEntryModel;
            dataEntryPiece.type = 'data-entry-piece';
            dataEntryPiece.Id = piece.Id;
            var switchJumpToBlockPiece = <BasePiece>(<WAMenu>piece).switchJumpToBlockModel;
            switchJumpToBlockPiece.type = 'switch-jump-to-block-piece';
            switchJumpToBlockPiece.Id = piece.Id;
            pieceSplitted = true;
            break;
          default:
            break;
        }
        if (pieceSplitted) {
          pieces.splice(i, 1, msgPiece);
          pieces.splice(i + 1, 0, dataEntryPiece);
          pieces.splice(i + 2, 0, switchJumpToBlockPiece);
        }
        i++;
      }
    };

    let flowCopy = _.cloneDeep(flow);
    flowCopy.BlockList.forEach(splitMethod);
    flowCopy.BlockGroups.forEach(g => g.Blocks.forEach(splitMethod));
    flowCopy.BlockDeletedList.forEach(splitMethod);
    flowCopy.GroupDeletedList.forEach(g => g.Blocks.forEach(splitMethod));
    flowCopy.DefaultBlocks.forEach(splitMethod);
    return flowCopy;
  }

  public setFlowTables(tables: Tables[]) {
    this.flowTables = tables;
    this.onTablesSet.emit();
  }

  public setFlowTablesStatus(tables: TablesStatus[]) {
    this.flowTablesStatus = tables;
    this.onTablesStatusSet.emit(tables)
  }

  public getFlowTables(): Tables[] {
    return this.flowTables;
  }

  public getFlowTablesStatus(): TablesStatus[] {
    return this.flowTablesStatus;
  }

  public addTableStatus(table: TablesStatus) {
    this.flowTablesStatus.push(table)
  }

  public addTable(table: Tables) {
    this.flowTables.push(table);
  }

  public setCurrentModule(moduleDefinition): void {
    this.mCurrentModule = moduleDefinition;
    this.onModuleChanged.next();
  }

  public getCurrentModule(): ModuleDefinition {
    if (this.mCurrentModule === undefined ||
      this.mCurrentModules === undefined) {
      let master = new ModuleDefinition();
      master.createMaster();
      return master;
    }
    return this.mCurrentModule;
  }

  public setCurrentFlow(flow: FlowDefinition, isProd: boolean) {
    //console.log(flow)
    this.mCurrentFlow = flow;
    this.mCurrentFlowId = flow.id;
    this.mOldFlowName = flow.name;
    this.isProductive = isProd;
    this.mSystemBlockGroup = null;
    this.mCurrentModule = undefined;

    this.onFlowSet.emit();
  }

  public setCurrentFlowId(id: number, isProd: boolean, channel: string, name: string, flowType: string, permissions: Permission[]) {
    this.mCurrentFlow = new FlowDefinition();
    this.mCurrentFlow.channel = channel;
    this.mCurrentFlow.name = name;
    this.mCurrentFlow.type = flowType;
    this.mCurrentFlow.users_permissions_flows = permissions;
    this.mOldFlowName = name;
    this.mCurrentFlowId = id;
    this.isProductive = isProd;
    this.mSystemBlockGroup = null;
  }

  public clearCurrentFlow() {
    this.mCurrentFlow = null;
  }

  public getCurrentFlow(): FlowDefinition {
    return this.mCurrentFlow;
  }

  public getCurrentFlowId(): number {
    return this.mCurrentFlowId;
  }

  public isEditingProductiveVersion(): boolean {
    return this.isProductive;
  }

  private createFlowFromTemplate(): ChatDefinition {
    var chatDefinition: ChatDefinition = new ChatDefinition();
    chatDefinition.DefaultBlocks = [];
    chatDefinition.BlockList = [];
    chatDefinition.BlockDeletedList = [];
    chatDefinition.BlockGroups = [];
    chatDefinition.GroupDeletedList = [];
    chatDefinition.VariableList = [];
    chatDefinition.FormatDefinitions = FormatDefinition.createDefaultDefinitions();
    chatDefinition.UserFormatDefinitions = [];
    chatDefinition.CommandDefinitions = [];
    chatDefinition.CommandDeletedDefinitions = [];
    chatDefinition.CognitivityDefinitions = [];
    //chatDefinition.NextBlockId = uuidv4();
    chatDefinition.NextVarId = 1;
    chatDefinition.IntegrationDefinitions = [];
    chatDefinition.IntegrationDeletedDefinitions = [];
    chatDefinition.NextIntegrationId = 0;
    chatDefinition.NextPieceId = 1;
    chatDefinition.NextCommandId = 0;
    chatDefinition.NextCognitivityId = 0;
    chatDefinition.PersistentMenuEntries = [];
    chatDefinition.NextPersistentMenuId = 1;
    chatDefinition.PersistentMenu = new PersistentMenu();
    chatDefinition.Greetings = this.createGreetings();
    chatDefinition.AccountLinking = new AccountLinking();
    chatDefinition.BusinessAvailability = this.createBusinessAvailability();
    chatDefinition.YSocialSettings = new YSocialSettings();
    chatDefinition.GoogleConfiguration = new GoogleConfiguration();
    chatDefinition.StatisticEventList = [];
    chatDefinition.CognitivityEnabled = false;
    chatDefinition.CognitivityProject = null;
    chatDefinition.PrioritizeCognitivity = true;
    chatDefinition.IceBreakers = null;
    chatDefinition.HsmJumpEnabled = false;
    chatDefinition.DefaultSettings = new DefaultSettings();
    return chatDefinition;
  }
  public createNewModuleFromTemplate(name: string, channel: string): string {
    var chatDefinition: ChatDefinition = this.createFlowFromTemplate();
    return this.getSerializedState(chatDefinition);
  }

  public createNewFromTemplate(name: string, channel: string): string {
    let module = new ModuleDefinition();
    module.createMaster();

    var chatDefinition: ChatDefinition = this.createFlowFromTemplate();
    let defaultBlock = new BlockDefinition();
    defaultBlock.init(SystemBlocks.SYSTEMBLOCK_WELCOME.toString(), this.translateService.instant('DEFAULTBLOCK_WELCOME'), true, null, true, module.id, true);
    chatDefinition.DefaultBlocks.push(defaultBlock);

    defaultBlock = new BlockDefinition();
    defaultBlock.init(SystemBlocks.SYSTEMBLOCK_TRANSFERED.toString(), this.translateService.instant('DEFAULTBLOCK_TRANSFERED'), true, null, false, module.id, true);
    chatDefinition.DefaultBlocks.push(defaultBlock);

    defaultBlock = new BlockDefinition();
    defaultBlock.init(SystemBlocks.SYSTEMBLOCK_CLOSED.toString(), this.translateService.instant('DEFAULTBLOCK_CLOSED'), true, null, false, module.id, true);
    chatDefinition.DefaultBlocks.push(defaultBlock);

    defaultBlock = new BlockDefinition();
    defaultBlock.init(SystemBlocks.SYSTEMBLOCK_ABANDONED.toString(), this.translateService.instant('DEFAULTBLOCK_ABANDONED'), true, null, false, module.id, true);
    chatDefinition.DefaultBlocks.push(defaultBlock);

    defaultBlock = new BlockDefinition();
    defaultBlock.init(SystemBlocks.SYSTEMBLOCK_ACCOUNT_LINKING.toString(), this.translateService.instant('DEFAULTBLOCK_ACCOUNTLINK'), true, null, true, module.id, true);
    chatDefinition.DefaultBlocks.push(defaultBlock);

    defaultBlock = new BlockDefinition();
    defaultBlock.init(SystemBlocks.SYSTEMBLOCK_ACCOUNT_UNLINKING.toString(), this.translateService.instant('DEFAULTBLOCK_ACCOUNTUNLINK'), true, null, true, module.id, true);
    chatDefinition.DefaultBlocks.push(defaultBlock);

    if (channel === ChannelTypes.FacebookMessenger ||
      channel === ChannelTypes.Instagram) {
      defaultBlock = new BlockDefinition();
      defaultBlock.init(SystemBlocks.SYSTEMBLOCK_USERPHONENUMBER.toString(), this.translateService.instant('DEFAULTBLOCK_USERPHONENUMBER'), true, null, true, module.id, true);
      chatDefinition.DefaultBlocks.push(defaultBlock);

      defaultBlock = new BlockDefinition();
      defaultBlock.init(SystemBlocks.SYSTEMBLOCK_USEREMAIL.toString(), this.translateService.instant('DEFAULTBLOCK_USEREMAIL'), true, null, true, module.id, true);
      chatDefinition.DefaultBlocks.push(defaultBlock);
    }

    defaultBlock = new BlockDefinition();
    defaultBlock.init(SystemBlocks.SYSTEMBLOCK_DEFAULT_ANSWER.toString(), this.translateService.instant('DEFAULTBLOCK_DEFAULTANSWER'), true, null, true, module.id, true);
    chatDefinition.DefaultBlocks.push(defaultBlock);

    /**
     * no puedo crear este bloque ya que necesito el flow instanciado para asignarle las piezas por defecto
     *
     * defaultBlock = new BlockDefinition();
     defaultBlock.init(SystemBlocks.SYSTEMBLOCK_INACTIVITY_CLOSED, this.translateService.instant('SYSTEMBLOCK_INACTIVITY_CLOSED'), true, this.getPiecesForIncativityClosed());
     chatDefinition.DefaultBlocks.push(defaultBlock);
     */


    if (channel === ChannelTypes.WhatsApp) {
      defaultBlock = new BlockDefinition();
      defaultBlock.init(SystemBlocks.SYSTEMBLOCK_HSM.toString(), this.translateService.instant('DEFAULTBLOCK_HSM'), true, null, true, module.id, true);
      chatDefinition.DefaultBlocks.push(defaultBlock);

      defaultBlock = new BlockDefinition();
      defaultBlock.init(SystemBlocks.SYSTEMBLOCK_WHATSAPP_ORDER.toString(), this.translateService.instant('DEFAULTBLOCK_WHATSAPPORDER'), true, null, true, module.id, true);
      chatDefinition.DefaultBlocks.push(defaultBlock);

      defaultBlock = new BlockDefinition();
      defaultBlock.init(SystemBlocks.SYSTEMBLOCK_WHATSAPP_REFERRER_PRODUCT.toString(), this.translateService.instant('DEFAULTBLOCK_WHATSAPPREFERRERPRODUCT'), true, null, true, module.id, true);
      chatDefinition.DefaultBlocks.push(defaultBlock);
    }


    /**
     * no puedo crear este bloque ya que necesito el flow instanciado para asignarle las piezas por defecto
     *
     * defaultBlock = new BlockDefinition();
     * defaultBlock.init(this.SYSTEMBLOCK_EXCEPTION, this.translateService.instant('SYSTEMBLOCK_EXCEPTION'), true, this.getPiecesForDefaultBlockExcepcion(channel));
     * chatDefinition.DefaultBlocks.push(defaultBlock);
     */

    if (channel === ChannelTypes.Chat) {
      if (!environment.standAlone) {
        defaultBlock = new BlockDefinition();
        defaultBlock.init(SystemBlocks.SYSTEMBLOCK_TRANSFERFAILED.toString(), this.translateService.instant('DEFAULTBLOCK_TRANSFERFAILED'), true, null, true, module.id, true);
        chatDefinition.DefaultBlocks.push(defaultBlock);
      }
    }

    this.restore(chatDefinition, channel, module);
    this.mCurrentChatDefinition.NextVarId = 1000 /*so we've a little offset for new implicits*/;
    chatDefinition.PersistentMenu.root = this.createPersistentMenu().id;

    this.mCurrentChatDefinition.YSocialSettings.Url = ySocialUrl();
    return this.getSerializedState();
  }

  public isDefaultBlockValidForChannel(id: string, channel: string) {
    switch (id) {
      case SystemBlocks.SYSTEMBLOCK_WELCOME.toString():
      case SystemBlocks.SYSTEMBLOCK_TRANSFERED.toString():
      case SystemBlocks.SYSTEMBLOCK_CLOSED.toString():
      case SystemBlocks.SYSTEMBLOCK_ABANDONED.toString():
      case SystemBlocks.SYSTEMBLOCK_ACCOUNT_LINKING.toString():
      case SystemBlocks.SYSTEMBLOCK_ACCOUNT_UNLINKING.toString():
      case SystemBlocks.SYSTEMBLOCK_DEFAULT_ANSWER.toString():
      case SystemBlocks.SYSTEMBLOCK_EXCEPTION.toString():
        return true;
      case SystemBlocks.SYSTEMBLOCK_USERPHONENUMBER.toString():
      case SystemBlocks.SYSTEMBLOCK_USEREMAIL.toString():
        if (channel === ChannelTypes.FacebookMessenger || channel === ChannelTypes.Instagram) {
          return true;
        }
        return false;
      case SystemBlocks.SYSTEMBLOCK_TRANSFERFAILED.toString():
        if (channel === ChannelTypes.Chat && !environment.standAlone) {
          return true;
        }
        return false;
      case SystemBlocks.SYSTEMBLOCK_HSM.toString():
      case SystemBlocks.SYSTEMBLOCK_WHATSAPP_ORDER.toString():
      case SystemBlocks.SYSTEMBLOCK_WHATSAPP_REFERRER_PRODUCT.toString():
        if (channel === ChannelTypes.WhatsApp) {
          return true;
        }
        return false;
      case SystemBlocks.SYSTEMBLOCK_INACTIVITY_CLOSED.toString():
        if (channel !== ChannelTypes.Chat) {
          return true;
        }
        return false;
      default:
        return false;
    }
  }

  public restoreFromSerializedString(serializedStr: string, channel: string) {
    let state: ChatDefinition = null;
    try {
      let module = new ModuleDefinition();
      module.createMaster();

      console.log(`[DEBUG] Iniciando deserialización. Tipo de serializedStr: ${typeof serializedStr}`);
      console.log(`[DEBUG] Valor de serializedStr:`, serializedStr);

      // Validar que serializedStr no sea undefined o null
      if (serializedStr === undefined || serializedStr === null) {
        console.error(`[ERROR] serializedStr es ${serializedStr}, no se puede deserializar`);
        return;
      }

      // Si es un objeto, convertirlo a string
      if (typeof serializedStr === 'object') {
        console.log(`[DEBUG] Convirtiendo objeto a string`);
        serializedStr = JSON.stringify(serializedStr);
      }

      // Si es string vacío, usar un objeto vacío por defecto
      if (typeof serializedStr === 'string' && serializedStr.trim() === '') {
        console.log(`[DEBUG] String vacío, usando objeto por defecto`);
        serializedStr = '{"BlockGroups":[],"BlockList":[]}';
      }

      console.log(`[DEBUG] Primeros 200 caracteres: ${serializedStr.substring(0, 200)}`);

      // Intentar parsear primero como JSON regular para validar estructura
      let jsonData = JSON.parse(serializedStr);
      console.log(`[DEBUG] JSON parseado correctamente. BlockGroups: ${jsonData.BlockGroups ? 'existe' : 'undefined'}, BlockList: ${jsonData.BlockList ? 'existe' : 'undefined'}`);

      // Inicializar arrays si no existen para evitar errores de deserialización
      if (!jsonData.BlockGroups) {
        console.log(`[DEBUG] Inicializando BlockGroups vacío`);
        jsonData.BlockGroups = [];
      }
      if (!jsonData.BlockList) {
        console.log(`[DEBUG] Inicializando BlockList vacío`);
        jsonData.BlockList = [];
      }

      console.log(`[DEBUG] Intentando deserialización tipada...`);
      // Ahora intentar la deserialización tipada
      state = TypedJSON.parse(JSON.stringify(jsonData), ChatDefinition);
      console.log(`[DEBUG] Deserialización tipada exitosa`);
      state = this.setModulesToFlow(this.mCurrentModules, state);
      this.restore(state, channel, module);
    }
    catch (e) {
      console.error(`[ERROR] No se pudo deserializar el JSON tipado ${typeof serializedStr === 'object' ? '[object Object]' : serializedStr}: ${e}`);
      console.error(`[ERROR] Stack trace:`, e.stack);
      console.error(`[ERROR] Tipo de error:`, e.constructor.name);
      console.error(`[ERROR] Valor completo de serializedStr:`, serializedStr);
    }
  }

  public setModulesToFlow(modules: ModuleDefinition[], flow: ChatDefinition): ChatDefinition {
    if (Array.isArray(modules)) {
      flow.BlockGroups.forEach(blockGroup => {
        blockGroup.Module = modules.find(module => module.id === blockGroup.ModuleId);
      });
    }

    return flow;
  }

  public restore(definition: ChatDefinition, channel: string, module: ModuleDefinition) {
    this.mCurrentChatDefinition = new ChatDefinition(); // little hack so creating implicit vars doesnt crash
    this.initImplicitVarList(channel);
    this.mCurrentChatDefinition = definition;
    this.mCurrentChatDefinition = this.recoverComplexPieces(this.mCurrentChatDefinition, channel);

    //saco el md5 antes de todos los cambios, ya que si agrego cosas no me deja guardarlo y el usuario
    //no sabe que tiene cambios pendientes.
    //TODO: Excluyo del calculo del md5 a los templates porque falla si estan, ver como resolver esto

    // Asegurar que YSocialSettings esté inicializado
    if (definition.YSocialSettings === null) {
      definition.YSocialSettings = new YSocialSettings();
    }

    var hsmTemplates = definition.YSocialSettings.WhatsappHSMTemplatesServices;
    definition.YSocialSettings.WhatsappHSMTemplatesServices = null;
    this.mCurrentFlowHash = Md5.hashStr(TypedJSON.stringify(definition, ChatDefinition, { preserveNull: true })) as string;
    definition.YSocialSettings.WhatsappHSMTemplatesServices = hsmTemplates;

    this.mEditorState = new EditorState();
    this.mRetrievedWhatsappCatalogs = false;
    this.mRetrievedWhatsappCatalogsInfo = null;
    this.mRetrievedWhatsappCatalogsInfoParsed = null;

    let defaultBlock: BlockDefinition;

    if (defaultBlock = this.mCurrentChatDefinition.DefaultBlocks.find(b => b.Id === SystemBlocks.SYSTEMBLOCK_TRANSFERED.toString())) {
    } else {
      defaultBlock = new BlockDefinition();
      defaultBlock.init(SystemBlocks.SYSTEMBLOCK_TRANSFERED.toString(), this.translateService.instant('DEFAULTBLOCK_TRANSFERED'), true, null, false, module.id, true);
      this.mCurrentChatDefinition.DefaultBlocks.push(defaultBlock);
    }

    if (defaultBlock = this.mCurrentChatDefinition.DefaultBlocks.find(b => b.Id === SystemBlocks.SYSTEMBLOCK_CLOSED.toString())) {
      defaultBlock.Name = this.translateService.instant('DEFAULTBLOCK_CLOSED');
      this.mEditorState.SelectedBlock = defaultBlock;
    } else {
      defaultBlock = new BlockDefinition();
      defaultBlock.init(SystemBlocks.SYSTEMBLOCK_CLOSED.toString(), this.translateService.instant('DEFAULTBLOCK_CLOSED'), true, null, false, module.id, true);
      this.mCurrentChatDefinition.DefaultBlocks.push(defaultBlock);
    }

    if (defaultBlock = this.mCurrentChatDefinition.DefaultBlocks.find(b => b.Id === SystemBlocks.SYSTEMBLOCK_ABANDONED.toString())) {
      defaultBlock.Name = this.translateService.instant('DEFAULTBLOCK_ABANDONED');
      this.mEditorState.SelectedBlock = defaultBlock;
    } else {
      defaultBlock = new BlockDefinition();
      defaultBlock.init(SystemBlocks.SYSTEMBLOCK_ABANDONED.toString(), this.translateService.instant('DEFAULTBLOCK_ABANDONED'), true, null, false, module.id, true);
      this.mCurrentChatDefinition.DefaultBlocks.push(defaultBlock);
    }

    if (defaultBlock = this.mCurrentChatDefinition.DefaultBlocks.find(b => b.Id === SystemBlocks.SYSTEMBLOCK_WELCOME.toString())) {
      defaultBlock.Name = this.translateService.instant('DEFAULTBLOCK_WELCOME');
      this.mEditorState.SelectedBlock = defaultBlock;
    }

    if (channel === ChannelTypes.WhatsApp) {
      defaultBlock = this.mCurrentChatDefinition.DefaultBlocks.find(b => b.Id === SystemBlocks.SYSTEMBLOCK_HSM.toString());
      if (!defaultBlock) {
        defaultBlock = new BlockDefinition();
        defaultBlock.init(SystemBlocks.SYSTEMBLOCK_HSM.toString(), this.translateService.instant('DEFAULTBLOCK_HSM'), true, null, true, module.id, true);
        this.mCurrentChatDefinition.DefaultBlocks.push(defaultBlock);
      }
      else {
        defaultBlock.Name = this.translateService.instant('DEFAULTBLOCK_HSM');
      }

      defaultBlock = this.mCurrentChatDefinition.DefaultBlocks.find(b => b.Id === SystemBlocks.SYSTEMBLOCK_WHATSAPP_ORDER.toString());
      if (!defaultBlock) {
        defaultBlock = new BlockDefinition();
        defaultBlock.init(SystemBlocks.SYSTEMBLOCK_WHATSAPP_ORDER.toString(), this.translateService.instant('DEFAULTBLOCK_WHATSAPPORDER'), true, null, true, module.id, true);
        this.mCurrentChatDefinition.DefaultBlocks.push(defaultBlock);
      }
      else {
        defaultBlock.Name = this.translateService.instant('DEFAULTBLOCK_WHATSAPPORDER');
      }

      defaultBlock = this.mCurrentChatDefinition.DefaultBlocks.find(b => b.Id === SystemBlocks.SYSTEMBLOCK_WHATSAPP_REFERRER_PRODUCT.toString());
      if (!defaultBlock) {
        defaultBlock = new BlockDefinition();
        defaultBlock.init(SystemBlocks.SYSTEMBLOCK_WHATSAPP_REFERRER_PRODUCT.toString(), this.translateService.instant('DEFAULTBLOCK_WHATSAPPREFERRERPRODUCT'), true, null, true, module.id, true);
        this.mCurrentChatDefinition.DefaultBlocks.push(defaultBlock);
      }
      else {
        defaultBlock.Name = this.translateService.instant('DEFAULTBLOCK_WHATSAPPREFERRERPRODUCT');
      }
    }

    defaultBlock = this.mCurrentChatDefinition.DefaultBlocks.find(b => b.Id === SystemBlocks.SYSTEMBLOCK_ACCOUNT_LINKING.toString());
    if (!defaultBlock) {
      defaultBlock = new BlockDefinition();
      defaultBlock.init(SystemBlocks.SYSTEMBLOCK_ACCOUNT_LINKING.toString(), this.translateService.instant('DEFAULTBLOCK_ACCOUNTLINK'), true, null, true, module.id, true);
      this.mCurrentChatDefinition.DefaultBlocks.push(defaultBlock);
    }
    else {
      defaultBlock.Name = this.translateService.instant('DEFAULTBLOCK_ACCOUNTLINK');
    }

    defaultBlock = this.mCurrentChatDefinition.DefaultBlocks.find(b => b.Id === SystemBlocks.SYSTEMBLOCK_ACCOUNT_UNLINKING.toString());
    if (!defaultBlock) {
      defaultBlock = new BlockDefinition();
      defaultBlock.init(SystemBlocks.SYSTEMBLOCK_ACCOUNT_UNLINKING.toString(), this.translateService.instant('DEFAULTBLOCK_ACCOUNTUNLINK'), true, null, true, module.id, true);
      this.mCurrentChatDefinition.DefaultBlocks.push(defaultBlock);
    }
    else {
      defaultBlock.Name = this.translateService.instant('DEFAULTBLOCK_ACCOUNTUNLINK');
    }

    if (channel === ChannelTypes.FacebookMessenger ||
      channel === ChannelTypes.Instagram) {

      defaultBlock = this.mCurrentChatDefinition.DefaultBlocks.find(b => b.Id === SystemBlocks.SYSTEMBLOCK_USERPHONENUMBER.toString());
      if (!defaultBlock) {
        defaultBlock = new BlockDefinition();
        defaultBlock.init(SystemBlocks.SYSTEMBLOCK_USERPHONENUMBER.toString(), this.translateService.instant('DEFAULTBLOCK_USERPHONENUMBER'), true, null, true, module.id, true);
        this.mCurrentChatDefinition.DefaultBlocks.push(defaultBlock);
      }
      else {
        defaultBlock.Name = this.translateService.instant('DEFAULTBLOCK_USERPHONENUMBER');
      }

      defaultBlock = this.mCurrentChatDefinition.DefaultBlocks.find(b => b.Id === SystemBlocks.SYSTEMBLOCK_USEREMAIL.toString());
      if (!defaultBlock) {
        defaultBlock = new BlockDefinition();
        defaultBlock.init(SystemBlocks.SYSTEMBLOCK_USEREMAIL.toString(), this.translateService.instant('DEFAULTBLOCK_USEREMAIL'), true, null, true, module.id, true);
        this.mCurrentChatDefinition.DefaultBlocks.push(defaultBlock);
      }
      else {
        defaultBlock.Name = this.translateService.instant('DEFAULTBLOCK_USEREMAIL');
      }
    }
    else {
      let index = this.mCurrentChatDefinition.DefaultBlocks.findIndex(b => b.Id === SystemBlocks.SYSTEMBLOCK_USERPHONENUMBER.toString());
      if (index >= 0) {
        this.mCurrentChatDefinition.DefaultBlocks.splice(index, 1);
      }

      index = this.mCurrentChatDefinition.DefaultBlocks.findIndex(b => b.Id === SystemBlocks.SYSTEMBLOCK_USEREMAIL.toString());
      if (index >= 0) {
        this.mCurrentChatDefinition.DefaultBlocks.splice(index, 1);
      }
    }

    defaultBlock = this.mCurrentChatDefinition.DefaultBlocks.find(b => b.Id === SystemBlocks.SYSTEMBLOCK_DEFAULT_ANSWER.toString());
    if (!defaultBlock) {
      defaultBlock = new BlockDefinition();
      defaultBlock.init(SystemBlocks.SYSTEMBLOCK_DEFAULT_ANSWER.toString(), this.translateService.instant('DEFAULTBLOCK_DEFAULTANSWER'), true, null, true, module.id, true);
      this.mCurrentChatDefinition.DefaultBlocks.push(defaultBlock);
    }
    else {
      defaultBlock.Name = this.translateService.instant('DEFAULTBLOCK_DEFAULTANSWER');
    }

    if (channel !== ChannelTypes.Chat) {
      defaultBlock = this.mCurrentChatDefinition.DefaultBlocks.find(b => b.Id === SystemBlocks.SYSTEMBLOCK_INACTIVITY_CLOSED.toString());
      if (!defaultBlock) {
        defaultBlock = new BlockDefinition();
        defaultBlock.init(SystemBlocks.SYSTEMBLOCK_INACTIVITY_CLOSED.toString(), this.translateService.instant('SYSTEMBLOCK_INACTIVITY_CLOSED'), true, this.getPiecesForIncativityClosed(), true, module.id, true);
        this.mCurrentChatDefinition.DefaultBlocks.push(defaultBlock);
      }
      else {
        defaultBlock.Name = this.translateService.instant('SYSTEMBLOCK_INACTIVITY_CLOSED');
      }
    }

    defaultBlock = this.mCurrentChatDefinition.DefaultBlocks.find(b => b.Id === SystemBlocks.SYSTEMBLOCK_EXCEPTION.toString());
    if (!defaultBlock) {
      defaultBlock = new BlockDefinition();
      defaultBlock.init(SystemBlocks.SYSTEMBLOCK_EXCEPTION.toString(), this.translateService.instant('SYSTEMBLOCK_EXCEPTION'), true, this.getPiecesForDefaultBlockExcepcion(channel), true, module.id, true);
      this.mCurrentChatDefinition.DefaultBlocks.push(defaultBlock);
    }
    else {
      defaultBlock.Name = this.translateService.instant('SYSTEMBLOCK_EXCEPTION');
    }

    if (channel === ChannelTypes.Chat) {
      if (!environment.standAlone) {
        defaultBlock = this.mCurrentChatDefinition.DefaultBlocks.find(b => b.Id === SystemBlocks.SYSTEMBLOCK_TRANSFERFAILED.toString());
        if (!defaultBlock) {
          defaultBlock = new BlockDefinition();
          defaultBlock.init(SystemBlocks.SYSTEMBLOCK_TRANSFERFAILED.toString(), this.translateService.instant('DEFAULTBLOCK_TRANSFERFAILED'), true, null, true, module.id, true);
          this.mCurrentChatDefinition.DefaultBlocks.push(defaultBlock);
        }
        else {
          defaultBlock.Name = this.translateService.instant('DEFAULTBLOCK_TRANSFERFAILED');
        }
      }
    }

    this.mCurrentChatDefinition.BlockList.forEach(b => {
      b.Pieces.forEach(p => {
        p.OwnerBlockId = b.Id;
        p.Channel = channel;

        if (p.type === 'quick-reply-piece') {
          let quickReplyPiece = <QuickReplyPiece>p;
          quickReplyPiece.Options.forEach(o => {
            o.Channel = channel;
          });
        }
      });
    });

    this.mCurrentChatDefinition.DefaultBlocks.forEach(b => {
      b.Pieces.forEach(p => {
        p.OwnerBlockId = b.Id;
        p.Channel = channel;

        if (p.type === 'quick-reply-piece') {
          let quickReplyPiece = <QuickReplyPiece>p;
          quickReplyPiece.Options.forEach(o => {
            o.Channel = channel;
          });
        }
      });
    });

    // to keep references
    this.mCurrentChatDefinition.BlockGroups.forEach(group => {
      var blocks: BlockDefinition[] = [];
      for (var i: number = 0; i < group.Blocks.length; i++) {
        let currentBlock = group.Blocks[i];
        if (typeof (currentBlock) !== 'undefined' && currentBlock !== null) {
          var block = this.findBlockWithId(currentBlock.Id);
          if (typeof (block) !== 'undefined' && block !== null) {
            blocks.push(block);
          }
        }
      }

      group.Blocks = blocks;
    });

    this.mCurrentChatDefinition.VariableList.forEach(variable => {
      this.mVariableMap.set(variable.Id, variable);
    });

    this.mCurrentChatDefinition.PersistentMenuEntries.forEach(menu => {
      this.mPersistentMenuMap.set(menu.id, menu);
    });

    if (this.mCurrentChatDefinition.FormatDefinitions === null || this.mCurrentChatDefinition.FormatDefinitions.length === 0) {
      this.mCurrentChatDefinition.FormatDefinitions = FormatDefinition.createDefaultDefinitions();
    }

    if (this.mCurrentChatDefinition.BusinessAvailability === null) {
      this.mCurrentChatDefinition.BusinessAvailability = this.createBusinessAvailability();
    }

    // Actualizamos Business Availability a la última versión
    if (this.mCurrentChatDefinition.BusinessAvailability.workingDates === null) {
      let workingDates = new WorkingDates();
      workingDates.setTimesFromOldSettings(workingDates.monday, this.mCurrentChatDefinition.BusinessAvailability.daysDefinition[0]);
      workingDates.setTimesFromOldSettings(workingDates.tuesday, this.mCurrentChatDefinition.BusinessAvailability.daysDefinition[1]);
      workingDates.setTimesFromOldSettings(workingDates.wednesday, this.mCurrentChatDefinition.BusinessAvailability.daysDefinition[2]);
      workingDates.setTimesFromOldSettings(workingDates.thursday, this.mCurrentChatDefinition.BusinessAvailability.daysDefinition[3]);
      workingDates.setTimesFromOldSettings(workingDates.friday, this.mCurrentChatDefinition.BusinessAvailability.daysDefinition[4]);
      workingDates.setTimesFromOldSettings(workingDates.saturday, this.mCurrentChatDefinition.BusinessAvailability.daysDefinition[5]);
      workingDates.setTimesFromOldSettings(workingDates.sunday, this.mCurrentChatDefinition.BusinessAvailability.daysDefinition[6]);
      this.mCurrentChatDefinition.BusinessAvailability.workingDates = workingDates;
    }
  }

  public recoverComplexPieces(flow: ChatDefinition, channel: string): ChatDefinition {
    const retrieverMethod = b => {
      let pieces = b.Pieces;

      for (let i = 0; i < pieces.length; i++) {
        let piece = pieces[i];
        //WA Menu
        if (piece.type === 'message-piece') {
          var msgPiece = <MessagePieceType>piece;
          var uuid = msgPiece.MenuUuid;
          if (uuid !== null) {
            var dataEntryPiece: DataEntry = null;
            var switchJumpToBlockPiece: SwitchJumpToBlockPiece = null;
            for (let j = i + 1; j < pieces.length; j++) {
              let p = pieces[j];
              if (p.type === 'data-entry-piece') {
                dataEntryPiece = (uuid === (<DataEntry>p).MenuUuid) ? <DataEntry>p : null;
              }
              else if (p.type === 'switch-jump-to-block-piece') {
                switchJumpToBlockPiece = (uuid === (<SwitchJumpToBlockPiece>p).MenuUuid) ? <SwitchJumpToBlockPiece>p : null;
              }
              if (dataEntryPiece !== null && switchJumpToBlockPiece !== null) {
                break;
              }
            }

            if (dataEntryPiece !== null && switchJumpToBlockPiece !== null) {
              let WAMenuPiece = new WAMenu(uuid, msgPiece, dataEntryPiece, switchJumpToBlockPiece);
              WAMenuPiece.type = 'wa-menu-piece';
              pieces.splice(i, 3, WAMenuPiece); //Se inserta la pieza WAMenu donde estaban las 3 piezas
              //pieces = pieces.filter(p => p.Uid !== msgPiece.Uid && p.Uid === dataEntryPiece.Uid && p.Uid === switchJumpToBlockPiece.Uid);
            }
          }

          if (channel === ChannelTypes.AppleMessaging &&
            msgPiece.Buttons !== null &&
            msgPiece.Buttons.length > 0) {

            let pieceDefinition = this.createPiece('PIECE_INTERACTIVE_MESSAGE_BUTTONS', 'fa fa-bars', 'interactive-message-buttons-piece', this.createInteractiveMessageButtons);

            let interactiveButtonsMessage: InteractiveMessageButtonsPiece = <InteractiveMessageButtonsPiece>pieceDefinition.modelFatory();
            interactiveButtonsMessage.type = pieceDefinition.PieceDefinitionType;
            interactiveButtonsMessage.Id = msgPiece.Id;
            interactiveButtonsMessage.OwnerBlockId = msgPiece.OwnerBlockId;
            interactiveButtonsMessage.Channel = msgPiece.Channel;
            interactiveButtonsMessage.Buttons = msgPiece.Buttons;
            interactiveButtonsMessage.TextList = msgPiece.TextList;
            interactiveButtonsMessage.Uid = msgPiece.Uid;
            interactiveButtonsMessage.MenuUuid = msgPiece.MenuUuid;
            pieces[i] = interactiveButtonsMessage;
          }
        }
      }
    }

    flow.BlockList.forEach(retrieverMethod);
    flow.BlockGroups.forEach(g => g.Blocks.forEach(retrieverMethod));
    flow.BlockDeletedList.forEach(retrieverMethod);
    flow.GroupDeletedList.forEach(g => g.Blocks.forEach(retrieverMethod));
    flow.DefaultBlocks.forEach(retrieverMethod);
    return flow;
  }

  public removePieceForBlockInEdition(piece: BasePiece) {
    if (this.mEditorState.SelectedBlock == null) {
      return;
    }
    var index = this.mEditorState.SelectedBlock.Pieces.indexOf(piece, 0);
    if (index > -1) {
      this.mEditorState.SelectedBlock.Pieces.splice(index, 1);
    }
  }

  public cloneOnePiece(piece: BasePiece): BasePiece {
    let json = TypedJSON.stringify(piece, BasePiece, { preserveNull: true });
    let newPiece: BasePiece = null;
    switch (piece.type) {
      case 'message-piece':
        newPiece = TypedJSON.parse(json, MessagePieceType);
        break;
      case 'interactive-message-list-piece':
        newPiece = TypedJSON.parse(json, InteractiveMessageListPiece);
        break;
      case 'interactive-message-productlist-piece':
        newPiece = TypedJSON.parse(json, InteractiveMessageProductListPiece);
        break;
      case 'interactive-message-product-piece':
        newPiece = TypedJSON.parse(json, InteractiveMessageProductPiece);
        break;
      case 'interactive-message-buttons-piece':
        newPiece = TypedJSON.parse(json, InteractiveMessageButtonsPiece);
        break;
      case 'interactive-message-urlbutton-piece':
        newPiece = TypedJSON.parse(json, InteractiveMessageUrlButtonPiece);
        break;
      case 'gallery-piece':
        newPiece = TypedJSON.parse(json, GalleryPiece);
        break;
      case 'dynamic-gallery-piece':
        newPiece = TypedJSON.parse(json, DynamicGalleryPiece);
        break;
      case 'attachment-piece':
        newPiece = TypedJSON.parse(json, AttachmentPiece);
        break;
      case 'video-embed-piece':
        newPiece = TypedJSON.parse(json, VideoEmbedPiece);
        break;
      case 'form-piece':
        newPiece = TypedJSON.parse(json, FormPiece);
        break;
      case 'coordinates-piece':
        newPiece = TypedJSON.parse(json, CoordinatesPiece);
        break;
      case 'multiple-coordinates-piece':
        newPiece = TypedJSON.parse(json, MultipleCoordinatesPiece);
        break;
      case 'nearest-coordinates-piece':
        newPiece = TypedJSON.parse(json, NearestCoordinatesPiece);
        break;
      case 'get-element-from-array-piece':
        newPiece = TypedJSON.parse(json, GetElementFromArrayPiece);
        break;
      case 'get-elements-from-array-piece':
        newPiece = TypedJSON.parse(json, GetElementsFromArrayPiece);
        break;
      case 'evaluate-commands-piece':
        newPiece = TypedJSON.parse(json, EvaluateCommandsPiece);
        break;
      case 'statistic-event-piece':
        newPiece = TypedJSON.parse(json, StatisticEventPiece);
        break;
      case 'store-message-piece':
        newPiece = TypedJSON.parse(json, StoreMessagePiece);
        break;
      case 'log-piece':
        newPiece = TypedJSON.parse(json, LogPiece);
        break;
      case 'post-message-piece':
        newPiece = TypedJSON.parse(json, PostMessagePiece);
        break;
      case 'tag-piece':
        newPiece = TypedJSON.parse(json, TagPiece);
        break;
      case 'quick-reply-piece':
        newPiece = TypedJSON.parse(json, QuickReplyPiece);
        break;
      case 'actions-piece':
        newPiece = TypedJSON.parse(json, ActionsPiece);
        break;
      case 'json-piece':
        newPiece = TypedJSON.parse(json, JsonPiece);
        break;
      case 'rich-link-piece':
        newPiece = TypedJSON.parse(json, RichLinkPiece);
        break;
      case 'apple-interactive-message-authentication-piece':
        newPiece = TypedJSON.parse(json, AppleInteractiveMessageAuthenticationPiece);
        break;
      case 'apple-interactive-message-imessage-app-piece':
        newPiece = TypedJSON.parse(json, AppleInteractiveMessageIMessageAppPiece);
        break;
      case 'apple-interactive-message-applepay-piece':
        newPiece = TypedJSON.parse(json, AppleInteractiveMessageApplePayPiece);
        break;
      case 'time-picker-piece':
        newPiece = TypedJSON.parse(json, TimePickerPiece);
        break;
      case 'integration-piece':
        newPiece = TypedJSON.parse(json, IntegrationPiece);
        break;
      case 'derive-piece':
        newPiece = TypedJSON.parse(json, DeriveOperatorPiece);
        break;
      case 'mark-message-as-pending-piece':
        newPiece = TypedJSON.parse(json, MarkMessageAsPendingPiece);
        break;
      case 'mail-piece':
        newPiece = TypedJSON.parse(json, MailPiece);
        break;
      case 'data-entry-piece':
        newPiece = TypedJSON.parse(json, DataEntry);
        break;
      case 'smart-form-piece':
        newPiece = TypedJSON.parse(json, SmartFormPiece);
        break;
      case 'knowledge-base-piece':
        newPiece = TypedJSON.parse(json, KnowledgeBasePiece);
        break;
      case 'set-variable-from-entity':
        newPiece = TypedJSON.parse(json, SetVariableFromEntity);
        break;
      case 'multimedia-entry-piece':
        newPiece = TypedJSON.parse(json, MultiMediaEntry);
        break;
      case 'geocoder-google-piece':
        newPiece = TypedJSON.parse(json, GeocoderGooglePiece);
        break;
      case 'condition-piece':
        newPiece = TypedJSON.parse(json, ConditionPiece);
        break;
      case 'variable-condition-piece':
        newPiece = TypedJSON.parse(json, VariableConditionPiece);
        break;
      case 'set-variable':
        newPiece = TypedJSON.parse(json, SetVariable);
        break;
      case 'shorten-url':
        newPiece = TypedJSON.parse(json, ShortenUrlPiece);
        break;
      case 'jump-to-block-piece':
        newPiece = TypedJSON.parse(json, JumpToBlockPiece);
        break;
      case 'call-block-as-procedure-piece':
        newPiece = TypedJSON.parse(json, CallBlockAsProcedurePiece);
        break;
      case 'switch-jump-to-block-piece':
        newPiece = TypedJSON.parse(json, SwitchJumpToBlockPiece);
        break;
      case 'return-to-last-block-piece':
        newPiece = TypedJSON.parse(json, ReturnToLastBlockPiece);
        break;
      case 'reset-variables-piece':
        newPiece = TypedJSON.parse(json, ResetVariablesPiece);
        break;
      case 'close-case-piece':
        newPiece = TypedJSON.parse(json, CloseCasePiece);
        break;
      case 'update-profile-piece':
        newPiece = TypedJSON.parse(json, UpdateProfilePiece);
        break;
      case 'update-case-piece':
        newPiece = TypedJSON.parse(json, UpdateCasePiece);
        break;
      case 'calendar-piece':
        newPiece = TypedJSON.parse(json, CalendarPiece);
        break;
      case 'evaluate-cognitivity-piece':
        newPiece = TypedJSON.parse(json, EvaluateCognitivityPiece);
        break;
      case 'db-query-piece':
        newPiece = TypedJSON.parse(json, DbQueryPiece);
        break;
      case 'wa-menu-piece':
        /**
         * Los menus para WA estan formados por tres modelos,
         * asi que no se pueden copiar por el json
         */
        newPiece = _.cloneDeep(piece);
        break;
      case 'signature-pad-piece':
        newPiece = TypedJSON.parse(json, SignaturePadPiece);
        break;
      case 'biometric-piece':
        newPiece = TypedJSON.parse(json, BiometricPiece);
        break;
      case 'account-linking-piece':
        newPiece = TypedJSON.parse(json, AccountLinkingPiece);
        break;
      case 'account-unlinking-piece':
        newPiece = TypedJSON.parse(json, AccountUnlinkingPiece);
        break;
      case 'validate-back-dni-piece':
        newPiece = TypedJSON.parse(json, ValidateBackDniPiece);
        break;
      case 'validate-frontal-dni-piece':
        newPiece = TypedJSON.parse(json, ValidateFrontalDniPiece);
        break;
      case 'sticker-piece':
        newPiece = TypedJSON.parse(json, StickerPiece);
        break;
      case 'send-hsm-piece':
        newPiece = TypedJSON.parse(json, SendHsmPiece);
        break;
      case 'comment-piece':
        newPiece = TypedJSON.parse(json, CommentPiece);
        break;
      case 'authenticate-anonymous-profile-piece':
        newPiece = TypedJSON.parse(json, AuthenticateAnonymousProfilePiece);
        break;
      case 'get-message-entities-piece':
        newPiece = TypedJSON.parse<GetMessageEntitiesPiece>(json, GetMessageEntitiesPiece);
        break;
      case 'invoke-whatsapp-flow-piece':
        newPiece = TypedJSON.parse<InvokeWhatsappFlowPiece>(json, InvokeWhatsappFlowPiece);
        break;
      case 'concatenate-piece':
        newPiece = TypedJSON.parse<Concatenate>(json, Concatenate);
        break;
      case 'encrypt-piece':
        newPiece = TypedJSON.parse<EncryptPiece>(json, EncryptPiece);
        break;
      case 'decrypt-piece':
        newPiece = TypedJSON.parse<DecryptPiece>(json, DecryptPiece);
        break;
      case 'multimedia-analysis-piece':
        newPiece = TypedJSON.parse<MultimediaAnalysisPiece>(json, MultimediaAnalysisPiece);
        break;
      case 'profile-list-piece':
        newPiece = TypedJSON.parse<ProfileListPiece>(json, ProfileListPiece);
        break;
    }

    // Ultima Pieza
    return newPiece;
  }

  public clonePiece(piece: BasePiece, destinationBlock?: BlockDefinition) {
    if (typeof (destinationBlock) === 'undefined' || destinationBlock === null) {
      destinationBlock = this.mEditorState.SelectedBlock;
    }

    if (destinationBlock === null) {
      return;
    }

    var newPiece = this.cloneOnePiece(piece);
    if (newPiece !== null) {
      newPiece.reset(this.mCurrentChatDefinition.NextPieceId);
      this.mCurrentChatDefinition.NextPieceId++;
      destinationBlock.Pieces.push(newPiece);
      newPiece.OwnerBlockId = destinationBlock.Id;
      newPiece.Channel = this.mCurrentFlow.channel;

      if (newPiece.type === 'quick-reply-piece') {
        let quickReplyPiece = <QuickReplyPiece>newPiece;
        quickReplyPiece.Options.forEach(o => {
          o.Channel = newPiece.Channel;
        });
      }
    }
  }

  public createPieceFromUntypedObject(piece: any): BasePiece {
    if (typeof (piece.__type) !== 'undefined') {
      delete piece.__type;
    }

    let json = JSON.stringify(piece);
    let newPiece: BasePiece = null;
    switch (piece.type) {
      case 'message-piece':
        newPiece = TypedJSON.parse<MessagePieceType>(json, MessagePieceType);
        break;
      case 'interactive-message-list-piece':
        newPiece = TypedJSON.parse<InteractiveMessageListPiece>(json, InteractiveMessageListPiece);
        break;
      case 'interactive-message-buttons-piece':
        newPiece = TypedJSON.parse<InteractiveMessageButtonsPiece>(json, InteractiveMessageButtonsPiece);
        break;
      case 'interactive-message-urlbutton-piece':
        newPiece = TypedJSON.parse<InteractiveMessageUrlButtonPiece>(json, InteractiveMessageUrlButtonPiece);
        break;
      case 'interactive-message-productlist-piece':
        newPiece = TypedJSON.parse<InteractiveMessageProductListPiece>(json, InteractiveMessageProductListPiece);
        break;
      case 'interactive-message-product-piece':
        newPiece = TypedJSON.parse<InteractiveMessageProductPiece>(json, InteractiveMessageProductPiece);
        break;
      case 'gallery-piece':
        newPiece = TypedJSON.parse<GalleryPiece>(json, GalleryPiece);
        break;
      case 'dynamic-gallery-piece':
        newPiece = TypedJSON.parse<DynamicGalleryPiece>(json, DynamicGalleryPiece);
        break;
      case 'attachment-piece':
        newPiece = TypedJSON.parse<AttachmentPiece>(json, AttachmentPiece);
        break;
      case 'video-embed-piece':
        newPiece = TypedJSON.parse<VideoEmbedPiece>(json, VideoEmbedPiece);
        break;
      case 'form-piece':
        newPiece = TypedJSON.parse<FormPiece>(json, FormPiece);
        break;
      case 'coordinates-piece':
        newPiece = TypedJSON.parse<CoordinatesPiece>(json, CoordinatesPiece);
        break;
      case 'mulitple-coordinates-piece':
        newPiece = TypedJSON.parse<MultipleCoordinatesPiece>(json, MultipleCoordinatesPiece);
        break;
      case 'nearest-coordinates-piece':
        newPiece = TypedJSON.parse<NearestCoordinatesPiece>(json, NearestCoordinatesPiece);
        break;
      case 'get-element-from-array-piece':
        newPiece = TypedJSON.parse<GetElementFromArrayPiece>(json, GetElementFromArrayPiece);
        break;
      case 'get-elements-from-array-piece':
        newPiece = TypedJSON.parse<GetElementsFromArrayPiece>(json, GetElementsFromArrayPiece);
        break;
      case 'evaluate-commands-piece':
        newPiece = TypedJSON.parse<EvaluateCommandsPiece>(json, EvaluateCommandsPiece);
        break;
      case 'statistic-event-piece':
        newPiece = TypedJSON.parse<StatisticEventPiece>(json, StatisticEventPiece);
        break;
      case 'store-message-piece':
        newPiece = TypedJSON.parse<StoreMessagePiece>(json, StoreMessagePiece);
        break;
      case 'log-piece':
        newPiece = TypedJSON.parse<LogPiece>(json, LogPiece);
        break;
      case 'post-message-piece':
        newPiece = TypedJSON.parse<LogPiece>(json, PostMessagePiece);
        break;
      case 'tag-piece':
        newPiece = TypedJSON.parse<TagPiece>(json, TagPiece);
        break;
      case 'quick-reply-piece':
        newPiece = TypedJSON.parse<QuickReplyPiece>(json, QuickReplyPiece);
        break;
      case 'actions-piece':
        newPiece = TypedJSON.parse<ActionsPiece>(json, ActionsPiece);
        break;
      case 'json-piece':
        newPiece = TypedJSON.parse<JsonPiece>(json, JsonPiece);
        break;
      case 'rich-link-piece':
        newPiece = TypedJSON.parse<RichLinkPiece>(json, RichLinkPiece);
        break;
      case 'apple-interactive-message-authentication-piece':
        newPiece = TypedJSON.parse<AppleInteractiveMessageAuthenticationPiece>(json, AppleInteractiveMessageAuthenticationPiece);
        break;
      case 'apple-interactive-message-imessage-app-piece':
        newPiece = TypedJSON.parse<AppleInteractiveMessageIMessageAppPiece>(json, AppleInteractiveMessageIMessageAppPiece);
        break;
      case 'apple-interactive-message-applepay-piece':
        newPiece = TypedJSON.parse<AppleInteractiveMessageApplePayPiece>(json, AppleInteractiveMessageApplePayPiece);
        break;
      case 'time-picker-piece':
        newPiece = TypedJSON.parse<TimePickerPiece>(json, TimePickerPiece);
        break;
      case 'integration-piece':
        newPiece = TypedJSON.parse<IntegrationPiece>(json, IntegrationPiece);
        break;
      case 'derive-piece':
        newPiece = TypedJSON.parse<DeriveOperatorPiece>(json, DeriveOperatorPiece);
        break;
      case 'mark-message-as-pending-piece':
        newPiece = TypedJSON.parse<MarkMessageAsPendingPiece>(json, MarkMessageAsPendingPiece);
        break;
      case 'mail-piece':
        newPiece = TypedJSON.parse<MailPiece>(json, MailPiece);
        break;
      case 'data-entry-piece':
        newPiece = TypedJSON.parse<DataEntry>(json, DataEntry);
        break;
      case 'smart-form-piece':
        newPiece = TypedJSON.parse<SmartFormPiece>(json, SmartFormPiece);
        break;
      case 'knowledge-base-piece':
        newPiece = TypedJSON.parse<KnowledgeBasePiece>(json, KnowledgeBasePiece);
        break;
      case 'set-variable-from-entity':
        newPiece = TypedJSON.parse<SetVariableFromEntity>(json, SetVariableFromEntity);
        break;
      case 'multimedia-entry-piece':
        newPiece = TypedJSON.parse<MultiMediaEntry>(json, MultiMediaEntry);
        break;
      case 'geocoder-google-piece':
        newPiece = TypedJSON.parse<GeocoderGooglePiece>(json, GeocoderGooglePiece);
        break;
      case 'condition-piece':
        newPiece = TypedJSON.parse<ConditionPiece>(json, ConditionPiece);
        break;
      case 'variable-condition-piece':
        newPiece = TypedJSON.parse<VariableConditionPiece>(json, VariableConditionPiece);
        break;
      case 'set-variable':
        newPiece = TypedJSON.parse<SetVariable>(json, SetVariable);
        break;
      case 'shorten-url':
        newPiece = TypedJSON.parse<ShortenUrlPiece>(json, ShortenUrlPiece);
        break;
      case 'jump-to-block-piece':
        newPiece = TypedJSON.parse<JumpToBlockPiece>(json, JumpToBlockPiece);
        break;
      case 'call-block-as-procedure-piece':
        newPiece = TypedJSON.parse<CallBlockAsProcedurePiece>(json, CallBlockAsProcedurePiece);
        break;
      case 'switch-jump-to-block-piece':
        newPiece = TypedJSON.parse<SwitchJumpToBlockPiece>(json, SwitchJumpToBlockPiece);
        break;
      case 'return-to-last-block-piece':
        newPiece = TypedJSON.parse<ReturnToLastBlockPiece>(json, ReturnToLastBlockPiece);
        break;
      case 'concatenate-piece':
        newPiece = TypedJSON.parse<Concatenate>(json, Concatenate);
        break;
      case 'multiple-messages-piece':
        newPiece = TypedJSON.parse<MultipleMessagePiece>(json, MultipleMessagePiece);
        break;
      case 'reset-variables-piece':
        newPiece = TypedJSON.parse<ResetVariablesPiece>(json, ResetVariablesPiece);
        break;
      case 'close-case-piece':
        newPiece = TypedJSON.parse<CloseCasePiece>(json, CloseCasePiece);
        break;
      case 'update-profile-piece':
        newPiece = TypedJSON.parse<UpdateProfilePiece>(json, UpdateProfilePiece);
        break;
      case 'update-case-piece':
        newPiece = TypedJSON.parse<UpdateCasePiece>(json, UpdateCasePiece);
        break;
      case 'calendar-piece':
        newPiece = TypedJSON.parse<CalendarPiece>(json, CalendarPiece);
        break;
      case 'evaluate-cognitivity-piece':
        newPiece = TypedJSON.parse<EvaluateCognitivityPiece>(json, EvaluateCognitivityPiece);
        break;
      case 'db-query-piece':
        newPiece = TypedJSON.parse<DbQueryPiece>(json, DbQueryPiece);
        break;
      case 'wa-menu-piece':
        /**
         * Los menus para WA estan formados por tres modelos,
         * asi que no se pueden copiar por el json
         */
        newPiece = <WAMenu>_.cloneDeep(piece);
        break;
      case 'multiple-attachments-piece':
        newPiece = TypedJSON.parse<MultipleAttachmentPiece>(json, MultipleAttachmentPiece);
        break;
      case 'payment-gateway-piece':
        newPiece = TypedJSON.parse<PaymentGatewayPiece>(json, PaymentGatewayPiece);
        break;
      case 'signature-pad-piece':
        newPiece = TypedJSON.parse<SignaturePadPiece>(json, SignaturePadPiece);
        break;
      case 'biometric-piece':
        newPiece = TypedJSON.parse<BiometricPiece>(json, BiometricPiece);
        break;
      case 'account-linking-piece':
        newPiece = TypedJSON.parse<AccountLinkingPiece>(json, AccountLinkingPiece);
        break;
      case 'account-unlinking-piece':
        newPiece = TypedJSON.parse<AccountUnlinkingPiece>(json, AccountUnlinkingPiece);
        break;
      case 'validate-back-dni-piece':
        newPiece = TypedJSON.parse<ValidateBackDniPiece>(json, ValidateBackDniPiece);
        break;
      case 'validate-frontal-dni-piece':
        newPiece = TypedJSON.parse<ValidateFrontalDniPiece>(json, ValidateFrontalDniPiece);
        break;
      case 'encode-base64-image':
        newPiece = TypedJSON.parse<EncodeBase64ImagePiece>(json, EncodeBase64ImagePiece);
        break;
      case 'sticker-piece':
        newPiece = TypedJSON.parse<StickerPiece>(json, StickerPiece);
        break;
      case 'send-hsm-piece':
        newPiece = TypedJSON.parse<SendHsmPiece>(json, SendHsmPiece);
        break;
      case 'comment-piece':
        newPiece = TypedJSON.parse<CommentPiece>(json, CommentPiece);
        break;
      case 'authenticate-anonymous-profile-piece':
        newPiece = TypedJSON.parse(json, AuthenticateAnonymousProfilePiece);
        break;
      case 'get-message-entities-piece':
        newPiece = TypedJSON.parse<GetMessageEntitiesPiece>(json, GetMessageEntitiesPiece);
        break;
      case 'invoke-whatsapp-flow-piece':
        newPiece = TypedJSON.parse<InvokeWhatsappFlowPiece>(json, InvokeWhatsappFlowPiece);
        break;
      case 'encrypt-piece':
        newPiece = TypedJSON.parse<EncryptPiece>(json, EncryptPiece);
        break;
      case 'decrypt-piece':
        newPiece = TypedJSON.parse<DecryptPiece>(json, DecryptPiece);
        break;
      case 'multimedia-analysis-piece':
        newPiece = TypedJSON.parse<MultimediaAnalysisPiece>(json, MultimediaAnalysisPiece);
        break;
      case 'profile-list-piece':
        newPiece = TypedJSON.parse<ProfileListPiece>(json, ProfileListPiece);
        break;
    }
    // Ultima pieza

    return newPiece;
  }

  public isPieceSupportedByType(piece: any, type: string) {
    switch (piece.type) {
      case 'attachment-piece':
      case 'calendar-piece':
      case 'close-case-piece':
      case 'data-entry-piece':
      case 'jump-to-block-piece':
      case 'message-piece':
      case 'multimedia-entry-piece':
      case 'derive-piece':
      case 'mark-message-as-pending-piece':
      case 'switch-jump-to-block-piece':
      case 'tag-piece':
      case 'variable-condition-piece':
      case 'wa-menu-piece':
      case 'sticker-piece':
      case 'rich-link-piece':
      case 'time-picker-piece':
      case 'comment-piece':
        return true;
      case 'gallery-piece':
      case 'dynamic-gallery-piece':
      case 'multiple-coordinates-piece':
      case 'update-case-piece':
      case 'update-profile-piece':
      case 'video-embed-piece':
      case 'form-piece':
      case 'store-message-piece':
      case 'post-message-piece':
      case 'actions-piece':
      case 'quick-reply-piece':
      case 'json-piece':
      case 'coordinates-piece':
      case 'integration-piece':
      case 'mail-piece':
      case 'evaluate-commands-piece':
      case 'condition-piece':
      case 'set-variable':
      case 'nearest-coordinates-piece':
      case 'call-block-as-procedure-piece':
      case 'get-element-from-array-piece':
      case 'evaluate-cognitivity-piece':
      case 'get-elements-from-array-piece':
      case 'return-to-last-block-piece':
      case 'concatenate-piece':
      case 'multiple-messages-piece':
      case 'reset-variables-piece':
      case 'shorten-url':
      case 'log-piece':
      case 'geocoder-google-piece':
      case 'multiple-attachments-piece':
      case 'statistic-event-piece':
      case 'interactive-message-list-piece':
      case 'interactive-message-productlist-piece':
      case 'interactive-message-product-piece':
      case 'interactive-message-buttons-piece':
      case 'interactive-message-urlbutton-piece':
      case 'payment-gateway-piece':
      case 'signature-pad-piece':
      case 'biometric-piece':
      case 'account-linking-piece':
      case 'account-unlinking-piece':
      case 'validate-back-dni-piece':
      case 'validate-frontal-dni-piece':
      case 'encode-base64-image':
      case 'send-hsm-piece':
      case 'smart-form-piece':
      case 'set-variable-from-entity':
      case 'authenticate-anonymous-profile-piece':
      case 'knowledge-base-piece':
      case 'get-message-entities-piece':
      case 'invoke-whatsapp-flow-piece':
      case 'encrypt-piece':
      case 'decrypt-piece':
      case 'multimedia-analysis-piece':
        if (type === FlowTypes.Lite) {
          return false;
        }
        break;
    }

    return true;
  }

  public isPieceSupportedByChannel(piece: any, channel: string): boolean {
    switch (piece.type) {
      case 'gallery-piece':
      case 'dynamic-gallery-piece':
        if (channel !== ChannelTypes.FacebookMessenger &&
          channel !== ChannelTypes.Instagram &&
          channel !== ChannelTypes.Chat &&
          channel !== ChannelTypes.Skype) {
          return false;
        }
        return true;
      case 'sticker-piece':
        if (channel !== ChannelTypes.WhatsApp &&
          channel !== ChannelTypes.Telegram) {
          return false;
        }
        return true;
      /* case 'attachment-piece':
         if (channel !== ChannelTypes.FacebookMessenger &&
           channel !== ChannelTypes.Chat &&
           channel !== ChannelTypes.WhatsApp &&
           channel !== ChannelTypes.Telegram) {
           return false;
         }
         return true;*/
      case 'multiple-coordinates-piece':
      case 'video-embed-piece':
      case 'store-message-piece':
      case 'post-message-piece':
      case 'authenticate-anonymous-profile-piece':
        return channel === ChannelTypes.Chat;
      case 'close-case-piece':
        return channel !== ChannelTypes.Chat;
      //&& channel !== ChannelTypes.Generic;
      case 'update-case-piece':
        if (channel === ChannelTypes.Chat) {
          return updateCasePieceEnabledInChat();
        }
        return true;
      case 'actions-piece':
        return channel === ChannelTypes.FacebookMessenger || channel === ChannelTypes.Instagram;
      case 'quick-reply-piece':
        return channel === ChannelTypes.FacebookMessenger ||
          channel === ChannelTypes.Instagram ||
          channel === ChannelTypes.Chat ||
          channel === ChannelTypes.Twitter;
      case 'json-piece':
        if (channel !== ChannelTypes.FacebookMessenger &&
          channel !== ChannelTypes.Instagram &&
          channel !== ChannelTypes.Twitter &&
          channel !== ChannelTypes.Skype &&
          channel !== ChannelTypes.Chat) {
          return false;
        }
        return true;
      case 'wa-menu-piece':
        return true;
      case 'coordinates-piece':
        if (channel === ChannelTypes.MercadoLibre) {
          return true;
        }
        return false;
      case 'interactive-message-list-piece':
      case 'interactive-message-buttons-piece':
        if (channel === ChannelTypes.WhatsApp ||
          channel === ChannelTypes.AppleMessaging) {
          return true;
        }
        return false;
      case 'interactive-message-urlbutton-piece':
      case 'interactive-message-productlist-piece':
      case 'interactive-message-product-piece':
      case 'invoke-whatsapp-flow-piece':
        if (channel === ChannelTypes.WhatsApp) {
          return true;
        }
        return false;
      case 'payment-gateway-piece':
      case 'signature-pad-piece':
      case 'biometric-piece':
      case 'account-linking-piece':
      case 'account-unlinking-piece':
      case 'encode-base64-image':
        if (channel === ChannelTypes.Chat) {
          return false;
        }
        return true;
      case 'form-piece':
      case 'rich-link-piece':
      case 'time-picker-piece':
      case 'apple-pay-piece':
      case 'apple-interactive-message-authentication-piece':
      case 'apple-interactive-message-imessage-app-piece':
      case 'apple-interactive-message-applepay-piece':
        if (channel !== ChannelTypes.AppleMessaging) {
          return false;
        }
        return true;
      case 'send-hsm-piece':
        if (channel === ChannelTypes.Chat) {
          return false;
        }
        return true;
      case 'message-piece':
      case 'attachment-piece':
      case 'integration-piece':
      case 'derive-piece':
      case 'mark-message-as-pending-piece':
      case 'mail-piece':
      case 'data-entry-piece':
      case 'evaluate-commands-piece':
      case 'condition-piece':
      case 'variable-condition-piece':
      case 'set-variable':
      case 'jump-to-block-piece':
      case 'multimedia-entry-piece':
      case 'nearest-coordinates-piece':
      case 'call-block-as-procedure-piece':
      case 'get-element-from-array-piece':
      case 'evaluate-cognitivity-piece':
      case 'get-elements-from-array-piece':
      case 'switch-jump-to-block-piece':
      case 'return-to-last-block-piece':
      case 'concatenate-piece':
      case 'multiple-messages-piece':
      case 'reset-variables-piece':
      case 'shorten-url':
      case 'tag-piece':
      case 'log-piece':
      case 'geocoder-google-piece':
      case 'calendar-piece':
      case 'multiple-attachments-piece':
      case 'db-query-piece':
      case 'statistic-event-piece':
      case 'validate-back-dni-piece':
      case 'validate-frontal-dni-piece':
      case 'smart-form-piece':
      case 'set-variable-from-entity':
      case 'comment-piece':
      case 'update-profile-piece':
      case 'knowledge-base-piece':
      case 'get-message-entities-piece':
      case 'encrypt-piece':
      case 'decrypt-piece':
      case 'multimedia-analysis-piece':
        return true;
    }

    return false;
  }

  public moveGroupDown(group: BlockGroupModel) {
    const groupList = this.mCurrentChatDefinition.BlockGroups;
    const index = groupList.indexOf(group, 0);
    if (index > -1 && index - 1 < groupList.length) {
      arrayMove.mut(groupList, index, index + 1);
    }
  }

  public moveGroupUp(group: BlockGroupModel) {
    const groupList = this.mCurrentChatDefinition.BlockGroups;
    const index = groupList.indexOf(group, 0);
    if (index > 0) {
      arrayMove.mut(groupList, index, index - 1);
    }
  }

  // Should we move this into its own service / factory?
  public createPiece(label: string, icon: string, definitionType: string, creator: () => BasePiece, requirePieces: string[] = null, isAdvancePiece: boolean = false): PieceType {
    var piece = new PieceType();
    piece.Id = this.mNextPieceTypeId++;
    piece.Label = label;
    piece.Icon = icon;
    piece.modelFatory = creator;
    piece.PieceDefinitionType = definitionType;
    piece.RequiredPieces = requirePieces;
    piece.Visible = !isAdvancePiece;
    piece.IsAdvancePiece = isAdvancePiece;
    return piece;
  }

  public addNewPiece(pieceDefinition: PieceType, afterPiece: BasePiece = null) {
    var newPiece = pieceDefinition.modelFatory();
    newPiece.type = pieceDefinition.PieceDefinitionType;
    newPiece.Id = this.mCurrentChatDefinition.NextPieceId++;
    newPiece.OwnerBlockId = this.mEditorState.SelectedBlock.Id;
    newPiece.Channel = this.mCurrentFlow.channel;

    if (newPiece.type === 'quick-reply-piece') {
      let quickReplyPiece = <QuickReplyPiece>newPiece;
      quickReplyPiece.Options.forEach(o => {
        o.Channel = newPiece.Channel;
      });
    }

    if (afterPiece != null) {
      let index = this.mEditorState.SelectedBlock.Pieces.indexOf(afterPiece);
      if (index > -1) {
        this.mEditorState.SelectedBlock.Pieces.splice(index + 1, 0, newPiece);
      }
      else {
        this.mEditorState.SelectedBlock.Pieces.push(newPiece);
      }
    }
    else {
      this.mEditorState.SelectedBlock.Pieces.push(newPiece);
    }
  }

  public insertNewPiece(pieceDefinition: PieceType, index: number) {
    var newPiece = pieceDefinition.modelFatory();
    newPiece.type = pieceDefinition.PieceDefinitionType;
    newPiece.Id = this.mCurrentChatDefinition.NextPieceId++;
    newPiece.OwnerBlockId = this.mEditorState.SelectedBlock.Id;
    newPiece.Channel = this.mCurrentFlow.channel;

    if (newPiece.type === 'quick-reply-piece') {
      let quickReplyPiece = <QuickReplyPiece>newPiece;
      quickReplyPiece.Options.forEach(o => {
        o.Channel = newPiece.Channel;
      });
    }
    this.mEditorState.SelectedBlock.Pieces.splice(index, 0, newPiece);
  }

  public createMesage(): BasePiece {
    var messageModel = new MessagePieceType();
    messageModel.TextList.push(new Text());
    return messageModel;
  }

  public createComment(): BasePiece {
    return new CommentPiece();
  }

  public createInvokeWhatsappFlow(): BasePiece {
    return new InvokeWhatsappFlowPiece();
  }

  public createInteractiveMesageList(): BasePiece {
    var messageModel = new InteractiveMessageListPiece();
    messageModel.TextList.push(new Text());
    return messageModel;
  }

  public createInteractiveMessageProductList(): BasePiece {
    var messageModel = new InteractiveMessageProductListPiece();
    messageModel.TextList.push(new Text());
    return messageModel;
  }

  public createInteractiveMessageProduct(): BasePiece {
    var messageModel = new InteractiveMessageProductPiece();
    messageModel.TextList.push(new Text());
    return messageModel;
  }

  public createInteractiveMessageButtons(): BasePiece {
    var messageModel = new InteractiveMessageButtonsPiece();
    messageModel.TextList.push(new Text());
    return messageModel;
  }

  public createInteractiveMessageUrlbutton(): BasePiece {
    var messageModel = new InteractiveMessageUrlButtonPiece();
    messageModel.TextList.push(new Text());
    return messageModel;
  }

  public createAppleInteractiveMessageAuthentication(): BasePiece {
    var messageModel = new AppleInteractiveMessageAuthenticationPiece();
    return messageModel;
  }

  public createAppleInteractiveMessageIMessageApp(): BasePiece {
    var messageModel = new AppleInteractiveMessageIMessageAppPiece();
    return messageModel;
  }

  public createAppleInteractiveMessageApplePay(): BasePiece {
    var messageModel = new AppleInteractiveMessageApplePayPiece();
    return messageModel;
  }

  public createWAMenu(): BasePiece {
    var WAMenuModel = new WAMenu();
    WAMenuModel.addEmptyMsg();
    return WAMenuModel;
  }

  public createGallery(): BasePiece {
    var galleryPiece = new GalleryPiece();
    galleryPiece.images.push(new GalleryImage());
    return galleryPiece;
  }

  public createPaymentGateway(): BasePiece {
    return new PaymentGatewayPiece();
  }

  public createCalendarCondition(): BasePiece {
    return new CalendarPiece();
  }

  public createDynamicGallery(): BasePiece {
    return new DynamicGalleryPiece();
  }

  public createAttachment(): BasePiece {
    return new AttachmentPiece();
  }

  public createVideoEmbed(): BasePiece {
    return new VideoEmbedPiece();
  }

  public createForm(): BasePiece {
    return new FormPiece();
  }

  public createCoordinates(): BasePiece {
    return new CoordinatesPiece();
  }

  public createMultipleCoordinates(): BasePiece {
    return new MultipleCoordinatesPiece();
  }

  public createNearestCoordinates(): BasePiece {
    return new NearestCoordinatesPiece();
  }

  public createGetElementFromArray(): BasePiece {
    return new GetElementFromArrayPiece();
  }

  public createGetElementsFromArray(): BasePiece {
    return new GetElementsFromArrayPiece();
  }

  public createLog(): BasePiece {
    return new LogPiece();
  }

  public createPostMessage(): BasePiece {
    return new PostMessagePiece();
  }

  public createStoreMessage(): BasePiece {
    return new StoreMessagePiece();
  }

  public createAuthenticateAnonymousProfilePiece(): BasePiece {
    return new AuthenticateAnonymousProfilePiece();
  }

  public createTag(): BasePiece {
    return new TagPiece();
  }

  public createQuickReply(): BasePiece {
    var quickReply = new QuickReplyPiece();
    quickReply.Options.push(quickReply.addOption());
    return quickReply;
  }

  public createAction(): BasePiece {
    return new ActionsPiece();
  }

  public createJson(): BasePiece {
    return new JsonPiece();
  }

  public createRichLink(): BasePiece {
    return new RichLinkPiece();
  }

  public createTimePicker(): BasePiece {
    return new TimePickerPiece();
  }

  public createIntegration(): BasePiece {
    return new IntegrationPiece();
  }

  public createDerivation(): BasePiece {
    return new DeriveOperatorPiece();
  }

  public createMarkMessageAsPendingPiece(): BasePiece {
    return new MarkMessageAsPendingPiece();
  }

  public createEvaluateCommands(): BasePiece {
    return new EvaluateCommandsPiece();
  }

  public createEvaluateCognitivity(): BasePiece {
    return new EvaluateCognitivityPiece();
  }

  public createSendHsm(): BasePiece {
    return new SendHsmPiece();
  }

  public createStatisticEventPiece(): BasePiece {
    return new StatisticEventPiece();
  }

  public createMail(): BasePiece {
    return new MailPiece();
  }

  public createSmartForm(): BasePiece {
    return new SmartFormPiece();
  }

  public createDataEntry(): BasePiece {
    return new DataEntry();
  }

  public createMultiMediaEntry(): BasePiece {
    return new MultiMediaEntry();
  }

  public createGeocoderGoogle(): BasePiece {
    return new GeocoderGooglePiece();
  }

  public createCondition(): BasePiece {
    return new ConditionPiece();
  }

  public createVariableCondition(): BasePiece {
    return new VariableConditionPiece();
  }

  public createValidateFrontalDni(): BasePiece {
    return new ValidateFrontalDniPiece();
  }

  public createValidateBackDni(): BasePiece {
    return new ValidateBackDniPiece();
  }

  public createEncodeBase64ImagePiece(): BasePiece {
    return new EncodeBase64ImagePiece();
  }

  public createSignaturePadPiece(): BasePiece {
    return new SignaturePadPiece();
  }

  public createBiometricPiece(): BasePiece {
    return new BiometricPiece();
  }

  public createAccountLinkingPiece(): BasePiece {
    return new AccountLinkingPiece();
  }

  public createAccountUnlinkingPiece(): BasePiece {
    return new AccountUnlinkingPiece();
  }

  public createSetVariable(): BasePiece {
    return new SetVariable();
  }

  public createSetVariableFromEntity(): BasePiece {
    return new SetVariableFromEntity();
  }

  public createKnowledgeBasePiece(): BasePiece {
    return new KnowledgeBasePiece();
  }

  public createGetMessageEntitiesPiece(): BasePiece {
    return new GetMessageEntitiesPiece();
  }

  public createShortenUrlPiece(): BasePiece {
    return new ShortenUrlPiece();
  }

  public createJumpToBlock(): BasePiece {
    return new JumpToBlockPiece();
  }

  public createSwitchJumpToBlock(): BasePiece {
    return new SwitchJumpToBlockPiece();
  }

  public createCallBlockAsProcedure(): BasePiece {
    return new CallBlockAsProcedurePiece();
  }

  public createReturnToLastBlock(): BasePiece {
    return new ReturnToLastBlockPiece();
  }

  public createConcatenate(): BasePiece {
    return new Concatenate();
  }

  public createMultipleMessage(): BasePiece {
    return new MultipleMessagePiece();
  }

  public createMultipleAttachments(): BasePiece {
    return new MultipleAttachmentPiece();
  }

  public createCloseCase(): BasePiece {
    return new CloseCasePiece();
  }

  public createUpdateProfile(): BasePiece {
    return new UpdateProfilePiece();
  }

  public createUpdateCase(): BasePiece {
    return new UpdateCasePiece();
  }

  public createResetVariables(): BasePiece {
    return new ResetVariablesPiece();
  }

  public createStickerPiece(): BasePiece {
    return new StickerPiece();
  }

  public createEncryptPiece(): BasePiece {
    return new EncryptPiece();
  }

  public createDecryptPiece(): BasePiece {
    return new DecryptPiece();
  }

  public createMultimediaAnalysisPiece(): BasePiece {
    return new MultimediaAnalysisPiece();
  }

  public createProfileListPiece(): BasePiece {
    return new ProfileListPiece()
  }

  public createIntegrationService(name?: string): Integration {
    let serviceName = this.translateService.instant('INTEGRATION_NAME_NEW');
    if (typeof (name) !== 'undefined' &&
      name !== null &&
      name.length > 0) {
      serviceName = name;
    }
    const newServiceName = ArrayUtils.getUniqueName(serviceName, this.mCurrentChatDefinition.IntegrationDefinitions, (str, instance) => { return str === instance.name; });
    const newElement = new Integration();
    newElement.name = newServiceName;
    newElement.method = 'GET';
    newElement.id = this.mCurrentChatDefinition.NextIntegrationId;
    this.mCurrentChatDefinition.NextIntegrationId++;
    this.mCurrentChatDefinition.IntegrationDefinitions.push(newElement);
    return newElement;
  }

  public createCommand(): CommandDefinition {
    let commandName = this.translateService.instant('COMMAND_NAME_NEW');
    const newCommandName = ArrayUtils.getUniqueName(commandName, this.mCurrentChatDefinition.CommandDefinitions, (str, instance) => { return str === instance.name; });
    const newElement = new CommandDefinition();
    newElement.name = newCommandName;
    newElement.rule = new ConditionGroup();
    newElement.id = this.mCurrentChatDefinition.NextCommandId;
    this.mCurrentChatDefinition.NextCommandId++;
    this.mCurrentChatDefinition.CommandDefinitions.push(newElement);
    this.onMaxCommands.emit(this.isMaxCommands());
    return newElement;
  }

  public cloneCommand(source: CommandDefinition): CommandDefinition {
    let newCommand = TypedJSON.parse(TypedJSON.stringify(source, CommandDefinition, { preserveNull: true }), CommandDefinition);
    newCommand.id = this.mCurrentChatDefinition.NextCommandId;
    this.mCurrentChatDefinition.NextCommandId++;
    newCommand.name = ArrayUtils.getUniqueName(newCommand.name, this.mCurrentChatDefinition.CommandDefinitions, (str, instance) => { return str === instance.name; });
    this.mCurrentChatDefinition.CommandDefinitions.push(newCommand);
    this.onMaxCommands.emit(this.isMaxCommands());
    return newCommand;
  }

  public createCognitivity(destinationBlockId: string, intent: Intent): CognitivityDefinition {
    const newElement = new CognitivityDefinition();
    newElement.destinationBlockId = destinationBlockId;
    newElement.intent = intent;
    return newElement;
  }
  /*
    public cloneCognitivity(source: CognitivityDefinition) : CognitivityDefinition {
      let newCognitivity = TypedJSON.parse(TypedJSON.stringify(source), CognitivityDefinition);
      newCognitivity.id = this.mCurrentChatDefinition.NextCognitivityId;
      this.mCurrentChatDefinition.NextCognitivityId++;
      newCognitivity.name = ArrayUtils.getUniqueName(newCognitivity.name, this.mCurrentChatDefinition.CognitivityDefinition, (str, instance)=> { return str=== instance.name;});
      this.mCurrentChatDefinition.CognitivityDefinition.push(newCognitivity);
      return newCognitivity;
    }*/

  public createDbQuery(): BasePiece {
    return new DbQueryPiece();
  }

  public initImplicitVarList(channel: string) {
    this.implicitVariableList = [];
    this.implicitVariableList.push(this.createVariable('messageId', TypeDefinition.Number, 'SYSTEMVARIABLE_MESSAGEID'));
    this.implicitVariableList.push(this.createVariable('text', TypeDefinition.Text, 'SYSTEMVARIABLE_TEXT'));
    if (channel !== ChannelTypes.Chat) {
      this.implicitVariableList.push(this.createVariable('date', TypeDefinition.Timestamp, 'SYSTEMVARIABLE_MESSAGEDATE'));
    }
    this.implicitVariableList.push(this.createVariable('emptyMessage', TypeDefinition.Bool, 'SYSTEMVARIABLE_EMPTYMESSAGE'));
    this.implicitVariableList.push(this.createVariable('private', TypeDefinition.Bool, 'SYSTEMVARIABLE_PRIVATE'));
    this.implicitVariableList.push(this.createVariable('hasAttach', TypeDefinition.Bool, 'SYSTEMVARIABLE_HASATTACH'));
    this.implicitVariableList.push(this.createVariable('attachments', TypeDefinition.Array, 'SYSTEMVARIABLE_ATTACHMENTS'));
    this.implicitVariableList.push(this.createVariable('attachFileName', TypeDefinition.Text, 'SYSTEMVARIABLE_ATTACHMENTFILENAME'));
    this.implicitVariableList.push(this.createVariable('attachFileSize', TypeDefinition.Decimal, 'SYSTEMVARIABLE_ATTACHMENTFILESIZE'));
    this.implicitVariableList.push(this.createVariable('attachMimeType', TypeDefinition.Text, 'SYSTEMVARIABLE_ATTACHMENTMIMETYPE'));
    this.implicitVariableList.push(this.createVariable('attachUrl', TypeDefinition.Text, 'SYSTEMVARIABLE_ATTACHMENTURL'));
    this.implicitVariableList.push(this.createVariable('attachmentsTotal', TypeDefinition.Number, 'SYSTEMVARIABLE_ATTACHMENTSTOTAL'));
    this.implicitVariableList.push(this.createVariable('hasCoordinates', TypeDefinition.Bool, 'SYSTEMVARIABLE_HASCOORDINATES'));
    this.implicitVariableList.push(this.createVariable('latitude', TypeDefinition.Decimal, 'SYSTEMVARIABLE_LATITUDE'));
    this.implicitVariableList.push(this.createVariable('longitude', TypeDefinition.Decimal, 'SYSTEMVARIABLE_LONGITUDE'));
    this.implicitVariableList.push(this.createVariable('payload', TypeDefinition.Text, 'SYSTEMVARIABLE_PAYLOAD'));
    this.implicitVariableList.push(this.createVariable('hasPayload', TypeDefinition.Bool, 'SYSTEMVARIABLE_HASPAYLOAD'));

    if (channel === ChannelTypes.Instagram) {
      this.implicitVariableList.push(this.createVariable('hasIceBreaker', TypeDefinition.Bool, 'SYSTEMVARIABLE_HASICEBKEAKER', null, ChannelTypes.Instagram));
      this.implicitVariableList.push(this.createVariable('instagramIsStoryMention', TypeDefinition.Bool, 'SYSTEMVARIABLE_INSTAGRAMISSTORYMENTION', null, ChannelTypes.Instagram));
      this.implicitVariableList.push(this.createVariable('instagramIsStoryReply', TypeDefinition.Bool, 'SYSTEMVARIABLE_INSTAGRAMISSTORYREPLY', null, ChannelTypes.Instagram));
    }
    else if (channel === ChannelTypes.FacebookMessenger) {
      this.implicitVariableList.push(this.createVariable('hasIceBreaker', TypeDefinition.Bool, 'SYSTEMVARIABLE_HASICEBKEAKER', null, ChannelTypes.FacebookMessenger));
    }
    else if (channel === ChannelTypes.Chat) {
      this.implicitVariableList.push(this.createVariable('hasIceBreaker', TypeDefinition.Bool, 'SYSTEMVARIABLE_HASICEBKEAKER', null, ChannelTypes.Chat));
    }

    // Integration
    this.implicitVariableList.push(this.createVariable('lastIntegrationCallFailed', TypeDefinition.Bool, 'SYSTEMVARIABLE_LAST_INTEGRATION_CALL_FAILED'));
    this.implicitVariableList.push(this.createVariable('lastIntegrationCallStatus', TypeDefinition.Text, 'SYSTEMVARIABLE_LAST_INTEGRATION_CALL_STATUS'));
    this.implicitVariableList.push(this.createVariable('lastIntegrationCallCode', TypeDefinition.Number, 'SYSTEMVARIABLE_LAST_INTEGRATION_CALL_CODE'));
    this.implicitVariableList.push(this.createVariable('lastIntegrationCallTimedOut', TypeDefinition.Bool, 'SYSTEMVARIABLE_LAST_INTEGRATION_CALL_TIMEOUT'));
    this.implicitVariableList.push(this.createVariable('lastIntegrationCallDisabled', TypeDefinition.Object, 'SYSTEMVARIABLE_LAST_INTEGRATION_CALL_DISABLED'));

    this.implicitVariableList.push(this.createVariable('lastIntegrationCallName', TypeDefinition.Text, 'SYSTEMVARIABLE_LAST_INTEGRATION_CALL_NAME'));
    this.implicitVariableList.push(this.createVariable('lastIntegrationCallRequest', TypeDefinition.Object, 'SYSTEMVARIABLE_LAST_INTEGRATION_CALL_REQUEST'));
    this.implicitVariableList.push(this.createVariable('lastIntegrationCallResponse', TypeDefinition.Object, 'SYSTEMVARIABLE_LAST_INTEGRATION_CALL_RESPONSE'));
    this.implicitVariableList.push(this.createVariable('lastIntegrationCallResponseHeaders', TypeDefinition.Object, 'SYSTEMVARIABLE_LAST_INTEGRATION_CALL_RESPONSE_HEADERS'));
    this.implicitVariableList.push(this.createVariable('lastIntegrationCallFailedResult', TypeDefinition.Text, 'SYSTEMVARIABLE_LAST_INTEGRATION_CALL_FAILED_RESULT'));

    // Service variables
    this.implicitVariableList.push(this.createVariable('serviceId', TypeDefinition.Number, 'SYSTEMVARIABLE_SERVICEID'));
    this.implicitVariableList.push(this.createVariable('serviceName', TypeDefinition.Text, 'SYSTEMVARIABLE_SERVICENAME'));
    this.implicitVariableList.push(this.createVariable('serviceAccountId', TypeDefinition.Text, 'SYSTEMVARIABLE_SERVICEACCOUNT'));
    this.implicitVariableList.push(this.createVariable('socialServiceTypeId', TypeDefinition.Number, 'SYSTEMVARIABLE_SOCIALSERVICETYPE'));

    // User variables
    this.implicitVariableList.push(this.createVariable('userId', TypeDefinition.Text, 'SYSTEMVARIABLE_USERID'));
    this.implicitVariableList.push(this.createVariable('alias', TypeDefinition.Text, 'SYSTEMVARIABLE_ALIAS'));
    this.implicitVariableList.push(this.createVariable('name', TypeDefinition.Text, 'SYSTEMVARIABLE_NAME'));
    this.implicitVariableList.push(this.createVariable('vip', TypeDefinition.Bool, 'SYSTEMVARIABLE_VIP'));
    this.implicitVariableList.push(this.createVariable('businessData', TypeDefinition.Text, 'SYSTEMVARIABLE_BUSINESSDATA'));
    this.implicitVariableList.push(this.createVariable('hasBusinessData', TypeDefinition.Bool, 'SYSTEMVARIABLE_HASBUSINESSDATA'));
    this.implicitVariableList.push(this.createVariable('businessDataParts', TypeDefinition.Array, 'SYSTEMVARIABLE_BUSINESSDATAPARTS'));
    this.implicitVariableList.push(this.createVariable('businessDataPartsLength', TypeDefinition.Number, 'SYSTEMVARIABLE_BUSINESSDATAPARTSLENGTH'));
    this.implicitVariableList.push(this.createVariable('businessDataParsed', TypeDefinition.Array, 'SYSTEMVARIABLE_BUSINESSDATAPARSED'));
    this.implicitVariableList.push(this.createVariable('hasBusinessDataParsed', TypeDefinition.Bool, 'SYSTEMVARIABLE_HASBUSINESSDATAPARSED'));
    this.implicitVariableList.push(this.createVariable('email', TypeDefinition.Text, 'SYSTEMVARIABLE_EMAIL'));
    this.implicitVariableList.push(this.createVariable('accountLinked', TypeDefinition.Bool, 'SYSTEMVARIABLE_ACCOUNTLINKED'));
    this.implicitVariableList.push(this.createVariable('accountLinkedToken', TypeDefinition.Text, 'SYSTEMVARIABLE_ACCOUNTLINKEDTOKEN'));

    this.implicitVariableList.push(this.createVariable('profileList', TypeDefinition.Array, 'SYSTEMVARIABLE_PROFILELIST'));
    this.implicitVariableList.push(this.createVariable('profileName', TypeDefinition.Text, 'SYSTEMVARIABLE_PROFILENAME'));
    this.implicitVariableList.push(this.createVariable('profileEmail', TypeDefinition.Text, 'SYSTEMVARIABLE_PROFILEEMAIL'));
    this.implicitVariableList.push(this.createVariable('profilePrimaryPhoneNumber', TypeDefinition.Number, 'SYSTEMVARIABLE_PROFILEPHONENUMBER'));
    if (channel !== ChannelTypes.Chat && !environment.standAlone) {
      this.implicitVariableList.push(this.createVariable('userMessagesSent', TypeDefinition.Number, 'SYSTEMVARIABLE_USERMESSAGESSENT'));
      this.implicitVariableList.push(this.createVariable('userIsFirstInteraction', TypeDefinition.Bool, 'SYSTEMVARIABLE_USERISFIRSTINTERACTION'));
      this.implicitVariableList.push(this.createVariable('userIsFirstInteractionMonthly', TypeDefinition.Bool, 'SYSTEMVARIABLE_USERISFIRSTINTERACTIONMONTHLY'));
      this.implicitVariableList.push(this.createVariable('userCasesQuantity', TypeDefinition.Number, 'SYSTEMVARIABLE_USERCASESQUANTITY'));
      this.implicitVariableList.push(this.createVariable('userCreatedDate', TypeDefinition.Timestamp, 'SYSTEMVARIABLE_USERCREATEDDATE'));
    }

    if (!environment.standAlone) {
      this.implicitVariableList.push(this.createVariable('userExtendedFields', TypeDefinition.Object, 'SYSTEMVARIABLE_USEREXTENDEDFIELDS'));
    }

    if (channel === ChannelTypes.FacebookMessenger) {
      this.implicitVariableList.push(this.createVariable('verified', TypeDefinition.Bool, 'SYSTEMVARIABLE_VERIFIED', null, ChannelTypes.FacebookMessenger));
      this.implicitVariableList.push(this.createVariable('sharedUsersPhoneNumber', TypeDefinition.Text, 'SYSTEMVARIABLE_USERSPHONENUMBER', null, ChannelTypes.FacebookMessenger));
      this.implicitVariableList.push(this.createVariable('sharedUsersEmail', TypeDefinition.Text, 'SYSTEMVARIABLE_USERSEMAIL', null, ChannelTypes.FacebookMessenger));
    }
    else if (channel === ChannelTypes.Instagram) {
      this.implicitVariableList.push(this.createVariable('verified', TypeDefinition.Bool, 'SYSTEMVARIABLE_VERIFIED', null, ChannelTypes.Instagram));
    }
    else if (channel === ChannelTypes.Twitter) {
      this.implicitVariableList.push(this.createVariable('verified', TypeDefinition.Bool, 'SYSTEMVARIABLE_VERIFIED', null, ChannelTypes.Twitter));
      this.implicitVariableList.push(this.createVariable('twitterNumberOfFollowers', TypeDefinition.Number, 'SYSTEMVARIABLE_TWITTERNUMBEROFFOLLOWERS', null, ChannelTypes.Twitter));
      this.implicitVariableList.push(this.createVariable('twitterNumberOfFriends', TypeDefinition.Number, 'SYSTEMVARIABLE_TWITTERNUMBEROFFRIENDS', null, ChannelTypes.Twitter));
      this.implicitVariableList.push(this.createVariable('twitterNumberOfStatuses', TypeDefinition.Number, 'SYSTEMVARIABLE_TWITTERNUMBEROFSTATUSES', null, ChannelTypes.Twitter));
      this.implicitVariableList.push(this.createVariable('twitterIsFollowing', TypeDefinition.Bool, 'SYSTEMVARIABLE_TWITTERISFOLLOWING', null, ChannelTypes.Twitter));
      this.implicitVariableList.push(this.createVariable('twitterIsFollowedBy', TypeDefinition.Bool, 'SYSTEMVARIABLE_TWITTERISFOLLOWEDBY', null, ChannelTypes.Twitter));
    }
    else if (channel === ChannelTypes.AppleMessaging) {
      this.implicitVariableList.push(this.createVariable('appleLocale', TypeDefinition.Text, 'SYSTEMVARIABLE_APPLE_LOCALE', null, ChannelTypes.AppleMessaging));
      this.implicitVariableList.push(this.createVariable('appleCapabilityList', TypeDefinition.Array, 'SYSTEMVARIABLE_APPLE_CAPABILITIES', null, ChannelTypes.AppleMessaging));
      this.implicitVariableList.push(this.createVariable('appleSupportsAuth', TypeDefinition.Bool, 'SYSTEMVARIABLE_APPLE_SUPPORTS_AUTH', null, ChannelTypes.AppleMessaging));
      this.implicitVariableList.push(this.createVariable('appleSupportsList', TypeDefinition.Bool, 'SYSTEMVARIABLE_APPLE_SUPPORTS_LIST', null, ChannelTypes.AppleMessaging));
      this.implicitVariableList.push(this.createVariable('appleSupportsTime', TypeDefinition.Bool, 'SYSTEMVARIABLE_APPLE_SUPPORTS_TIME', null, ChannelTypes.AppleMessaging));
      this.implicitVariableList.push(this.createVariable('appleSupportsForm', TypeDefinition.Bool, 'SYSTEMVARIABLE_APPLE_SUPPORTS_FORM', null, ChannelTypes.AppleMessaging));
      this.implicitVariableList.push(this.createVariable('appleSupportsQuick', TypeDefinition.Bool, 'SYSTEMVARIABLE_APPLE_SUPPORTS_QUICK', null, ChannelTypes.AppleMessaging));
      this.implicitVariableList.push(this.createVariable('appleIntent', TypeDefinition.Text, 'SYSTEMVARIABLE_APPLE_INTENT', null, ChannelTypes.AppleMessaging));
      this.implicitVariableList.push(this.createVariable('appleGroup', TypeDefinition.Text, 'SYSTEMVARIABLE_APPLE_GROUP', null, ChannelTypes.AppleMessaging));

      this.implicitVariableList.push(this.createVariable('appleAuthenticateSuccess', TypeDefinition.Bool, 'SYSTEMVARIABLE_APPLE_AUTHENTICATE_SUCCESS', null, ChannelTypes.AppleMessaging));
      this.implicitVariableList.push(this.createVariable('appleAuthenticateToken', TypeDefinition.Text, 'SYSTEMVARIABLE_APPLE_AUTHENTICATE_TOKEN', null, ChannelTypes.AppleMessaging));
      this.implicitVariableList.push(this.createVariable('appleAuthenticateValidToken', TypeDefinition.Bool, 'SYSTEMVARIABLE_APPLE_AUTHENTICATE_VALIDTOKEN', null, ChannelTypes.AppleMessaging));
      this.implicitVariableList.push(this.createVariable('appleAuthenticateErrorCode', TypeDefinition.Number, 'SYSTEMVARIABLE_APPLE_AUTHENTICATE_ERRORCODE', null, ChannelTypes.AppleMessaging));
      this.implicitVariableList.push(this.createVariable('appleAuthenticateErrorMessage', TypeDefinition.Text, 'SYSTEMVARIABLE_APPLE_AUTHENTICATE_ERRORMESSAGE', null, ChannelTypes.AppleMessaging));
      this.implicitVariableList.push(this.createVariable('appleAuthenticateErrorDomain', TypeDefinition.Text, 'SYSTEMVARIABLE_APPLE_AUTHENTICATE_ERRORDOMAIN', null, ChannelTypes.AppleMessaging));
      this.implicitVariableList.push(this.createVariable('appleIsProcessingAuthenticationResult', TypeDefinition.Bool, 'SYSTEMVARIABLE_APPLE_AUTHENTICATE_PROCESSINGRESULT', null, ChannelTypes.AppleMessaging));

      this.implicitVariableList.push(this.createVariable('appleSourceRequestIdentifier', TypeDefinition.Text, 'SYSTEMVARIABLE_APPLE_SOURCEREQUESTIDENTIFIER', null, ChannelTypes.AppleMessaging));

      this.implicitVariableList.push(this.createVariable('applePaymentSuccess', TypeDefinition.Bool, 'SYSTEMVARIABLE_APPLE_PAYMENT_SUCCESS', null, ChannelTypes.AppleMessaging));
      this.implicitVariableList.push(this.createVariable('applePaymentState', TypeDefinition.Text, 'SYSTEMVARIABLE_APPLE_PAYMENT_STATE', null, ChannelTypes.AppleMessaging));
      this.implicitVariableList.push(this.createVariable('applePaymentMerchantSessionIdentifier', TypeDefinition.Text, 'SYSTEMVARIABLE_APPLE_PAYMENT_MERCHANTSESSIONIDENTIFIER', null, ChannelTypes.AppleMessaging));
    }

    //TODO: Limitar el canal a Whatsapp
    this.implicitVariableList.push(this.createVariable('menuInput', TypeDefinition.Text, 'SYSTEM_VARIABLE_WAMENU_INPUT'));

    //control.variables.parameters = body.message.user.parameters;
    //control.variables.parametersByService = body.message.user.parametersByService;

    // Case parameters
    this.implicitVariableList.push(this.createVariable('caseId', TypeDefinition.Number, 'SYSTEMVARIABLE_CASEID'));
    this.implicitVariableList.push(this.createVariable('caseIsNew', TypeDefinition.Bool, 'SYSTEMVARIABLE_CASEISNEW'));
    this.implicitVariableList.push(this.createVariable('casePopupUrl', TypeDefinition.Text, 'SYSTEMVARIABLE_CASEPOPUPURL'));
    if (channel !== ChannelTypes.Chat) {
      this.implicitVariableList.push(this.createVariable('profileId', TypeDefinition.Number, 'SYSTEMVARIABLE_PROFILEID'));
      this.implicitVariableList.push(this.createVariable('caseHasTags', TypeDefinition.Bool, 'SYSTEMVARIABLE_CASEHASTAGS'));
      this.implicitVariableList.push(this.createVariable('caseTags', TypeDefinition.Array, 'SYSTEMVARIABLE_CASETAGS'));
      this.implicitVariableList.push(this.createVariable('caseHasPrevious', TypeDefinition.Bool, 'SYSTEMVARIABLE_CASEHASPREVIOUS'));
      this.implicitVariableList.push(this.createVariable('casePreviousId', TypeDefinition.Number, 'SYSTEMVARIABLE_CASEPREVIOUSID'));
      this.implicitVariableList.push(this.createVariable('casePreviousHasTags', TypeDefinition.Bool, 'SYSTEMVARIABLE_CASEPREVIOUSHASTAGS'));
      this.implicitVariableList.push(this.createVariable('casePreviousTags', TypeDefinition.Array, 'SYSTEMVARIABLE_CASEPREVIOUSTAGS'));
      this.implicitVariableList.push(this.createVariable('casePreviousClosedDate', TypeDefinition.Timestamp, 'SYSTEMVARIABLE_CASEPREVIOUSCLOSEDDATE'));
      this.implicitVariableList.push(this.createVariable('casePreviousClosedSince', TypeDefinition.Decimal, 'SYSTEMVARIABLE_CASEPREVIOUSCLOSEDSINCE'));
      this.implicitVariableList.push(this.createVariable('casePreviousClosedBy', TypeDefinition.Number, 'SYSTEMVARIABLE_CASEPREVIOUSCLOSEDBY'));
      this.implicitVariableList.push(this.createVariable('isFirstIncomingMessage', TypeDefinition.Bool, 'SYSTEMVARIABLE_ISFIRSTINCOMINGMESSAGE'));
      this.implicitVariableList.push(this.createVariable('caseContainsOutgoingMessage', TypeDefinition.Bool, 'SYSTEMVARIABLE_CASECONTAINSOUTGOINGMESSAGE'));
      this.implicitVariableList.push(this.createVariable('caseLastOutgoingMessageId', TypeDefinition.Number, 'SYSTEMVARIABLE_LASTOUTGOINGMESSAGEID'));
      this.implicitVariableList.push(this.createVariable('caseLastOutgoingMessageSocialServiceTypeId', TypeDefinition.Number, 'SYSTEMVARIABLE_LASTOUTGOINGMESSAGESOCIALSERVICETYPEID'));
      this.implicitVariableList.push(this.createVariable('paymentGatewayToken', TypeDefinition.Text, 'SYSTEMVARIABLE_PAYMENTGATEWATTOKEN'));

      if (!environment.standAlone) {
        this.implicitVariableList.push(this.createVariable('caseExtendedFields', TypeDefinition.Object, 'SYSTEMVARIABLE_CASEEXTENDEDFIELDS'));
      }

      if (channel === ChannelTypes.WhatsApp) {
        this.implicitVariableList.push(this.createVariable('caseLastOutgoingMessageIsHSM', TypeDefinition.Bool, 'SYSTEMVARIABLE_LASTOUTGOINGMESSAGEISHSM', null, ChannelTypes.WhatsApp));
        this.implicitVariableList.push(this.createVariable('caseLastOutgoingMessageHSMNamespace', TypeDefinition.Text, 'SYSTEMVARIABLE_LASTOUTGOINGMESSAGEHSMNAMESPACE', null, ChannelTypes.WhatsApp));
        this.implicitVariableList.push(this.createVariable('caseLastOutgoingMessageHSMElementName', TypeDefinition.Text, 'SYSTEMVARIABLE_LASTOUTGOINGMESSAGEHSMELEMENTNAME', null, ChannelTypes.WhatsApp));
        this.implicitVariableList.push(this.createVariable('whatsappRepliesTemplate', TypeDefinition.Bool, 'SYSTEMVARIABLE_WHATSAPPREPLIESTEMPLATE', null, ChannelTypes.WhatsApp));
        this.implicitVariableList.push(this.createVariable('whatsappRepliesTemplateName', TypeDefinition.Text, 'SYSTEMVARIABLE_WHATSAPPREPLIESTEMPLATENAME', null, ChannelTypes.WhatsApp));
        this.implicitVariableList.push(this.createVariable('whatsappRepliesFromHSMWithoutCase', TypeDefinition.Bool, 'SYSTEMVARIABLE_WHATSAPPREPLIESFROMHSMWITHOUTCASE', null, ChannelTypes.WhatsApp));
        this.implicitVariableList.push(this.createVariable('whatsappRepliesTemplateLanguage', TypeDefinition.Text, 'SYSTEMVARIABLE_WHATSAPPREPLIESTEMPLATELANGUAGE', null, ChannelTypes.WhatsApp));
        this.implicitVariableList.push(this.createVariable('whatsappRelatedCampaign', TypeDefinition.Text, 'SYSTEMVARIABLE_WHATSAPPRELATEDCAMPAIGN', null, ChannelTypes.WhatsApp));

        this.implicitVariableList.push(this.createVariable('whatsappMessageIsOrder', TypeDefinition.Bool, 'SYSTEMVARIABLE_WHATSAPPMESSAGEISORDER', null, ChannelTypes.WhatsApp));
        this.implicitVariableList.push(this.createVariable('whatsappMessageOrderCatalogId', TypeDefinition.Text, 'SYSTEMVARIABLE_WHATSAPPMESSAGEORDERCATALOGID', null, ChannelTypes.WhatsApp));
        this.implicitVariableList.push(this.createVariable('whatsappMessageOrderTotalItems', TypeDefinition.Number, 'SYSTEMVARIABLE_WHATSAPPMESSAGEORDERTOTALITEMS', null, ChannelTypes.WhatsApp));
        this.implicitVariableList.push(this.createVariable('whatsappMessageOrderItems', TypeDefinition.Array, 'SYSTEMVARIABLE_WHATSAPPMESSAGEORDERITEMS', null, ChannelTypes.WhatsApp));
        this.implicitVariableList.push(this.createVariable('whatsappMessageIsReferrerProduct', TypeDefinition.Bool, 'SYSTEMVARIABLE_WHATSAPPMESSAGEISREFERRERPRODUCT', null, ChannelTypes.WhatsApp));
        this.implicitVariableList.push(this.createVariable('whatsappMessageReferrerProductCatalogId', TypeDefinition.Text, 'SYSTEMVARIABLE_WHATSAPPMESSAGEREFERRERPRODUCTCATALOGID', null, ChannelTypes.WhatsApp));
        this.implicitVariableList.push(this.createVariable('whatsappMessageReferrerProductRetailerId', TypeDefinition.Text, 'SYSTEMVARIABLE_WHATSAPPMESSAGEREFERRERPRODUCTRETAILERID', null, ChannelTypes.WhatsApp));
        this.implicitVariableList.push(this.createVariable('whatsappMessageHasReferral', TypeDefinition.Bool, 'SYSTEMVARIABLE_WHATSAPPMESSAGEHASREFERRAL', null, ChannelTypes.WhatsApp));
        this.implicitVariableList.push(this.createVariable('whatsappMessageReferralSourceId', TypeDefinition.Text, 'SYSTEMVARIABLE_WHATSAPPMESSAGEREFERRALSOURCEID', null, ChannelTypes.WhatsApp));
        this.implicitVariableList.push(this.createVariable('whatsappMessageReferralSourceUrl', TypeDefinition.Text, 'SYSTEMVARIABLE_WHATSAPPMESSAGEREFERRALSOURCEURL', null, ChannelTypes.WhatsApp));
        this.implicitVariableList.push(this.createVariable('whatsappMessageReferralSourceType', TypeDefinition.Text, 'SYSTEMVARIABLE_WHATSAPPMESSAGEREFERRALSOURCETYPE', null, ChannelTypes.WhatsApp));

        this.implicitVariableList.push(this.createVariable('whatsappVoiceCallsEnabled', TypeDefinition.Bool, 'SYSTEMVARIABLE_WHATSAPPVOICECALLENABLED', null, ChannelTypes.WhatsApp));
        this.implicitVariableList.push(this.createVariable('whatsappVoiceCallsConnectedAgents', TypeDefinition.Number, 'SYSTEMVARIABLE_WHATSAPPVOICECALLCONNECTEDAGENTS', null, ChannelTypes.WhatsApp));
      }
    }

    // Date parameters
    this.implicitVariableList.push(this.createVariable('now', TypeDefinition.Timestamp, 'SYSTEMVARIABLE_NOW'));
    this.implicitVariableList.push(this.createVariable('gmt', TypeDefinition.Text, 'SYSTEMVARIABLE_GMT'));
    this.implicitVariableList.push(this.createVariable('nowDate', TypeDefinition.Text, 'SYSTEMVARIABLE_NOWDATE'));
    this.implicitVariableList.push(this.createVariable('hour', TypeDefinition.Number, 'SYSTEMVARIABLE_HOUR'));
    this.implicitVariableList.push(this.createVariable('minutes', TypeDefinition.Number, 'SYSTEMVARIABLE_MINUTES'));
    this.implicitVariableList.push(this.createVariable('seconds', TypeDefinition.Number, 'SYSTEMVARIABLE_SECONDS'));
    this.implicitVariableList.push(this.createVariable('milliseconds', TypeDefinition.Number, 'SYSTEMVARIABLE_MILLISECONDS'));
    this.implicitVariableList.push(this.createVariable('day', TypeDefinition.Number, 'SYSTEMVARIABLE_DAY'));
    this.implicitVariableList.push(this.createVariable('month', TypeDefinition.Number, 'SYSTEMVARIABLE_MONTH'));
    this.implicitVariableList.push(this.createVariable('year', TypeDefinition.Number, 'SYSTEMVARIABLE_YEAR'));
    this.implicitVariableList.push(this.createVariable('dayOfWeek', TypeDefinition.Number, 'SYSTEMVARIABLE_DAYOFWEEK'));
    this.implicitVariableList.push(this.createVariable('isWeekDay', TypeDefinition.Bool, 'SYSTEMVARIABLE_ISWEEKDAY'));
    this.implicitVariableList.push(this.createVariable('isWeekend', TypeDefinition.Bool, 'SYSTEMVARIABLE_ISWEEKEND'));
    this.implicitVariableList.push(this.createVariable('isNonWorkingTime', TypeDefinition.Bool, 'SYSTEMVARIABLE_ISNONWORKINGTIME'));
    this.implicitVariableList.push(this.createVariable('isNonWorkingTimeException', TypeDefinition.Bool, 'SYSTEMVARIABLE_ISNONWORKINGTIMEEXCEPTION'));
    this.implicitVariableList.push(this.createVariable('isNonWorkingTimeHoliday', TypeDefinition.Bool, 'SYSTEMVARIABLE_ISNONWORKINGTIMEHOLIDAY'));
    this.implicitVariableList.push(this.createVariable('isWorkingTime', TypeDefinition.Bool, 'SYSTEMVARIABLE_ISWORKINGTIME'));

    // flow variables
    this.implicitVariableList.push(this.createVariable('flowId', TypeDefinition.Number, 'SYSTEMVARIABLE_FLOWID'));
    this.implicitVariableList.push(this.createVariable('blockId', TypeDefinition.Text, 'SYSTEMVARIABLE_BLOCKID'));
    this.implicitVariableList.push(this.createVariable('blockName', TypeDefinition.Text, 'SYSTEMVARIABLE_BLOCKNAME'));
    this.implicitVariableList.push(this.createVariable('lastCommandId', TypeDefinition.Number, 'SYSTEMVARIABLE_LASTCOMMANDID'));
    this.implicitVariableList.push(this.createVariable('lastCommandName', TypeDefinition.Text, 'SYSTEMVARIABLE_LASTCOMMANDNAME'));
    //this.implicitVariableList.push(this.createVariable('lastCognitivityId', TypeDefinition.Number, 'SYSTEMVARIABLE_LASTCOGNITIVITYID'));
    //this.implicitVariableList.push(this.createVariable('lastCognitivityName', TypeDefinition.Text, 'SYSTEMVARIABLE_LASTCOGNITIVITYNAME'));
    this.implicitVariableList.push(this.createVariable('lastIntent', TypeDefinition.Object, 'SYSTEMVARIABLE_LASTINTENT'));
    this.implicitVariableList.push(this.createVariable('lastEntities', TypeDefinition.Array, 'SYSTEMVARIABLE_LASTENTITIES'));
    this.implicitVariableList.push(this.createVariable('lastEntity', TypeDefinition.Array, 'SYSTEMVARIABLE_LASTENTITY'));
    this.implicitVariableList.push(this.createVariable('smartAnswer', TypeDefinition.Text, 'SYSTEMVARIABLE_SMARTANSWER'));
    this.implicitVariableList.push(this.createVariable('sentimentType', TypeDefinition.Number, 'SYSTEMVARIABLE_SENTIMENTTYPE'));
    this.implicitVariableList.push(this.createVariable('emotionType', TypeDefinition.Number, 'SYSTEMVARIABLE_EMOTIONTYPE'));

    if (!environment.standAlone) {
      this.implicitVariableList.push(this.createVariable('connectedAgents', TypeDefinition.Number, 'SYSTEMVARIABLE_CONNECTEDAGENTES'));
      this.implicitVariableList.push(this.createVariable('areAgentsConnected', TypeDefinition.Bool, 'SYSTEMVARIABLE_AREAGENTSCONNECTED'));
      this.implicitVariableList.push(this.createVariable('totalEnqueuedMessages', TypeDefinition.Number, 'SYSTEMVARIABLE_TOTALENQUEUEDMESSAGES'));
      this.implicitVariableList.push(this.createVariable('areMessagesEnqueued', TypeDefinition.Bool, 'SYSTEMVARIABLE_AREMESSAGESENQUEUED'));
      this.implicitVariableList.push(this.createVariable('sharedQueuesEnqueuedMessages', TypeDefinition.Number, 'SYSTEMVARIABLE_SHAREDQUEUESENQUEUEDMESSAGES'));
      this.implicitVariableList.push(this.createVariable('areMessagesEnqueuedInSharedQueues', TypeDefinition.Bool, 'SYSTEMVARIABLE_AREMESSAGESENQUEUEDINSHAREDQUEUES'));
      this.implicitVariableList.push(this.createVariable('hasOpenCasesInOtherServices', TypeDefinition.Bool, 'SYSTEMVARIABLE_HASOPENCASESINOTHERSERVICES'));
      this.implicitVariableList.push(this.createVariable('derivationEnabled', TypeDefinition.Bool, 'SYSTEMVARIABLE_DERIVATIONENABLED'));
      this.implicitVariableList.push(this.createVariable('sharedQueuesConnectedAgents', TypeDefinition.Number, 'SYSTEMVARIABLE_SHAREDQUEUESCONNECTEDAGENTS'));
      this.implicitVariableList.push(this.createVariable('areAgentsConnectedInSharedQueues', TypeDefinition.Bool, 'SYSTEMVARIABLE_AREAGENTSCONNECTEDINSHAREDQUEUES'));

      this.implicitVariableList.push(this.createVariable('isYSocialNonWorkingTime', TypeDefinition.Bool, 'SYSTEMVARIABLE_ISYSOCIALNONWORKINGTIME'));
      this.implicitVariableList.push(this.createVariable('isYSocialWorkingTime', TypeDefinition.Bool, 'SYSTEMVARIABLE_ISYSOCIALWORKINGTIME'));
      this.implicitVariableList.push(this.createVariable('queueConnectionAndMessagesData', TypeDefinition.Array, 'SYSTEMVARIABLE_QUEUECONNECTIONANDMESSAGEDATA'));
    }

    this.implicitVariableList.push(this.createVariable('requestMethod', TypeDefinition.Text, 'SYSTEMVARIABLE_REQUESTMETHOD'));
    this.implicitVariableList.push(this.createVariable('requestUrl', TypeDefinition.Text, 'SYSTEMVARIABLE_REQUESTURL'));
    this.implicitVariableList.push(this.createVariable('requestBody', TypeDefinition.Text, 'SYSTEMVARIABLE_REQUESTBODY'));
    this.implicitVariableList.push(this.createVariable('requestHeaders', TypeDefinition.Object, 'SYSTEMVARIABLE_REQUESTHEADERS'));
    this.implicitVariableList.push(this.createVariable('errorMessage', TypeDefinition.Text, 'SYSTEMVARIABLE_ERRORMESSAGE'));

    if (channel === ChannelTypes.Chat) {
      this.implicitVariableList.push(this.createVariable('browserName', TypeDefinition.Text, 'SYSTEMVARIABLE_BROWSERNAME', null, ChannelTypes.Chat));
      this.implicitVariableList.push(this.createVariable('browserVersion', TypeDefinition.Text, 'SYSTEMVARIABLE_BROWSERVERSION', null, ChannelTypes.Chat));
      this.implicitVariableList.push(this.createVariable('browserUserAgent', TypeDefinition.Text, 'SYSTEMVARIABLE_BROWSERUSERAGENT', null, ChannelTypes.Chat));
      this.implicitVariableList.push(this.createVariable('queryStringParameters', TypeDefinition.Object, 'SYSTEMVARIABLE_QUERYSTRINGPARAMETERS', null, ChannelTypes.Chat));
    }

    this.implicitVariableList.push(this.createVariable('userDni', TypeDefinition.Object, 'SYSTEMVARIABLE_USERDNI'));
    this.implicitVariableList.push(this.createVariable('signatureLink', TypeDefinition.Text, 'SYSTEMVARIABLE_SIGNATURELINK'));
    this.implicitVariableList.push(this.createVariable('accountLinkingLink', TypeDefinition.Text, 'SYSTEMVARIABLE_ACCOUNTLINKINGLINK'));
    this.implicitVariableList.push(this.createVariable('accountUnlinkingLink', TypeDefinition.Text, 'SYSTEMVARIABLE_ACCOUNTUNLINKINGLINK'));

    /*this.implicitVariableList.push(this.createVariable('biometricLink', TypeDefinition.Text, 'SYSTEMVARIABLE_BIOMETRICLINK'));
    this.implicitVariableList.push(this.createVariable('biometricIdentificationId', TypeDefinition.Text, 'SYSTEMVARIABLE_BIOMETRICIDENTIFICATIONID'));
    this.implicitVariableList.push(this.createVariable('biometricVerificationId', TypeDefinition.Text, 'SYSTEMVARIABLE_BIOMETRICVERIFICATIONID'));
    this.implicitVariableList.push(this.createVariable('biometricIdentityStatus', TypeDefinition.Number, 'SYSTEMVARIABLE_BIOMETRICIDENTITYSTATUS'));
    this.implicitVariableList.push(this.createVariable('biometricHasOpenProcess', TypeDefinition.Number, 'SYSTEMVARIABLE_BIOMETRICHASOPENPROCESS'));
    this.implicitVariableList.push(this.createVariable('lastBiometric', TypeDefinition.Object, 'SYSTEMVARIABLE_LASTBIOMETRIC'));
    this.implicitVariableList.push(this.createVariable('lastBiometricVerificationId', TypeDefinition.Text, 'SYSTEMVARIABLE_BIOMETRICVERIFICATIONID'));
    this.implicitVariableList.push(this.createVariable('lastBiometricIdentificationId', TypeDefinition.Text, 'SYSTEMVARIABLE_BIOMETRICIDENTIFICATIONID'));
    this.implicitVariableList.push(this.createVariable('lastBiometricIdentityStatus', TypeDefinition.Number, 'SYSTEMVARIABLE_BIOMETRICIDENTITYSTATUS'));*/
  }

  public findBlocksReferencingVariable(varDef: VariableDefinition): string[] {
    var referencingBlocks: string[] = [];
    var allblocks: BlockDefinition[] = this.mCurrentChatDefinition.BlockList.concat(this.mCurrentChatDefinition.DefaultBlocks);
    allblocks.forEach(block => {
      if (block.Pieces !== null &&
        block.Pieces.length > 0) {
        for (let i = 0; i < block.Pieces.length; i++) {
          let piece = block.Pieces[i];
          if (piece.referencesVariable(varDef, this)) {
            referencingBlocks.push(block.Id);
            break;
          }
        }
      }
    });

    return referencingBlocks;
  }

  public findBlocksReferencingBlock(blockId: string): string[] {
    var referencingBlocks: string[] = [];
    var allblocks: BlockDefinition[] = this.mCurrentChatDefinition.BlockList.concat(this.mCurrentChatDefinition.DefaultBlocks);
    allblocks.forEach(block => {
      if (block.Id != blockId) {
        let blockReferences = this.getBlocksReferencedByBlock(block);
        if (blockReferences.find(value => value == blockId) != null) {
          if (referencingBlocks.indexOf(block.Id) === -1) {
            referencingBlocks.push(block.Id);
          }
        }
      }
    });
    return referencingBlocks;
  }

  public findBlocksReferencingIntegration(integrationId: number): string[] {
    var referencingBlocks: string[] = [];
    var allblocks: BlockDefinition[] = this.mCurrentChatDefinition.BlockList.concat(this.mCurrentChatDefinition.DefaultBlocks);
    allblocks.forEach(block => {
      block.Pieces.forEach(piece => {
        if (piece.type === 'integration-piece') {
          let integrationPiece = <IntegrationPiece>piece;
          if (integrationPiece.integrationId === integrationId) {
            if (referencingBlocks.indexOf(block.Id) === -1) {
              referencingBlocks.push(block.Id);
            }
          }
        }
      });
    });
    return referencingBlocks;
  }

  public isPersistentMenuReferencingBlock(blockId: string): boolean {
    if (this.mCurrentChatDefinition.PersistentMenuEntries !== null) {
      for (let i = 0; i < this.mCurrentChatDefinition.PersistentMenuEntries.length; i++) {
        let m = this.mCurrentChatDefinition.PersistentMenuEntries[i];
        if (m.buttons !== null) {
          for (let j = 0; j < m.buttons.length; j++) {
            let button = m.buttons[j];
            if (button.Type === ButtonType.Redirect && button.BlockID === blockId) {
              return true;
            }
          }
        }
      }
    }

    return false;
  }

  public isIceBreakersReferencingBlock(blockId: string): boolean {
    if (this.mCurrentChatDefinition.IceBreakers !== null) {
      for (let i = 0; i < this.mCurrentChatDefinition.IceBreakers.buttons.length; i++) {
        let button = this.mCurrentChatDefinition.IceBreakers.buttons[i];
        if (button.Type === ButtonType.Redirect && button.BlockID === blockId) {
          return true;
        }
      }
    }

    return false;
  }

  public isYSocialSettingsReferencingBlock(blockId: string): boolean {
    if (environment.standAlone) {
      return false;
    }

    if (this.mCurrentChatDefinition.YSocialSettings !== null &&
      this.mCurrentChatDefinition.YSocialSettings.ReturnsFromAgent !== null &&
      this.mCurrentChatDefinition.YSocialSettings.ReturnsFromAgent.length > 0) {
      for (let i = 0; i < this.mCurrentChatDefinition.YSocialSettings.ReturnsFromAgent.length; i++) {
        let m = this.mCurrentChatDefinition.YSocialSettings.ReturnsFromAgent[i];
        if (m.BlockId === blockId) {
          return true;
        }
      }
    }

    return false;
  }

  public findCommandsReferencingBlock(blockId: string): CommandDefinition[] {
    var referencingCommands: CommandDefinition[] = [];
    this.mCurrentChatDefinition.CommandDefinitions.forEach(command => {
      if (command.destinationBlockId === blockId) {
        referencingCommands.push(command);
      }
    });
    return referencingCommands;
  }

  //Hay que revisar
  public ExistsCognitivitiesReferencingBlock(blockId: string): boolean {
    return this.mCurrentChatDefinition.CognitivityDefinitions.some(cognitivity => cognitivity.destinationBlockId === blockId);
  }

  /*public findCognitivitiesReferencingBlock(blockId: number) : CognitivityDefinition {
    var referencingCognitivities : CognitivityDefinition = null;
    this.mCurrentChatDefinition.CognitivityDefinition.forEach(cognitivity => {
      if (cognitivity.destinationBlockId === blockId) {
        referencingCognitivities.push(cognitivity);
      }
    });
    return referencingCognitivities;
  }*/

  public getBlocksReferencedByBlock(block: BlockDefinition): string[] {
    if (block == null) {
      return [];
    }

    var blocks: string[] = [];
    block.Pieces.forEach(piece => {
      switch (piece.type) {
        case 'message-piece': {
          let msgPiece = piece as MessagePieceType;
          msgPiece.Buttons.forEach(btn => {
            if (btn.Type === ButtonType.Redirect &&
              btn.BlockID != null && btn.BlockID !== "-1") {
              if (blocks.indexOf(btn.BlockID) === -1) {
                blocks.push(btn.BlockID);
              }
            }
          });
        } break;

        case 'interactive-message-buttons-piece': {
          let msgPiece = piece as InteractiveMessageButtonsPiece;
          msgPiece.Buttons.forEach(btn => {
            if (btn.Type === ButtonType.Redirect &&
              btn.BlockID != null && btn.BlockID !== "-1") {
              if (blocks.indexOf(btn.BlockID) === -1) {
                blocks.push(btn.BlockID);
              }
            }
          });
        } break;

        case 'interactive-message-list-piece': {
          let msgPiece = piece as InteractiveMessageListPiece;
          msgPiece.Sections.forEach(section => {
            section.Rows.forEach(row => {
              if (row.BlockID !== null && row.BlockID !== "-1") {
                if (blocks.indexOf(row.BlockID) === -1) {
                  blocks.push(row.BlockID);
                }
              }
            })

          });
        } break;

        case 'gallery-piece': {
          let galleryPiece = piece as GalleryPiece;
          galleryPiece.images.forEach(image => {
            image.buttons.forEach(btn => {
              if (btn.Type === ButtonType.Redirect &&
                btn.BlockID != null && btn.BlockID !== "-1") {
                if (blocks.indexOf(btn.BlockID) === -1) {
                  blocks.push(btn.BlockID);
                }
              }
            })
          });
        } break;

        case 'dynamic-gallery-piece': {
          let galleryPiece = piece as DynamicGalleryPiece;
          galleryPiece.image.buttons.forEach(btn => {
            if (btn.Type === ButtonType.Redirect &&
              btn.BlockID != null && btn.BlockID != "-1") {
              if (blocks.indexOf(btn.BlockID) === -1) {
                blocks.push(btn.BlockID);
              }
            }
          });
        } break;

        case 'attachment-piece':
          break;

        case 'quick-reply-piece': {
          let quickReplyPiece = piece as QuickReplyPiece;
          quickReplyPiece.Options.forEach(opt => {
            if (opt.BlockId != null && opt.BlockId !== "-1") {
              if (blocks.indexOf(opt.BlockId) === -1) {
                blocks.push(opt.BlockId);
              }
            }
          });
        } break;

        case 'actions-piece': {
          let actionsPiece = piece as ActionsPiece;
          if (actionsPiece.BlockID != null && actionsPiece.BlockID !== "-1") {
            if (blocks.indexOf(actionsPiece.BlockID) === -1) {
              blocks.push(actionsPiece.BlockID);
            }
          }
        } break;

        case 'json-piece':
          break;

        case 'integration-piece': {
          let integrationPiece = piece as IntegrationPiece;
          if (integrationPiece.errorBlockId != null && integrationPiece.errorBlockId !== "-1") {
            if (blocks.indexOf(integrationPiece.errorBlockId) === -1) {
              blocks.push(integrationPiece.errorBlockId);
            }
          }
        } break;

        case 'derive-piece':
          break;

        case 'mark-message-as-pending-piece':
          break;

        case 'mail-piece':
          break;

        case 'data-entry-piece': {
          let dataEntryPiece = piece as DataEntry;
          if (dataEntryPiece.ErrorBlockId != null && dataEntryPiece.ErrorBlockId !== "-1") {
            if (blocks.indexOf(dataEntryPiece.ErrorBlockId) === -1) {
              blocks.push(dataEntryPiece.ErrorBlockId);
            }
          }

          if (dataEntryPiece.PendingReplyFromCustomerBlockId !== null && dataEntryPiece.PendingReplyFromCustomerBlockId !== "-1") {
            if (blocks.indexOf(dataEntryPiece.PendingReplyFromCustomerBlockId) === -1) {
              blocks.push(dataEntryPiece.PendingReplyFromCustomerBlockId);
            }
          }
        } break;

        case 'smart-form-piece': {
          let smartFormPiece = piece as SmartFormPiece;
          if (smartFormPiece.ErrorBlockId != null && smartFormPiece.ErrorBlockId !== "-1") {
            if (blocks.indexOf(smartFormPiece.ErrorBlockId) === -1) {
              blocks.push(smartFormPiece.ErrorBlockId);
            }
          }
        } break;

        case 'knowledge-base-piece':
          break;

        case 'close-case-piece': {
          let closeCasePiece = piece as CloseCasePiece;
          if (closeCasePiece.pendingReplyFromCustomerBlockId !== null && closeCasePiece.pendingReplyFromCustomerBlockId !== "-1") {
            if (blocks.indexOf(closeCasePiece.pendingReplyFromCustomerBlockId) === -1) {
              blocks.push(closeCasePiece.pendingReplyFromCustomerBlockId);
            }
          }
        } break;

        case 'multimedia-entry-piece': {
          let dataEntryPiece = piece as MultiMediaEntry;
          if (dataEntryPiece.ErrorBlockId != null && dataEntryPiece.ErrorBlockId !== "-1") {
            if (blocks.indexOf(dataEntryPiece.ErrorBlockId) === -1) {
              blocks.push(dataEntryPiece.ErrorBlockId);
            }
          }
        } break;

        case 'condition-piece': {
          let conditionPiece = piece as ConditionPiece;
          if (conditionPiece.ErrorBlockId != null && conditionPiece.ErrorBlockId !== "-1") {
            if (blocks.indexOf(conditionPiece.ErrorBlockId) === -1) {
              blocks.push(conditionPiece.ErrorBlockId);
            }
          }
        } break;

        case 'variable-condition-piece': {
          let conditionPiece = piece as VariableConditionPiece;
          if (conditionPiece.ErrorBlockId != null && conditionPiece.ErrorBlockId !== "-1") {
            if (blocks.indexOf(conditionPiece.ErrorBlockId) === -1) {
              blocks.push(conditionPiece.ErrorBlockId);
            }
          }
        } break;

        case 'calendar-piece': {
          let calendarPiece = piece as CalendarPiece;
          if (calendarPiece.ErrorBlockId != null && calendarPiece.ErrorBlockId !== "-1") {
            if (blocks.indexOf(calendarPiece.ErrorBlockId) === -1) {
              blocks.push(calendarPiece.ErrorBlockId);
            }
          }
        } break;

        case 'jump-to-block-piece': {
          let jumpPiece = piece as JumpToBlockPiece;
          if (jumpPiece.BlockId != null && jumpPiece.BlockId !== "-1") {
            if (blocks.indexOf(jumpPiece.BlockId) === -1) {
              blocks.push(jumpPiece.BlockId);
            }
          }
        } break;

        case 'call-block-as-procedure-piece': {
          let jumpPiece = piece as CallBlockAsProcedurePiece;
          if (jumpPiece.BlockId != null && jumpPiece.BlockId !== "-1") {
            if (blocks.indexOf(jumpPiece.BlockId) === -1) {
              blocks.push(jumpPiece.BlockId);
            }
          }
        } break;

        case 'form-piece': {
          let formPiece = piece as FormPiece;
          if (formPiece.blockId != null && formPiece.blockId !== "-1") {
            if (blocks.indexOf(formPiece.blockId) === -1) {
              blocks.push(formPiece.blockId);
            }
          }
        } break;

        case 'switch-jump-to-block-piece': {
          let switchJumpPiece = piece as SwitchJumpToBlockPiece;
          if (switchJumpPiece.Conditions !== null && switchJumpPiece.Conditions.length > 0) {
            for (let i = 0; i < switchJumpPiece.Conditions.length; i++) {
              let jumpPiece = switchJumpPiece.Conditions[i];
              if (jumpPiece.BlockId != null && jumpPiece.BlockId !== "-1") {
                if (blocks.indexOf(jumpPiece.BlockId) === -1) {
                  blocks.push(jumpPiece.BlockId);
                }
              }
            }
          }
        } break;

        case 'wa-menu-piece': {
          let WaMenuPiece = piece as WAMenu;

          if (WaMenuPiece.dataEntryModel.ErrorBlockId != null && WaMenuPiece.dataEntryModel.ErrorBlockId !== "-1") {
            if (blocks.indexOf(WaMenuPiece.dataEntryModel.ErrorBlockId) === -1) {
              blocks.push(WaMenuPiece.dataEntryModel.ErrorBlockId);
            }
          }
          if (WaMenuPiece.dataEntryModel.PendingReplyFromCustomerBlockId !== null && WaMenuPiece.dataEntryModel.PendingReplyFromCustomerBlockId !== "-1") {
            if (blocks.indexOf(WaMenuPiece.dataEntryModel.PendingReplyFromCustomerBlockId) === -1) {
              blocks.push(WaMenuPiece.dataEntryModel.PendingReplyFromCustomerBlockId);
            }
          }
          if (WaMenuPiece.switchJumpToBlockModel.Conditions !== null && WaMenuPiece.switchJumpToBlockModel.Conditions.length > 0) {
            for (let i = 0; i < WaMenuPiece.switchJumpToBlockModel.Conditions.length; i++) {
              let jumpPiece = WaMenuPiece.switchJumpToBlockModel.Conditions[i];
              if (jumpPiece.BlockId != null && jumpPiece.BlockId !== "-1") {
                if (blocks.indexOf(jumpPiece.BlockId) === -1) {
                  blocks.push(jumpPiece.BlockId);
                }
              }
            }
          }
        } break;
      }
    });
    return blocks;
  }

  public isCognitivityEnabled() {
    return this.mCurrentChatDefinition.CognitivityEnabled;
  }

  public getCurrentCognitivityProject() {
    return this.mCurrentChatDefinition.CognitivityProject;
  }

  public getCognitivityProjectToken() {
    return (this.mCurrentChatDefinition.CognitivityProject != null) ? this.mCurrentChatDefinition.CognitivityProject.token : null;
  }

  public enableCognitivity() {
    this.mCurrentChatDefinition.CognitivityEnabled = true;
    this.onCognitivityStatusChanged.emit(true);
  }

  public disableCognitivity() {
    this.mCurrentChatDefinition.CognitivityEnabled = false;
    this.mCurrentChatDefinition.CognitivityProject = null;
    this.onCognitivityStatusChanged.emit(false);
  }

  public selectCognitivityProject(project: CognitivityProject, clearAllDefinition: boolean = true) {
    this.mCurrentChatDefinition.CognitivityProject = project;
    this.mCurrentChatDefinition.CognitivityDefinitions = clearAllDefinition ? [] : this.mCurrentChatDefinition.CognitivityDefinitions;
  }

  public deleteCognitivityProject() {
    this.mCurrentChatDefinition.CognitivityProject = null;
    this.mCurrentChatDefinition.CognitivityDefinitions = [];
  }

  public setAvailableIntents(intents: Intent[]) {
    this.availableCognitivityIntents = intents;
    this.mCurrentChatDefinition.CognitivityDefinitions.forEach((cog) => {
      //para mantener retrocompatibilidad con el viejo ysmart
      cog.intent.cognitiveServiceId = cog.intent.cognitiveServiceId === null ? cog.intent.name : cog.intent.cognitiveServiceId;
      if (!intents.some(i => i.cognitiveServiceId === cog.intent.cognitiveServiceId)) {
        cog.intent.cognitiveServiceId = "";
        cog.intent.name = "";
      }
    });
  }

  public getIntents(): Intent[] {
    return this.availableCognitivityIntents;
  }

  public setAvailableProjects(projects: CognitivityProject[]) {
    this.availableCognitivityProjects = projects;
  }

  public setAvailableMetamaps(metamaps: Metamap[]) {
    this.availableBiometricMetamaps = metamaps;
  }

  public isSmtpAvailable(): boolean {
    return this.smtpConfig !== null && this.smtpConfig.enabled;
  }

  public setSmtpConfig(smtpConfig: SmtpConfiguration) {
    this.smtpConfig = smtpConfig;

    this.onSmtpConfigured.emit(smtpConfig);
  }

  public getCognitivityProjects(): CognitivityProject[] {
    return this.availableCognitivityProjects;
  }

  public getBiometricMetamaps(): Metamap[] {
    return this.availableBiometricMetamaps;
  }

  public setAvailableEntities(entities: Entity[]) {
    this.availableCognitivityEntities = entities;
    const updateEntityName = (p) => {
      var parsedPiece = null
      if (p.type === 'switch-jump-to-block-piece') {
        parsedPiece = <SwitchJumpToBlockPiece>p;
        if (parsedPiece.UsesCognitiveEntities) {
          let entity = entities.find(e => parsedPiece.EntityId === e.cognitiveServiceId);
          if (typeof (entity) !== 'undefined') {
            parsedPiece.EntityName = entity.name;
          }
        }
      } else if (p.type === 'variable-condition-piece') {
        parsedPiece = <VariableConditionPiece>p;
        if (parsedPiece.UsesCognitiveEntities) {
          let entity = entities.find(e => parsedPiece.EntityId === e.cognitiveServiceId);
          if (typeof (entity) !== 'undefined') {
            parsedPiece.EntityName = entity.name;
          }
        }
      }
    };

    this.mCurrentChatDefinition.BlockList.forEach(b => {
      b.Pieces.forEach(updateEntityName);
    })
    this.mCurrentChatDefinition.DefaultBlocks.forEach(b => {
      b.Pieces.forEach(updateEntityName);
    });
    this.mCurrentChatDefinition.BlockGroups.forEach(g => {
      g.Blocks.forEach(b => {
        b.Pieces.forEach(updateEntityName);
      });
    });
  }

  public setAvailableForms(forms: FormDefinition[]) {
    this.availableCognitivityForms = forms;
    const updateEntityName = (p) => {
      /*var parsedPiece = null
      if (p.type === 'switch-jump-to-block-piece') {
        parsedPiece = <SwitchJumpToBlockPiece>p;
        if (parsedPiece.UsesCognitiveEntities) {
          let entity = entities.find(e => parsedPiece.EntityId === e.cognitiveServiceId);
          if (typeof (entity) !== 'undefined') {
            parsedPiece.EntityName = entity.name;
          }
        }
      } else if (p.type === 'variable-condition-piece') {
        parsedPiece = <VariableConditionPiece>p;
        if (parsedPiece.UsesCognitiveEntities) {
          let entity = entities.find(e => parsedPiece.EntityId === e.cognitiveServiceId);
          if (typeof (entity) !== 'undefined') {
            parsedPiece.EntityName = entity.name;
          }
        }
      }*/
    };

    this.mCurrentChatDefinition.BlockList.forEach(b => {
      b.Pieces.forEach(updateEntityName);
    })
    this.mCurrentChatDefinition.DefaultBlocks.forEach(b => {
      b.Pieces.forEach(updateEntityName);
    });
    this.mCurrentChatDefinition.BlockGroups.forEach(g => {
      g.Blocks.forEach(b => {
        b.Pieces.forEach(updateEntityName);
      });
    });
  }

  public setAvailableCategories(categories: Category[]) {
    this.availableCognitivityCategories = categories;
  }

  public setAvailableExtractionFormats(extractionFormats: ExtractionFormat[]) {
    this.availableCognitivityExtractionFormats = extractionFormats;
  }

  public getForms(): FormDefinition[] {
    return this.availableCognitivityForms;
  }

  public getEntities(): Entity[] {
    return this.availableCognitivityEntities;
  }

  public getCategories(): Category[] {
    return this.availableCognitivityCategories;
  }

  public getExtractionFormats(): ExtractionFormat[] {
    return this.availableCognitivityExtractionFormats;
  }

  public setGlobalCognitivityPriority(value: boolean) {
    this.mCurrentChatDefinition.PrioritizeCognitivity = value;
  }

  public getGlobalCogntivityPriority(): boolean {
    return this.mCurrentChatDefinition.PrioritizeCognitivity;
  }

  public isHsmJumpEnabled() {
    return this.mCurrentChatDefinition.HsmJumpEnabled;
  }

  public enableHsmJump() {
    this.mCurrentChatDefinition.HsmJumpEnabled = true;
  }

  public disableHsmJump() {
    this.mCurrentChatDefinition.HsmJumpEnabled = false;
  }
  findInvalidBlocks(): BlockDefinition[] {
    var blockList: BlockDefinition[] = [];
    if (typeof (this.mCurrentChatDefinition) !== 'undefined' && this.mCurrentChatDefinition !== null) {
      const set = new Set(this.mCurrentChatDefinition.BlockList.map(blockList => blockList.Name));
      const hasDuplicates = set.size !== this.mCurrentChatDefinition.BlockList.length;
      
      const deletedBlockIds = new Set(this.mCurrentChatDefinition.BlockDeletedList.map(block => block.Id));
      
      this.mCurrentChatDefinition.BlockList.forEach(block => {
        if (!deletedBlockIds.has(block.Id) && !block.isValid(this, hasDuplicates)) {
          blockList.push(block);
        }
      });
      this.mCurrentChatDefinition.DefaultBlocks.forEach(block => {
        if (!deletedBlockIds.has(block.Id) && !block.isValid(this, hasDuplicates)) {
          blockList.push(block);
        }
      });
    }
    return blockList;
  }

  isChatValid(errorInfo: any): boolean {
    if (this.mCurrentFlow.name.length === 0) {
      errorInfo.desc = 'INVALID_CHAT_NAMEISINVALID';
      console.log('El nombre del flow es inválido');
      return false;
    }
    if (this.mCurrentModule.isMaster()) {
      let defaultBlock = this.findBlockWithId(SystemBlocks.SYSTEMBLOCK_WELCOME.toString());
      if (!defaultBlock.returnsMessage(this, [])) {
        errorInfo.desc = 'INVALID_CHAT_WELCOMEBLOCK_DOESNTRETURNMESSAGE';
        console.log('El flow es inválido porque el bloque de bienvenida no devuelve mensajes');
        return false;
      }

      defaultBlock = this.findBlockWithId(SystemBlocks.SYSTEMBLOCK_DEFAULT_ANSWER.toString());
      if (!defaultBlock.returnsMessage(this, [])) {
        errorInfo.desc = 'INVALID_CHAT_DEFAULTANSWETBLOCK_DOESNTRETURNMESSAGE';
        console.log('El flow es inválido porque la respuesta por defecto no devuelve mensajes');
        return false;
      }

      if (!this.isCommandsValid()) {
        errorInfo.desc = 'INVALID_CHAT_CONTAINSINVALIDCOMMANDS';
        return false;
      }

      if (!this.isConfigurationValid()) {
        errorInfo.desc = 'INVALID_CHAT_INVALIDCONFIG';
        return false;
      }

      if (!this.isIntegrationsValid()) {
        errorInfo.desc = 'INVALID_CHAT_CONTAINSINVALIDINTEGRATIONS';
        return false;
      }

      if (!this.isCognitivityValid()) {
        errorInfo.desc = 'INVALID_CHAT_CONTAINSINVALIDCOGNITIVITY';
        return false;
      }
    }

    if (!this.isBlocksValid()) {
      errorInfo.desc = 'INVALID_CHAT_CONTAINSINVALIDBLOCKS';
      return false;
    }

    return true;
  }

  lastStatusBlocksValidation(): boolean {
    return this.lastStatusBlockIsValid;
  }

  isBlocksValid(): boolean {
    let invalidBlocks = this.findInvalidBlocks();
    if (invalidBlocks.length > 0) {
      console.log(`El chat es inválido porque hay ${invalidBlocks.length} bloques inválidos`);

      // Agregar log detallado de cada bloque invalidado
      invalidBlocks.forEach((block, index) => {
        console.log(`Bloque inválido #${index + 1}: ID=${block.Id}, Nombre=${block.Name}, ModuleId=${block.ModuleId}`);
      });

      let module = this.getCurrentModule();
      if (module.isMaster()) {
        this.selectedBlock(invalidBlocks[0]);
        this.lastStatusBlockIsValid = false;
        return false;
      }

      let invalidBlock = invalidBlocks.find(block => block.ModuleId === this.getCurrentModule().id);
      if (invalidBlock) {
        this.selectedBlock(invalidBlock);
        this.lastStatusBlockIsValid = false;
        return false;
      }
    }

    this.selectedBlock(this.getSelectedBlock());
    this.lastStatusBlockIsValid = true;
    return true;
  }

  isCommandsValid(): boolean {
    let invalidCommands = this.findInvalidCommands();
    if (invalidCommands.length > 0) {
      //console.log(`El chat es inválido porque hay ${invalidCommands.length} comandos inválidos`);
      return false;
    }

    return true;
  }

  isCognitivityValid(): boolean {
    var valid = true;
    this.mCurrentChatDefinition.CognitivityDefinitions.forEach(item => {
      if (!item.isValid()) {
        valid = false;
        return;
      }
    });
    return valid;
  }

  isIntegrationsValid(): boolean {
    let invalidIntegrations = this.findInvalidIntegrations();
    if (invalidIntegrations.length > 0) {
      //console.log(`El chat es inválido porque hay ${invalidIntegrations.length} integraciones inválidas`);
      return false;
    }
    return true;
  }

  containsDisabledIntegrations(): boolean {
    let disabledIntegrations = this.findDisabledIntegrations();
    if (disabledIntegrations.length > 0) {
      return true;
    }
    return false;
  }


  isConfigurationValid(): boolean {
    if (typeof (this.mCurrentChatDefinition) !== 'undefined' && this.mCurrentChatDefinition !== null) {
      if (this.mCurrentChatDefinition.PersistentMenu != null &&
        !this.mCurrentChatDefinition.PersistentMenu.isValid(this)) {
        //console.log(`El flow es inválido porque la definición del menú persistente es inválida`);
        return false;
      }

      if (this.mCurrentChatDefinition.IceBreakers != null &&
        !this.mCurrentChatDefinition.IceBreakers.isValid(this)) {
        console.log(`El flow es inválido porque la definición del ice-breakers es inválida`);
        return false;
      }

      if (this.mCurrentChatDefinition.GoogleConfiguration != null &&
        !this.mCurrentChatDefinition.GoogleConfiguration.isValid(this)) {
        console.log(`El flow es inválido porque la definición de la configuración de google es inválida`);
        return false;
      }

      if (!this.statisticEventConfigurationIsValid()) {
        console.log(`El flow es inválido porque hay eventos de estatisticas con el mismo nombre`);
        return false;
      }

      if (!environment.standAlone) {
        if (this.mCurrentChatDefinition.YSocialSettings === null ||
          !this.mCurrentChatDefinition.YSocialSettings.isValid(this)) {
          return false;
        }
      }

      if (this.mCurrentFlow.channel === ChannelTypes.Chat) {
        if (this.mCurrentChatDefinition.Greetings === null ||
          this.mCurrentChatDefinition.Greetings.Locales === null ||
          this.mCurrentChatDefinition.Greetings.Locales.length === 0 ||
          this.mCurrentChatDefinition.Greetings.Locales[0].text.length === 0) {
          console.log(`El flow es inválido porque no hay un saludo inicial configurado`);
          return false;
        }
      }
      else {
        if (!environment.standAlone &&
          this.mCurrentChatDefinition.YSocialSettings.ReturnsFromAgent !== null &&
          this.mCurrentChatDefinition.YSocialSettings.ReturnsFromAgent.length > 0) {
          for (let i = 0; i < this.mCurrentChatDefinition.YSocialSettings.ReturnsFromAgent.length; i++) {
            if (!this.mCurrentChatDefinition.YSocialSettings.ReturnsFromAgent[i].isValid(this)) {
              console.log(`El flow es inválido porque el retorno ${i} de agentes de ysocial es inválido`);
              return false;
            }
          }
        }
      }

      if (this.mCurrentChatDefinition.CognitivityEnabled && this.mCurrentChatDefinition.CognitivityProject === null) {
        console.log(`El flow es inválido porque no hay un projecto de cognitividad configurado y el servicio de cognitividad está activado`);
        return false;
      }
    }

    return true;
  }

  findInvalidCommands(): CommandDefinition[] {
    let commandList: CommandDefinition[] = [];
    if (typeof (this.mCurrentChatDefinition) !== 'undefined' && this.mCurrentChatDefinition !== null) {
      this.mCurrentChatDefinition.CommandDefinitions.forEach(cmd => {
        if (!cmd.isValid(this)) {
          commandList.push(cmd);
        }
      });
    }
    return commandList;
  }

  /*findInvalidCognitivities(): CognitivityDefinition {
    let cognitivityList : CognitivityDefinition = null;
    if (typeof(this.mCurrentChatDefinition) !== 'undefined' && this.mCurrentChatDefinition !== null) {
      this.mCurrentChatDefinition.CognitivityDefinition.forEach(cog => {
        if (!cog.isValid(this)) {
          cognitivityList.push(cog);
        }
      });
    }
    return cognitivityList;
  }*/

  findInvalidIntegrations(): Integration[] {
    let list: Integration[] = [];
    if (typeof (this.mCurrentChatDefinition) !== 'undefined' && this.mCurrentChatDefinition !== null) {
      this.mCurrentChatDefinition.IntegrationDefinitions.forEach(integration => {
        if (!integration.isValid(this)) {
          list.push(integration);
        }
      });
    }
    return list;
  }

  findDisabledIntegrations(): Integration[] {
    let list: Integration[] = [];
    if (this.mCurrentChatDefinition && this.mCurrentChatDefinition.IntegrationDefinitions) {
      this.mCurrentChatDefinition.IntegrationDefinitions.forEach(integration => {
        if (!integration.enabled) {
          list.push(integration);
        }
      });
    }
    return list;
  }

  isIntegrationReferenced(id: number): boolean {
    var inUse = false;
    this.mCurrentChatDefinition.BlockList.forEach(block => {
      block.Pieces.forEach(piece => {
        if (piece.type == 'integration-piece') {
          let integrationPiece = piece as IntegrationPiece;
          if (integrationPiece.integrationId === id) {
            inUse = true;
          }
        }
      });
    });

    if (!inUse) {
      this.mCurrentChatDefinition.DefaultBlocks.forEach(block => {
        block.Pieces.forEach(piece => {
          if (piece.type == 'integration-piece') {
            let integrationPiece = piece as IntegrationPiece;
            if (integrationPiece.integrationId === id) {
              inUse = true;
            }
          }
        });
      });
    }

    return inUse;
  }

  isQuickReplyInValidPosition(quickReply: QuickReplyPiece): boolean {
    if (typeof (quickReply.OwnerBlockId) !== 'undefined' && quickReply.OwnerBlockId !== null) {
      let parentBlock: BlockDefinition = this.findBlockWithId(quickReply.OwnerBlockId);
      if (typeof (parentBlock) !== 'undefined' && parentBlock !== null) {
        let currentIndex = parentBlock.Pieces.findIndex(p => p.Id === quickReply.Id);
        if (currentIndex !== -1) {
          if (currentIndex === 0) {
            return false;
          }

          let previousPiece = parentBlock.Pieces[currentIndex - 1];
          if (previousPiece.type !== 'message-piece' &&
            previousPiece.type !== 'gallery-piece' &&
            previousPiece.type !== 'dynamic-gallery-piece' &&
            previousPiece.type !== 'attachment-piece' &&
            previousPiece.type !== 'video-embed-piece') {
            return false;
          }
        }
      }
    }

    return true;
  }

  isReturnToLastBlockInValidPosition(piece: ReturnToLastBlockPiece): boolean {
    if (typeof (piece.OwnerBlockId) !== 'undefined' && piece.OwnerBlockId !== null) {
      let parentBlock: BlockDefinition = this.findBlockWithId(piece.OwnerBlockId);
      if (typeof (parentBlock) !== 'undefined' && parentBlock !== null) {
        let currentIndex = parentBlock.Pieces.findIndex(p => p.Id === piece.Id);
        if (currentIndex !== -1) {
          return currentIndex === parentBlock.Pieces.length - 1;
        }
      }
    }

    return true;
  }

  getChannelTypeName(channel: string): string {
    if (typeof (channel) === 'undefined') {
      channel = this.mCurrentFlow.channel;
    }
    switch (channel) {
      case ChannelTypes.Chat: return 'chat';
      case ChannelTypes.FacebookMessenger: return 'messenger';
      case ChannelTypes.Instagram: return 'instagram';
      case ChannelTypes.WhatsApp: return 'whatsapp';
      case ChannelTypes.Twitter: return 'twitter';
      case ChannelTypes.Skype: return 'skype';
      case ChannelTypes.MercadoLibre: return 'ml';
      case ChannelTypes.Telegram: return 'telegram';
      case ChannelTypes.Generic: return 'generic';
      default: return channel.toString();
    }
  }

  getPiecesForIncativityClosed() {
    let pieces = []
    var newPiece, pieceType: PieceType;

    pieceType = this.createPiece('PIECE_CLOSECASE', 'fa-edit', 'close-case-piece', this.createCloseCase);
    newPiece = pieceType.modelFatory();
    newPiece.type = pieceType.PieceDefinitionType;
    newPiece.Id = this.mCurrentChatDefinition.NextPieceId++;
    pieces.push(newPiece);

    return pieces
  }

  getPiecesForDefaultBlockExcepcion(channel: string): BasePiece[] {
    var pieces = [];
    var newPiece, pieceType: PieceType;

    pieceType = this.createPiece('PIECE_MESSAGE', 'fa-align-left', 'message-piece', this.createMesage);
    newPiece = pieceType.modelFatory();
    newPiece.type = pieceType.PieceDefinitionType;
    newPiece.Id = this.mCurrentChatDefinition.NextPieceId++;
    newPiece.TextList[0].text = "Ha ocurrido un error. Será derivado con un operador.";
    pieces.push(newPiece);

    if (channel === ChannelTypes.Chat) {
      pieceType = this.createPiece('PIECE_STORE_MESSAGE', 'fa-bolt', 'store-message-piece', this.createStoreMessage);
      newPiece = pieceType.modelFatory();
      newPiece.type = pieceType.PieceDefinitionType;
      newPiece.Id = this.mCurrentChatDefinition.NextPieceId++;
      newPiece.Message = "Ha ocurrido un error en yFlow. {{errorMessage}}";
      pieces.push(newPiece);
    }

    pieceType = this.createPiece('PIECE_OPERATOR', 'fa-user', 'derive-piece', this.createDerivation);
    newPiece = pieceType.modelFatory();
    newPiece.type = pieceType.PieceDefinitionType;
    newPiece.Id = this.mCurrentChatDefinition.NextPieceId++;
    pieces.push(newPiece);

    return pieces;
  }

  addIntegrationInput(id: number, input: VariableDefinition) {
    //this.onIntegrationInputAdded.emit({id, input});
    this.mCurrentChatDefinition.DefaultBlocks.forEach(block => {
      block.Pieces.forEach(piece => {
        if (piece.type === 'integration-piece') {
          let integrationPiece = <IntegrationPiece>piece;
          if (integrationPiece.integrationId === id) {
            let inputMap = new InputVariableMap();
            inputMap.integrationVriableId = input.Id;
            inputMap.integrationVariableName = input.Name;
            inputMap.type = input.Type;
            inputMap.isMasked = input.IsMasked
            integrationPiece.inputs.push(inputMap);
          }
        }
      });
    });
    this.mCurrentChatDefinition.BlockList.forEach(block => {
      block.Pieces.forEach(piece => {
        if (piece.type === 'integration-piece') {
          let integrationPiece = <IntegrationPiece>piece;
          if (integrationPiece.integrationId === id) {
            let inputMap = new InputVariableMap();
            inputMap.integrationVriableId = input.Id;
            inputMap.integrationVariableName = input.Name;
            inputMap.type = input.Type;
            inputMap.isMasked = input.IsMasked
            integrationPiece.inputs.push(inputMap);
          }
        }
      });
    });
  }

  deleteIntegrationInput(id: number, input: VariableDefinition) {
    //this.onIntegrationInputDeleted.emit({id, input});
    this.mCurrentChatDefinition.DefaultBlocks.forEach(block => {
      let pieces = block.Pieces.filter(piece => piece.type === 'integration-piece');
      if (pieces) {
        pieces.forEach(p => {
          let integrationPiece = <IntegrationPiece>p;
          if (integrationPiece.integrationId === id) {
            integrationPiece.inputs = integrationPiece.inputs.filter(el => el.integrationVariableName !== input.Name);
          }
        });
      }
    });
    this.mCurrentChatDefinition.BlockList.forEach(block => {
      let pieces = block.Pieces.filter(piece => piece.type === 'integration-piece');
      if (pieces) {
        pieces.forEach(p => {
          let integrationPiece = <IntegrationPiece>p;
          if (integrationPiece.integrationId === id) {
            integrationPiece.inputs = integrationPiece.inputs.filter(el => el.integrationVariableName !== input.Name);
          }
        });
      }
    });
  }

  addIntegrationOutput(id: number, output: IntegrationOutput) {
    //this.onIntegrationOutputAdded.emit({id, output});
    this.mCurrentChatDefinition.DefaultBlocks.forEach(block => {
      block.Pieces.forEach(piece => {
        if (piece.type === 'integration-piece') {
          let integrationPiece = <IntegrationPiece>piece;
          if (integrationPiece.integrationId === id) {
            let outputMap = new OutputVariableMap();
            outputMap.integrationVariableName = output.name;
            outputMap.type = output.type;
            outputMap.isMasked = output.IsMasked;
            integrationPiece.outputs.push(outputMap);
          }
        }
      });
    });
    this.mCurrentChatDefinition.BlockList.forEach(block => {
      block.Pieces.forEach(piece => {
        if (piece.type === 'integration-piece') {
          let integrationPiece = <IntegrationPiece>piece;
          if (integrationPiece.integrationId === id) {
            let outputMap = new OutputVariableMap();
            outputMap.integrationVariableName = output.name;
            outputMap.type = output.type;
            outputMap.isMasked = output.IsMasked;
            integrationPiece.outputs.push(outputMap);
          }
        }
      });
    });
  }

  deleteIntegrationOutput(id: number, output: IntegrationOutput) {
    //this.onIntegrationOutputDeleted.emit({id, output});
    this.mCurrentChatDefinition.DefaultBlocks.forEach(block => {
      let pieces = block.Pieces.filter(piece => piece.type === 'integration-piece');
      if (pieces) {
        pieces.forEach(p => {
          let integrationPiece = <IntegrationPiece>p;
          if (integrationPiece.integrationId === id) {
            integrationPiece.outputs = integrationPiece.outputs.filter(el => el.integrationVariableName !== output.name);
          }
        });
      }
    });
    this.mCurrentChatDefinition.BlockList.forEach(block => {
      let pieces = block.Pieces.filter(piece => piece.type === 'integration-piece');
      if (pieces) {
        pieces.forEach(p => {
          let integrationPiece = <IntegrationPiece>p;
          if (integrationPiece.integrationId === id) {
            integrationPiece.outputs = integrationPiece.outputs.filter(el => el.integrationVariableName !== output.name);
          }
        });
      }
    });
  }

  getIntegrationById(id: string): Integration {
    return this.getIntegrations().find(i => i.id.toString() === id);
  }

  getIntegrationsState() {
    return {
      integrations: this.getIntegrations().map(integration => ({
        id: integration.id,
        enabled: integration.enabled
      }))
    };
  }

  getIsContingencyBot(): boolean {
    return this.isContingencyBot;
  }
}
