import { promises } from 'fs';
import sql from 'mssql';
const flowsPath = 'C:\\Users\\<USER>\\Desktop\\flowsVersions';
const host = 'localhost';
const username = 'yflow';
const password = 'yflow';
const name = 'yflow';

(async () => {
  try {

    let connection;

    try {
      connection = await sql.connect({
        server: host || '',
        user: username || '',
        password: password || '',
        database: name || '',
      });

      console.log('Conexión establecida a la base de datos MSSQL');
    } catch (err) {
      console.error('Error de conexión:', err);
      return;
    }

    let folders = await promises.readdir(flowsPath);

    if (folders.length === 0) {
      console.error('No se encontraron carpetas de Flows en el directorio:', flowsPath);
      return;
    }

    console.error("Se encontraron los flows", folders);

    for (let folder of folders) {
      await processFiles(folder, connection);
    }

  } catch (err) {
    console.error('Error al leer el storagePath:', err);
  }



})();

async function processFiles(flowId, connection) {
  let files = await promises.readdir(`${flowsPath}/${flowId}`);

  if (files.length === 0) {
    console.error('No se encontraron archivos en el directorio:', flowsPath);
    return;
  }

  console.log(`Se encontraron las versiones ${files} para el flowid ${flowId}`);

  for (let file of files) {
    const filePath = `${flowsPath}/${flowId}/${file}`;

    try {
      const data = await promises.readFile(filePath, 'utf8');

      let blob = null;
      try {
        blob = JSON.parse(data);
      }
      catch {
        console.log("JSON INVALIDO", file);
        continue;
      }

      const versionNumber = file.replace(".json", "");
      const request = connection.request();

      try {
        const query = `UPDATE flow_versions SET blob = @blob WHERE number = @versionNumber AND flow_id = @flowId`;
        const result = await request
          .input('blob', data)
          .input('versionNumber', versionNumber)
          .input('flowId', flowId)
          .query(query);

        if (result.rowsAffected[0] === 0) {
          console.log(`No se encontró flowVersion para ${file} y FlowID  ${flowId} en DB`);
        }
        else {
          console.log(`ARCHIVO ${file} afecto ${result.rowsAffected[0]} rows`);
        }
      }
      catch (err) {
        console.error('Error Insertando FlowVersions', err);
      }

    } catch (err) {
      console.error('Error al procesar el archivo:', err);
    }
  }
}

