/****** Object:  Table `logs_error`    Script Date: 24/5/2023 14:11:04 ******/
CREATE TABLE `logs`(
	`id` int NOT NULL AUTO_INCREMENT NOT NULL,
	`type` int,
	`message_id` VARCHAR(100) NULL,
	`date` DATETIME(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6),
	`message` TEXT,
	`stack` TEXT
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;

/****** Object:  Table `abandoned_case`    Script Date: 24/5/2023 14:11:04 ******/
CREATE TABLE `abandoned_case`(
	`id` int NOT NULL AUTO_INCREMENT NOT NULL,
	`date` DATETIME(6) NOT NULL,
	`interval` INT NOT NULL,
	`flow_id` INT NOT NULL,
	`channel` VARCHAR(10) NOT NULL,
	`block_id` VARCHAR(100) NULL,
	`data` TEXT NULL,
	`total` INT NULL,
	`version` INT NULL,
	PRIMARY KEY(`id`) 
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;

/****** Object:  Table `companies`    Script Date: 24/5/2023 14:11:04 ******/
CREATE TABLE `companies`(
	`id` int NOT NULL AUTO_INCREMENT NOT NULL,
	`created_at` DATETIME(6) NULL,
	`updated_at` DATETIME(6) NULL,
	`name` VARCHAR(255) NOT NULL,
	PRIMARY KEY(`id`) 
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;

/****** Object:  Table `configurations`    Script Date: 24/5/2023 14:11:04 ******/
CREATE TABLE `configurations`(
	`name` VARCHAR(100) NOT NULL,
	`content` TEXT NULL,
	PRIMARY KEY (`name`) 
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;

/****** Object:  Table `detail_statistic_event`    Script Date: 24/5/2023 14:11:04 ******/
CREATE TABLE `detail_statistic_event`(
	`id` int NOT NULL AUTO_INCREMENT NOT NULL,
	`date` DATETIME(6) NOT NULL,
	`interval` INT NOT NULL,
	`interval_datetime` DATETIME(6) NOT NULL,
	`flow_id` INT NOT NULL,
	`statistic_event_id` INT NOT NULL,
	`channel` VARCHAR(10) NOT NULL,
	`data` TEXT NULL,
	`version` INT NULL,
	`block_id` VARCHAR(100) NULL,
	PRIMARY KEY(`id`) 
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;

/****** Object:  Table `flow_table`    Script Date: 24/5/2023 14:11:04 ******/
CREATE TABLE `flow_table`(
	`id` int NOT NULL AUTO_INCREMENT NOT NULL,
	`flow_id` INT NOT NULL,
	`version` INT NOT NULL,
	`table_name` VARCHAR(80) NOT NULL,
	`status` VARCHAR(10) NOT NULL,
	`extension` VARCHAR(5) NOT NULL,
	`created_at` DATETIME(6) NULL,
	`updated_at` DATETIME(6) NULL,
	`headers` TEXT NULL,
	PRIMARY KEY(`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;

/****** Object:  Table `flow_versions`    Script Date: 24/5/2023 14:11:04 ******/
CREATE TABLE `flow_versions`(
	`id` int NOT NULL AUTO_INCREMENT NOT NULL,
	`created_at` DATETIME(6) NULL,
	`number` INT NULL,
	`blob` TEXT NULL,
	`flow_id` INT NOT NULL,
	`stats` TEXT NULL,
	`published_at` DATETIME(6) NULL,
	`published_by_user_id` INT NULL,
	`comments` TEXT NULL,
	`user_id` INT NULL,
	PRIMARY KEY(`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;

/****** Object:  Table `flows`    Script Date: 24/5/2023 14:11:04 ******/
CREATE TABLE `flows`(
	`id` int NOT NULL AUTO_INCREMENT NOT NULL,
	`name` VARCHAR(255) NOT NULL,
	`created_at` DATETIME(6) NULL,
	`updated_at` DATETIME(6) NULL,
	`user_id` INT NOT NULL,
	`active_staging_version_id` INT NULL,
	`active_production_version_id` INT NULL,
	`deleted_at` DATETIME(6) NULL,
	`channel` VARCHAR(255) NULL,
	`company_id` INT NULL,
	`type` VARCHAR(10) NULL,
	`master_flow_id` INT NULL,
	`active_production_version_master_id` INT NULL,
	PRIMARY KEY(`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;

/****** Object:  Table `history_daily`    Script Date: 24/5/2023 14:11:04 ******/
CREATE TABLE `history_daily`(
	`id` int NOT NULL AUTO_INCREMENT NOT NULL,
	`date` DATETIME(6) NOT NULL,
	`interval` INT NOT NULL,
	`interval_datetime` DATETIME(6) NOT NULL,
	`data` TEXT NULL,
	`new_cases` INT DEFAULT 0,
	`transferred` INT DEFAULT 0,
	`closed_by_yflow` INT DEFAULT 0,
	`new_messages` INT DEFAULT 0,
	`hsm_case` INT DEFAULT 0,
	`case_abandoned` INT DEFAULT 0,
	`monthly_users` INT DEFAULT 0,
	PRIMARY KEY(`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;

/****** Object:  Table `history_daily_by_blocks`    Script Date: 24/5/2023 14:11:04 ******/
CREATE TABLE `history_daily_by_blocks`(
	`id` int NOT NULL AUTO_INCREMENT NOT NULL,
	`date` DATETIME(6) NOT NULL,
	`interval` INT NOT NULL,
	`interval_datetime` DATETIME(6) NOT NULL,
	`flow_id` INT NOT NULL,
	`block_id` VARCHAR(100) NULL,
	`channel` VARCHAR(255) NOT NULL,
	`total` INT NULL,
	`version` INT NULL,
	PRIMARY KEY(`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;

/****** Object:  Table `history_daily_by_blocks_sequence`    Script Date: 24/5/2023 14:11:04 ******/
CREATE TABLE `history_daily_by_blocks_sequence`(
	`id` int NOT NULL AUTO_INCREMENT NOT NULL,
	`date` DATETIME(6) NOT NULL,
	`interval` INT NOT NULL,
	`interval_datetime` DATETIME(6) NOT NULL,
	`flow_id` INT NOT NULL,
	`source_block_id` VARCHAR(100) NULL,
	`dest_block_id` VARCHAR(100) NULL,
	`type` INT NOT NULL,
	`channel` VARCHAR(10) NOT NULL,
	`total` INT NULL,
	`version` INT NULL,
	PRIMARY KEY(`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;

/****** Object:  Table `history_daily_by_commands`    Script Date: 24/5/2023 14:11:04 ******/
CREATE TABLE `history_daily_by_commands`(
	`id` int NOT NULL AUTO_INCREMENT NOT NULL,
	`date` DATETIME(6) NOT NULL,
	`interval` INT NOT NULL,
	`interval_datetime` DATETIME(6) NOT NULL,
	`flow_id` INT NOT NULL,
	`command_id` INT NOT NULL,
	`channel` VARCHAR(255) NOT NULL,
	`total` INT NULL,
	`version` INT NULL,
	PRIMARY KEY(`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;

/****** Object:  Table `history_daily_by_default_answers`    Script Date: 24/5/2023 14:11:04 ******/
CREATE TABLE `history_daily_by_default_answers`(
	`id` int NOT NULL AUTO_INCREMENT NOT NULL,
	`date` DATETIME(6) NOT NULL,
	`interval` INT NOT NULL,
	`interval_datetime` DATETIME(6) NOT NULL,
	`flow_id` INT NOT NULL,
	`block_id` VARCHAR(100) NULL,
	`channel` VARCHAR(255) NOT NULL,
	`total` INT NULL,
	`version` INT NULL,
	PRIMARY KEY(`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;

/****** Object:  Table `history_daily_by_derivation_key`    Script Date: 24/5/2023 14:11:04 ******/
CREATE TABLE `history_daily_by_derivation_key`(
	`id` int NOT NULL AUTO_INCREMENT NOT NULL,
	`date` DATETIME(6) NOT NULL,
	`interval` INT NOT NULL,
	`interval_datetime` DATETIME(6) NOT NULL,
	`flow_id` INT NOT NULL,
	`derivation_key` VARCHAR(255) NOT NULL,
	`channel` VARCHAR(255) NOT NULL,
	`total` INT NULL,
	`version` INT NULL,
	PRIMARY KEY(`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;

/****** Object:  Table `history_daily_by_flow`    Script Date: 24/5/2023 14:11:04 ******/
CREATE TABLE `history_daily_by_flow`(
	`id` int NOT NULL AUTO_INCREMENT NOT NULL,
	`date` DATETIME(6) NOT NULL,
	`interval` INT NOT NULL,
	`interval_datetime` DATETIME(6) NOT NULL,
	`flow_id` INT NOT NULL,
	`channel` VARCHAR(255) NOT NULL,
	`data` TEXT NULL,
	`new_cases` INT DEFAULT 0,
	`transferred` INT DEFAULT 0,
	`closed_by_yflow` INT DEFAULT 0,
	`new_messages` INT DEFAULT 0,
	`hsm_case` INT DEFAULT 0,
	`case_abandoned` INT DEFAULT 0,
	`monthly_users` INT DEFAULT 0,
	PRIMARY KEY(`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;

/****** Object:  Table `history_daily_by_group`    Script Date: 24/5/2023 14:11:04 ******/
CREATE TABLE `history_daily_by_group`(
	`id` int NOT NULL AUTO_INCREMENT NOT NULL,
	`date` DATETIME(6) NOT NULL,
	`interval` INT NOT NULL,
	`interval_datetime` DATETIME(6) NOT NULL,
	`flow_id` INT NOT NULL,
	`group_id` VARCHAR(50) NOT NULL,
	`channel` VARCHAR(10) NOT NULL,
	`total` INT NULL,
	`version` INT NULL,
	PRIMARY KEY(`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;

/****** Object:  Table `history_daily_by_groups_sequence`    Script Date: 24/5/2023 14:11:04 ******/
CREATE TABLE `history_daily_by_groups_sequence`(
	`id` int NOT NULL AUTO_INCREMENT NOT NULL,
	`date` DATETIME(6) NOT NULL,
	`interval` INT NOT NULL,
	`interval_datetime` DATETIME(6) NOT NULL,
	`flow_id` INT NOT NULL,
	`source_group_id` VARCHAR(50) NULL,
	`dest_group_id` VARCHAR(50) NULL,
	`type` INT NOT NULL,
	`channel` VARCHAR(10) NOT NULL,
	`total` INT NULL,
	`version` INT NULL,
	PRIMARY KEY(`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;

/****** Object:  Table `history_daily_by_integrations`    Script Date: 24/5/2023 14:11:04 ******/
CREATE TABLE `history_daily_by_integrations`(
	`id` int NOT NULL AUTO_INCREMENT NOT NULL,
	`date` DATETIME(6) NOT NULL,
	`interval` INT NOT NULL,
	`interval_datetime` DATETIME(6) NOT NULL,
	`flow_id` INT NOT NULL,
	`integration_id` INT NOT NULL,
	`channel` VARCHAR(255) NOT NULL,
	`total` INT NULL,
	`error` INT NULL,
	`version` INT NULL,
	`total_response_time` DECIMAL(18, 2) DEFAULT 0,
	`error_response_time` DECIMAL(18, 2) DEFAULT 0,
	PRIMARY KEY(`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;
/****** Object:  Table `history_daily_by_statistic_event`    Script Date: 24/5/2023 14:11:04 ******/
CREATE TABLE `history_daily_by_statistic_event`(
	`id` int NOT NULL AUTO_INCREMENT NOT NULL,
	`date` DATETIME(6) NOT NULL,
	`interval` INT NOT NULL,
	`interval_datetime` DATETIME(6) NOT NULL,
	`flow_id` INT NOT NULL,
	`statistic_event_id` INT NOT NULL,
	`channel` VARCHAR(10) NOT NULL,
	`total` INT NULL,
	`version` INT NULL,
	`block_id` VARCHAR(100) NULL,
	PRIMARY KEY(`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;

/****** Object:  Table `migrations`    Script Date: 24/5/2023 14:11:04 ******/
CREATE TABLE `migrations`(
	`id` int NOT NULL AUTO_INCREMENT NOT NULL,
	`name` VARCHAR(255) NOT NULL,
	`run_on` DATETIME(6) NOT NULL,
	PRIMARY KEY(`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;

/****** Object:  Table `reports`    Script Date: 24/5/2023 14:11:04 ******/
CREATE TABLE `reports`(
	`id` int NOT NULL AUTO_INCREMENT NOT NULL,
	`report_type` VARCHAR(50) NOT NULL,
	`date_start` VARCHAR(30) NOT NULL,
	`date_end` VARCHAR(30) NOT NULL,
	`user_id` INT NOT NULL,
	`flow_id` INT NOT NULL,
	`status` VARCHAR(50) NULL,
	`message` VARCHAR(50) NULL,
	`created_at` DATETIME(6) NULL,
	`updated_at` DATETIME(6) NULL,
	`events` TEXT NULL,
	PRIMARY KEY(`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;

/****** Object:  Table `system_status`    Script Date: 24/5/2023 14:11:04 ******/
CREATE TABLE `system_status`(
	`id` int NOT NULL AUTO_INCREMENT NOT NULL,
	`type` VARCHAR(255) NULL,
	`date` DATETIME(6) NOT NULL,
	PRIMARY KEY(`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;

/****** Object:  Table `user_action_logs`    Script Date: 24/5/2023 14:11:04 ******/
CREATE TABLE `user_action_logs`(
	`id` int NOT NULL AUTO_INCREMENT NOT NULL,
	`created_at` DATETIME(6) NULL,
	`action` TEXT NULL,
	`user_id` INT NOT NULL,
	`flow_version_id` INT NOT NULL,
PRIMARY KEY(`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;

/****** Object:  Table `users`    Script Date: 24/5/2023 14:11:04 ******/
CREATE TABLE `users`(
	`id` int NOT NULL AUTO_INCREMENT NOT NULL,
	`name` VARCHAR(255) NOT NULL,
	`password` VARCHAR(255) NULL,
	`created_at` DATETIME(6) NULL,
	`updated_at` DATETIME(6) NULL,
	`enabled` INT NULL,
	`company_id` INT NULL,
	`access_token` VARCHAR(4000) NULL,
	`is_admin` INT NULL,
	`can_edit` INT NULL,
	`can_publish` INT NULL,
	`can_see_statistics` INT NULL,
	`can_validate_passwords` INT NULL,
	`lang` VARCHAR(5) NULL,
	`can_access_ysmart` INT NULL,
	`login_type` VARCHAR(50) DEFAULT 'LOCAL' NULL,
	PRIMARY KEY(`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;

/****** Object:  Table `users_permissions_flows`    Script Date: 24/5/2023 14:11:04 ******/
CREATE TABLE `users_permissions_flows`(
	`id` int NOT NULL AUTO_INCREMENT NOT NULL,
	`user_id` INT NOT NULL,
	`flow_id` INT NOT NULL,
	`can_edit` INT NULL,
	`can_publish` INT NULL,
	`can_see_statistics` INT NULL,
	`can_access_ysmart` INT NULL,
PRIMARY KEY(`id`)
);


SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
CREATE TABLE [dbo].[user_sessions](
    [id] [int] IDENTITY(1,1) NOT NULL,
    [username] [nvarchar](100) NOT NULL,
    [login_time] [datetime2](7) NOT NULL,
    [logout_time] [datetime2](7) NULL,
    [ip_address] [nvarchar](50) NULL,
    [session_duration] [nvarchar](20) NULL,
PRIMARY KEY CLUSTERED 
(
    [id] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON, OPTIMIZE_FOR_SEQUENTIAL_KEY = OFF) ON [PRIMARY]
) ON [PRIMARY]
GO

CREATE NONCLUSTERED INDEX [idx_login_time] ON [dbo].[user_sessions]
(
    [login_time] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, SORT_IN_TEMPDB = OFF, DROP_EXISTING = OFF, ONLINE = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON, OPTIMIZE_FOR_SEQUENTIAL_KEY = OFF) ON [PRIMARY]
GO

CREATE NONCLUSTERED INDEX [idx_username] ON [dbo].[user_sessions]
(
    [username] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, SORT_IN_TEMPDB = OFF, DROP_EXISTING = OFF, ONLINE = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON, OPTIMIZE_FOR_SEQUENTIAL_KEY = OFF) ON [PRIMARY]
GO


/* Indices */
CREATE INDEX IX_ABANDONED_CASE_DATE ON abandoned_case (date ASC);
CREATE INDEX IX_ABANDONED_CASE_FLOW_ID ON abandoned_case (flow_id ASC);
CREATE UNIQUE INDEX IX_COMPANIES_NAME ON companies (name ASC);
CREATE INDEX IX_DETAIL_STATISTIC_EVENT_BY_DATE_FLOW_ID_STATISTIC_EVENT_ID ON detail_statistic_event (flow_id ASC, statistic_event_id ASC, date ASC);
CREATE INDEX IX_DETAIL_STATISTIC_EVENT_DATE ON detail_statistic_event (date ASC);
CREATE INDEX IX_DETAIL_STATISTIC_EVENT_FLOW_ID ON detail_statistic_event (flow_id ASC);
CREATE INDEX IX_FLOW_TABLE_FLOW_ID ON flow_table (flow_id ASC);
CREATE INDEX IX_FLOW_VERSION_FLOW_ID ON flow_versions (flow_id ASC);
CREATE INDEX IX_FLOWS_CHANNEL ON flows (channel ASC);
CREATE INDEX IX_FLOWS_COMPANY_ID ON flows (company_id ASC);
CREATE INDEX IX_FLOWS_ENABLED ON flows (id ASC, company_id ASC, deleted_at ASC);
CREATE UNIQUE INDEX IX_FLOWS_ID ON flows (id ASC);
CREATE INDEX IX_HISTORY_DAILY_DATE ON history_daily (date ASC);
CREATE INDEX IX_HISTORY_DAILY_BY_BLOCKS_BLOCK_ID ON history_daily_by_blocks (block_id ASC);
CREATE INDEX IX_HISTORY_DAILY_BY_BLOCKS_BY_DATE_FLOW_ID_BLOCK_ID ON history_daily_by_blocks (flow_id ASC, date ASC, block_id ASC);
CREATE INDEX IX_HISTORY_DAILY_BY_BLOCKS_DATE ON history_daily_by_blocks (date ASC, flow_id ASC);
CREATE INDEX IX_HISTORY_DAILY_BY_BLOCKS_FLOW_ID ON history_daily_by_blocks (flow_id ASC);
CREATE INDEX IX_HISTORY_DAILY_BY_BLOCKS_SEQUENCE_BY_DATE_FLOW_ID_DEST_BLOCK_ID_SOURCE_BLOCK_ID ON history_daily_by_blocks_sequence (flow_id ASC, source_block_id ASC, dest_block_id ASC, date ASC);
CREATE INDEX IX_HISTORY_DAILY_BY_BLOCKS_SEQUENCE_DATE ON history_daily_by_blocks_sequence (date ASC);
CREATE INDEX IX_HISTORY_DAILY_BY_BLOCKS_SEQUENCE_DEST_BLOCK_ID ON history_daily_by_blocks_sequence (dest_block_id ASC);
CREATE INDEX IX_HISTORY_DAILY_BY_BLOCKS_SEQUENCE_FLOW_ID ON history_daily_by_blocks_sequence (flow_id ASC);
CREATE INDEX IX_HISTORY_DAILY_BY_BLOCKS_SEQUENCE_SOURCE_BLOCK_ID ON history_daily_by_blocks_sequence (source_block_id ASC);
CREATE INDEX IX_HISTORY_DAILY_BY_COMMANDS_BY_DATE_FLOW_ID_COMMAND_ID ON history_daily_by_commands (flow_id ASC, date ASC, command_id ASC);
CREATE INDEX IX_HISTORY_DAILY_BY_COMMANDS_COMMAND_ID ON history_daily_by_commands (command_id ASC);
CREATE INDEX IX_HISTORY_DAILY_BY_COMMANDS_DATE ON history_daily_by_commands (date ASC, flow_id ASC);
CREATE INDEX IX_HISTORY_DAILY_BY_COMMANDS_FLOW_ID ON history_daily_by_commands (flow_id ASC);
CREATE INDEX IX_HISTORY_DAILY_BY_DEFAULT_ANSWERS_BY_DATE_FLOW_ID_BLOCK_ID ON history_daily_by_default_answers (flow_id ASC, date ASC, block_id ASC);
CREATE INDEX IX_HISTORY_DAILY_BY_DEFAULT_ANSWERS_DATE ON history_daily_by_default_answers (date ASC, flow_id ASC);
CREATE INDEX IX_HISTORY_DAILY_BY_DEFAULT_ANSWERS_FLOW_ID ON history_daily_by_default_answers (flow_id ASC);
CREATE INDEX IX_HISTORY_DAILY_BY_DERIVATION_KEY_BY_DATE_FLOW_ID_DERIVATION_KEY ON history_daily_by_derivation_key (flow_id ASC, date ASC, derivation_key ASC);
CREATE INDEX IX_HISTORY_DAILY_BY_DERIVATION_KEY_DATE ON history_daily_by_derivation_key (date ASC, flow_id ASC);
CREATE INDEX IX_HISTORY_DAILY_BY_DERIVATION_KEY_FLOW_ID ON history_daily_by_derivation_key (flow_id ASC);
CREATE INDEX IX_HISTORY_DAILY_BY_FLOW_BY_DATE_FLOW_ID ON history_daily_by_flow (flow_id ASC, date ASC);
CREATE INDEX IX_HISTORY_DAILY_BY_FLOW_DATE ON history_daily_by_flow (date ASC, flow_id ASC);
CREATE INDEX IX_HISTORY_DAILY_BY_FLOW_FLOW_ID ON history_daily_by_flow (flow_id ASC);
CREATE INDEX IX_HISTORY_DAILY_BY_GROUP_BY_DATE_FLOW_ID_GROUP_ID ON history_daily_by_group (flow_id ASC, date ASC, group_id ASC);
CREATE INDEX IX_HISTORY_DAILY_BY_GROUP_DATE ON history_daily_by_group (date ASC, flow_id ASC);
CREATE INDEX IX_HISTORY_DAILY_BY_GROUP_FLOW_ID ON history_daily_by_group (flow_id ASC);
CREATE INDEX IX_HISTORY_DAILY_BLOCKS_SEQ_BY_DATE_FLOW_ID_DEST_SRC_BLOCK_ID ON history_daily_by_blocks_sequence (flow_id ASC, source_block_id ASC, dest_block_id ASC, date ASC);
CREATE INDEX IX_HISTORY_DAILY_BY_GROUPS_SEQUENCE_DATE ON history_daily_by_groups_sequence (date ASC);
CREATE INDEX IX_HISTORY_DAILY_BY_GROUPS_SEQUENCE_DEST_GROUP_ID ON history_daily_by_groups_sequence (dest_group_id ASC);
CREATE INDEX IX_HISTORY_DAILY_BY_GROUPS_SEQUENCE_FLOW_ID ON history_daily_by_groups_sequence (flow_id ASC);
CREATE INDEX IX_HISTORY_DAILY_BY_GROUPS_SEQUENCE_SOURCE_GROUP_ID ON history_daily_by_groups_sequence (source_group_id ASC);
CREATE INDEX IX_HISTORY_DAILY_BY_INTEGRATIONS_BY_DATE_FLOW_ID_INTEGRATION_ID ON history_daily_by_integrations (flow_id ASC, date ASC, integration_id ASC);
CREATE INDEX IX_HISTORY_DAILY_BY_INTEGRATIONS_DATE ON history_daily_by_integrations (`date` ASC, `flow_id` ASC);
CREATE INDEX IX_HISTORY_DAILY_BY_INTEGRATIONS_FLOW_ID ON history_daily_by_integrations (`flow_id` ASC);
CREATE INDEX IX_HISTORY_DAILY_BY_INTEGRATIONS_INTEGRATION_ID ON history_daily_by_integrations (`integration_id` ASC);
CREATE INDEX IX_HISTORY_DAILY_STATS_EVENT_DATE_FLOW_STATS_EVENT ON history_daily_by_statistic_event (`flow_id` ASC, `statistic_event_id` ASC, `date` ASC);
CREATE INDEX IX_HISTORY_DAILY_BY_STATISTIC_EVENT_DATE ON history_daily_by_statistic_event (`date` ASC, `flow_id` ASC);
CREATE INDEX IX_HISTORY_DAILY_BY_STATISTIC_EVENT_FLOW_ID ON history_daily_by_statistic_event (`flow_id` ASC);
CREATE INDEX IX_HISTORY_DAILY_BY_STATISTIC_EVENT_STATS_EVENT ON history_daily_by_statistic_event (`statistic_event_id` ASC);
CREATE INDEX REPORTS_FLOWS_FK ON reports (`user_id` ASC);
CREATE INDEX REPORTS_USERS_FK ON reports (`flow_id` ASC);
CREATE INDEX IX_USER_ACTION_LOGS_FLOW_VERSION_ID ON user_action_logs (`flow_version_id` ASC);
CREATE INDEX IX_USERS_PERMISSIONS_FLOWS_FLOW_ID ON users_permissions_flows (`flow_id` ASC);
CREATE INDEX IX_USERS_PERMISSIONS_FLOWS_USER_ID ON users_permissions_flows (`user_id` ASC);
CREATE INDEX IX_LOGS_DATE ON logs (`date` ASC);
ALTER TABLE `users` 
ADD COLUMN `login_type` VARCHAR(50) DEFAULT 'LOCAL' NULL;
ALTER TABLE `users` 
ADD COLUMN `can_delete` INT DEFAULT 0 NULL;
ALTER TABLE `users` 
ADD COLUMN `is_deleted` INT DEFAULT 0 NULL;


CREATE TABLE IF NOT EXISTS `detailed_events` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `fecha` datetime NOT NULL,
  `accion_realizada` varchar(50) NOT NULL,
  `entidad` varchar(100) NOT NULL,
  `usuario` varchar(100) NOT NULL,
  `usuario_editado` varchar(100) DEFAULT NULL,
  `id_entidad` varchar(50) NOT NULL,
  `nombre_entidad` varchar(100) NOT NULL,
  `propiedad` varchar(100) NOT NULL,
  `valor_actual` text DEFAULT NULL,
  `valor_anterior` text DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `IX_detailed_events_fecha` (`fecha`),
  KEY `IX_detailed_events_usuario` (`usuario`),
  KEY `IX_detailed_events_entidad` (`entidad`),
  KEY `IX_detailed_events_accion` (`accion_realizada`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;