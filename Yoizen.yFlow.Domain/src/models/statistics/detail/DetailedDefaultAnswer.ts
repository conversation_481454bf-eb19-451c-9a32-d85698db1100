import { Moment } from "moment";
import { DetailedBase, DetailedInfoTypes } from "./DetailedBase";
import { DataTypes } from "sequelize";
import sequelize from '../../../../../Yoizen.yFlow.Web/helpers/sequelize';

export class DetailedDefaultAnswer extends DetailedBase {
    declare flowId: any;
    declare channel: any;
    declare version: any;
    declare data: { text: any; messageId: string; caseId: string; lastBlockId: string; lastBlockName: string; };

    init(datetime: Moment, data: { flowId: number, text: string, channel: string, messageId: string, caseId: string, lastBlockId: string, lastBlockName: string, version: number }) {
        this.initBase(datetime);
        this.flowId = data.flowId;
        this.channel = data.channel;
        this.version = data.version;

        this.data = {
            text: data.text,
            messageId: data.messageId,
            caseId: data.caseId,
            lastBlockId: data.lastBlockId,
            lastBlockName: data.lastBlockName
        }
    }

    type() {
        return DetailedInfoTypes.DefaultAnswers;
    }
}

DetailedDefaultAnswer.init({
    date: DataTypes.DATE,
    channel: DataTypes.STRING,
    data: DataTypes.JSON,
    version: DataTypes.INTEGER,
    flowId: {
        type: DataTypes.INTEGER,
        field: 'flow_id'
    },
}, {
    sequelize: sequelize,
    modelName: 'no_hace_falta_tabla',
    tableName: 'no_hace_falta_tabla',
    timestamps: false
})