import { HistoryDailyBase, HistoryDailyInfoTypes } from "./HistoryDailyBase";
import { Moment } from "moment";
import { DataTypes } from 'sequelize';
import sequelize from '../../../../../Yoizen.yFlow.Web/helpers/sequelize';
import { isNullOrUndefined } from "../../../../../Yoizen.yFlow.Helpers/src/Helpers";

export class HistoryDaily extends HistoryDailyBase {
    declare newCase: number;
    declare transferredToYSocial: number;
    declare closedByYFlow: number;
    declare newMessage: number;
    declare hsmCase: number;
    declare caseAbandoned: number;
    declare monthlyUsers: number;

    init(datetime: Moment, daily) {
        this.initBase(datetime);
        this.newCase = isNullOrUndefined(daily.newCase) ? 0 : parseInt(daily.newCase);
        this.newCase = isNullOrUndefined(daily.newCase) ? 0 : parseInt(daily.newCase);
        this.transferredToYSocial = isNullOrUndefined(daily.transferredToYSocial) ? 0 : parseInt(daily.transferredToYSocial);
        this.closedByYFlow = isNullOrUndefined(daily.closedByYFlow) ? 0 : parseInt(daily.closedByYFlow);
        this.newMessage = isNullOrUndefined(daily.newMessage) ? 0 : parseInt(daily.newMessage);
        this.hsmCase = isNullOrUndefined(daily.hsmCase) ? 0 : parseInt(daily.hsmCase);
        this.caseAbandoned = isNullOrUndefined(daily.caseAbandoned) ? 0 : parseInt(daily.caseAbandoned);
        this.monthlyUsers = isNullOrUndefined(daily.monthlyUsers) ? 0 : parseInt(daily.monthlyUsers);
    }

    type() {
        return HistoryDailyInfoTypes.Normal;
    }
}

HistoryDaily.init({
    /*id: {
        type: Sequelize.INTEGER,
        primaryKey: true
    },*/
    date: DataTypes.DATE,
    datetime: {
        type: DataTypes.DATE,
        field: 'interval_datetime'
    },
    interval: DataTypes.INTEGER,
    newCase: {
        type: DataTypes.INTEGER,
        field: 'new_cases'
    },
    transferredToYSocial: {
        type: DataTypes.INTEGER,
        field: 'transferred'
    },
    closedByYFlow: {
        type: DataTypes.INTEGER,
        field: 'closed_by_yflow'
    },
    newMessage: {
        type: DataTypes.INTEGER,
        field: 'new_messages'
    },
    hsmCase: {
        type: DataTypes.INTEGER,
        field: 'hsm_case'
    },
    caseAbandoned: {
        type: DataTypes.INTEGER,
        field: 'case_abandoned'
    },
    monthlyUsers: {
        type: DataTypes.INTEGER,
        field: 'monthly_users'
    }
}, {
    sequelize: sequelize,
    modelName: 'history_daily',
    tableName: 'history_daily',
    timestamps: false
});
