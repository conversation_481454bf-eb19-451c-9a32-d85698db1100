import { HistoryDailyBase, HistoryDailyInfoTypes } from "./HistoryDailyBase";
import { Moment } from "moment";
import { DataTypes } from 'sequelize';
import sequelize from '../../../../../Yoizen.yFlow.Web/helpers/sequelize';

export class HistoryDailyByCommands extends HistoryDailyBase {
    declare channel: any;
    declare flowId: any;
    declare commandId: any;
    declare version: any;
    declare total: number;
    command: { id: any; name: any; };

    init(datetime: Moment, data) {
        this.initBase(datetime);
        this.channel = data.channel;
        this.flowId = data.flowId;
        this.commandId = data.commandId;
        this.version = data.version;
        this.total = data.total;
        this.command = {
            id: data.commandId,
            name: undefined
        }
    }

    type() {
        return HistoryDailyInfoTypes.Commnads;
    }
}

HistoryDailyByCommands.init({
    /*id: {
        type: Sequelize.INTEGER,
        primaryKey: true
    },*/
    date: DataTypes.DATE,
    datetime: {
        type: DataTypes.DATE,
        field: 'interval_datetime'
    },
    interval: DataTypes.INTEGER,
    total: DataTypes.INTEGER,
    version: DataTypes.INTEGER,
    flowId: {
        type: DataTypes.INTEGER,
        field: 'flow_id'
    },
    commandId: {
        type: DataTypes.INTEGER,
        field: 'command_id',
    },
    channel: DataTypes.STRING
}, {
    sequelize: sequelize,
    modelName: 'history_daily_by_commands',
    tableName: 'history_daily_by_commands',
    timestamps: false
})