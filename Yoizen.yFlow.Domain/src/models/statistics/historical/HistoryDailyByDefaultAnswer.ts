import { HistoryDailyBase, HistoryDailyInfoTypes } from "./HistoryDailyBase";
import { Moment } from "moment";
import { DataTypes } from 'sequelize';
import sequelize from '../../../../../Yoizen.yFlow.Web/helpers/sequelize';

export class HistoryDailyByDefaultAnswer extends HistoryDailyBase {
    declare channel: string;
    declare flowId: number;
    declare version: number;
    declare total: number;
    block: { id: string; name: string; };
    declare blockId: string;

    init(datetime: Moment, data) {
        this.initBase(datetime);
        this.channel = data.channel;
        this.flowId = data.flowId;
        this.blockId = data.blockId;
        this.version = data.version;
        this.total = data.total;
        this.block = {
            id: data.blockId,
            name: undefined
        }
    }
}

HistoryDailyByDefaultAnswer.init({
    /*id: {
        type: Sequelize.INTEGER,
        primaryKey: true
    },*/
    date: DataTypes.DATE,
    datetime: {
        type: DataTypes.DATE,
        field: 'interval_datetime'
    },
    interval: DataTypes.INTEGER,
    total: DataTypes.INTEGER,
    version: DataTypes.INTEGER,
    flowId: {
        type: DataTypes.INTEGER,
        field: 'flow_id'
    },
    blockId: {
        type: DataTypes.STRING,
        field: 'block_id',
    },
    channel: DataTypes.STRING
}, {
    sequelize: sequelize,
    modelName: 'history_daily_by_default_answers',
    tableName: 'history_daily_by_default_answers',
    timestamps: false
});