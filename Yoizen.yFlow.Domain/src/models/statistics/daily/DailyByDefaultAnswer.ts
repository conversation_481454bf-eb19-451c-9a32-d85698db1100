import { DailyBase, DailyInfoTypes } from "./DailyBase";
import { Moment } from "moment";

export class DailyByDefaultAnswer extends DailyBase {
    channel: string;
    flowId: number;
    version: number;
    total: number;
    block: { id: string; name: string; };
    blockId: string;

    constructor(datetime: Moment, data?: { flowId: number, channel: string, blockId: string, version: number }) {
        super(datetime);
        if (data) {
            this.channel = data.channel;
            this.flowId = data.flowId;
            this.blockId = data.blockId;
            this.version = data.version;

            this.block = {
                id: data.blockId,
                name: undefined
            }
        }
        this.total = 0;
    }

    type() {
        return DailyInfoTypes.DefaultAnswers;
    }

    getColumns(): Array<string> {
        return ["date", "interval", "interval_datetime", "flow_id", "block_id", "channel", "total", "version"];
    }

    getValues(): Array<any> {
        return [this.date.utc().format('YYYY-MM-DD hh:mm:ss'), this.interval, this.datetime.utc().format('YYYY-MM-DD hh:mm:ss'), this.flowId, this.blockId, this.channel, this.total, this.version];
    }

    getType() {
        return 'daily_default_answers';
    }
}