﻿import { DailyBase, DailyInfoTypes } from "./DailyBase";
import { Moment } from "moment";

export class DailyByBlocks extends DailyBase {
    channel: string;
    flowId: number;
    blockId: string;
    version: number;
    total: number;
    block: { id: string; name: string; };

    constructor(datetime: Moment, data?: { flowId: number, channel: string, blockId: string, version: number }) {
        super(datetime);
        if (data) {
            this.flowId = data.flowId;
            this.channel = data.channel;
            this.blockId = data.blockId;
            this.version = data.version;
            this.block = {
                id: data.blockId,
                name: undefined
            }
        }
        this.total = 0;
    }

    type() {
        return DailyInfoTypes.Blocks;
    }

    getType() {
        return 'daily_blocks';
    }

    getColumns(): Array<string> {
        return ["date", "interval", "interval_datetime", "flow_id", "block_id", "channel", "version", "total"];
    }

    getValues(): Array<any> {
        return [this.date.utc().format('YYYY-MM-DD hh:mm:ss'), this.interval, this.datetime.utc().format('YYYY-MM-DD hh:mm:ss'), this.flowId, this.blockId, this.channel, this.version, this.total];
    }
}